# Get Count of task-sessions by difficulty and wallet address API

### Method: `GET`
### URI: `/api/task-sessions/count-by-difficulty`
### HEADER: `X-API-KEY: ${api-key}`
### PARAMS: `difficulty=`, `seasonId=`, `address=`
> e.g. api-key=1TAf7rWvQ5f0xGb5l9DvUkNCzQR_Zn-NqvrjPAekSIY=

> difficulty=beginner | intermediate | advanced | expert

> seasonId=3

> address=******************************************

### RESPONSE: `{ data: ${count}, success: true }`

# Way to create api-key:

USE Admin account call:
> POST /v1/api-keys 
> { appName, appId, type: 'data-service', signatureRequired: false }
