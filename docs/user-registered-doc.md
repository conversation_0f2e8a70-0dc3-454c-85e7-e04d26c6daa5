# Is registered of users API

### Method: `GET`
### URI: `/api/users/registered`
### HEADER: `X-API-KEY: ${api-key}`
### PARAMS: `address=`
> e.g. api-key=1TAf7rWvQ5f0xGb5l9DvUkNCzQR_Zn-NqvrjPAekSIY=

> address=******************************************

### RESPONSE: `{ data: ${true | false}, success: true }`

# Way to create api-key:

USE Admin account call:
> POST /v1/api-keys 
> { appName, appId, type: 'data-service', signatureRequired: false }
