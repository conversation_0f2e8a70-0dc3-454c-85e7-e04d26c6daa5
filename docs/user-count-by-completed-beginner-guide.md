# Get Count of users by completed beginner guide API

### Method: `GET`
### URI: `/api/users/count-by-completed-beginner-guide`
### HEADER: `X-API-KEY: ${api-key}`
### PARAMS: None
> e.g. api-key=1TAf7rWvQ5f0xGb5l9DvUkNCzQR_Zn-NqvrjPAekSIY=

### RESPONSE: `{ data: ${count}, success: true }`

# Way to create api-key:

USE Admin account call:
> POST /v1/api-keys 
> { appName, appId, type: 'data-service', signatureRequired: false }
