diff --git a/build.gradle.kts b/build.gradle.kts
index 07b92b02..d4f5c526 100644
--- a/build.gradle.kts
+++ b/build.gradle.kts
@@ -12,7 +12,6 @@ plugins {
   alias(libs.plugins.org.openapi.generator)
   alias(libs.plugins.com.diffplug.spotless)
   alias(libs.plugins.net.ltgt.errorprone)
-  id("io.freefair.lombok") version "8.10"
 }
 
 repositories {
@@ -32,7 +31,9 @@ dependencies {
   implementation(libs.io.kamon.kamon.core.v2.v13)
   implementation(libs.io.kamon.kamon.akka.v2.v13)
   implementation(libs.io.kamon.kamon.akka.http.v2.v13)
+  implementation(libs.io.kamon.kanela.agent)
   implementation(libs.io.kamon.kamon.prometheus.v2.v13)
+  implementation(libs.net.bytebuddy.byte.buddy.agent)
   implementation(libs.cn.hutool.hutool.core)
   implementation(libs.com.auth0.java.jwt)
   implementation(libs.com.baomidou.mybatis.plus.spring.boot3.starter)
@@ -86,32 +87,28 @@ dependencies {
   implementation(libs.systems.manifold.manifold.ext.rt)
   implementation(libs.org.roaringbitmap.roaringbitmap)
 
-  // These agents are loaded dynamically using byte-buddy-agent
-  implementation(libs.net.bytebuddy.byte.buddy.agent)
-  implementation("org.springframework:spring-instrument")
-  implementation("org.aspectj:aspectjweaver:********")
-  implementation(libs.io.kamon.kanela.agent)
-
-  // This contains some useful aspects for Spring, including support for @Transactional
-  // on private methods and self-invocations, etc.
-  implementation("org.springframework:spring-aspects:6.1.12")
-
+  compileOnly(libs.org.projectlombok.lombok)
+  annotationProcessor(libs.org.projectlombok.lombok)
   annotationProcessor(libs.systems.manifold.manifold.exceptions)
   annotationProcessor(libs.systems.manifold.manifold.ext)
   annotationProcessor(libs.systems.manifold.manifold.strings)
 
+  testCompileOnly(libs.org.projectlombok.lombok)
+  testAnnotationProcessor(libs.org.projectlombok.lombok)
   testAnnotationProcessor(libs.systems.manifold.manifold.exceptions)
   testAnnotationProcessor(libs.systems.manifold.manifold.ext)
   testAnnotationProcessor(libs.systems.manifold.manifold.strings)
 
+
   runtimeOnly(libs.dev.akkinoc.spring.boot.logback.access.spring.boot.starter)
   runtimeOnly(libs.io.micrometer.micrometer.registry.prometheus)
   runtimeOnly(libs.software.amazon.awssdk.crt.aws.crt)
   runtimeOnly(libs.org.springframework.boot.spring.boot.devtools)
 
-  // For Spring tracing and reporting to Grafana tempo using zipkin format.
-  implementation(libs.io.micrometer.micrometer.tracing.bridge.brave)
-  implementation(libs.io.zipkin.reporter2.zipkin.reporter.brave)
+  // For Spring tracing and reporting to Grafana tempo using zipkin format. Consider converting
+  // to implementation dependency if tracing features are needed in code.
+  runtimeOnly(libs.io.micrometer.micrometer.tracing.bridge.brave)
+  runtimeOnly(libs.io.zipkin.reporter2.zipkin.reporter.brave)
 
   testImplementation(libs.com.github.codemonstur.embedded.redis)
   testImplementation(libs.com.squareup.okhttp3.mockwebserver)
@@ -182,7 +179,7 @@ tasks.openApiGenerate {
 // Add generated sources to main source set
 sourceSets {
   main {
-    java.srcDir(files("${openApiGenerate.outputDir.get()}/src/main").builtBy(tasks.openApiGenerate))
+    java.srcDir("${openApiGenerate.outputDir.get()}/src/main")
   }
 }
 
diff --git a/gradle/libs.versions.toml b/gradle/libs.versions.toml
index e91d184b..0b6d9547 100644
--- a/gradle/libs.versions.toml
+++ b/gradle/libs.versions.toml
@@ -33,7 +33,7 @@ com-squareup-okhttp3-okhttp-tls = "4.12.0"
 com-statsig-serversdk = "1.22.0"
 com-typesafe-akka-akka-http-v2-v13 = "10.2.6"
 dev-akkinoc-spring-boot-logback-access-spring-boot-starter = "4.0.0"
-io-micrometer-micrometer-registry-prometheus = "1.13.3"
+io-micrometer-micrometer-registry-prometheus = "1.11.2"
 io-micrometer-micrometer-tracing-bridge-brave = "1.3.3"
 io-zipkin-reporter2-zipkin-reporter-brave = "3.4.0"
 io-zonky-test-embedded-database-spring-test = "2.5.1"
@@ -67,6 +67,7 @@ org-openapi-generator = { id = "org.openapi.generator", version.ref = "org-opena
 com-diffplug-spotless = { id = "com.diffplug.spotless", version.ref = "com-diffplug-spotless" }
 net-ltgt-errorprone = { id = "net.ltgt.errorprone", version.ref = "net-ltgt-errorprone" }
 
+
 [libraries]
 cn-hutool-hutool-core = { module = "cn.hutool:hutool-core", version.ref = "cn-hutool-hutool-core" }
 com-auth0-java-jwt = { module = "com.auth0:java-jwt", version.ref = "com-auth0-java-jwt" }
@@ -155,4 +156,4 @@ systems-manifold-manifold-strings = { module = "systems.manifold:manifold-string
 systems-manifold-manifold-exceptions = { module = "systems.manifold:manifold-exceptions", version.ref = "systems-manifold-manifold" }
 
 io-micrometer-micrometer-tracing-bridge-brave = { module = "io.micrometer:micrometer-tracing-bridge-brave", version.ref = "io-micrometer-micrometer-tracing-bridge-brave" }
-io-zipkin-reporter2-zipkin-reporter-brave = { module = "io.zipkin.reporter2:zipkin-reporter-brave", version.ref = "io-zipkin-reporter2-zipkin-reporter-brave" }
\ No newline at end of file
+io-zipkin-reporter2-zipkin-reporter-brave = { module = "io.zipkin.reporter2:zipkin-reporter-brave", version.ref = "io-zipkin-reporter2-zipkin-reporter-brave" }
diff --git a/pom.xml b/pom.xml
index 380c86ef..e8ce59f7 100644
--- a/pom.xml
+++ b/pom.xml
@@ -412,15 +412,6 @@
       <artifactId>RoaringBitmap</artifactId>
       <version>1.2.1</version>
     </dependency>
-    <dependency>
-      <groupId>org.springframework</groupId>
-      <artifactId>spring-instrument</artifactId>
-    </dependency>
-    <dependency>
-      <groupId>org.aspectj</groupId>
-      <artifactId>aspectjweaver</artifactId>
-      <version>********</version>
-    </dependency>
   </dependencies>
   <dependencyManagement>
     <dependencies>
diff --git a/src/main/java/ai/saharaa/AiPlatformApplication.java b/src/main/java/ai/saharaa/AiPlatformApplication.java
index d2059f05..205e853b 100644
--- a/src/main/java/ai/saharaa/AiPlatformApplication.java
+++ b/src/main/java/ai/saharaa/AiPlatformApplication.java
@@ -4,11 +4,9 @@ import java.lang.instrument.Instrumentation;
 import kamon.Kamon;
 import kanela.agent.Kanela;
 import net.bytebuddy.agent.ByteBuddyAgent;
-import org.aspectj.weaver.loadtime.Agent;
 import org.mybatis.spring.annotation.MapperScan;
 import org.springframework.boot.SpringApplication;
 import org.springframework.boot.autoconfigure.SpringBootApplication;
-import org.springframework.instrument.InstrumentationSavingAgent;
 
 @SpringBootApplication
 @MapperScan("ai.saharaa.mappers")
@@ -16,12 +14,6 @@ public class AiPlatformApplication {
   public static void preStart() {
     Instrumentation instrumentation = ByteBuddyAgent.install();
     Kanela.premain("", instrumentation);
-    // spring-instrument.jar, handles Spring AOP with AspectJ load time weaving. Without this,
-    // Spring AOP will be proxy based, and does not support private methods, self-invocation, etc.
-    InstrumentationSavingAgent.premain("", instrumentation);
-
-    // aspectjweaver.jar, handles AspectJ load time weaving in non-Spring classes.
-    Agent.premain("", instrumentation);
   }
 
   public static void main(String[] args) {
diff --git a/src/main/java/ai/saharaa/ApplicationConfiguration.java b/src/main/java/ai/saharaa/ApplicationConfiguration.java
index 162889f6..b80bf428 100644
--- a/src/main/java/ai/saharaa/ApplicationConfiguration.java
+++ b/src/main/java/ai/saharaa/ApplicationConfiguration.java
@@ -28,11 +28,9 @@ import org.springframework.boot.web.servlet.FilterRegistrationBean;
 import org.springframework.context.ApplicationContext;
 import org.springframework.context.annotation.Bean;
 import org.springframework.context.annotation.Configuration;
-import org.springframework.context.annotation.EnableLoadTimeWeaving;
 import org.springframework.web.filter.ShallowEtagHeaderFilter;
 
 @Configuration
-@EnableLoadTimeWeaving(aspectjWeaving = EnableLoadTimeWeaving.AspectJWeaving.ENABLED)
 class ApplicationConfiguration {
   private final Logger logger = LoggerFactory.getLogger(ApplicationConfiguration.class);
 
diff --git a/src/main/java/ai/saharaa/actors/HttpClientActor.java b/src/main/java/ai/saharaa/actors/HttpClientActor.java
index 1bd279da..f7bdb2de 100644
--- a/src/main/java/ai/saharaa/actors/HttpClientActor.java
+++ b/src/main/java/ai/saharaa/actors/HttpClientActor.java
@@ -5,10 +5,9 @@ import static ai.saharaa.utils.Constants.recaptchaBodyTemplate;
 import static ai.saharaa.utils.Constants.recaptchaBodyTemplate2;
 
 import ai.saharaa.actors.tasks.BatchSampleActor;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
 import ai.saharaa.model.SimpleResult;
-import ai.saharaa.model.TraceableMessage;
 import akka.actor.ActorSystem;
 import akka.dispatch.ExecutionContexts;
 import akka.http.javadsl.Http;
@@ -124,7 +123,7 @@ public class HttpClientActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long sampleId;
 
     public OpBase(Long id) {
diff --git a/src/main/java/ai/saharaa/actors/PingActor.java b/src/main/java/ai/saharaa/actors/PingActor.java
index 799a5d0c..aac98576 100644
--- a/src/main/java/ai/saharaa/actors/PingActor.java
+++ b/src/main/java/ai/saharaa/actors/PingActor.java
@@ -1,9 +1,8 @@
 package ai.saharaa.actors;
 
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.SimpleResult;
 import ai.saharaa.model.TaskSession;
-import ai.saharaa.model.TraceableMessage;
 import akka.actor.UntypedAbstractActor;
 import java.util.Optional;
 import lombok.NoArgsConstructor;
@@ -19,7 +18,7 @@ public class PingActor extends UntypedAbstractActor {
   private final Logger log = LoggerFactory.getLogger(PingActor.class);
 
   @NoArgsConstructor
-  public static final class Cmd extends TraceableMessage implements IClusterMessage {
+  public static final class Cmd implements IMessage {
     public String data;
     private Optional<String> name;
     private long id;
diff --git a/src/main/java/ai/saharaa/actors/Resource/ResourceSyncActor.java b/src/main/java/ai/saharaa/actors/Resource/ResourceSyncActor.java
index 00a69e2c..a975b55a 100644
--- a/src/main/java/ai/saharaa/actors/Resource/ResourceSyncActor.java
+++ b/src/main/java/ai/saharaa/actors/Resource/ResourceSyncActor.java
@@ -1,12 +1,13 @@
 package ai.saharaa.actors.Resource;
 
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.hybridTask.HybridItemTask;
 import ai.saharaa.model.hybridTask.ZipFile;
 import ai.saharaa.services.AwsS3SyncTaskService;
 import ai.saharaa.services.JsonUploadService;
 import akka.actor.AbstractActorWithTimers;
 import java.time.Duration;
+import java.util.concurrent.*;
 import java.util.stream.Collectors;
 import lombok.NoArgsConstructor;
 import org.springframework.beans.factory.config.ConfigurableBeanFactory;
@@ -71,11 +72,11 @@ public class ResourceSyncActor extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class ResourceSync extends TraceableMessage {}
+  private static final class ResourceSync implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class JsonUploadItemsSync extends TraceableMessage {}
+  private static final class JsonUploadItemsSync implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class UnZipFileSync extends TraceableMessage {}
+  private static final class UnZipFileSync implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/SendEmailActor.java b/src/main/java/ai/saharaa/actors/SendEmailActor.java
index 370c6d7c..f7ee9248 100644
--- a/src/main/java/ai/saharaa/actors/SendEmailActor.java
+++ b/src/main/java/ai/saharaa/actors/SendEmailActor.java
@@ -1,7 +1,7 @@
 package ai.saharaa.actors;
 
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.SimpleResult;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.EmailService;
 import akka.actor.AbstractActor;
 import lombok.Getter;
@@ -57,7 +57,7 @@ public class SendEmailActor extends AbstractActor {
 
   @Getter
   @NoArgsConstructor
-  public static final class SendVerificationEmail extends TraceableMessage {
+  public static final class SendVerificationEmail implements BaseMessage {
     private String accountName;
     private String reception;
     private String code;
@@ -71,7 +71,7 @@ public class SendEmailActor extends AbstractActor {
 
   @Getter
   @NoArgsConstructor
-  public static final class SendResetPasswordEmail extends TraceableMessage {
+  public static final class SendResetPasswordEmail implements BaseMessage {
     private String accountName;
     private String reception;
     private String token;
diff --git a/src/main/java/ai/saharaa/actors/UserRegisterActor.java b/src/main/java/ai/saharaa/actors/UserRegisterActor.java
index 3b3a8311..04ecff2b 100644
--- a/src/main/java/ai/saharaa/actors/UserRegisterActor.java
+++ b/src/main/java/ai/saharaa/actors/UserRegisterActor.java
@@ -2,7 +2,7 @@ package ai.saharaa.actors;
 
 import ai.saharaa.config.cluster.ClusterConfiguration;
 import ai.saharaa.mappers.UserMapper;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.User;
 import ai.saharaa.services.UserService;
 import ai.saharaa.services.WalletAddressWhitelistService;
@@ -12,7 +12,6 @@ import java.util.Optional;
 import lombok.AllArgsConstructor;
 import lombok.Builder;
 import lombok.Data;
-import lombok.EqualsAndHashCode;
 import lombok.NoArgsConstructor;
 import org.springframework.beans.factory.annotation.Value;
 import org.springframework.beans.factory.config.ConfigurableBeanFactory;
@@ -118,12 +117,12 @@ public class UserRegisterActor extends ActorBase {
     return this.userService.createUserNoCheck(registerUser);
   }
 
-  @EqualsAndHashCode(callSuper = false)
   @Data
   @Builder
+  // @formatter:off
   @NoArgsConstructor
   @AllArgsConstructor
-  public static final class RegisterUser extends TraceableMessage {
+  public static final class RegisterUser implements BaseMessage {
     private String walletAddress;
     private int role;
     private String firstName;
@@ -137,12 +136,13 @@ public class UserRegisterActor extends ActorBase {
     private Optional<String> hashPassword = Optional.empty();
   }
 
-  @EqualsAndHashCode(callSuper = false)
+  // @formatter:on
   @Data
   @Builder
+  // @formatter:off
   @NoArgsConstructor
   @AllArgsConstructor
-  public static final class RegisterUserOlympic extends TraceableMessage {
+  public static final class RegisterUserOlympic implements BaseMessage {
     private String walletAddress;
     private int role;
     private String firstName;
diff --git a/src/main/java/ai/saharaa/actors/UserSessionActor.java b/src/main/java/ai/saharaa/actors/UserSessionActor.java
index 0de6e881..5968c3e9 100644
--- a/src/main/java/ai/saharaa/actors/UserSessionActor.java
+++ b/src/main/java/ai/saharaa/actors/UserSessionActor.java
@@ -1,7 +1,6 @@
 package ai.saharaa.actors;
 
-import ai.saharaa.model.IClusterMessage;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.UserSession;
 import ai.saharaa.services.UserSessionService;
 import ai.saharaa.utils.ControllerUtils;
@@ -245,7 +244,7 @@ public class UserSessionActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long userId;
 
     public OpBase(Long userId) {
diff --git a/src/main/java/ai/saharaa/actors/achievement/AchievementReqs.java b/src/main/java/ai/saharaa/actors/achievement/AchievementReqs.java
deleted file mode 100644
index 8b137891..00000000
--- a/src/main/java/ai/saharaa/actors/achievement/AchievementReqs.java
+++ /dev/null
@@ -1 +0,0 @@
-
diff --git a/src/main/java/ai/saharaa/actors/achievement/AchievementRouter.java b/src/main/java/ai/saharaa/actors/achievement/AchievementRouter.java
index a0542332..2fbc9abe 100644
--- a/src/main/java/ai/saharaa/actors/achievement/AchievementRouter.java
+++ b/src/main/java/ai/saharaa/actors/achievement/AchievementRouter.java
@@ -4,7 +4,7 @@ import static ai.saharaa.actors.achievement.AchievementTriggers.*;
 import static ai.saharaa.model.achievement.Achievement.Symbol.*;
 
 import ai.saharaa.config.cluster.ClusterConfiguration;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.utils.ActorUtils;
 import akka.actor.ActorRef;
 import java.util.concurrent.CompletionStage;
@@ -18,7 +18,7 @@ import org.springframework.stereotype.Component;
 public class AchievementRouter {
   private final ClusterConfiguration clusterConfiguration;
 
-  public CompletionStage trigger(IClusterMessage message) {
+  public CompletionStage trigger(IMessage message) {
     // TODO upgrade to java21 case SaharaPoint m ->
     if (message instanceof SaharaPoint m) {
       return ask(clusterConfiguration.achievementFlushActor, Req.of(m.getId(), RISING_STAR));
diff --git a/src/main/java/ai/saharaa/actors/achievement/AchievementTriggers.java b/src/main/java/ai/saharaa/actors/achievement/AchievementTriggers.java
index 13e5dab7..1654355d 100644
--- a/src/main/java/ai/saharaa/actors/achievement/AchievementTriggers.java
+++ b/src/main/java/ai/saharaa/actors/achievement/AchievementTriggers.java
@@ -1,7 +1,6 @@
 package ai.saharaa.actors.achievement;
 
-import ai.saharaa.model.IClusterMessage;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.IMessage;
 import lombok.Getter;
 import lombok.NoArgsConstructor;
 
@@ -11,7 +10,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class SaharaPoint extends TraceableMessage implements IClusterMessage {
+  public static class SaharaPoint implements IMessage {
     private Long id;
 
     public SaharaPoint(Long id) {
@@ -26,7 +25,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class VerifyEmail extends TraceableMessage implements IClusterMessage {
+  public static class VerifyEmail implements IMessage {
     private Long id;
 
     public VerifyEmail(Long id) {
@@ -41,7 +40,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class Referral extends TraceableMessage implements IClusterMessage {
+  public static class Referral implements IMessage {
     private Long userId;
     private Long inviterId;
 
@@ -58,7 +57,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class Review extends TraceableMessage implements IClusterMessage {
+  public static class Review implements IMessage {
     private Long id;
     private Long reviewerId;
     private Long labelerId;
@@ -79,7 +78,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class JobUserPoints extends TraceableMessage implements IClusterMessage {
+  public static class JobUserPoints implements IMessage {
     private Long userId;
 
     public JobUserPoints(Long userId) {
@@ -94,7 +93,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class UserPointsDetails extends TraceableMessage implements IClusterMessage {
+  public static class UserPointsDetails implements IMessage {
     private Long userId;
 
     public UserPointsDetails(Long userId) {
@@ -109,7 +108,7 @@ public class AchievementTriggers {
 
   @Getter
   @NoArgsConstructor
-  public static class UserDailyVisit extends TraceableMessage implements IClusterMessage {
+  public static class UserDailyVisit implements IMessage {
     private Long userId;
 
     public UserDailyVisit(Long userId) {
diff --git a/src/main/java/ai/saharaa/actors/achievement/OnChainCheckDailyScheduler.java b/src/main/java/ai/saharaa/actors/achievement/OnChainCheckDailyScheduler.java
index 5b780512..ae004e7e 100644
--- a/src/main/java/ai/saharaa/actors/achievement/OnChainCheckDailyScheduler.java
+++ b/src/main/java/ai/saharaa/actors/achievement/OnChainCheckDailyScheduler.java
@@ -99,9 +99,9 @@ public class OnChainCheckDailyScheduler extends AbstractActorWithTimers {
 
   @Getter
   @NoArgsConstructor
-  public static class AchievementOnChainCheck extends TraceableMessage {}
+  public static class AchievementOnChainCheck implements BaseMessage {}
 
   @Getter
   @NoArgsConstructor
-  public static class SaharaLevelOnChainCheck extends TraceableMessage {}
+  public static class SaharaLevelOnChainCheck implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/achievement/Req.java b/src/main/java/ai/saharaa/actors/achievement/Req.java
index 3761a61f..a1ddb9cd 100644
--- a/src/main/java/ai/saharaa/actors/achievement/Req.java
+++ b/src/main/java/ai/saharaa/actors/achievement/Req.java
@@ -1,11 +1,11 @@
 package ai.saharaa.actors.achievement;
 
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.achievement.Achievement;
 import lombok.Data;
 
 @Data
-public class Req implements IClusterMessage {
+public class Req implements IMessage {
   private final Long id;
   private final Achievement.Symbol symbol;
 
diff --git a/src/main/java/ai/saharaa/actors/achievement/UserSaharaLevelActor.java b/src/main/java/ai/saharaa/actors/achievement/UserSaharaLevelActor.java
index cede323f..82e88b94 100644
--- a/src/main/java/ai/saharaa/actors/achievement/UserSaharaLevelActor.java
+++ b/src/main/java/ai/saharaa/actors/achievement/UserSaharaLevelActor.java
@@ -1,8 +1,7 @@
 package ai.saharaa.actors.achievement;
 
 import ai.saharaa.actors.ActorBase;
-import ai.saharaa.model.IClusterMessage;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.services.achievement.SaharaLevelService;
 import lombok.AllArgsConstructor;
 import lombok.Getter;
@@ -37,7 +36,7 @@ public class UserSaharaLevelActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class UserSaharaLevelUp extends TraceableMessage implements IClusterMessage {
+  public static class UserSaharaLevelUp implements IMessage {
     private Long userId;
     private Long exp;
 
diff --git a/src/main/java/ai/saharaa/actors/batch/BatchContentActor.java b/src/main/java/ai/saharaa/actors/batch/BatchContentActor.java
index 8b43e1d5..f4e52652 100644
--- a/src/main/java/ai/saharaa/actors/batch/BatchContentActor.java
+++ b/src/main/java/ai/saharaa/actors/batch/BatchContentActor.java
@@ -113,7 +113,7 @@ public class BatchContentActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long batchId;
 
     public OpBase(Long batchId) {
diff --git a/src/main/java/ai/saharaa/actors/batch/BatchJobManagerActor.java b/src/main/java/ai/saharaa/actors/batch/BatchJobManagerActor.java
index a9f58b76..b7403594 100644
--- a/src/main/java/ai/saharaa/actors/batch/BatchJobManagerActor.java
+++ b/src/main/java/ai/saharaa/actors/batch/BatchJobManagerActor.java
@@ -2,9 +2,8 @@ package ai.saharaa.actors.batch;
 
 import ai.saharaa.actors.ActorBase;
 import ai.saharaa.daos.JobInvitationDao;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Job;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.JobService;
 import lombok.Getter;
 import lombok.NoArgsConstructor;
@@ -66,7 +65,7 @@ public class BatchJobManagerActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long batchId;
 
     protected OpBase(Long batchId) {
diff --git a/src/main/java/ai/saharaa/actors/batch/DataTaskScheduler.java b/src/main/java/ai/saharaa/actors/batch/DataTaskScheduler.java
index 9ef232c9..820ffa2c 100644
--- a/src/main/java/ai/saharaa/actors/batch/DataTaskScheduler.java
+++ b/src/main/java/ai/saharaa/actors/batch/DataTaskScheduler.java
@@ -3,7 +3,7 @@ package ai.saharaa.actors.batch;
 import ai.saharaa.actors.ActorBase;
 import ai.saharaa.dto.batch.BatchExportQueryDTO;
 import ai.saharaa.model.*;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.services.*;
 import akka.actor.AbstractActorWithTimers;
 import com.fasterxml.jackson.databind.ObjectMapper;
@@ -132,8 +132,8 @@ public class DataTaskScheduler extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  public static final class CheckPendingDataTask extends TraceableMessage {}
+  public static final class CheckPendingDataTask implements BaseMessage {}
 
   @NoArgsConstructor
-  public static final class CheckFailedDataTask extends TraceableMessage {}
+  public static final class CheckFailedDataTask implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/batch/ProjectActor.java b/src/main/java/ai/saharaa/actors/batch/ProjectActor.java
index 6205c37f..7a928641 100644
--- a/src/main/java/ai/saharaa/actors/batch/ProjectActor.java
+++ b/src/main/java/ai/saharaa/actors/batch/ProjectActor.java
@@ -1,6 +1,6 @@
 package ai.saharaa.actors.batch;
 
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.services.*;
 import akka.actor.AbstractActorWithTimers;
 import java.time.Duration;
@@ -33,5 +33,5 @@ public class ProjectActor extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class ProjectAssignAM extends TraceableMessage {}
+  private static final class ProjectAssignAM implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java b/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java
index 796d2716..19fe4761 100644
--- a/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java
@@ -447,7 +447,7 @@ public class IndividualJobSessionActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long jobId;
 
     public OpBase(Long jobId) {
diff --git a/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java b/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java
index d67d47b5..ad706805 100644
--- a/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java
@@ -641,7 +641,7 @@ public class JobSessionActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long jobId;
 
     public OpBase(Long jobId) {
diff --git a/src/main/java/ai/saharaa/actors/jobs/JobStatActor.java b/src/main/java/ai/saharaa/actors/jobs/JobStatActor.java
index 4df53280..9678112b 100644
--- a/src/main/java/ai/saharaa/actors/jobs/JobStatActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/JobStatActor.java
@@ -166,5 +166,5 @@ public class JobStatActor extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  public static class UpdatePendingJobStat extends TraceableMessage {}
+  public static class UpdatePendingJobStat implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/jobs/JobStateScheduler.java b/src/main/java/ai/saharaa/actors/jobs/JobStateScheduler.java
index ee235a2b..9d4e6a26 100644
--- a/src/main/java/ai/saharaa/actors/jobs/JobStateScheduler.java
+++ b/src/main/java/ai/saharaa/actors/jobs/JobStateScheduler.java
@@ -240,8 +240,8 @@ public class JobStateScheduler extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class CheckExpiredJobs extends TraceableMessage {}
+  private static final class CheckExpiredJobs implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class CheckJobUserPoints extends TraceableMessage {}
+  private static final class CheckJobUserPoints implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/notification/NotificationActor.java b/src/main/java/ai/saharaa/actors/notification/NotificationActor.java
index 01565589..e6e97b11 100644
--- a/src/main/java/ai/saharaa/actors/notification/NotificationActor.java
+++ b/src/main/java/ai/saharaa/actors/notification/NotificationActor.java
@@ -2,10 +2,9 @@ package ai.saharaa.actors.notification;
 
 import ai.saharaa.actors.ActorBase;
 import ai.saharaa.dto.NotificationPageDTO;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Notification;
 import ai.saharaa.model.PageResult;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.NotificationService;
 import ai.saharaa.utils.DateUtils;
 import java.util.Objects;
@@ -80,7 +79,7 @@ public class NotificationActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long userId;
 
     protected OpBase(Long userId) {
diff --git a/src/main/java/ai/saharaa/actors/notification/NotificationTimersActor.java b/src/main/java/ai/saharaa/actors/notification/NotificationTimersActor.java
index 9eb7747b..a9ba1ca6 100644
--- a/src/main/java/ai/saharaa/actors/notification/NotificationTimersActor.java
+++ b/src/main/java/ai/saharaa/actors/notification/NotificationTimersActor.java
@@ -10,9 +10,9 @@ import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.config.cluster.ClusterConfiguration;
 import ai.saharaa.daos.BatchDao;
 import ai.saharaa.enums.CertificateCategory;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.Batch;
 import ai.saharaa.model.Batch.BatchStatus;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.NotificationService;
 import ai.saharaa.utils.DateUtils;
 import ai.saharaa.utils.ObjectUtils;
@@ -100,7 +100,7 @@ public class NotificationTimersActor extends AbstractActorWithTimers {
     // durationList.add(Duration.ofDays(7));
     durationList.add(Duration.ofDays(7));
     durationList.add(Duration.ofDays(7));
-    List<TraceableMessage> messageList = new ArrayList<>();
+    List<BaseMessage> messageList = new ArrayList<>();
     // messageList.add(new OpSendNewCurses());
     // messageList.add(new OpExamRemind());
     messageList.add(new OpNewJobNotice());
@@ -127,7 +127,7 @@ public class NotificationTimersActor extends AbstractActorWithTimers {
       List<Calendar> calendarList,
       List<String> trickList,
       List<Duration> durationList,
-      List<TraceableMessage> messageList) {
+      List<BaseMessage> messageList) {
     var size = calendarList.size();
     for (int i = 0; i < size; i++) {
       getTimers()
@@ -246,14 +246,14 @@ public class NotificationTimersActor extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class OpSendNewCurses extends TraceableMessage {}
+  private static final class OpSendNewCurses implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class OpExamRemind extends TraceableMessage {}
+  private static final class OpExamRemind implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class OpNewJobNotice extends TraceableMessage {}
+  private static final class OpNewJobNotice implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class OpJobNotCompleteNotice extends TraceableMessage {}
+  private static final class OpJobNotCompleteNotice implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/notification/NotificationUserActor.java b/src/main/java/ai/saharaa/actors/notification/NotificationUserActor.java
index 4cfa8756..094e7b2d 100644
--- a/src/main/java/ai/saharaa/actors/notification/NotificationUserActor.java
+++ b/src/main/java/ai/saharaa/actors/notification/NotificationUserActor.java
@@ -9,7 +9,7 @@ import ai.saharaa.actors.notification.notice.UserNeedToClaim;
 import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.daos.season.SeasonUserDao;
 import ai.saharaa.mappers.JobUserPointsMapper;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.services.BatchService;
 import ai.saharaa.services.JobService;
 import ai.saharaa.services.JobUserService;
@@ -277,7 +277,7 @@ public class NotificationUserActor extends ActorBase {
     iGlobalCache.set(genUserCacheKey(noticeInterType), ObjectUtils.toJson(lastReadUserId));
   }
 
-  public interface OpBase extends IClusterMessage {
+  public interface OpBase extends IMessage {
     Long getUserId();
 
     void setUserId(Long userId);
diff --git a/src/main/java/ai/saharaa/actors/pay/NodeWithdrawalActor.java b/src/main/java/ai/saharaa/actors/pay/NodeWithdrawalActor.java
index f5650641..61615170 100644
--- a/src/main/java/ai/saharaa/actors/pay/NodeWithdrawalActor.java
+++ b/src/main/java/ai/saharaa/actors/pay/NodeWithdrawalActor.java
@@ -1,9 +1,8 @@
 package ai.saharaa.actors.pay;
 
 import ai.saharaa.actors.ActorBase;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.model.pay.NodeWithdrawal;
 import ai.saharaa.model.pay.Order;
 import ai.saharaa.services.NodeService;
@@ -88,7 +87,7 @@ public class NodeWithdrawalActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
 
     private Long projectId;
     private Long nodeId;
diff --git a/src/main/java/ai/saharaa/actors/pay/OrderActor.java b/src/main/java/ai/saharaa/actors/pay/OrderActor.java
index 6a89dc28..86302c23 100644
--- a/src/main/java/ai/saharaa/actors/pay/OrderActor.java
+++ b/src/main/java/ai/saharaa/actors/pay/OrderActor.java
@@ -3,9 +3,8 @@ package ai.saharaa.actors.pay;
 import ai.saharaa.actors.ActorBase;
 import ai.saharaa.enums.OrderStatus;
 import ai.saharaa.enums.PayType;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.OrderService;
 import java.util.Date;
 import lombok.Getter;
@@ -66,7 +65,7 @@ public class OrderActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long flowNo;
 
     public OpBase(Long flowNo) {
diff --git a/src/main/java/ai/saharaa/actors/pay/OrderTransactionActor.java b/src/main/java/ai/saharaa/actors/pay/OrderTransactionActor.java
index ccfd4e7c..54506c05 100644
--- a/src/main/java/ai/saharaa/actors/pay/OrderTransactionActor.java
+++ b/src/main/java/ai/saharaa/actors/pay/OrderTransactionActor.java
@@ -4,9 +4,8 @@ import ai.saharaa.actors.ActorBase;
 import ai.saharaa.enums.OrderStatus;
 import ai.saharaa.enums.OrderType;
 import ai.saharaa.enums.PayType;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.OrderTransactionService;
 import java.math.BigDecimal;
 import lombok.Getter;
@@ -61,7 +60,7 @@ public class OrderTransactionActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long flowNo;
 
     public OpBase(Long flowNo) {
diff --git a/src/main/java/ai/saharaa/actors/pay/PayOrderTimeoutScheduler.java b/src/main/java/ai/saharaa/actors/pay/PayOrderTimeoutScheduler.java
index f053a6ae..a52e284c 100644
--- a/src/main/java/ai/saharaa/actors/pay/PayOrderTimeoutScheduler.java
+++ b/src/main/java/ai/saharaa/actors/pay/PayOrderTimeoutScheduler.java
@@ -2,7 +2,7 @@ package ai.saharaa.actors.pay;
 
 import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.config.cluster.ClusterConfiguration;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.utils.ActorUtils;
 import ai.saharaa.utils.Constants;
 import akka.actor.AbstractActorWithTimers;
@@ -67,5 +67,5 @@ public class PayOrderTimeoutScheduler extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class OpBase extends TraceableMessage {}
+  private static final class OpBase implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/pay/PayWeb3EventScheduler.java b/src/main/java/ai/saharaa/actors/pay/PayWeb3EventScheduler.java
index 336cee14..2d964a5b 100644
--- a/src/main/java/ai/saharaa/actors/pay/PayWeb3EventScheduler.java
+++ b/src/main/java/ai/saharaa/actors/pay/PayWeb3EventScheduler.java
@@ -1,7 +1,7 @@
 package ai.saharaa.actors.pay;
 
 import ai.saharaa.exception.PayException;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.pay.OrderEvent;
 import ai.saharaa.services.OrderService;
 import akka.actor.AbstractActorWithTimers;
@@ -60,5 +60,5 @@ public class PayWeb3EventScheduler extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class OpBase extends TraceableMessage {}
+  private static final class OpBase implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/pay/ProjectPaymentActor.java b/src/main/java/ai/saharaa/actors/pay/ProjectPaymentActor.java
index 1053da55..d359071b 100644
--- a/src/main/java/ai/saharaa/actors/pay/ProjectPaymentActor.java
+++ b/src/main/java/ai/saharaa/actors/pay/ProjectPaymentActor.java
@@ -1,9 +1,8 @@
 package ai.saharaa.actors.pay;
 
 import ai.saharaa.actors.ActorBase;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.model.pay.Order;
 import ai.saharaa.model.pay.ProjectPayment;
 import ai.saharaa.services.OrderService;
@@ -81,7 +80,7 @@ public class ProjectPaymentActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long projectId;
 
     public OpBase(Long projectId) {
diff --git a/src/main/java/ai/saharaa/actors/season/SeasonExpActor.java b/src/main/java/ai/saharaa/actors/season/SeasonExpActor.java
index e94fdf8e..2b0fffb3 100644
--- a/src/main/java/ai/saharaa/actors/season/SeasonExpActor.java
+++ b/src/main/java/ai/saharaa/actors/season/SeasonExpActor.java
@@ -3,7 +3,7 @@ package ai.saharaa.actors.season;
 import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.daos.season.SeasonUserDao;
 import ai.saharaa.daos.season.SeasonUserExpDetailDao;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.season.SeasonUser;
 import ai.saharaa.model.season.SeasonUserExpDetail;
 import ai.saharaa.services.NotificationService;
@@ -178,5 +178,5 @@ public class SeasonExpActor extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class DoUpdateLeaderboardSaharaExpDashboard extends TraceableMessage {}
+  private static final class DoUpdateLeaderboardSaharaExpDashboard implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/season/SettleSeasonJobPointsActor.java b/src/main/java/ai/saharaa/actors/season/SettleSeasonJobPointsActor.java
index e1f62549..a148d411 100644
--- a/src/main/java/ai/saharaa/actors/season/SettleSeasonJobPointsActor.java
+++ b/src/main/java/ai/saharaa/actors/season/SettleSeasonJobPointsActor.java
@@ -8,7 +8,7 @@ import ai.saharaa.actors.achievement.AchievementTriggers;
 import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.daos.season.SeasonUserDao;
 import ai.saharaa.daos.season.SeasonUserPointsDetailDao;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.season.Season;
 import ai.saharaa.model.season.SeasonUser;
 import ai.saharaa.model.season.SeasonUserPointsDetail;
@@ -511,17 +511,17 @@ public class SettleSeasonJobPointsActor extends AbstractActorWithTimers {
   }
 
   @NoArgsConstructor
-  private static final class DoSettlingSeasonJobPoints extends TraceableMessage {}
+  private static final class DoSettlingSeasonJobPoints implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class DoUpdateLeaderboardSaharaaPoints extends TraceableMessage {}
+  private static final class DoUpdateLeaderboardSaharaaPoints implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class DoUpdateLeaderboardDataPoints extends TraceableMessage {}
+  private static final class DoUpdateLeaderboardDataPoints implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class DoUpdateLeaderboardDelta extends TraceableMessage {}
+  private static final class DoUpdateLeaderboardDelta implements BaseMessage {}
 
   @NoArgsConstructor
-  private static final class DoDistributeLeaderboardRankPoints extends TraceableMessage {}
+  private static final class DoDistributeLeaderboardRankPoints implements BaseMessage {}
 }
diff --git a/src/main/java/ai/saharaa/actors/settings/ArraySettingsActor.java b/src/main/java/ai/saharaa/actors/settings/ArraySettingsActor.java
index 7bee0971..e8bafd1c 100644
--- a/src/main/java/ai/saharaa/actors/settings/ArraySettingsActor.java
+++ b/src/main/java/ai/saharaa/actors/settings/ArraySettingsActor.java
@@ -1,8 +1,7 @@
 package ai.saharaa.actors.settings;
 
 import ai.saharaa.actors.ActorBase;
-import ai.saharaa.model.IClusterMessage;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.UserSetting;
 import ai.saharaa.services.UserSettingService;
 import java.util.List;
@@ -104,7 +103,7 @@ public abstract class ArraySettingsActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long userId;
 
     public OpBase(Long userId) {
diff --git a/src/main/java/ai/saharaa/actors/settings/SingleValueSettingsActor.java b/src/main/java/ai/saharaa/actors/settings/SingleValueSettingsActor.java
index 08cdd382..666bdd76 100644
--- a/src/main/java/ai/saharaa/actors/settings/SingleValueSettingsActor.java
+++ b/src/main/java/ai/saharaa/actors/settings/SingleValueSettingsActor.java
@@ -1,9 +1,8 @@
 package ai.saharaa.actors.settings;
 
 import ai.saharaa.actors.ActorBase;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.model.UserSetting;
 import ai.saharaa.services.UserSettingService;
 import java.util.List;
@@ -105,7 +104,7 @@ public abstract class SingleValueSettingsActor extends ActorBase {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long userId;
 
     public OpBase(Long userId) {
diff --git a/src/main/java/ai/saharaa/actors/tasks/BatchSampleActor.java b/src/main/java/ai/saharaa/actors/tasks/BatchSampleActor.java
index dda06729..6939a512 100644
--- a/src/main/java/ai/saharaa/actors/tasks/BatchSampleActor.java
+++ b/src/main/java/ai/saharaa/actors/tasks/BatchSampleActor.java
@@ -1,11 +1,10 @@
 package ai.saharaa.actors.tasks;
 
 import ai.saharaa.daos.TaskListDao;
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import ai.saharaa.model.Messages;
 import ai.saharaa.model.Task;
 import ai.saharaa.model.TaskList;
-import ai.saharaa.model.TraceableMessage;
 import ai.saharaa.services.BatchSampleService;
 import ai.saharaa.services.BatchService;
 import ai.saharaa.services.TaskService;
@@ -101,7 +100,7 @@ public class BatchSampleActor extends UntypedAbstractActor {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long sampleId;
 
     public OpBase(Long taskId) {
diff --git a/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java b/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java
index 6e79e295..43d4604f 100644
--- a/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java
+++ b/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java
@@ -42,8 +42,6 @@ import reactor.util.context.Context;
 
 public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavior.Command> {
 
-  Logger log = LoggerFactory.getLogger(MachineReviewBehavior.class);
-
   @Override
   public Receive<Command> createReceive() {
     var builder = this.newReceiveBuilder();
@@ -76,6 +74,7 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
   private final TaskSessionDao taskSessionDao;
   private final TaskQuestionMapper taskQuestionMapper;
   private final ObjectMapper objectMapper;
+  private final Logger log = LoggerFactory.getLogger(MachineReviewBehavior.class);
 
   public MachineReviewBehavior(
       ActorContext<Command> context,
@@ -101,8 +100,6 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
   }
 
   private Behavior<Command> onCheckSimilarity(CheckSimilarity message) {
-    getContext().getLog().info("Checking similarity for project {}", message.projectId);
-    this.log.info("Checking similarity for project {}", message.projectId);
     checkSimilarityMono(message.projectId, message.submits)
         .doFinally((v) -> message.replyTo.ifPresent(r -> r.tell(new CheckSimilarityCompleted())))
         .subscribe();
diff --git a/src/main/java/ai/saharaa/actors/tasks/TaskActor.java b/src/main/java/ai/saharaa/actors/tasks/TaskActor.java
index fd489117..20b2b843 100644
--- a/src/main/java/ai/saharaa/actors/tasks/TaskActor.java
+++ b/src/main/java/ai/saharaa/actors/tasks/TaskActor.java
@@ -131,7 +131,7 @@ public class TaskActor extends UntypedAbstractActor {
 
   @Getter
   @NoArgsConstructor
-  public static class OpBase extends TraceableMessage implements IClusterMessage {
+  public static class OpBase implements IMessage {
     private Long taskId;
 
     public OpBase(Long taskId) {
diff --git a/src/main/java/ai/saharaa/aspect/AkkaTracingAspect.java b/src/main/java/ai/saharaa/aspect/AkkaTracingAspect.java
deleted file mode 100644
index 27fde48b..00000000
--- a/src/main/java/ai/saharaa/aspect/AkkaTracingAspect.java
+++ /dev/null
@@ -1,60 +0,0 @@
-package ai.saharaa.aspect;
-
-import ai.saharaa.utils.BeanAccessor;
-import akka.actor.typed.Behavior;
-import akka.actor.typed.javadsl.Behaviors;
-import akka.japi.function.Function;
-import brave.Tracer;
-import brave.propagation.TraceContext;
-import com.google.common.reflect.TypeToken;
-import java.util.Collections;
-import java.util.Map;
-import org.aspectj.lang.ProceedingJoinPoint;
-import org.aspectj.lang.annotation.Around;
-import org.aspectj.lang.annotation.Aspect;
-import org.slf4j.Logger;
-import org.slf4j.LoggerFactory;
-
-/** Aspect for adding Spring tracing to Akka actors. */
-@Aspect
-public class AkkaTracingAspect {
-
-  Logger log = LoggerFactory.getLogger(AkkaTracingAspect.class);
-
-  public AkkaTracingAspect() {}
-
-  /**
-   * We can only get this to work with AspectJ post-compile weaving. Compile-time weaving is not
-   * compatible with Lombok. For Spring load-time weaving, we couldn't weave this into
-   * AbstractBehavior possibly due to the target not being a Spring component. Load-time weaving
-   * into Spring components works though. For these reasons, we use post-compile weaving supported
-   * by a freefair gradle plugin.
-   */
-  @SuppressWarnings("unchecked")
-  @Around("execution(static * akka.actor.typed.javadsl.Behaviors.setup(..))")
-  public <T> Object aroundCreateReceive(ProceedingJoinPoint pjp) throws Throwable {
-    log.info("Around create receive");
-    var tracer = BeanAccessor.getBean(Tracer.class);
-
-    var originalBehavior = (Behavior<T>) pjp.proceed();
-    var typeToken = new TypeToken<Behavior<T>>() {};
-    var paramType = typeToken.resolveType(Behavior.class.getTypeParameters()[0]);
-
-    Function<T, Map<String, String>> mdcForMessage = (T msg) -> {
-      log.info("Setting MDC for actor");
-
-      Map<String, String> mdc = Collections.emptyMap();
-      if (tracer != null && tracer.currentSpan() != null) {
-        TraceContext context = tracer.currentSpan().context();
-        if (context != null) {
-          mdc = Map.of(
-              "traceId", context.traceIdString(),
-              "spanId", context.spanIdString());
-        }
-        log.info("Setting MDC for actor: {}", mdc);
-      }
-      return mdc;
-    };
-    return Behaviors.withMdc((Class<T>) paramType.getRawType(), mdcForMessage, originalBehavior);
-  }
-}
diff --git a/src/main/java/ai/saharaa/controllers/AuthController.java b/src/main/java/ai/saharaa/controllers/AuthController.java
index c71213bc..693a5c9b 100644
--- a/src/main/java/ai/saharaa/controllers/AuthController.java
+++ b/src/main/java/ai/saharaa/controllers/AuthController.java
@@ -13,7 +13,7 @@ import ai.saharaa.dto.LoginFormDTO;
 import ai.saharaa.dto.LoginResultDTO;
 import ai.saharaa.dto.web2email.Web2EmailLoginDTO;
 import ai.saharaa.dto.web2email.Web2EmailSendVerificationCodeDTO;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.BaseMessage;
 import ai.saharaa.model.User.RegisterType;
 import ai.saharaa.services.AuditLogService;
 import ai.saharaa.services.CaptchaService;
@@ -183,7 +183,7 @@ public class AuthController {
             HttpStatus.UNAUTHORIZED, "user not found", null, null, null);
       }
       var hostName = getHost(request);
-      TraceableMessage reg;
+      BaseMessage reg;
       if (isOlympic) {
         reg = UserRegisterActor.RegisterUserOlympic.builder()
             .walletAddress(Ascii.toLowerCase(address))
diff --git a/src/main/java/ai/saharaa/extensions/ClusterMessageExtractor.java b/src/main/java/ai/saharaa/extensions/ClusterMessageExtractor.java
index 322e3f0e..1b70e6f1 100644
--- a/src/main/java/ai/saharaa/extensions/ClusterMessageExtractor.java
+++ b/src/main/java/ai/saharaa/extensions/ClusterMessageExtractor.java
@@ -1,14 +1,14 @@
 package ai.saharaa.extensions;
 
-import ai.saharaa.model.IClusterMessage;
+import ai.saharaa.model.IMessage;
 import akka.cluster.sharding.ShardRegion;
 
 public class ClusterMessageExtractor implements ShardRegion.MessageExtractor {
 
   @Override
   public String entityId(Object message) {
-    if (message instanceof IClusterMessage) {
-      return String.valueOf(((IClusterMessage) message).entityId());
+    if (message instanceof IMessage) {
+      return String.valueOf(((IMessage) message).entityId());
     }
     return null;
   }
@@ -21,8 +21,8 @@ public class ClusterMessageExtractor implements ShardRegion.MessageExtractor {
   @Override
   public String shardId(Object message) {
     int numberOfShards = 100;
-    if (message instanceof IClusterMessage) {
-      return String.valueOf(((IClusterMessage) message).entityId() % numberOfShards);
+    if (message instanceof IMessage) {
+      return String.valueOf(((IMessage) message).entityId() % numberOfShards);
     }
 
     return null;
diff --git a/src/main/java/ai/saharaa/interop/LookupIpDTO.java b/src/main/java/ai/saharaa/interop/LookupIpDTO.java
index 3133083f..7be5e18c 100644
--- a/src/main/java/ai/saharaa/interop/LookupIpDTO.java
+++ b/src/main/java/ai/saharaa/interop/LookupIpDTO.java
@@ -1,17 +1,18 @@
 package ai.saharaa.interop;
 
-import ai.saharaa.model.IClusterMessage;
-import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.model.IMessage;
 import java.math.BigInteger;
 import java.net.InetAddress;
 import java.security.MessageDigest;
 import java.security.NoSuchAlgorithmException;
+import lombok.Data;
 import lombok.Getter;
 import lombok.NoArgsConstructor;
 
 @Getter
+@Data
 @NoArgsConstructor
-public final class LookupIpDTO extends TraceableMessage implements IClusterMessage {
+public final class LookupIpDTO implements IMessage {
   private InetAddress address;
 
   public LookupIpDTO(InetAddress address) {
diff --git a/src/main/java/ai/saharaa/model/BaseMessage.java b/src/main/java/ai/saharaa/model/BaseMessage.java
new file mode 100644
index 00000000..81195cc2
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/BaseMessage.java
@@ -0,0 +1,3 @@
+package ai.saharaa.model;
+
+public interface BaseMessage {}
diff --git a/src/main/java/ai/saharaa/model/IClusterMessage.java b/src/main/java/ai/saharaa/model/IMessage.java
similarity index 61%
rename from src/main/java/ai/saharaa/model/IClusterMessage.java
rename to src/main/java/ai/saharaa/model/IMessage.java
index c64952dd..e1ba3e2c 100644
--- a/src/main/java/ai/saharaa/model/IClusterMessage.java
+++ b/src/main/java/ai/saharaa/model/IMessage.java
@@ -1,5 +1,5 @@
 package ai.saharaa.model;
 
-public interface IClusterMessage {
+public interface IMessage {
   public long entityId();
 }
diff --git a/src/main/java/ai/saharaa/model/SimpleResult.java b/src/main/java/ai/saharaa/model/SimpleResult.java
index f7f755f9..4b9dd8ce 100644
--- a/src/main/java/ai/saharaa/model/SimpleResult.java
+++ b/src/main/java/ai/saharaa/model/SimpleResult.java
@@ -8,7 +8,7 @@ import lombok.Setter;
 @Getter
 @Setter
 @NoArgsConstructor
-public class SimpleResult<T> extends TraceableMessage {
+public class SimpleResult<T> implements BaseMessage {
   private T result;
   private String reason;
   private boolean success;
diff --git a/src/main/java/ai/saharaa/model/TraceableMessage.java b/src/main/java/ai/saharaa/model/TraceableMessage.java
deleted file mode 100644
index befae9a0..00000000
--- a/src/main/java/ai/saharaa/model/TraceableMessage.java
+++ /dev/null
@@ -1,28 +0,0 @@
-package ai.saharaa.model;
-
-import lombok.Builder;
-import lombok.Data;
-import lombok.Getter;
-import lombok.Setter;
-import lombok.extern.jackson.Jacksonized;
-
-/**
- * Used to pass Spring tracing information in Akka. This should be handled by aspects.
- *
- * <p>We don't generate equals and hashcode for this abstract class. If we do in the future, whether
- * to include the tracing field should be carefully considered.
- */
-@Getter
-@Setter
-public abstract class TraceableMessage {
-  @Data
-  @Builder
-  @Jacksonized
-  static class SpringTracing {
-    String traceId;
-    String spanId;
-    String parentId;
-  }
-
-  SpringTracing tracing;
-}
diff --git a/src/main/resources/META-INF/aop.xml b/src/main/resources/META-INF/aop.xml
deleted file mode 100644
index 337b5252..00000000
--- a/src/main/resources/META-INF/aop.xml
+++ /dev/null
@@ -1,11 +0,0 @@
-<!DOCTYPE aspectj PUBLIC "-//AspectJ//DTD//EN" "http://www.eclipse.org/aspectj/dtd/aspectj.dtd">
-<aspectj>
-    <weaver options="-verbose -showWeaveInfo">
-        <include within="ai.saharaa.aspect..*" />
-        <include within="akka..*" />
-    </weaver>
-    <aspects>
-        <!-- These are the two aspects we want to switch on for now. -->
-        <aspect name="ai.saharaa.aspect.AkkaTracingAspect"/>
-    </aspects>
-</aspectj>
\ No newline at end of file
diff --git a/src/main/resources/akka/akka-base.conf b/src/main/resources/akka/akka-base.conf
index 0d481980..b1c0521a 100644
--- a/src/main/resources/akka/akka-base.conf
+++ b/src/main/resources/akka/akka-base.conf
@@ -28,8 +28,8 @@ akka {
 
     serialization-bindings {
       "ai.saharaa.model.SimpleResult" = custom-json
-      "ai.saharaa.model.IClusterMessage" = jackson-json
-      "ai.saharaa.model.TraceableMessage" = jackson-json
+      "ai.saharaa.model.IMessage" = jackson-json
+      "ai.saharaa.model.BaseMessage" = jackson-json
       "java.lang.Record" = jackson-json
     }
 
diff --git a/src/main/resources/akka/akka-staging.conf b/src/main/resources/akka/akka-staging.conf
index e998493e..16c90ca6 100644
--- a/src/main/resources/akka/akka-staging.conf
+++ b/src/main/resources/akka/akka-staging.conf
@@ -10,8 +10,8 @@ akka {
 
     serialization-bindings {
       "ai.saharaa.model.SimpleResult" = custom-json
-      "ai.saharaa.model.IClusterMessage" = jackson-json
-      "ai.saharaa.model.TraceableMessage" = jackson-json
+      "ai.saharaa.model.IMessage" = jackson-json
+      "ai.saharaa.model.BaseMessage" = jackson-json
     }
   }
 
diff --git a/src/main/resources/application.properties b/src/main/resources/application.properties
index 5eeb905c..f6a27c54 100644
--- a/src/main/resources/application.properties
+++ b/src/main/resources/application.properties
@@ -1,6 +1,5 @@
 
 spring.main.banner-mode=off
-
 logging.level.org.springframework=ERROR
 logging.file.name=./logs/campfire-platform-logger.log
 # TODO consider removing the following config and let Spring to use logging.file.name to figure it out
diff --git a/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java b/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java
index 9832202d..192c28c6 100644
--- a/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java
+++ b/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java
@@ -15,11 +15,9 @@ import ai.saharaa.model.TaskSession;
 import ai.saharaa.services.FeatureFlagService;
 import ai.saharaa.utils.TestUtils;
 import akka.actor.testkit.typed.javadsl.ActorTestKit;
-import akka.actor.testkit.typed.javadsl.LoggingTestKit;
 import akka.actor.testkit.typed.javadsl.TestProbe;
 import akka.actor.typed.javadsl.ActorContext;
 import akka.actor.typed.javadsl.Behaviors;
-import brave.Tracer;
 import com.fasterxml.jackson.databind.ObjectMapper;
 import com.google.common.collect.Streams;
 import com.typesafe.config.Config;
@@ -43,8 +41,6 @@ import org.junit.jupiter.params.ParameterizedTest;
 import org.junit.jupiter.params.provider.ValueSource;
 import org.skyscreamer.jsonassert.JSONAssert;
 import org.skyscreamer.jsonassert.JSONCompareMode;
-import org.slf4j.Logger;
-import org.slf4j.LoggerFactory;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.boot.test.context.SpringBootTest;
 import org.springframework.test.context.junit.jupiter.SpringExtension;
@@ -56,7 +52,6 @@ import org.springframework.web.reactive.function.client.WebClient;
 @AutoConfigureEmbeddedDatabase(
     refresh = AutoConfigureEmbeddedDatabase.RefreshMode.AFTER_EACH_TEST_METHOD)
 public class MachineReviewBehaviorTest {
-  Logger log = LoggerFactory.getLogger(MachineReviewBehaviorTest.class);
 
   @Autowired
   private TaskSessionDao taskSessionDao;
@@ -76,9 +71,6 @@ public class MachineReviewBehaviorTest {
   @Autowired
   private FeatureFlagService featureFlagService;
 
-  @Autowired
-  private Tracer tracer;
-
   ActorTestKit testKit;
 
   @BeforeEach
@@ -104,59 +96,58 @@ public class MachineReviewBehaviorTest {
   @Test
   void asyncCheckSimilarity_whenCalled_shouldRequestSimilarityByRestAndPersistResult()
       throws Exception {
-    log.error("asyncCheckSimilarity_whenCalled_shouldRequestSimilarityByRestAndPersistResult");
 
     try (MockWebServer mockWebServer = new MockWebServer()) {
       mockWebServer.start();
 
       final String responseBody =
           """
-                            {
-                              "data": {
-                                "similarity_check_passed": true,
-                                "author_sim_check_passed": false,
-                                "project_sim_check_passed": true,
-                                "similar_data_points": {
-                                  "789": [
-                                    {
-                                      "id": 1,
-                                      "project_id": 1001,
-                                      "submission_id": 123,
-                                      "author_id": 456,
-                                      "question_id": 789,
-                                      "task_id": 101,
-                                      "author_check_rejected": false,
-                                      "project_check_rejected": false,
-                                      "rejected": false,
-                                      "created_at_ts_ms": 1723776168834,
-                                      "updated_at_ts_ms": 1723776168834,
-                                      "embedding_info": {
-                                        "distance": 0.85
-                                      }
-                                    }
-                                  ],
-                                  "790": [
-                                    {
-                                      "id": 2,
-                                      "project_id": 1002,
-                                      "submission_id": 124,
-                                      "author_id": 457,
-                                      "question_id": 790,
-                                      "task_id": 101,
-                                      "author_check_rejected": true,
-                                      "project_check_rejected": false,
-                                      "rejected": true,
-                                      "created_at_ts_ms": 1723776168835,
-                                      "updated_at_ts_ms": 1723776168835,
-                                      "embedding_info": {
-                                        "distance": 0.92
-                                      }
-                                    }
-                                  ]
-                                }
-                              }
+                  {
+                    "data": {
+                      "similarity_check_passed": true,
+                      "author_sim_check_passed": false,
+                      "project_sim_check_passed": true,
+                      "similar_data_points": {
+                        "789": [
+                          {
+                            "id": 1,
+                            "project_id": 1001,
+                            "submission_id": 123,
+                            "author_id": 456,
+                            "question_id": 789,
+                            "task_id": 101,
+                            "author_check_rejected": false,
+                            "project_check_rejected": false,
+                            "rejected": false,
+                            "created_at_ts_ms": 1723776168834,
+                            "updated_at_ts_ms": 1723776168834,
+                            "embedding_info": {
+                              "distance": 0.85
                             }
-                            """;
+                          }
+                        ],
+                        "790": [
+                          {
+                            "id": 2,
+                            "project_id": 1002,
+                            "submission_id": 124,
+                            "author_id": 457,
+                            "question_id": 790,
+                            "task_id": 101,
+                            "author_check_rejected": true,
+                            "project_check_rejected": false,
+                            "rejected": true,
+                            "created_at_ts_ms": 1723776168835,
+                            "updated_at_ts_ms": 1723776168835,
+                            "embedding_info": {
+                              "distance": 0.92
+                            }
+                          }
+                        ]
+                      }
+                    }
+                  }
+                  """;
 
       TestUtils.JobAndEverything j = testUtils.createWorkingJobAndReturnEverything(1);
 
@@ -246,18 +237,11 @@ public class MachineReviewBehaviorTest {
               objectMapper,
               featureFlagService)));
       TestProbe<MachineReviewBehavior.CheckSimilarityCompleted> probe = testKit.createTestProbe();
+      // Send the message to the actor
+      actor.tell(
+          new MachineReviewBehavior.CheckSimilarity(1001L, submits, Optional.of(probe.ref())));
 
-      try (Tracer.SpanInScope ws = tracer.withSpanInScope(tracer.nextSpan().start())) {
-        // Send the message to the actor, and verify Spring trace ids are passed
-        LoggingTestKit.info("Checking similarity")
-            .withCheckExcess(false)
-            .expect(testKit.system(), () -> {
-              actor.tell(new MachineReviewBehavior.CheckSimilarity(
-                  1001L, submits, Optional.of(probe.ref())));
-              return null;
-            });
-        probe.expectMessageClass(MachineReviewBehavior.CheckSimilarityCompleted.class);
-      }
+      probe.expectMessageClass(MachineReviewBehavior.CheckSimilarityCompleted.class);
 
       List<Timestamp> requestTimes = new ArrayList<>();
       for (int i = 0; i < 2; i++) {
@@ -358,11 +342,13 @@ public class MachineReviewBehaviorTest {
 
       JSONAssert.assertEquals(
           """
-                            {
-                                project_id: ${j.project().getId()},
-                                submission_id: ${taskSession.getId()}
+
+                          {
+                              project_id: ${j.project().getId()},
+                              submission_id: ${taskSession.getId()}
                             }
-                            """,
+
+                          """,
           recordedRequest.getBody().readUtf8(),
           true);
     }
@@ -421,11 +407,11 @@ public class MachineReviewBehaviorTest {
               taskSessions.map(
                   taskSession ->
                       """
-                                                    {
-                                                      project_id: ${j.project().getId()},
-                                                      submission_id: ${taskSession.getId()}
-                                                    }
-                                                    """),
+                                                  {
+                                                    project_id: ${j.project().getId()},
+                                                    submission_id: ${taskSession.getId()}
+                                                  }
+                                                  """),
               requests.stream(),
               Pair::of)
           .forEach((pair) -> JSONAssert.assertEquals(pair.getKey(), pair.getValue(), true));
