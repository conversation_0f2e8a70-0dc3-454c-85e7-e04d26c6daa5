/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */
import de.undercouch.gradle.tasks.download.Download
import net.ltgt.gradle.errorprone.errorprone

plugins {
  java
  checkstyle
  id("org.openapi.generator") version "7.10.0"
  id("com.diffplug.spotless") version "7.0.0.BETA4"
  id("net.ltgt.errorprone") version "4.1.0"
  id("org.springframework.boot") version "3.4.1"
  id("io.freefair.lombok") version "8.10"
  id("de.undercouch.download") version "5.6.0"
  id("org.flywaydb.flyway") version "10.19.0"
  id("com.squareup.sort-dependencies") version "0.13"
  id("org.sonarqube") version "6.0.1.5171"
}

repositories {
  mavenCentral()

  // Temporary for the Akka newest versions
  maven("https://repo.akka.io/nT28_6z7O_BwaMEFRn56JavtSzCVTQ8rPw1EdZKtDoNGoz_a/secure")
}

buildscript {
  dependencies {
    // Required by flyway gradle plugin
    classpath("org.flywaydb:flyway-database-postgresql:10.19.0")
  }
}

dependencies {
  implementation(platform("com.google.cloud:libraries-bom:26.52.0"))
  implementation(platform("com.lightbend.akka:akka-dependencies_2.13:24.05.3"))
  implementation(platform("io.kamon:kamon-bom:2.7.5"))
  implementation(platform("org.springframework.boot:spring-boot-dependencies:3.4.1"))
  implementation(platform("software.amazon.awssdk:bom:2.29.43"))
  implementation("cn.hutool:hutool-core:5.8.35")
  implementation("com.auth0:java-jwt:4.4.0")
  implementation("com.baomidou:mybatis-plus-jsqlparser:3.5.9")
  implementation("com.baomidou:mybatis-plus-spring-boot3-starter:3.5.9")
  implementation("com.github.yulichang:mybatis-plus-join-boot-starter:1.5.2")
  implementation("com.google.cloud:google-cloud-storage")
  implementation("com.google.guava:guava")
  implementation("com.graphql-java:graphql-java:21.0")
  implementation("com.lightbend.akka.discovery:akka-discovery-kubernetes-api_2.13")
  implementation("com.lightbend.akka.management:akka-management-cluster-bootstrap_2.13")
  implementation("com.lightbend.akka.management:akka-management-cluster-http_2.13")
  implementation("com.lightbend.akka.management:akka-rolling-update-kubernetes_2.13:1.5.3")
  implementation("com.maxmind.geoip2:geoip2:4.2.1")
  implementation("com.oracle.oci.sdk:oci-java-sdk-objectstorage:3.55.1")
  implementation("com.statsig:serversdk:1.35.0")
  implementation("com.typesafe.akka:akka-actor-typed_2.13")
  implementation("com.typesafe.akka:akka-actor_2.13")
  implementation("com.typesafe.akka:akka-cluster-sharding_2.13")
  implementation("com.typesafe.akka:akka-cluster-tools_2.13")
  implementation("com.typesafe.akka:akka-cluster-typed_2.13")
  implementation("com.typesafe.akka:akka-cluster_2.13")
  implementation("com.typesafe.akka:akka-serialization-jackson_2.13")
  implementation("com.typesafe.akka:akka-stream_2.13")
  implementation("io.github.resilience4j:resilience4j-ratelimiter:2.2.0")
  implementation("io.kamon:kamon-akka-http_2.13")
  implementation("io.kamon:kamon-akka_2.13")
  implementation("io.kamon:kamon-core_2.13")
  implementation("io.kamon:kamon-prometheus_2.13")
  // For automatic Spring tracing and reporting to Grafana tempo. Consider converting
  // to implementation dependency if tracing features are needed in code.
  implementation("io.micrometer:micrometer-tracing-bridge-otel")
  implementation("io.opentelemetry.javaagent:opentelemetry-javaagent:2.16.0")
  implementation("net.bytebuddy:byte-buddy-agent")
  // For JDBC tracing. opentelemetry-jdbc is a possible future alternative.
  implementation("net.ttddyy.observation:datasource-micrometer-spring-boot:1.0.5")
  implementation("nl.basjes.parse.useragent:yauaa:7.29.0")
  implementation("org.apache.commons:commons-csv:1.12.0")
  implementation("org.apache.httpcomponents.client5:httpclient5")
  implementation("org.bouncycastle:bcpkix-jdk18on:1.79")
  implementation("org.bouncycastle:bcprov-jdk18on:1.79")
  // Required for conditionals in logback-spring.xml. Can remove if we no longer use
  // logback-spring.xml for appending to Loki.
  implementation("org.codehaus.janino:janino:3.1.12")
  implementation("org.flywaydb:flyway-database-postgresql")
  implementation("org.freemarker:freemarker")
  implementation("org.openapitools:jackson-databind-nullable:0.2.6")
  implementation("org.owasp.esapi:esapi:2.6.0.0") { artifact { classifier = "jakarta" } }
  implementation("org.postgresql:postgresql")
  implementation("org.roaringbitmap:RoaringBitmap:1.3.0")
  implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0")
  // This contains some useful aspects for Spring, including support for injecting Spring
  // dependencies into non-Spring objects, @Transactional on private methods and self-invocations,
  // etc. Must be used with the AspectJ weaver.
  implementation("org.springframework:spring-aspects")
  implementation("org.springframework:spring-core")
  implementation("org.springframework:spring-instrument")
  implementation("org.springframework.boot:spring-boot-starter-actuator")
  implementation("org.springframework.boot:spring-boot-starter-aop")
  implementation("org.springframework.boot:spring-boot-starter-data-jdbc")
  implementation("org.springframework.boot:spring-boot-starter-data-redis")
  implementation("org.springframework.boot:spring-boot-starter-jdbc")
  implementation("org.springframework.boot:spring-boot-starter-security")
  implementation("org.springframework.boot:spring-boot-starter-validation")
  implementation("org.springframework.boot:spring-boot-starter-web")
  implementation("org.springframework.boot:spring-boot-starter-webflux")
  implementation("org.springframework.kafka:spring-kafka")
  implementation("org.springframework.security:spring-security-crypto")
  implementation("org.springframework.security:spring-security-oauth2-resource-server")
  implementation("org.springframework.session:spring-session-data-redis")
  implementation("org.web3j:core:4.12.3")
  implementation("org.zalando:logbook-spring-boot-starter:3.10.0")
  implementation("redis.clients:jedis")
  implementation("software.amazon.awssdk:cloudfront")
  implementation("software.amazon.awssdk:s3-transfer-manager")
  implementation("software.amazon.awssdk:ses")
  implementation("software.amazon.awssdk:sts")
  implementation("software.amazon.msk:aws-msk-iam-auth:2.2.0")
  implementation("systems.manifold:manifold-collections:2024.1.44")
  implementation("systems.manifold:manifold-ext-rt:2024.1.44")

  runtimeOnly("io.kamon:kanela-agent:1.0.18")
  runtimeOnly("io.micrometer:micrometer-registry-prometheus")
  runtimeOnly("io.opentelemetry:opentelemetry-exporter-otlp")
  runtimeOnly("org.aspectj:aspectjweaver:1.9.22.1")
  //  runtimeOnly("org.springframework.boot:spring-boot-devtools")
  runtimeOnly("software.amazon.awssdk.crt:aws-crt:0.33.7")

  annotationProcessor("systems.manifold:manifold-exceptions:2024.1.44")
  annotationProcessor("systems.manifold:manifold-ext:2024.1.44")
  annotationProcessor("systems.manifold:manifold-strings:2024.1.44")

  testAnnotationProcessor("systems.manifold:manifold-exceptions:2024.1.44")
  testAnnotationProcessor("systems.manifold:manifold-ext:2024.1.44")
  testAnnotationProcessor("systems.manifold:manifold-strings:2024.1.44")

  testImplementation(
      enforcedPlatform("io.zonky.test.postgres:embedded-postgres-binaries-bom:17.2.0"))
  testImplementation("com.github.codemonstur:embedded-redis:1.4.3")
  testImplementation("com.squareup.okhttp3:mockwebserver:4.12.0")
  testImplementation("com.squareup.okhttp3:okhttp-tls:4.12.0")
  testImplementation("com.typesafe.akka:akka-actor-testkit-typed_2.13")
  testImplementation("io.zonky.test:embedded-database-spring-test:2.6.0")
  testImplementation("io.zonky.test:embedded-postgres:2.1.0")
  testImplementation("org.databene:contiperf:2.3.4")
  testImplementation("org.junit.platform:junit-platform-launcher")
  testImplementation("org.springframework.boot:spring-boot-starter-test")
  testImplementation("org.springframework.security:spring-security-test")

  errorprone("com.google.errorprone:error_prone_core:2.32.0")
}

group = "ai.saharaa"

version = "0.0.1-SNAPSHOT"

description = "ai-platform"

java { toolchain { languageVersion.set(JavaLanguageVersion.of(17)) } }

checkstyle { toolVersion = "10.18.2" }

// If spotlessFiles is specified, also use it for checkstyle. This can speed up the checks for CI.
tasks.withType<Checkstyle>().configureEach {
  if (project.hasProperty("spotlessFiles")) {
    val files = project.property("spotlessFiles").toString()
    if (files.isBlank()) {
      exclude("**/*.java")
    } else {
      files.split(",").forEach { path ->
        listOf("src/main/java/", "src/test/java/")
            .find { path.startsWith(it) }
            ?.let { include(path.removePrefix(it)) }
      }
    }
  }
}

tasks.withType<JavaCompile>().configureEach {
  options.encoding = "UTF-8"
  // -parameters Enable parameter names for reflection. Useful for mybatis, jackson, etc.
  options.compilerArgs.addAll(
      listOf("-parameters", "-implicit:class", "-Werror", "-Xplugin:Manifold no-bootstrap"))
  options.isFork = true
  options.forkOptions.memoryMaximumSize = "4g"
  options.forkOptions.memoryInitialSize = "2g"
  options.errorprone {
    isEnabled =
        !project.hasProperty("disableErrorProne") &&
            !gradle.startParameter.excludedTaskNames.contains("check")
    disableWarningsInGeneratedCode.set(true)
    excludedPaths.set(".*/(common/contracts|src/test)/.*")
    disable("DefaultCharset", "StringCaseLocaleUsage", "HidingField")
  }
}

tasks.withType<Javadoc> { options.encoding = "UTF-8" }

tasks.compileJava {
  // Format code, and generate openapi sources before compile
  dependsOn(tasks.spotlessApply, tasks.openApiGenerate)
}

tasks.spotlessInternalRegisterDependencies { dependsOn(tasks.sortDependencies) }

// Configure openapi generator
tasks.openApiGenerate {
  generatorName = "java"
  inputSpec = "$projectDir/src/main/resources/openapi/machine-review-api.yaml"
  library = "restclient"
  apiPackage = "ai.saharaa.client.api"
  modelPackage = "ai.saharaa.client.model"
  generateApiTests = false
  generateModelTests = false
  configOptions.putAll(
      mapOf("dateLibrary" to "java8", "useJakartaEe" to "true", "useBeanValidation" to "true"))
}

// Add generated sources to main source set
sourceSets {
  main {
    java.srcDir(files("${openApiGenerate.outputDir.get()}/src/main").builtBy(tasks.openApiGenerate))
  }
}

// Required for junit5
tasks {
  test {
    useJUnitPlatform { excludeTags("benchmark") }
    maxHeapSize = "2g"
    minHeapSize = "1g"
  }
}

spotless {
  // Turn off ratchetFrom in GitHub action.
  // https://github.com/diffplug/spotless/issues/2249
  if (System.getenv("CI") == null) {
    ratchetFrom("origin/develop")
  }

  java {
    palantirJavaFormat("2.47.0").style("GOOGLE").formatJavadoc(true)
    target("src/*/java/**/*.java")
  }

  kotlinGradle { ktfmt("0.53") }
}

task<Download>("downloadIpDatabase") {
  src("https://github.com/P3TERX/GeoLite.mmdb/raw/download/GeoLite2-Country.mmdb")
  dest(layout.buildDirectory.file("resources/main/GeoLite2-Country.mmdb"))
  // When rebuilding, don't overwrite and let it be UP-TO-DATE
  overwrite(false)
  // Enable build cache for this task. (With a clean rebuild, will be FROM-CACHE)
  outputs.cacheIf { true }
}

tasks.processResources { dependsOn(tasks["downloadIpDatabase"]) }

// Print task execution time. If we find a good plugin, this can be replaced.
tasks.forEach { task ->
  var startTime: Long = 0

  task.doFirst { startTime = System.currentTimeMillis() }

  task.doLast {
    val finishTime = System.currentTimeMillis()
    println("> Task ${task.path} took ${(finishTime - startTime) / 1000.0} seconds")
  }
}
