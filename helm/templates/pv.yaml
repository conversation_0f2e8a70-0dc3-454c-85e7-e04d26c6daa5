{{- if eq .Values.cloudProvider "aws" }}
apiVersion: v1
kind: PersistentVolume
metadata:
 name: efs-pv
 namespace: {{ .Values.namespace }}
spec:
 capacity:
   storage: {{ .Values.efs.capacity }}
 volumeMode: Filesystem
 accessModes:
   - ReadWriteMany
 persistentVolumeReclaimPolicy: Retain
 storageClassName: efs-sc
 csi:
   driver: efs.csi.aws.com
   volumeHandle: "{{ .Values.efs.fileSystemId }}"
{{- end }}
