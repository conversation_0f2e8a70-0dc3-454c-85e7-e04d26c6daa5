apiVersion: v1
kind: Service
metadata:
  name: {{ include "campfire-platform.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "campfire-platform.labels" . | nindent 4 }}
  {{- if eq .Values.cloudProvider "gcp" }}
  annotations:
    cloud.google.com/neg: '{"exposed_ports": {"{{ .Values.service.port }}":{}}}'
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "campfire-platform.selectorLabels" . | nindent 4 }}
