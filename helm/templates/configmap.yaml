apiVersion: v1
kind: ConfigMap
metadata:
  name: service-config
  namespace: "{{ .Values.namespace }}"
data:
  AI_DATABASE: "{{ .Values.platform.database.url }}"
  AI_DB_USER: "{{ .Values.platform.database.user }}"
  AI_DB_PASS: "{{ .Values.platform.database.password }}"
  AI_REDIS_HOST: "{{ .Values.platform.redis.host }}"
  AI_REDIS_PORT: "{{ .Values.platform.redis.port }}"
  AI_REDIS_PASSWORD: "{{ .Values.platform.redis.password }}"
  AI_REDIS_SSL_ENABLED: "{{ .Values.platform.redis.ssl }}"
  AI_CLUSTER_NAME: "{{ .Values.platform.cluster.name }}"
  AI_PLATFORM_ENV: "{{ .Values.platform.env }}"
  AI_AKKA_MODE: kubernetes
  AI_JWT_SECRET: "{{ .Values.platform.settings.AI_JWT_SECRET }}"
  GCS_BUCKET: "{{ .Values.platform.settings.GCS_BUCKET }}"
  CLOUD_PROVIDER: "{{ .Values.platform.settings.CLOUD_PROVIDER }}"
  AWS_S3_ENABLE: "{{ .Values.platform.settings.AWS_S3_ENABLE }}"
  AWS_S3_REGION: "{{ .Values.platform.settings.AWS_S3_REGION }}"
  AWS_S3_BUCKET: "{{ .Values.platform.settings.AWS_S3_BUCKET }}"
  AWS_S3_ACCESS_KEY_ID: "{{ .Values.platform.settings.AWS_S3_ACCESS_KEY_ID }}"
  AWS_S3_SECRET_ACCESS_KEY: "{{ .Values.platform.settings.AWS_S3_SECRET_ACCESS_KEY }}"
  AWS_SES_REGION: "{{ .Values.platform.settings.AWS_SES_REGION }}"
  AWS_SES_ACCESS_KEY_ID: "{{ .Values.platform.settings.AWS_SES_ACCESS_KEY_ID }}"
  AWS_SES_SECRET_ACCESS_KEY: "{{ .Values.platform.settings.AWS_SES_SECRET_ACCESS_KEY }}"
  AI_STORE_PATH: "{{ .Values.platform.settings.AI_STORE_PATH }}"
  AI_DATA_URL: "{{ .Values.platform.settings.AI_DATA_URL }}"
  SITE_URL: "{{ .Values.platform.settings.SITE_URL }}"
  AI_WEB2_IPSTACK_ACCESS_KEY: "{{ .Values.platform.settings.AI_WEB2_IPSTACK_ACCESS_KEY }}"
  CAPTCHA_SECRET_CF: "{{ .Values.platform.settings.CAPTCHA_SECRET_CF }}"
  OPEN_AI_KEY: "{{ .Values.platform.settings.OPEN_AI_KEY }}"
  AI_WEB3_CHAIN_ID: "{{ .Values.platform.settings.AI_WEB3_CHAIN_ID}}"
  REQUIRED_CONTACT_POINT_NR: "{{ .Values.platform.cluster.requiredContactPointNr }}"
  AI_WEB3_RPC_URL: "{{.Values.platform.settings.AI_WEB3_RPC_URL}}"
  AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS}}"
  AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS}}"
  AI_WEB3_DSP_REWARD_CREDENTIALS_PK: "{{.Values.platform.settings.AI_WEB3_DSP_REWARD_CREDENTIALS_PK}}"
  AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS}}"
  AI_WEB3_DSP_REWARD_CHAIN_ID: "{{.Values.platform.settings.AI_WEB3_DSP_REWARD_CHAIN_ID}}"
  AI_WEB3_DSP_REWARD_GRAPHQL_URL: "{{.Values.platform.settings.AI_WEB3_DSP_REWARD_GRAPHQL_URL}}"
  AI_WEB3_DSP_REWARD_RPC_URL: "{{.Values.platform.settings.AI_WEB3_DSP_REWARD_RPC_URL}}"
  GOOGLE_RECAPTCHA_SECRET_V3: "{{.Values.platform.settings.GOOGLE_RECAPTCHA_SECRET_V3}}"
  GOOGLE_RECAPTCHA_SECRET_V2: "{{.Values.platform.settings.GOOGLE_RECAPTCHA_SECRET_V2}}"
  AWS_ACCESS_KEY_ID: "{{.Values.platform.settings.AWS_ACCESS_KEY_ID}}"
  AWS_SECRTE_ACCESS_KEY: "{{.Values.platform.settings.AWS_SECRTE_ACCESS_KEY}}"
  KAFKA_SASL_ENABLE: "{{.Values.platform.settings.KAFKA_SASL_ENABLE}}"
  KAFKA_SERVERS: "{{.Values.platform.settings.KAFKA_SERVERS}}"
  WORKLOAD_LIMIT_PLATFORM_IP_DAILY: "{{.Values.platform.settings.WORKLOAD_LIMIT_PLATFORM_IP_DAILY}}"
  WORKLOAD_LIMIT_PLATFORM_USER_DAILY: "{{.Values.platform.settings.WORKLOAD_LIMIT_PLATFORM_USER_DAILY}}"
  CAMPFIRE_MACHINE_REVIEW_URL: "{{.Values.platform.settings.CAMPFIRE_MACHINE_REVIEW_URL}}"
  CAMPFIRE_MACHINE_REVIEW_KEY: "{{.Values.platform.settings.CAMPFIRE_MACHINE_REVIEW_KEY}}"
  ONCHAIN_GRAPHQL_ENDPOINT: "{{.Values.platform.settings.ONCHAIN_GRAPHQL_ENDPOINT}}"
  DATABASE_ONCHAIN: "{{.Values.platform.settings.DATABASE_ONCHAIN}}"
  DB_SCHEMA_ONCHAIN: "{{.Values.platform.settings.DB_SCHEMA_ONCHAIN}}"
  DB_USER_ONCHAIN: "{{.Values.platform.settings.DB_USER_ONCHAIN}}"
  DB_PASS_ONCHAIN: "{{.Values.platform.settings.DB_PASS_ONCHAIN}}"
  ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE: "{{.Values.platform.settings.ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE}}"
  ACHIEVEMENT_ORACLE_DISABLED: "{{.Values.platform.settings.ACHIEVEMENT_ORACLE_DISABLED}}"
  OTEL_EXPORTER_OTLP_ENDPOINT: "{{ .Values.platform.settings.OTEL_EXPORTER_OTLP_ENDPOINT }}"
  OTEL_EXPORTER_OTLP_PROTOCOL: "{{ .Values.platform.settings.OTEL_EXPORTER_OTLP_PROTOCOL }}"
  OTEL_LOGS_EXPORTER: "{{ .Values.platform.settings.OTEL_LOGS_EXPORTER }}"
  OTEL_RESOURCE_ATTRIBUTES: "{{ .Values.platform.settings.OTEL_RESOURCE_ATTRIBUTES }}"
  OTEL_EXPORTER_OTLP_HTTP_ENDPOINT: "{{ .Values.platform.settings.OTEL_EXPORTER_OTLP_HTTP_ENDPOINT }}"
  AUTHENTICATION_URL: "{{ .Values.platform.settings.AUTHENTICATION_URL }}"
  INTERNAL_API_KEY: "{{ .Values.platform.settings.INTERNAL_API_KEY }}"
  BINANCE_API_HOSTNAME: "{{ .Values.platform.settings.BINANCE_API_HOSTNAME }}"
