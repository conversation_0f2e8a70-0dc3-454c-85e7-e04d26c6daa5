commit baa3254e6ce74f423bb490f84541794b787dea91
Author: zequn <<EMAIL>>
Date:   Fri Jul 4 15:46:07 2025 +0800

    feat: pts replacement WIP

diff --git a/src/main/java/ai/saharaa/model/BatchSetting.java b/src/main/java/ai/saharaa/model/BatchSetting.java
index 728a8a63..ceaafd1a 100644
--- a/src/main/java/ai/saharaa/model/BatchSetting.java
+++ b/src/main/java/ai/saharaa/model/BatchSetting.java
@@ -86,6 +86,7 @@ public class BatchSetting implements Serializable {
   private DistributeType distributeType;
 
   private Timestamp cutoffTime;
+  private TokenType rewardTokenType;
 
   @Serial
   private static final long serialVersionUID = 1L;
@@ -165,6 +166,24 @@ public class BatchSetting implements Serializable {
     }
   }
 
+  public enum TokenType implements BaseIntegerEnum {
+    SAHARA(0),
+    USD1(1);
+
+    @EnumValue
+    @JsonValue
+    public final Integer value;
+
+    TokenType(Integer value) {
+      this.value = value;
+    }
+
+    @Override
+    public Integer getValue() {
+      return value;
+    }
+  }
+
   public enum TaskPaymentType implements BaseEnum {
     TRADITIONAL("traditional"),
     REWARD_POOL("reward_pool");
diff --git a/src/main/java/ai/saharaa/model/newTask/ThirdPartyRewardToken.java b/src/main/java/ai/saharaa/model/newTask/ThirdPartyRewardToken.java
new file mode 100644
index 00000000..3d4b4b82
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTask/ThirdPartyRewardToken.java
@@ -0,0 +1,41 @@
+package ai.saharaa.model.newTask;
+
+import ai.saharaa.model.BatchSetting;
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "third_party_reward_token", autoResultMap = true)
+public class ThirdPartyRewardToken implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long resourceId;
+  private Long batchId;
+  private BigDecimal amount;
+  private String tokenName;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+//  id                BIGSERIAL       PRIMARY KEY,
+//  resource_id       BIGINT          NOT NULL,
+//  batch_id          BIGINT          NOT NULL,
+//  amount            numeric(24, 6)  NOT NULL,
+//  token_name        varchar(255)    NOT NULL,
+//  deleted           BOOLEAN         NOT NULL DEFAULT false,
+//  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+//  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+}
diff --git a/src/main/java/ai/saharaa/model/newTask/UserTokenRewardClaims.java b/src/main/java/ai/saharaa/model/newTask/UserTokenRewardClaims.java
new file mode 100644
index 00000000..428a608a
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTask/UserTokenRewardClaims.java
@@ -0,0 +1,64 @@
+package ai.saharaa.model.newTask;
+
+import ai.saharaa.enums.BaseIntegerEnum;
+import ai.saharaa.model.BatchSetting;
+import com.baomidou.mybatisplus.annotation.EnumValue;
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import com.fasterxml.jackson.annotation.JsonValue;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "user_token_reward_claims", autoResultMap = true)
+public class UserTokenRewardClaims implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long userId;
+  private BigDecimal amount;
+  private BatchSetting.TokenType rewardTokenType;
+  private TokenClaimStatus status;
+  private Long thirdPartyTokenId;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+  public enum TokenClaimStatus implements BaseIntegerEnum {
+    PENDING(0),
+    DISTRIBUTED(1),
+    FAILED(2);
+
+    @EnumValue
+    @JsonValue
+    public final Integer value;
+
+    TokenClaimStatus(Integer value) {
+      this.value = value;
+    }
+
+    @Override
+    public Integer getValue() {
+      return value;
+    }
+  }
+//  id                BIGSERIAL       PRIMARY KEY,
+//  user_id           BIGINT          NOT NULL,
+//  amount            numeric(20, 6)  NOT NULL,
+//  reward_token_type smallint        NOT NULL DEFAULT 0,
+//  status            smallint        NOT NULL DEFAULT 0,
+//  tx_hash           varchar(255)    NOT NULL DEFAULT '',
+//  deleted           BOOLEAN         NOT NULL DEFAULT false,
+//  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+//  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+}
diff --git a/src/main/java/ai/saharaa/model/newTask/UserTokenRewards.java b/src/main/java/ai/saharaa/model/newTask/UserTokenRewards.java
new file mode 100644
index 00000000..d0f9fc58
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTask/UserTokenRewards.java
@@ -0,0 +1,41 @@
+package ai.saharaa.model.newTask;
+
+import ai.saharaa.model.BatchSetting;
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "user_token_rewards", autoResultMap = true)
+public class UserTokenRewards implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long userId;
+  private BigDecimal amount;
+  private BatchSetting.TokenType rewardTokenType;
+  private Long thirdPartyTokenId;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+//  id                BIGSERIAL       PRIMARY KEY,
+//  user_id           BIGINT          NOT NULL,
+//  amount            numeric(20, 6)  NOT NULL,
+//  reward_token_type smallint        NOT NULL DEFAULT 0,
+//  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+//  deleted           BOOLEAN         NOT NULL DEFAULT false,
+//  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+//  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+}
diff --git a/src/main/java/ai/saharaa/model/newTask/UserTokenTaskRewards.java b/src/main/java/ai/saharaa/model/newTask/UserTokenTaskRewards.java
new file mode 100644
index 00000000..bdd9b6ee
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTask/UserTokenTaskRewards.java
@@ -0,0 +1,48 @@
+package ai.saharaa.model.newTask;
+
+import ai.saharaa.common.typeHandler.HoneyPotBatchListTypeHandler;
+import ai.saharaa.enums.BaseEnum;
+import ai.saharaa.enums.BaseIntegerEnum;
+import ai.saharaa.model.BatchSetting;
+import ai.saharaa.model.HoneyPotBatch;
+import com.baomidou.mybatisplus.annotation.*;
+import com.fasterxml.jackson.annotation.JsonValue;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serial;
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+import java.util.List;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "user_token_task_rewards", autoResultMap = true)
+public class UserTokenTaskRewards implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long userId;
+  private Long jobId;
+  private BigDecimal amount;
+  private BatchSetting.TokenType rewardTokenType;
+  private Long thirdPartyTokenId;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+//  id                    BIGSERIAL       PRIMARY KEY,
+//  user_id               BIGINT          NOT NULL,
+//  job_id                BIGINT          NOT NULL,
+//  amount                numeric(20, 6)  NOT NULL,
+//  reward_token_type     smallint        NOT NULL DEFAULT 0,
+//  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+//  deleted               BOOLEAN         NOT NULL DEFAULT false,
+//  created_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+//  updated_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+}
diff --git a/src/main/resources/db/migration/V49_22__publicTaskUpdate.sql b/src/main/resources/db/migration/V49_22__publicTaskUpdate.sql
new file mode 100644
index 00000000..28934de8
--- /dev/null
+++ b/src/main/resources/db/migration/V49_22__publicTaskUpdate.sql
@@ -0,0 +1,59 @@
+ALTER TABLE batch_setting ADD COLUMN IF NOT EXISTS reward_token_type smallint not null default 0;
+
+
+CREATE TABLE IF NOT EXISTS third_party_reward_token
+(
+  id                BIGSERIAL       PRIMARY KEY,
+  resource_id       BIGINT          NOT NULL,
+  batch_id          BIGINT          NOT NULL,
+  amount            numeric(24, 6)  NOT NULL,
+  token_name        varchar(255)    NOT NULL,
+  deleted           BOOLEAN         NOT NULL DEFAULT false,
+  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+CREATE INDEX IF NOT EXISTS third_party_reward_token_batch_idx ON third_party_reward_token (batch_id);
+
+CREATE TABLE IF NOT EXISTS user_token_task_rewards
+(
+  id                    BIGSERIAL       PRIMARY KEY,
+  user_id               BIGINT          NOT NULL,
+  job_id                BIGINT          NOT NULL,
+  amount                numeric(20, 6)  NOT NULL,
+  reward_token_type     smallint        NOT NULL DEFAULT 0,
+  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+  deleted               BOOLEAN         NOT NULL DEFAULT false,
+  created_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+
+CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_idx ON user_token_task_rewards (user_id);
+CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_status_idx ON user_token_task_rewards (user_id, status);
+
+CREATE TABLE IF NOT EXISTS user_token_rewards
+(
+  id                BIGSERIAL       PRIMARY KEY,
+  user_id           BIGINT          NOT NULL,
+  amount            numeric(20, 6)  NOT NULL,
+  reward_token_type smallint        NOT NULL DEFAULT 0,
+  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+  deleted           BOOLEAN         NOT NULL DEFAULT false,
+  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+CREATE INDEX IF NOT EXISTS user_token_rewards_uid_idx ON user_token_rewards (user_id);
+
+
+CREATE TABLE IF NOT EXISTS user_token_reward_claims
+(
+  id                BIGSERIAL       PRIMARY KEY,
+  user_id           BIGINT          NOT NULL,
+  amount            numeric(20, 6)  NOT NULL,
+  reward_token_type smallint        NOT NULL DEFAULT 0,
+  status            smallint        NOT NULL DEFAULT 0,
+  tx_hash           varchar(255)    NOT NULL DEFAULT '',
+  deleted           BOOLEAN         NOT NULL DEFAULT false,
+  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+CREATE INDEX IF NOT EXISTS user_token_reward_claims_uid_idx ON user_token_reward_claims (user_id);
