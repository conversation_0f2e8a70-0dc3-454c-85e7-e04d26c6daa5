

spring.main.banner-mode=off
logging.level.org.springframework=ERROR
logging.file.name=./logs/campfire-platform-logger.log
# TODO consider removing the following config and let Spring to use logging.file.name to figure it out
logging.logback.rollingpolicy.file-name-pattern=./logs/archived/campfire-platform-logger-%d{yyyy-MM-dd}.%i.log
spring.jpa.hibernate.ddl-auto=none

# Tracing configurations https://docs.spring.io/spring-boot/reference/actuator/tracing.html
# Without this customization, the default tracing correlation will produce a lot of empty spaces.
logging.pattern.correlation=[%X{traceId:-},%X{spanId:-}] 
# The default only pushes 10% of traces to Zipkin. Push 100% instead.
management.tracing.sampling.probability=1

spring.datasource.primary.jdbc-url=${AI_DATABASE:*****************************************}
spring.datasource.primary.username=${AI_DB_USER:root}
spring.datasource.primary.password=${AI_DB_PASS:123456}
#spring.datasource.primary.jdbc-url=${AI_DATABASE:*****************************************}
#spring.datasource.primary.username=${AI_DB_USER:postgres}
#spring.datasource.primary.password=${AI_DB_PASS:saharalabs@2024SH}

spring.datasource.primary.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=${AI_DB_POOL_SIZE:10}
spring.datasource.onchain.jdbc-url=${DATABASE_ONCHAIN:*********************************************************************}
spring.datasource.onchain.username=${DB_USER_ONCHAIN:graph-node}
spring.datasource.onchain.password=${DB_PASS_ONCHAIN:let-me-in}
spring.datasource.onchain.driver-class-name=org.postgresql.Driver

#spring.datasource.url=${AI_DATABASE:********************************************}
#spring.datasource.username=${AI_DB_USER:questqa}
#spring.datasource.password=${AI_DB_PASS:questV587}
spring.data.redis.host=${AI_REDIS_HOST:127.0.0.1}
spring.data.redis.database=0
spring.data.redis.port=${AI_REDIS_PORT:6379}
spring.data.redis.password=${AI_REDIS_PASSWORD:123456}
spring.data.redis.ssl.enabled=${AI_REDIS_SSL_ENABLED:false}

#spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

akka.cluster.name = ${AI_CLUSTER_NAME:ai-dev-cluster}

spring.flyway.url=${AI_DATABASE:*****************************************}
spring.flyway.user=${AI_DB_USER:root}
spring.flyway.password=${AI_DB_PASS:123456}
spring.flyway.validate-migration-naming=true
spring.flyway.baseline-on-migrate=true

spring.servlet.multipart.max-file-size=1024MB
spring.servlet.multipart.max-request-size=1024MB

spring.devtools.remote.restart.enabled=false

ai.saharaa.jwt.secret=${AI_JWT_SECRET:ai-secret}
# in seconds, default 7 days
ai.saharaa.jwt.ttl=${AI_JWT_TTL:604800}
# in seconds, default 3 days
ai.saharaa.jwt.refreshTTL=${AI_JWT_REFRESH_TTL:259200}

ai.saharaa.data.url=${AI_DATA_URL:https://data-stage.saharaa.ai}

server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=20s

#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

resource.base-path=${AI_STORE_PATH:/Users/<USER>/aiTempFiles}

web3auth.token-expire=${AI_WEB3AUTH_TOKEN_EXPIRE:259200}

aws.url_expires=${AI_AWS_URL_EXPIRES:3600}

aws.s3.enable=${AWS_S3_ENABLE:true}
aws.s3.region=${AWS_S3_REGION:us-east-2}
aws.s3.bucket=${AWS_S3_BUCKET:saharaa-public}
aws.s3.id=${AWS_S3_ACCOUNT_ID:25edafaea9040d9026d1a831e53171379efb56bebe1db22a82e6b8ab11baf8c3}
aws.s3.access_key_id=${AWS_S3_ACCESS_KEY_ID:********************}
aws.s3.secret_access_key=${AWS_S3_SECRET_ACCESS_KEY:4ME0Ll+lLA4FAGQ+MDqX0qRQnFEvzc7Z5455nWwO}
aws.s3.url=${AWS_S3_URL:}

aws.sts.region=${AWS_STS_REGION:}
aws.sts.bucket=${AWS_STS_BUCKET:}
aws.sts.access_key_id=${AWS_STS_ACCESS_KEY_ID:}
aws.sts.secret_access_key=${AWS_STS_SECRET_ACCESS_KEY:}
aws.ses.region=${AWS_SES_REGION:us-west-2}
aws.ses.access_key_id=${AWS_SES_ACCESS_KEY_ID:xx}
aws.ses.secret_access_key=${AWS_SES_SECRET_ACCESS_KEY:xx}

aws.sts.assume_role_session=${AWS_STS_ASSUME_ROLE_SESSION:SESSION}
aws.sts.assume_role_duration=${AWS_STS_ASSUME_ROLE_DURATION:3600}
captcha.google.v3.secret=${GOOGLE_RECAPTCHA_SECRET_V3:6Lc-DhEqAAAAAAto1-w1cWGmmReJGmaDe2gEUiXO}
captcha.google.v2.secret=${GOOGLE_RECAPTCHA_SECRET_V2:6LddMRwqAAAAAHbFgy7w4tmSl22YrIdiqj3AdU4K}
captcha.google.v3.endpoint=${GOOGLE_RECAPTCHA_V3_ENDPOINT:https://www.recaptcha.net/recaptcha/api/siteverify}
ai.saharaa.platform.env=${AI_PLATFORM_ENV:dev}
ai.saharaa.web2.auth_enabled=${AI_WEB2_AUTH_ENABLED:false}
# in seconds, default 5 minutes
ai.saharaa.web2.email_code_ttl=${AI_WEB2_EMAIL_CODE_TTL:300}
ai.saharaa.web2.email_sender_address=${AI_WEB2_EMAIL_SENDER_ADDRESS:<EMAIL>}
ai.saharaa.web2.email_reset_password_url=${AI_WEB2_EMAIL_RESET_PASSWORD_URL:https://saharaa.ai/#/newPassword?token=%s}
# in seconds, default 1 minutes
ai.saharaa.web2.email_send_to_same_address_interval=${AI_WEB2_EMAIL_SEND_TO_SAME_ADDRESS_INTERVAL:60}
ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
ai.saharaa.site_url=${SITE_URL:https://saharaa.ai}

openai.key=${OPEN_AI_KEY:}
captcha.secret=${CAPTCHA_SECRET_CF:}

esapi.validator.body_size_limit=${ESAPI_BODY_SIZE_LIMIT:2000000}

custom.user.address.whitelist=${USER_ADDRESS_WHITELIST_OPEN:false}
custom.user.olympic.login=${USER_NEED_OLYMPIC_LOGIN:true}

management.endpoint.metrics.enabled=true
management.endpoints.web.exposure.include=*

campfire.machine.review.url=${CAMPFIRE_MACHINE_REVIEW_URL:http://*************:8888}
logging.structured.format.console=
workload.limits.platform.ip.daily=${WORKLOAD_LIMIT_PLATFORM_IP_DAILY:6555}
workload.limits.platform.user.daily=${WORKLOAD_LIMIT_PLATFORM_USER_DAILY:33}
cache.workload.db.index=14

authentication.url=${AUTHENTICATION_URL:http://localhost:8082}
# Binance API proxy configuration
binance.api.proxy.url=${BINANCE_PROXY_URL:}

# Enable TRACE logging for Apache HttpClient components
logging.level.org.apache.hc.client5.http=TRACE
# This logs the actual request/response wire content
logging.level.org.apache.hc.client5.http.wire=TRACE
binance.api.hostname=${BINANCE_API_HOSTNAME:}

ai.saharaa.web3.rpc_url=http://127.0.0.1:8545
ai.saharaa.web3.credentials_pk=0x3295915f2980b634e2fd3980936b9dbc4d4a95daf8db694dede8f3a6b1f0aecd
#ai.saharaa.web3.dsp_reward_contract_address=0x57F4Bc382d39fE0F9aD3D91d4B33A23Fae234445
ai.saharaa.web3.dsp_reward_contract_address=0x0DCd1Bf9A1b36cE34237eEaFef220932846BCD82
#ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
#ai.saharaa.web3.dsp_reward_chain_id=313313
ai.saharaa.web3.dsp_reward_chain_id=31337
ai.saharaa.web3.dsp_reward_credentials_pk=${AI_WEB3_DSP_REWARD_CREDENTIALS_PK:0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80}
#ai.saharaa.web3.dsp_reward_credentials_pk=${AI_WEB3_DSP_REWARD_CREDENTIALS_PK:0x3295915f2980b634e2fd3980936b9dbc4d4a95daf8db694dede8f3a6b1f0aecd}
