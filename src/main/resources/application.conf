kamon {

  prometheus {
    include-environment-tags = true
    embedded-server {
      port = 8081
      path = /actuator/prometheus
    }
  }
  instrumentation.akka.filters {

    actors.track {
      includes = [ "/user/**" ]
      excludes = [  ]
    }

    actors.trace {
      includes = [ "/user/**" ]
      excludes = [  ]
    }


    routers {
      includes = [  ]
    }
  }
}

kamon.instrumentation.spring {
  server.metrics {
    enabled = no
  }
}
kamon.instrumentation.play.http {
  server {
    propagation {
      enabled = no
      channel = default
    }
  }

  client {
    propagation {
      enabled = no
      channel = default
    }
  }
}