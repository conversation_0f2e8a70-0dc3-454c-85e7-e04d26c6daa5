-- Step 1: Create table season_user_point_amplifier
CREATE TABLE IF NOT EXISTS user_point_amplifier (
    id bigserial NOT NULL,
    user_id int8 NOT NULL,
    point_amplifier numeric(3, 1) NOT NULL,
    deleted bool DEFAULT true NOT NULL,
    created_at timestamptz DEFAULT now() NOT NULL,
    started_at timestamptz DEFAULT NULL,
    ended_at timestamptz DEFAULT NULL,
    CONSTRAINT season_user_point_amplifier_pkey PRIMARY KEY (id),
    CONSTRAINT fk_season_user_point_amplifier_user_id FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE UNIQUE INDEX IF NOT EXISTS idx_user_point_amplifier ON user_point_amplifier (user_id, point_amplifier);