CREATE TABLE user_session
(
  id                  BIGSERIAL PRIMARY KEY,
  user_id             BIGINT      NOT NULL,
  duration            BIGINT NOT NULL DEFAULT 0,
  total_online_time   BIGINT NOT NULL DEFAULT 0,
  total_session_count BIGINT NOT NULL DEFAULT 0,
  end_at     TIMESTAMPTZ,
  deleted             <PERSON><PERSON><PERSON><PERSON>N     NOT NULL DEFAULT false,
  refresh_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at          TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_user_session_user_id ON user_session (user_id);
