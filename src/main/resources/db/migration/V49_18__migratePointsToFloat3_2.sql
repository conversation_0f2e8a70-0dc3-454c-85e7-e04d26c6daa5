
ALTER TABLE season_user RENAME COLUMN season_points TO season_points_legacy;
ALTER TABLE season_user ADD COLUMN IF NOT EXISTS season_points numeric(20, 6) NOT NULL default 0.000000;
UPDATE season_user SET season_points = CAST(season_points_legacy AS numeric(20, 6));


-- revert script:
-- ALTER TABLE season_user DROP COLUMN season_points;
-- ALTER TABLE season_user RENAME COLUMN season_points_legacy TO season_points;
