CREATE TABLE IF NOT EXISTS exp_updated_mutable
(
    vid                 BIGSERIAL       PRIMARY KEY,
    block_range         varchar(255)    NOT NULL,
    id                  varchar(255)    NOT NULL,
    user_id             BIGINT          NOT NULL,
    account             varchar(255)    NOT NULL,
    exp_added           numeric         NOT NULL,
    level               numeric         NOT NULL,
    total_exp           numeric         NOT NULL,
    block_number        numeric         NOT NULL,
    block_timestamp     numeric         NOT NULL,
    deleted             boolean         NOT NULL default false,
    transaction_hash    varchar(255)    NOT NULL,
    created_at          TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);

