
alter table certificate
drop constraint certificate_parent_certificate_id_fkey;

alter table user_certificate
drop constraint user_certificate_parent_user_certificate_id_fkey;


INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('course', 'course', '', '', '', '2024-03-28 16:28:48.686939 +00:00', '2024-03-28 16:28:48.686939 +00:00', null, null, null, 0, null, false);

INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('vetting-test', 'vetting-test', '', '', '', '2024-03-28 16:30:28.752192 +00:00', '2024-03-28 16:30:28.752192 +00:00', null, null, null, 0, null, false);

