CREATE TABLE certificate (
  id bigserial primary key,
  name varchar(255) not null,
  category varchar(255) not null,
  certificate_type varchar(255) not null,
  description text not null default '',
  pic varchar(255) not null default '',
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  -- available on parent certificate --
  lv2exp jsonb,
  -- available on child certificate --
  knowledge_list jsonb,
  difficulty bigint,
  parent_certificate_id bigint references certificate(id),
  exp bigint
);

CREATE INDEX idx_certificate_category ON certificate (category);
CREATE INDEX idx_certificate_certificate_type ON certificate (certificate_type);
CREATE INDEX idx_certificate_knowledge_list ON certificate USING gin (knowledge_list jsonb_path_ops);
CREATE INDEX idx_certificate_difficulty ON certificate (difficulty);
CREATE INDEX idx_certificate_parent_certificate_id ON certificate (parent_certificate_id);