-- ---------------------------------------------------------------------------------------------------------------------

UPDATE achievement_level
SET requirement = 'Once you earn at least 100 Sahara Points in any one of the three categories: Model Optimization, Persona Emulation, Tech Chronicles, you can claim 30 EXP'
WHERE achievement_id = 18;

UPDATE achievement_level
SET requirement = 'Checked in for 31 days based on UTC time during Data Services Season 2. (We have 31 days total in Data Services Season 2)'
WHERE id = 43;

UPDATE achievement_level
SET requirement = 'Earn at least 20 Sahara Points for 31 days based on UTC time during Data Services Season 2. (We have 31 days total in Data Services Season 2)'
WHERE id = 44;

UPDATE achievement_requirement
SET denominator = 100
WHERE achievement_id = 18;

UPDATE achievement_requirement
SET requirement   = 'Tech Chronicles',
    name          = 'Tech Chronicles',
    behavior_code = 'ORACLE_OF_KNOWLEDGE_0128:TECH_CHRONICLES'
WHERE id = 173;
-- ---------------------------------------------------------------------------------------------------------------------
