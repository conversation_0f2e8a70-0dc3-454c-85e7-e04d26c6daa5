create table aws_s3_sync_task (
    id bigserial primary key,
    batch_id bigint not null references batch(id),
    owner bigint not null,
    e_tag varchar(64) not null,
    bucket varchar(20) not null,
    save_type varchar(20) not null default '',
    region varchar(20) not null default '',
    content_type varchar(64) not null default '',
    prefix varchar(255) not null,
    file_name varchar(255) not null,
    sort int not null default 0,
    status int not null default 0,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);
