CREATE TABLE skip_task_session (
    id bigserial primary key,
    job_id bigint not null,
    task_session_id bigint not null,
    user_id bigint not null,
    deleted boolean not null default false,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

create index skip_task_session_job_id_idx on skip_task_session (job_id);
create index skip_task_session_user_id_idx on skip_task_session (user_id);

CREATE TABLE job_task_report (
    id bigserial primary key,
    job_id bigint not null,
    task_id bigint not null,
    user_id bigint not null,
    status smallint not null default 0,
    report_content text not null,
    deleted boolean not null default false,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

create index job_task_report_job_id_idx on job_task_report (job_id);
create index job_task_report_user_id_idx on job_task_report (user_id);
create index job_task_report_task_id_idx on job_task_report (task_id);
