-- achievement start finish time
ALTER TABLE achievement
  ADD started_at  TIMESTAMPTZ,
  ADD finished_at TIMESTAMPTZ;

-- achievement_level note
ALTER TABLE achievement_level
  ADD note VARCHAR(255) NOT NULL DEFAULT '';

-- achievement_requirement.achievement_level_id can be null
ALTER TABLE achievement_requirement
  ADD exp BIGINT NOT NULL DEFAULT 0,
  ALTER COLUMN achievement_level_id DROP NOT NULL;

-- user_achievement requirement_bitmap
ALTER TABLE user_achievement
  ADD requirement_bitmap          BIGINT NOT NULL DEFAULT 0,
  ADD on_chain_requirement_bitmap BIGINT NOT NULL DEFAULT 0;

-- user_achievement_progress on_chain_passed on_chain_passed_at
ALTER TABLE user_achievement_progress
  ADD on_chain_passed    BOOLEAN NOT NULL DEFAULT false,
  ADD on_chain_passed_at TIMESTAMPTZ;

-- user_daily_check_in
CREATE TABLE user_daily_check_in
(
  id              BIGSERIAL PRIMARY KEY,
  user_id         BIGINT      NOT NULL REFERENCES users (id),
  type            VARCHAR(64) NOT NULL DEFAULT '',
  year            INTEGER     NOT NULL,
  month           INTEGER     NOT NULL,
  check_in_bitmap INTEGER     NOT NULL DEFAULT 0,
  deleted         BOOLEAN     NOT NULL DEFAULT FALSE,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX user_daily_check_in_uni_idx ON user_daily_check_in USING BTREE (user_id, type, year, month);
