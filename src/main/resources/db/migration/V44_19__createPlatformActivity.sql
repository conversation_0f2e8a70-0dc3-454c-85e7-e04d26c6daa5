-- create activity table
create table platform_activity
(
    id bigserial primary key,
    season_id bigint,
    user_id bigint,
    job_id bigint,
    user_certificate_id bigint,
    activity_type varchar(255) not null,
    tx_hash varchar(80),
    detail json not null,
    deleted boolean not null default false,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null
);

create index platform_activity_user_id_idx on platform_activity (user_id);
create index platform_activity_season_id_idx on platform_activity (season_id);
create index platform_activity_job_id_idx on platform_activity (job_id);
create index platform_activity_user_certificate_id_idx on platform_activity (user_certificate_id);
