-- create season job table in pgsql
create table season_job
(
    id bigserial primary key,
    season_id bigint not null,
    job_id bigint not null,
    next_user_pos_to_settle bigint default 1 not null,
    rule_version json not null,
    status varchar(32) not null,
    deleted boolean not null default false,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null,
    constraint fk_season_job_season_id foreign key (season_id) references season (id),
    constraint fk_season_job_job_id foreign key (job_id) references job (id)
);

create index season_job_season_id_idx on season_job (season_id);
create index season_job_job_id_idx on season_job (job_id);
