-- create batch nda table, with title and content
create table batch_nda (
  id bigserial primary key,
  title text not null default '',
  content text not null default '',
  batch_id bigint not null references batch(id),
  owner_id bigint not null references users(id),
  sort integer not null default 0,
  deleted boolean not null default false,
  created_at timestamptz default current_timestamp,
  updated_at timestamptz default current_timestamp
);
