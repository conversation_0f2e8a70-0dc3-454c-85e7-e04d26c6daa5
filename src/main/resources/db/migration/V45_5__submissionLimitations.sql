ALTER TABLE batch ADD workload_max_per_job_reviewer INTEGER DEFAULT 0;
ALTER TABLE batch ADD workload_max_per_job_reviewer_day INTEGER DEFAULT 0;
ALTER TABLE batch ADD workload_max_per_job_reviewer_hour INTEGER DEFAULT 0;

ALTER TABLE batch ALTER COLUMN workload_max_per_job_user SET DEFAULT 0;
ALTER TABLE batch ALTER COLUMN workload_max_per_job_user_day SET DEFAULT 0;
ALTER TABLE batch ALTER COLUMN workload_max_per_job_user_hour SET DEFAULT 0;

UPDATE batch SET workload_max_per_job_user = 0 WHERE workload_max_per_job_user IS NULL;
UPDATE batch SET workload_max_per_job_user_day = 0 WHERE workload_max_per_job_user_day IS NULL;
UPDATE batch SET workload_max_per_job_user_hour = 0 WHERE workload_max_per_job_user_hour IS NULL;