alter table project
  add project_type varchar(20) default 'node' not null;

alter table project
  add datapoint_type varchar(20) default '' not null;

alter table project
  add knowledge varchar(20) default '' not null;

alter table project
  alter column requester_id drop not null;




CREATE TABLE client
(
  id bigserial primary key,
  org_id bigint not null,
  company_name varchar(50) NOT NULL,
  language varchar(50) NOT NULL,
  location varchar(50) NOT NULL,
  timezone varchar(50) NOT NULL,
  deleted boolean not null default false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE TABLE client_contact_person
(
  id bigserial primary key,
  client_id bigint not null,
  name varchar(50) NOT NULL default '',
  role varchar(50) NOT NULL default '',
  first boolean not null default false,
  email  varchar(50) NOT NULL default '',
  weChat  varchar(50) NOT NULL default '',
  telegram  varchar(50) NOT NULL default '',
  iMessage varchar(50) NOT NULL default '',
  whatsApp varchar(50) NOT NULL default '',
  slack varchar(50) NOT NULL default '',
  deleted boolean not null default false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);


CREATE TABLE client_relation
(
  id bigserial primary key,
  project_id bigint NOT NULL,
  client_id bigint NOT NULL,
  deleted boolean not null default false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

