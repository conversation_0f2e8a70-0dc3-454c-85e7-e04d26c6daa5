ALTER TABLE batch_setting ADD COLUMN honey_pot_batch_id_review varchar(255) NOT NULL default '';
ALTER TABLE batch_setting ADD COLUMN honey_pot_number_per_submit_review INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD COLUMN honey_pot_weight_review INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD COLUMN honey_pot_accuracy_required_review INTEGER DEFAULT 0;

CREATE TABLE honey_pot_review_session
(
  id                BIGSERIAL PRIMARY KEY,
  user_id           bigint      NOT NULL,
  review_session_id   bigint      NOT NULL,
  question_id       bigint      NOT NULL,
  answer            text        NOT NULL,
  status            smallint    NOT NULL default 0,
  deleted           BOOLEAN     NOT NULL DEFAULT false,

  created_at        TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_honey_pot_review_session_user_id ON honey_pot_review_session (user_id);
CREATE INDEX idx_honey_pot_review_session_review_session_id ON honey_pot_review_session (review_session_id);
CREATE INDEX idx_honey_pot_review_session_question_id ON honey_pot_review_session (question_id);
