
ALTER TABLE season_user RENAME COLUMN total_points TO total_points_legacy;
ALTER TABLE season_user ADD COLUMN IF NOT EXISTS total_points numeric(20, 6) NOT NULL default 0.000000;
UPDATE season_user SET total_points = CAST(total_points_legacy AS numeric(20, 6));


-- revert script:
-- ALTER TABLE season_user DROP COLUMN total_points;
-- ALTER TABLE season_user RENAME COLUMN total_points_legacy TO total_points;
--