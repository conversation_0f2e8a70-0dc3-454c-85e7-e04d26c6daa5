
create table upload_json (
    id             bigserial primary key,
    batch_id       bigint not null,
    region         varchar(64) not null,
    bucket         varchar(64) not null,
    file_name      varchar(255) not null,
    file_key       varchar(255) not null,

    status         smallint not null default 0,
    deleted        boolean not null default false,
    created_at     timestamptz not null default now(),
    updated_at     timestamptz not null default now(),
    constraint fk_upload_json_batch_id foreign key (batch_id) references batch (id)
);

create table zip_file (
    id             bigserial primary key,
    batch_id       bigint not null,
    json_file_id   bigint not null,
    region         varchar(64) not null,
    bucket         varchar(64) not null,
    file_name      varchar(255) not null,
    file_key       varchar(255) not null,

    status         smallint not null default 0,
    deleted        boolean not null default false,
    created_at     timestamptz not null default now(),
    updated_at     timestamptz not null default now()
--     constraint fk_zip_file_batch_id foreign key (batch_id) references batch (id),
--     constraint fk_zip_file_json_file_id foreign key (json_file_id) references upload_json (id)
);
create index zip_file_batch_id_idx on zip_file (batch_id);
create index zip_file_json_id_idx on zip_file (json_file_id);

create table hybrid_item_task (
    id             bigserial primary key,
    batch_id       bigint not null,

    aws_key        varchar(255) null,
    file_name      varchar(255) null,
    file_key       varchar(255) null,
    file_size      int null,

    type           varchar(64) null,
    region         varchar(64) not null,
    bucket         varchar(64) not null,

    hybrid_type    varchar(64) not null,
    mimetype       varchar(64) null,
    parent_id      bigint null,
    sort           smallint null,

    url            varchar(1024) null,
    metadata       varchar(255) null,

    json_file_id   bigint not null,

    status         smallint not null default 0,
    deleted        boolean not null default false,
    created_at     timestamptz not null default now(),
    updated_at     timestamptz not null default now()
--     constraint fk_hybrid_it_json_file_id foreign key (json_file_id) references upload_json (id)
);
create index hybrid_item_task_batch_id_idx on hybrid_item_task (batch_id);
