create table am_audit_session (
    id bigserial primary key,
    task_session_id bigint not null references task_session(id),
    user_id bigint not null references users(id),
    job_id bigint not null references job(id),
    task_id bigint not null references task(id),
    status varchar(20) not null default 'pending',
    result varchar(20) not null default 'none',
    duration int not null default 0,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);

create table client_audit_session (
    id bigserial primary key,
    task_session_id bigint not null references task_session(id),
    user_id bigint not null references users(id),
    job_id bigint not null references job(id),
    task_id bigint not null references task(id),
    status varchar(20) not null default 'pending',
    result varchar(20) not null default 'none',
    duration int not null default 0,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);
