create table api_keys
(
  id          bigserial primary key,
  app_name    varchar(255)             not null,
  app_id      bigint                   not null,
  api_key     varchar(255)             not null unique,
  public_key  text                     not null,
  private_key text                     not null,
  type        varchar(64)              NOT NULL default '',
  trusted_ips varchar(255)             NOT NULL default '',
  deleted     boolean                  not null default false,
  active      boolean                           default true,
  created_at  timestamp with time zone not null default now(),
  updated_at  timestamp with time zone not null default now(),
  expires_at  timestamp
);
