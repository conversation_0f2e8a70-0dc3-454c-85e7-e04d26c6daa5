-- These indexes optimize checks for user access to resources via tasks.

CREATE INDEX IF NOT EXISTS exam_session_user_task_index ON exam_session (user_id, task_id);

CREATE INDEX IF NOT EXISTS review_session_user_task_index ON review_session (user_id, task_id);

CREATE INDEX IF NOT EXISTS spot_session_user_task_index ON spot_session (user_id, task_id);

CREATE INDEX IF NOT EXISTS task_session_user_task_index ON task_session (user_id, task_id);

CREATE INDEX IF NOT EXISTS job_user_job_id_user_id_index ON job_user (task_list_session_id, user_id);