-- ---------------------------------------------------------------------------------------------------------------------
INSERT INTO achievement(id, name, symbol, description, category, level_type, standard, standard_uri, network, feature,
                        sort)
VALUES (15, 'Demi-god', 'DEMI_GOD', 'Demi god', 'AI Data Services Achievements', 'disposable-hidden',
        'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 15)
ON CONFLICT (id) DO NOTHING;

-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_level(id, achievement_id, level, exp, behavior_code, requirement, denominator, logo, note)
VALUES
  -- Demi God
  (42, 15, 1, 150, '', 'In total 1 requirement', 1, 'res:1221222', '')
ON CONFLICT (id) DO NOTHING;

-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_requirement(id, achievement_id, achievement_level_id, requirement, denominator, exp,
                                    behavior_code,
                                    name,
                                    description, logo, action_text, action_passed_text, action_uri, sort)
VALUES
  -- Demi God
  (110, 15, 42, 'Complete all demigod requirement', 1, 0,
   'SAHARA_DEMI_GOD',
   'sahara demi god', '', '', '', '', 'SAHARA_DEMI_GOD', 1)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------
