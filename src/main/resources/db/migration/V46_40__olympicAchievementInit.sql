-- -----------------------------------------------------------------------------------------------

INSERT INTO achievement(id, name, symbol, description, category, level_type, standard, standard_uri, network, feature,
                        sort, started_at, finished_at)
VALUES (9, 'Olympic Daily Check-in', 'OLYMPIC_DAILY_CHECK_IN', 'Olympic Daily Check-in Description',
        'AI Genesis Olympics Series', 'daily-trickle', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 9,
        '2024-09-16 00:00:00', '2024-09-22 23:59:59.999'),
       (10, 'Olympic Daily Work', 'OLYMPIC_DAILY_WORK', 'Olympic Daily Work Description', 'AI Genesis Olympics Series',
        'daily-trickle', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 10, '2024-09-16 00:00:00',
        '2024-09-22 23:59:59.999'),
       (11, 'Knowledge Expert', '<PERSON><PERSON><PERSON>LEDGE_EXPERT', 'Knowledge Expert Description', 'AI Genesis Olympics Series',
        'disposable-fake-level', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 11, null,
        '2024-09-22 23:59:59.999')
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------

INSERT INTO achievement_level(id, achievement_id, level, exp, behavior_code, requirement, denominator, logo, note)
VALUES (31, 9, 1, 0, '',
        'Checked in for 16 days based on UTC time during Al Genesis Olympics Series. (We have 18 days total in Al Genesis Olympics Series)',
        18, '',
        'You will be eligible to claim the NFT which represents the final degree realized in this achievement at the end of Al Genesis Olympics Series'),
       (32, 10, 1, 0, '',
        'Earn at least 20 Sahara Points for 14 days based on PST time during Al Genesis Olympics Series. (We have 18 days total in Al Genesis Olympics Series)',
        18, '',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of Al Genesis Olympics Series'),
       (33, 11, 1, 0, '',
        'Complete tasks to earn at least 18 Sahara points in one of the Web3, Gaming, Investment and Dating knowledge',
        4, '',
        'Knowledge Expert I: Earn at least 18 Sahara Points in tasks under any of the four knowledge categories: web3, investment, gaming and dating.'),
       (34, 11, 2, 0, '',
        'Complete tasks to earn at least 18 Sahara points in one of the Web3, Gaming, Investment and Dating knowledge',
        4, '',
        'Knowledge Expert II: Earn at least 18 Sahara Points in tasks under two of the four knowledge categories: web3, investment, gaming and dating.'),
       (35, 11, 3, 0, '',
        'Complete tasks to earn at least 18 Sahara points in one of the Web3, Gaming, Investment and Dating knowledge',
        4, '',
        'Knowledge Expert III: Earn at least 18 Sahara Points in tasks under three of the four knowledge categories: web3, investment, gaming and dating.'),
       (36, 11, 4, 0, '',
        'Complete tasks to earn at least 18 Sahara points in one of the Web3, Gaming, Investment and Dating knowledge',
        4, '',
        'Knowledge Expert IV: Earn at least 18 Sahara Points in tasks under all of the four knowledge categories: web3, investment, gaming and dating.')
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------

INSERT INTO achievement_requirement(id, achievement_id, achievement_level_id, requirement, denominator, exp,
                                    behavior_code, name, description, logo, action_text, action_passed_text, action_uri,
                                    sort)
VALUES
  -- Olympic Daily Check-in
  (5, 9, null, '1', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:1', '1', '', '', '', '', '', 1),
  (6, 9, null, '2', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:2', '2', '', '', '', '', '', 2),
  (7, 9, null, '3', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:3', '3', '', '', '', '', '', 3),
  (8, 9, null, '4', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:4', '4', '', '', '', '', '', 4),
  (9, 9, null, '5', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:5', '5', '', '', '', '', '', 5),
  (10, 9, null, '6', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:6', '6', '', '', '', '', '', 6),
  (11, 9, null, '7', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:7', '7', '', '', '', '', '', 7),
  (12, 9, null, '8', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:8', '8', '', '', '', '', '', 8),
  (13, 9, null, '9', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:9', '9', '', '', '', '', '', 9),
  (14, 9, null, '10', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:10', '10', '', '', '', '', '', 10),
  (15, 9, null, '11', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:11', '11', '', '', '', '', '', 11),
  (16, 9, null, '12', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:12', '12', '', '', '', '', '', 12),
  (17, 9, null, '13', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:13', '13', '', '', '', '', '', 13),
  (18, 9, null, '14', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:14', '14', '', '', '', '', '', 14),
  (19, 9, null, '15', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:15', '15', '', '', '', '', '', 15),
  (20, 9, null, '16', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:16', '16', '', '', '', '', '', 16),
  (21, 9, null, '17', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:17', '17', '', '', '', '', '', 17),
  (22, 9, null, '18', 1, 5, 'OLYMPIC_DAILY_CHECK_IN:18', '18', '', '', '', '', '', 18),
  -- Olympic Daily Work
  (23, 10, null, '1', 1, 18, 'OLYMPIC_DAILY_WORK:1', '1', '', '', '', '', '', 1),
  (24, 10, null, '2', 1, 18, 'OLYMPIC_DAILY_WORK:2', '2', '', '', '', '', '', 2),
  (25, 10, null, '3', 1, 18, 'OLYMPIC_DAILY_WORK:3', '3', '', '', '', '', '', 3),
  (26, 10, null, '4', 1, 18, 'OLYMPIC_DAILY_WORK:4', '4', '', '', '', '', '', 4),
  (27, 10, null, '5', 1, 18, 'OLYMPIC_DAILY_WORK:5', '5', '', '', '', '', '', 5),
  (28, 10, null, '6', 1, 18, 'OLYMPIC_DAILY_WORK:6', '6', '', '', '', '', '', 6),
  (29, 10, null, '7', 1, 18, 'OLYMPIC_DAILY_WORK:7', '7', '', '', '', '', '', 7),
  (30, 10, null, '8', 1, 18, 'OLYMPIC_DAILY_WORK:8', '8', '', '', '', '', '', 8),
  (31, 10, null, '9', 1, 18, 'OLYMPIC_DAILY_WORK:9', '9', '', '', '', '', '', 9),
  (32, 10, null, '10', 1, 18, 'OLYMPIC_DAILY_WORK:10', '10', '', '', '', '', '', 10),
  (33, 10, null, '11', 1, 18, 'OLYMPIC_DAILY_WORK:11', '11', '', '', '', '', '', 11),
  (34, 10, null, '12', 1, 18, 'OLYMPIC_DAILY_WORK:12', '12', '', '', '', '', '', 12),
  (35, 10, null, '13', 1, 18, 'OLYMPIC_DAILY_WORK:13', '13', '', '', '', '', '', 13),
  (36, 10, null, '14', 1, 18, 'OLYMPIC_DAILY_WORK:14', '14', '', '', '', '', '', 14),
  (37, 10, null, '15', 1, 18, 'OLYMPIC_DAILY_WORK:15', '15', '', '', '', '', '', 15),
  (38, 10, null, '16', 1, 18, 'OLYMPIC_DAILY_WORK:16', '16', '', '', '', '', '', 16),
  (39, 10, null, '17', 1, 18, 'OLYMPIC_DAILY_WORK:17', '17', '', '', '', '', '', 17),
  (40, 10, null, '18', 1, 18, 'OLYMPIC_DAILY_WORK:18', '18', '', '', '', '', '', 18),
  -- Knowledge Expert
  (41, 11, null, 'Web3', 18, 50, 'KNOWLEDGE_EXPERT:WEB3', 'Web3', '', '', '', '', '', 1),
  (42, 11, null, 'Gaming', 18, 50, 'KNOWLEDGE_EXPERT:GAMING', 'Gaming', '', '', '', '', '', 2),
  (43, 11, null, 'Investment', 18, 50, 'KNOWLEDGE_EXPERT:INVESTMENT', 'Investment', '', '', '', '', '', 3),
  (44, 11, null, 'Dating', 18, 50, 'KNOWLEDGE_EXPERT:DATING', 'Dating', '', '', '', '', '', 4)
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------
