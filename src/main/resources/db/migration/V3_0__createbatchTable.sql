
-- create cloud storage table
create table cloud_storage (
  id bigserial primary key,
  provider varchar(64) not null, -- s3, gcp,azure...
  owner_id bigint not null,
  auth_account varchar(2048) not null,
  bucket varchar(4096) not null,
  prefix varchar(4096) not null,
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_cloud_storage_user_id foreign key (owner_id) references users (id)
);

-- create resource table
create table resource(
  id bigserial primary key,
  name varchar(2048) not null,
  cloud_storage_id bigint,
  owner_id bigint not null,
  size int not null, -- in bytes
  hash varchar(256) not null default '', -- md5 hash
  file_type varchar(64) not null, -- video,image,audio,url
  suffix varchar(64) not null, -- mp4,jpg,mp3
  width int not null default 0,
  height int not null default 0,
  duration int not null default 0, -- in milliseconds
  path varchar(2048) not null default '', -- path in our local cache
  original_path varchar(4096) not null default '', -- path in cloud storage
  need_transcode boolean not null default false,
  done_transcode boolean not null default false,
  content_detection varchar(30) not null, -- adult, spoof..
  status smallint not null default 0, -- 0: uploading, 1: uploaded, 2: upload_failed , 3: transcoding, 4: transcoded,
  -- 5: transcode_failed, 6 success, 7: unsafe
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_resource_cloud_storage_id foreign key (cloud_storage_id) references cloud_storage (id),
  constraint fk_resource_user_id foreign key (owner_id) references users (id)
);

-- create a batch table
create table batch (
    id bigserial primary key,
    name varchar(255) not null,
    owner_id bigint not null,
    project_id bigint not null,
    summary text not null,
    description text not null,
    extra_info text not null,
    label_type varchar(64) not null,
    label_body text not null,
    status smallint not null default 0,
    deadline timestamptz not null,
    data_type varchar not null,
    required_accuracy int not null,
    required_regions text not null,
    required_languages text not null,
    required_skills text not null,
    labeling_repeat int not null,
    review_repeat int not null,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now(),
    deleted boolean not null default false,
    constraint fk_batch_user_id foreign key (owner_id) references users (id),
    constraint fk_batch_project_id foreign key (project_id) references project (id)
);

-- batch sample table
create table batch_sample(
  id bigserial primary key,
  batch_id bigint not null,
  resource_id bigint not null,
  owner_id bigint not null,
  sample_type varchar(64) not null, -- good or bad
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_batch_sample_batch_id foreign key (batch_id) references batch (id),
  constraint fk_batch_sample_resource_id foreign key (resource_id) references resource (id),
  constraint fk_batch_sample_user_id foreign key (owner_id) references users (id)
)
