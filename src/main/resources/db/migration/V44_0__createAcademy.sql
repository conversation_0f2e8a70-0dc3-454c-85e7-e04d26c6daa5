alter table batch
  add course_type varchar(128);

alter table batch
  add course_cover varchar(512);

alter table batch
  alter column project_id drop not null;

alter table batch
  add course_difficulty varchar(128);

alter table batch
  alter column data_type drop not null;

alter table batch
  add academy_type varchar(128);

alter table batch
  add certificate_id bigint;

alter table batch
  add knowledge varchar(128) default '';




CREATE TABLE academy
(
  id bigserial primary key,
  batch_id bigint not null,
  deleted boolean not null default false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE TABLE academy_learning_material
(
  id bigserial primary key,
  batch_id bigint not null,
  name varchar(50) NOT NULL default '',
  type varchar(64) NOT NULL default '',
  source_key varchar(512) default '',
  article text default '',
  reading_time bigint default 0,
  minimal_study_time bigint default 0,
  deleted boolean not null default false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);








