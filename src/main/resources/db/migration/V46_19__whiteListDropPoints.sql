-- Description: Create two white list related tables to support dropping Sahara points feature
-- Create white_list_management table to store white list name
CREATE TABLE IF NOT EXISTS white_list_management (
  id bigserial NOT NULL,
  white_list_name varchar(255) NOT NULL,
  types text,
  status varchar(255) DEFAULT 'Todo' NOT NULL,
  deleted boolean default false,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  CONSTRAINT white_list_management_pkey PRIMARY KEY (id)
);

-- Create white_list_wallet_address_mapping table to map white list and wallet address
CREATE TABLE IF NOT EXISTS white_list_wallet_address_mapping (
  id bigserial NOT NULL,
  white_list_id bigint NOT NULL,
  wallet_address_id bigint NOT NULL,
  deleted boolean default false,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  CONSTRAINT white_list_wallet_address_mapping_pkey PRIMARY KEY (id),
  CONSTRAINT fk_white_list_id FOREIGN KEY (white_list_id) REFERENCES white_list_management (id),
  CONSTRAINT fk_wallet_address_id FOREIGN KEY (wallet_address_id) REFERENCES wallet_address_white_list (id),
  CONSTRAINT unique_white_list_wallet UNIQUE (white_list_id, wallet_address_id)
);

-- Insert into white_list_management whitelist1 to store all existing records of wallet address
INSERT INTO white_list_management (white_list_name)
SELECT 'whitelist1'
WHERE NOT EXISTS (
  SELECT 1 FROM white_list_management wlm WHERE wlm.white_list_name = 'whitelist1'
);

-- Store all existing records of wallet address into white_list1 in table white_list_wallet_address_mapping
INSERT INTO white_list_wallet_address_mapping (white_list_id, wallet_address_id)
SELECT wm.id, wal.id
FROM wallet_address_white_list wal
       JOIN white_list_management wm ON wm.white_list_name = 'whitelist1'
WHERE NOT EXISTS (
  SELECT 1
  FROM white_list_wallet_address_mapping wwam
  WHERE wwam.white_list_id = wm.id AND wwam.wallet_address_id = wal.id
);

-- Set whitelist1 types as all the type of wallet address joint by /
UPDATE white_list_management SET types = (
  SELECT string_agg(DISTINCT type, '/')
  FROM wallet_address_white_list)
WHERE white_list_management.white_list_name = 'whitelist1';