-- create audit log table in pgsql
create table audit_log
(
    id bigserial primary key,
    user_id bigint not null,
    wallet_address varchar(255),
    email varchar(255) not null,
    role int not null,
    ip varchar(255) not null,
    browser varchar(255) not null,
    os varchar(255) not null,
    user_agent varchar(255) not null,
    operation varchar(25) not null,
    detail varchar(255) not null default '',
    created_at timestamptz not null default now(),
    constraint fk_audit_log_user_id foreign key (user_id) references users (id)
);

create index audit_log_user_id_idx on audit_log (user_id);
