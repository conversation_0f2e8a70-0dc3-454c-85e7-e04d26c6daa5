
CREATE TABLE notification_config (
                                     id bigserial NOT NULL,
                                     title text NOT NULL DEFAULT 'default title'::text,
                                     "content" text NOT NULL,
                                     created_at timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
                                     updated_at timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
                                     status int8 NULL DEFAULT 0, -- 0 is Draft, 1 is Sent, 2 is pending, 3 is Processing
                                     "type" int8 NULL DEFAULT 1, -- 0 is Jump to,1 is Read only
                                     schedule_at timestamptz NULL, -- the sent time of schedule
                                     cta_text text NULL DEFAULT 'View'::text, -- cta text
                                     target_user_type int8 NULL DEFAULT 0, -- the sent target of user. 0 is All, 1 is Input wallet address
                                     send_at timestamptz NULL,
                                     expiry_day int8 NULL, -- expirt time after sending
                                     CONSTRAINT notification_config_pk PRIMARY KEY (id)
);
CREATE INDEX notification_config_title_status_type_idx ON public.notification_config USING btree (title, status, type);


COMMENT ON COLUMN public.notification_config.status IS '0 is Draft, 1 is Sent, 2 is pending, 3 is Processing';
COMMENT ON COLUMN public.notification_config."type" IS '0 is Jump to,1 is Read only';
COMMENT ON COLUMN public.notification_config.schedule_at IS 'the sent time of schedule';
COMMENT ON COLUMN public.notification_config.cta_text IS 'cta text';
COMMENT ON COLUMN public.notification_config.target_user_type IS 'the sent target of user. 0 is All, 1 is Input wallet address';
COMMENT ON COLUMN public.notification_config.expiry_day IS 'expirt time after sending';