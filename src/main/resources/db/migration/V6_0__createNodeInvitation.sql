
-- create node invitation table
create table node_invitation (
    id bigserial primary key,
    node_id bigint not null references node(id),
    user_id bigint not null references users(id),
    invited_by bigint references users(id),
    status varchar(20) not null,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);

-- create task list invitation table
create table task_list_invitation (
    id bigserial primary key,
    node_id bigint not null references node(id),
    task_list_id bigint not null references task_list(id),
    status varchar(20) not null,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);