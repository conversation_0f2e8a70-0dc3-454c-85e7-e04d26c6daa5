
-- remove sample type frm batch sample
alter table batch_sample drop column sample_type;

-- create batch example table
create table batch_example(
  id bigserial primary key,
  batch_id bigint not null,
  task_list_id bigint not null,
  owner_id bigserial not null,
  example_type varchar(20) not null,
  deleted boolean not null default false,
  created_at timestamp not null default now(),
  updated_at timestamp not null default now(),
  constraint batch_example_batch_id_fk foreign key (batch_id) references batch(id),
  constraint batch_example_task_list_id_fk foreign key (task_list_id) references task_list(id),
  constraint batch_example_owner_id_fk foreign key (owner_id) references users(id)
);

-- add an index for example_type field
create index batch_example_example_type_idx on batch_example(example_type);