CREATE TABLE referral
(
  id           BIGSERIAL PRIMARY KEY,
  user_id      BIGINT      NOT NULL,
  max_invites  BIGINT,
  invite_code  VARCHAR(2550),
  is_customize BOOLEAN     NOT NULL DEFAULT false,
  deleted      BOOLEAN     NOT NULL DEFAULT false,
  created_at   TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expire_at    TIMESTAMPTZ,
  updated_at   TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_referral_user_id ON referral (user_id);
CREATE INDEX idx_referral_invite_code ON referral (invite_code);

CREATE TABLE referral_list
(
  id              BIGSERIAL PRIMARY KEY,
  inviter_user_id BIGINT      NOT NULL,
  friend_user_id  BIGINT      NOT NULL,
  referral_id     BIGINT      NOT NULL,
  schedule        INTEGER,
  deleted         BOOLEAN     NOT NULL DEFAULT false,
  invite_platform VARCHAR(2550),
  created_at      TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_referral_list_inviter_user_id ON referral_list (inviter_user_id);
CREATE INDEX idx_referral_list_friend_user_id ON referral_list (friend_user_id);
CREATE INDEX idx_referral_list_referral_id ON referral_list (referral_id);

CREATE TABLE referral_reward
(
  id                BIGSERIAL PRIMARY KEY,
  reward            BIGINT,
  need_invite_count BIGINT,
  level             BIGINT,
  deleted           BOOLEAN     NOT NULL DEFAULT false,
  created_at        TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_referral_reward_reward ON referral_reward (reward);
CREATE INDEX idx_referral_reward_need_invite_count ON referral_reward (need_invite_count);
CREATE INDEX idx_referral_reward_level ON referral_reward (level);

CREATE TABLE referral_user_reward
(
  id          BIGSERIAL PRIMARY KEY,
  reward_id   BIGINT      NOT NULL,
  user_id     BIGINT      NOT NULL,
  referral_id BIGINT      NOT NULL,
  deleted     BOOLEAN     NOT NULL DEFAULT false,
  created_at  TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at  TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_referral_user_reward_reward_id ON referral_user_reward (reward_id);
CREATE INDEX idx_referral_user_reward_user_id ON referral_user_reward (user_id);
CREATE INDEX idx_referral_user_reward_referral_id ON referral_user_reward (referral_id);