create table pre_task_exam
(
  id         bigserial                              not null primary key,
  batch_id   bigint                                 not null,
  user_id    bigint                                 not null,
  node_id    bigint                                 not null,
  deleted    boolean                  default false not null,
  exam_grade bigint                   default 0     not null,
  passed     boolean                  default false not null,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now(),
  constraint fk_pre_task_exam_user_id foreign key (user_id) references users (id),
  constraint fk_pre_task_exam_batch_id foreign key (batch_id) references batch (id),
  constraint fk_pre_task_exam_node_id foreign key (node_id) references node (id)
);

create index pre_task_exam_batch_id_idx on pre_task_exam (batch_id);
create index pre_task_exam_user_id_idx on pre_task_exam (user_id);
create index pre_task_exam_node_id_idx on pre_task_exam (node_id);