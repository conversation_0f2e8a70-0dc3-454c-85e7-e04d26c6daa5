-- create season user table in pgsql
create table season_user
(
    id bigserial primary key,
    season_id bigint not null,
    user_id bigint not null,
    total_points bigint default 0 not null,
    deleted boolean not null default false,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null,
    constraint fk_season_user_user_id foreign key (user_id) references users (id),
    constraint fk_season_user_season_id foreign key (season_id) references season (id)
);

create index season_user_user_id_idx on season_user (user_id);
create index season_user_season_id_idx on season_user (season_id);