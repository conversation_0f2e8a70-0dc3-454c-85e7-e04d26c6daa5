
-- create node stat table
create table node_stat (
  id bigserial primary key,
  job_id bigint references job(id),
  node_id bigint not null references node(id),
  stat_level varchar(16) not null,
  year int not null,
  week int not null,
  data_type varchar(30) not null,
  reviewed_session_count bigint not null default 0,
  approved_session_count bigint not null default 0,
  rejected_session_count bigint not null default 0,
  job_count bigint not null default 0,
  approved_job_count bigint not null default 0,
  status varchar(30) default 'pending',
  deleted boolean not null default false,
  created_at timestamp not null default now(),
  updated_at timestamp not null default now()
)
