ALTER TABLE reward_token_info ADD COLUMN IF NOT EXISTS token_decimal smallint NOT NULL DEFAULT 18;
ALTER TABLE batch_reward_record ADD COLUMN IF NOT EXISTS token_decimal smallint NOT NULL DEFAULT 18;

ALTER TABLE user_token_task_rewards ADD COLUMN IF NOT EXISTS status smallint NOT NULL DEFAULT 0;

CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_stt_idx ON user_token_task_rewards (user_id, status);

ALTER TABLE batch_reward_record ALTER COLUMN amount TYPE numeric(38, 0);
ALTER TABLE user_token_task_rewards ALTER COLUMN amount TYPE numeric(38, 0);
ALTER TABLE user_token_rewards ALTER COLUMN amount TYPE numeric(38, 0);
ALTER TABLE user_token_reward_claims ALTER COLUMN amount TYPE numeric(38, 0);

ALTER TABLE reward_token_info RENAME COLUMN onwer_id TO owner_id;
