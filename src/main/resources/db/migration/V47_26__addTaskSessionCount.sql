CREATE TABLE IF NOT EXISTS task_session_dis_count
(
    id                          bigserial PRIMARY KEY,
    job_id                      bigint NOT NULL,
    task_id                     bigint NOT NULL,
    dis_count                   int NOT NULL default 0,

    deleted                     boolean NOT NULL default false,
    created_at                  timestamptz not null default now(),
    updated_at                  timestamptz not null default now()
);

CREATE INDEX IF NOT EXISTS task_session_dis_count_job_idx ON task_session_dis_count (job_id) where deleted = false;
CREATE INDEX IF NOT EXISTS task_session_dis_count_task_idx ON task_session_dis_count (task_id) where deleted = false;
