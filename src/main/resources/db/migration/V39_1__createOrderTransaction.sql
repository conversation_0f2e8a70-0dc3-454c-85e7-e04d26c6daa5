CREATE TABLE IF NOT EXISTS order_transaction
(
    id bigserial primary key,
    project_id bigint NOT NULL,
    user_id bigint NOT NULL,
    original_amount numeric(20,2) NOT NULL,
    amount numeric(20,2) NOT NULL,
    new_amount numeric(20,2) NOT NULL,
    order_type varchar(64) NOT NULL,
    order_status varchar(64) NOT NULL,
    pay_type varchar(64) NULL,
    flow_no bigint NOT NULL,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);

CREATE INDEX IF NOT EXISTS order_transaction_idx_flow_no
ON order_transaction USING btree
(flow_no ASC NULLS LAST);