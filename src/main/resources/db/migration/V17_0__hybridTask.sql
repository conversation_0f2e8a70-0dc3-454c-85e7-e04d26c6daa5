
create table hybrid_task_resource (
    id bigserial primary key,
    task_id bigint not null,
    resource_id bigint not null,
    sort int not null default 0,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now(),
    constraint fk_hybrid_sub_task_id foreign key (task_id) references task (id),
    constraint fk_hybrid_sub_res_id foreign key (resource_id) references resource (id)
);