
create table review_session (
    id bigserial primary key,
    task_session_id bigint not null references task_session(id),
    user_id bigint not null references users(id),
    job_user_id bigint not null references job_user(id),
    job_id bigint not null references job(id), -- 冗余字段，可以从关联的job_user拿取
    task_id bigint not null, -- 冗余字段，可以从关联的job_task拿取
    status varchar(20) not null default 'pending',
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);
-- create an index on status flag
create index review_session_status_idx on review_session(status);

create table spot_session (
                              id bigserial primary key,
                              task_session_id bigint not null references task_session(id),
                              user_id bigint not null references users(id),
                              job_user_id bigint not null references job_user(id),
                              job_id bigint not null references job(id), -- 冗余字段，可以从关联的job_user拿取
                              task_id bigint not null references task(id), -- 冗余字段，可以从关联的job_task拿取
                              status varchar(20) not null default 'pending',
                              deleted boolean not null default false,
                              created_at timestamptz not null default now(),
                              updated_at timestamptz not null default now()
);
-- create an index on status flag
create index spot_session_status_idx on spot_session(status);

-- add a status flag to task_session
alter table task_session add column status varchar(20) not null default 'pending';
-- and add a index to status flag
create index task_session_status_idx on task_session(status);

