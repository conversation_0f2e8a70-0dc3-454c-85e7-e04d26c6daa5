CREATE TABLE IF NOT EXISTS reward_token_info
(
  id                BIGSERIAL       PRIMARY KEY,
  token_name        varchar(255)    NOT NULL,
  reward_token_type smallint        NOT NULL DEFAULT 0,
  resource_id       BIGINT          DEFAULT NULL,
  icon_url          varchar(255)    NOT NULL DEFAULT '',
  onwer_id          BIGINT          NOT NULL DEFAULT 0,
  tge_already       BOOLEAN         NOT NULL DEFAULT false,
  deleted           BOOLEAN         NOT NULL DEFAULT false,
  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);

INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already) VALUES (1, 'SAHARA', 0, true) ON CONFLICT (id) DO NOTHING;
INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already) VALUES (2, 'USD1', 1, true) ON CONFLICT (id) DO NOTHING;
INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already) VALUES (3, 'TEST_TK', 9, false) ON CONFLICT (id) DO NOTHING;

-- CREATE INDEX IF NOT EXISTS reward_token_info_token_type_idx ON reward_token_info (reward_token_type);
-- CREATE INDEX IF NOT EXISTS reward_token_info_token_name_idx ON reward_token_info (token_name);
-- CREATE INDEX IF NOT EXISTS reward_token_info_resource_id_idx ON reward_token_info (resource_id);

CREATE TABLE IF NOT EXISTS batch_reward_record
(
  id                BIGSERIAL       PRIMARY KEY,
  batch_id          BIGINT          NOT NULL,
  token_info_id     BIGINT          NOT NULL,
  reward_token_type smallint        NOT NULL DEFAULT 0,
  amount            numeric(20, 6)  NOT NULL,
  deleted           BOOLEAN         NOT NULL DEFAULT false,
  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
  );

CREATE INDEX IF NOT EXISTS batch_reward_record_batch_id_idx ON batch_reward_record (batch_id);
-- CREATE INDEX IF NOT EXISTS batch_reward_record_token_info_id_idx ON batch_reward_record (token_info_id);

CREATE TABLE IF NOT EXISTS user_token_task_rewards
(
  id                    BIGSERIAL       PRIMARY KEY,
  user_id               BIGINT          NOT NULL,
  job_id                BIGINT          NOT NULL,
  amount                numeric(20, 6)  NOT NULL,
  reward_token_type     smallint        NOT NULL DEFAULT 0,
  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
  deleted               BOOLEAN         NOT NULL DEFAULT false,
  created_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
  updated_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_idx ON user_token_task_rewards (user_id);
-- CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_status_idx ON user_token_task_rewards (user_id, status);

CREATE TABLE IF NOT EXISTS user_token_rewards
(
  id                BIGSERIAL       PRIMARY KEY,
  user_id           BIGINT          NOT NULL,
  amount            numeric(20, 6)  NOT NULL,
  reward_token_type smallint        NOT NULL DEFAULT 0,
  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
  deleted           BOOLEAN         NOT NULL DEFAULT false,
  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS user_token_rewards_uid_idx ON user_token_rewards (user_id);


CREATE TABLE IF NOT EXISTS user_token_reward_claims
(
  id                BIGSERIAL       PRIMARY KEY,
  user_id           BIGINT          NOT NULL,
  amount            numeric(20, 6)  NOT NULL,
  reward_token_type smallint        NOT NULL DEFAULT 0,
  status            smallint        NOT NULL DEFAULT 0,
  tx_hash           varchar(255)    NOT NULL DEFAULT '',
  deleted           BOOLEAN         NOT NULL DEFAULT false,
  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS user_token_reward_claims_uid_idx ON user_token_reward_claims (user_id);
