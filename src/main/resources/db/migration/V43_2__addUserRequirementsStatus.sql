
CREATE TABLE user_requirements_status (
    id bigserial primary key,
    batch_id bigint not null,
    requirement_target_id bigint not null,
    user_id bigint not null,
    type varchar(20) NOT NULL,
    status smallint not null default 0,
    deleted boolean not null default false,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

create index if not exists user_requirements_status_batch_id_idx on user_requirements_status (batch_id);
create index if not exists batch_access_requirement_batch_id_idx on batch_access_requirement (batch_id);
create index if not exists user_requirements_status_user_id_idx on user_requirements_status (user_id);
