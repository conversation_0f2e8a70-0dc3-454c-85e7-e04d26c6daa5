CREATE TABLE achievement
(
  id           BIGSERIAL PRIMARY KEY,
  name         <PERSON><PERSON><PERSON><PERSON>(255)  NOT NULL DEFAULT '',
  description  TEXT          NOT NULL DEFAULT '',
  category     VARCHAR(64)   NOT NULL DEFAULT '',
  level_type   VARCHAR(64)   NOT NULL DEFAULT '',
  standard     VARCHAR(64)   NOT NULL DEFAULT '',
  standard_uri VARCHAR(2048) NOT NULL DEFAULT '',
  network      VARCHAR(64)   NOT NULL DEFAULT '',
  feature      VARCHAR(64)   NOT NULL DEFAULT '',
  sort         INTEGER       NOT NULL DEFAULT 0,
  active       BOOLEAN       NOT NULL DEFAULT true,
  deleted      BOOLEAN       NOT NULL DEFAULT false,
  created_at   TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at   TIMESTAMPTZ   NOT NULL DEFAULT NOW()
);

CREATE TABLE achievement_level
(
  id             BIGSERIAL PRIMARY KEY,
  achievement_id BIGINT       NOT NULL REFERENCES achievement (id),
  level          INTEGER      NOT NULL DEFAULT 1,
  exp            BIGINT       NOT NULL DEFAULT 0,
  behavior_code  VARCHAR(255) NOT NULL DEFAULT '',
  requirement    VARCHAR(255) NOT NULL DEFAULT '',
  denominator    BIGINT       NOT NULL DEFAULT 1,
  logo           VARCHAR(255) NOT NULL DEFAULT '',
  deleted        BOOLEAN      NOT NULL DEFAULT false,
  created_at     TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
  updated_at     TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_achievement_level_behavior_code ON achievement_level (behavior_code);

CREATE TABLE achievement_requirement
(
  id                   BIGSERIAL PRIMARY KEY,
  achievement_id       BIGINT        NOT NULL REFERENCES achievement (id),
  achievement_level_id BIGINT        NOT NULL REFERENCES achievement_level (id),
  requirement          VARCHAR(255)  NOT NULL DEFAULT '',
  denominator          BIGINT        NOT NULL DEFAULT 1,
  behavior_code        VARCHAR(255)  NOT NULL DEFAULT '',
  name                 VARCHAR(255)  NOT NULL DEFAULT '',
  description          TEXT          NOT NULL DEFAULT '',
  logo                 VARCHAR(255)  NOT NULL DEFAULT '',
  action_text          VARCHAR(64)   NOT NULL DEFAULT '',
  action_passed_text   VARCHAR(64)   NOT NULL DEFAULT '',
  action_uri           VARCHAR(2048) NOT NULL DEFAULT '',
  sort                 INTEGER       NOT NULL DEFAULT 0,
  deleted              BOOLEAN       NOT NULL DEFAULT false,
  created_at           TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at           TIMESTAMPTZ   NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_achievement_requirement_behavior_code ON achievement_requirement (behavior_code);

CREATE TABLE user_achievement
(
  id             BIGSERIAL PRIMARY KEY,
  user_id        BIGINT      NOT NULL REFERENCES users (id),
  achievement_id BIGINT      NOT NULL REFERENCES achievement (id),
  level          INTEGER     NOT NULL DEFAULT 1,
  passed         BOOLEAN     NOT NULL DEFAULT false,
  unclaimed_exp  BIGINT      NOT NULL DEFAULT 0,
  passed_at      TIMESTAMPTZ,
  deleted        BOOLEAN     NOT NULL DEFAULT false,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at     TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE user_achievement_progress
(
  id                         BIGSERIAL PRIMARY KEY,
  user_id                    BIGINT      NOT NULL REFERENCES users (id),
  achievement_id             BIGINT      NOT NULL REFERENCES achievement (id),
  achievement_level_id       BIGINT,
  achievement_requirement_id BIGINT,
  progress                   BIGINT      NOT NULL DEFAULT 0,
  passed                     BOOLEAN     NOT NULL DEFAULT false,
  passed_at                  TIMESTAMPTZ,
  deleted                    BOOLEAN     NOT NULL DEFAULT false,
  created_at                 TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at                 TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE user_achievement_record
(
  id                         BIGSERIAL PRIMARY KEY,
  user_id                    BIGINT       NOT NULL REFERENCES users (id),
  achievement_id             BIGINT       NOT NULL REFERENCES achievement (id),
  achievement_level_id       BIGINT,
  achievement_requirement_id BIGINT,
  behavior_hash              VARCHAR(255) NOT NULL DEFAULT '',
  completed_at               TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
  previous_exp               BIGINT       NOT NULL DEFAULT 0,
  increased_exp              BIGINT       NOT NULL DEFAULT 0,
  deleted                    BOOLEAN      NOT NULL DEFAULT false,
  created_at                 TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
  updated_at                 TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE TABLE sahara_level
(
  id         BIGSERIAL PRIMARY KEY,
  level      INTEGER       NOT NULL,
  title      VARCHAR(64)   NOT NULL DEFAULT '',
  target_exp BIGINT        NOT NULL,
  strategy   VARCHAR(2048) NOT NULL DEFAULT '',
  deleted    BOOLEAN       NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ   NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ   NOT NULL DEFAULT NOW()
);

CREATE TABLE user_sahara_level
(
  id         BIGSERIAL PRIMARY KEY,
  user_id    BIGINT      NOT NULL REFERENCES users (id),
  type       VARCHAR(64) NOT NULL DEFAULT '',
  exp        BIGINT      NOT NULL DEFAULT 0,
  level      INTEGER     NOT NULL DEFAULT 1,
  deleted    BOOLEAN     NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_user_sahara_level_exp ON user_sahara_level (exp);
CREATE INDEX idx_user_sahara_level_level ON user_sahara_level (level);
