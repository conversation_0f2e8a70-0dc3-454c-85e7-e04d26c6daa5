ALTER TABLE users
  ADD COLUMN sybil_flag B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT false;

ALTER TABLE batch_setting
  ADD COLUMN workload_max_sybil_job_user_percent INT;
ALTER TABLE batch_setting
  ADD COLUMN workload_max_sybil_job_reviewer_percent INT;

CREATE TABLE sybil_user_counts (
  id BIGINT PRIMARY KEY,
  user_id BIGINT NOT NULL,
  take_count BIGINT NOT NULL,
  job_id BIGINT NOT NULL,
  role VARCHAR(20) NOT NULL,
  created_at timestamptz NOT NULL DEFAULT NOW(),
  updated_at timestamptz NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_sybil_user_counts_user_id ON sybil_user_counts(job_id, user_id);