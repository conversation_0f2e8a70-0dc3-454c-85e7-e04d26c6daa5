create table question_group (
  id bigserial primary key,
  task_id bigint references task(id),
  name varchar(64) not null,
  page_size int not null default 0, -- default 0 no pagination
  sort int not null default 0,
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

alter table task_question add column group_id bigint not null default 0;
