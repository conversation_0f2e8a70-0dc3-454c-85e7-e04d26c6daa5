-- Start transaction to ensure all changes are applied atomically
BEGIN;

-- Step 1: Add new columns
ALTER TABLE batch_setting
  ADD COLUMN IF NOT EXISTS honeypot_type                     varchar(20) DEFAULT 'domain'    NOT NULL,
  ADD COLUMN IF NOT EXISTS honeypot_question_location        smallint    DEFAULT 0           NOT NULL,
  ADD COLUMN IF NOT EXISTS honeypot_review_question_location smallint    DEFAULT 0           NOT NULL,
  ADD COLUMN IF NOT EXISTS hp_target_user_type               varchar(20) DEFAULT 'all'       NOT NULL,
  ADD COLUMN IF NOT EXISTS honey_pot_review_batches          jsonb       DEFAULT '[]'::jsonb NOT NULL,
  ADD COLUMN IF NOT EXISTS honey_pot_batches                 jsonb       DEFAULT '[]'::jsonb NOT NULL;

-- Step 2: Populate new jsonb columns
DO
$$
  DECLARE
    error_message text;
  BEGIN
    UPDATE batch_setting
    SET honey_pot_review_batches = (SELECT jsonb_agg(jsonb_build_object('batchId', hp_batch_id::int))
                                    FROM unnest(string_to_array(honey_pot_batch_id_review, ',')) AS hp_batch_id)
    WHERE trim(honey_pot_batch_id_review) <> '';

    UPDATE batch_setting
    SET honey_pot_batches = (SELECT jsonb_agg(jsonb_build_object('batchId', hp_batch_id::int))
                             FROM unnest(string_to_array(honey_pot_batch_id, ',')) AS hp_batch_id)
    WHERE trim(honey_pot_batch_id) <> '';
  EXCEPTION
    WHEN undefined_column THEN
      GET STACKED DIAGNOSTICS error_message = MESSAGE_TEXT;
      RAISE NOTICE 'Error: %', error_message;
  END
$$;

-- Step 3: Remove obsolete text columns
ALTER TABLE batch_setting
  DROP COLUMN IF EXISTS honey_pot_batch_id_review,
  DROP COLUMN IF EXISTS honey_pot_batch_id;

-- Commit transaction to finalize all changes
COMMIT;
