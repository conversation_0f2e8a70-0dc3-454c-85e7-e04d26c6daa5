-- Create table sync_cursor_from_chain
CREATE TABLE IF NOT EXISTS sync_cursor_from_chain
(
    id              BIGSERIAL       PRIMARY KEY,
    season_id       BIGINT          NOT NULL,
    block           BIGINT          NOT NULL,
    tx_hash         VARCHAR(255)    NOT NULL DEFAULT '',
    type            int8            NOT NULL,
    count_in_block  int8            NOT NULL DEFAULT 0,
    deleted         BOOLEAN         NOT NULL DEFAULT false,
    created_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    updated_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS sync_cursor_from_chain_blk_idx ON sync_cursor_from_chain (block, type);
