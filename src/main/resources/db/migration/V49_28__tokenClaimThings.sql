-- Add withdrawal-related fields to user_token_reward_claims table
ALTER TABLE user_token_reward_claims ADD COLUMN IF NOT EXISTS wallet_address VARCHAR(255) NOT NULL DEFAULT '';
ALTER TABLE user_token_reward_claims ADD COLUMN IF NOT EXISTS freeze_end_time TIMESTAMPTZ NULL;
ALTER TABLE user_token_reward_claims ADD COLUMN IF NOT EXISTS requires_reconciliation BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE user_token_reward_claims ADD COLUMN IF NOT EXISTS reconciliation_passed BOOLEAN NULL;
ALTER TABLE user_token_reward_claims ADD COLUMN IF NOT EXISTS reconciliation_reason TEXT NULL;

-- Create additional indexes for withdrawal functionality
CREATE INDEX IF NOT EXISTS user_token_reward_claims_freeze_end_time_idx ON user_token_reward_claims (freeze_end_time);
CREATE INDEX IF NOT EXISTS user_token_reward_claims_uid_status_idx ON user_token_reward_claims (user_id, status);
ALTER TABLE reward_token_info ADD COLUMN IF NOT EXISTS contract_address VARCHAR(255);

ALTER TABLE user_token_task_rewards ADD COLUMN IF NOT EXISTS merkle_proof JSONB;

UPDATE reward_token_info set contract_address = '0x8d0D000Ee44948FC98c9B98A4FA4921476f08B0d' where token_name = 'USD1' and reward_token_type = 1;
UPDATE reward_token_info set contract_address = '0xfdffb411c4a70aa7c95d5c981a6fb4da867e1111' where token_name = 'SAHARA' and reward_token_type = 0;

ALTER TABLE reward_token_info RENAME COLUMN contract_address TO contract_address_bsc;

ALTER TABLE batch_reward_record ADD COLUMN IF NOT EXISTS pool_status smallint default 0;
