CREATE TABLE IF NOT EXISTS order_base
(
    id bigserial primary key,
    flow_no bigint NOT NULL,
    total_amount numeric(20,2) NOT NULL,
    user_id bigint NOT NULL,
    order_type varchar(64) NOT NULL,
    order_status varchar(64) NOT NULL,
    pay_amount numeric(20,2) NOT NULL,
    pay_type varchar(64) NULL,
    pay_time timestamp with time zone,
    expiration_time timestamp with time zone,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS order_base_uni_idx_flow_no
ON order_base USING btree
(flow_no ASC NULLS LAST);

CREATE TABLE IF NOT EXISTS project_payment
(
    id bigserial primary key,
    project_id bigint NOT NULL,
    amount numeric(20,2) NOT NULL,
    paid_amount numeric(20,2) NOT NULL,
    paying_amount numeric(20,2) NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS project_payment_uni_idx_project_id
ON project_payment USING btree
(project_id ASC NULLS LAST);

CREATE TABLE IF NOT EXISTS project_payment_order
(
    id bigserial primary key,
    flow_no bigint NOT NULL,
    project_id bigint NOT NULL,
    project_payment_id bigint NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS project_payment_order_idx_project_id
ON project_payment_order USING btree
(project_id ASC NULLS LAST) ;

CREATE INDEX IF NOT EXISTS project_payment_order_idx_project_payment_id
ON project_payment_order USING btree
(project_payment_id ASC NULLS LAST) ;

CREATE UNIQUE INDEX IF NOT EXISTS project_payment_order_uni_idx_flow_no
ON project_payment_order USING btree
(flow_no ASC NULLS LAST);

CREATE TABLE IF NOT EXISTS node_withdrawal
(
  id bigserial primary key,
  node_id bigint NOT NULL,
  project_id bigint NOT NULL,
  amount numeric(20,2) NOT NULL,
  paid_amount numeric(20,2) NOT NULL,
  paying_amount numeric(20,2) NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS node_withdrawal_uni_idx_project_id_node_id
ON node_withdrawal USING btree
(project_id ASC NULLS LAST, node_id ASC NULLS LAST);

CREATE TABLE IF NOT EXISTS node_withdrawal_order
(
  id bigserial primary key,
  flow_no bigint NOT NULL,
  node_id bigint NOT NULL,
  project_id bigint NOT NULL,
  node_withdrawal_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS node_withdrawal_order_idx_node_id
ON node_withdrawal_order USING btree
(node_id ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS node_withdrawal_order_idx_node_withdrawal_id
ON node_withdrawal_order USING btree
(node_withdrawal_id ASC NULLS LAST);

CREATE UNIQUE INDEX IF NOT EXISTS node_withdrawal_order_uni_idx_flow_no
ON node_withdrawal_order USING btree
(flow_no ASC NULLS LAST);

CREATE TABLE IF NOT EXISTS order_event
(
    id serial primary key,
    event_index bigint NOT NULL,
    event_type varchar(64) NOT NULL,
    flow_no bigint NOT NULL,
    amount numeric(36,0) NOT NULL,
    address char(42) NOT NULL,
    status varchar(64) NOT NULL,
    failed_reason varchar(255) NULL,
    transaction_hash char(66) NOT NULL,
    pay_time timestamp with time zone NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS order_event_uni_idx_flow_no
ON order_event USING btree
(flow_no ASC NULLS LAST);

CREATE UNIQUE INDEX IF NOT EXISTS order_event_uni_idx_event_index
ON order_event USING btree
(event_index ASC NULLS LAST);

CREATE TABLE IF NOT EXISTS scanner_status
(
  id serial NOT NULL,
  name varchar(64) NOT NULL,
  block_height bigint,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS scanner_status_uni_idx_name
ON scanner_status USING btree
(name ASC NULLS LAST);