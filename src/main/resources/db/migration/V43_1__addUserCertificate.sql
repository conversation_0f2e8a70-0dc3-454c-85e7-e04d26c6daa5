CREATE TABLE user_certificate (
  id bigserial primary key,
  certificate_id bigint not null,
  user_id bigint not null,
  name varchar(255) not null,
  category varchar(255) not null,
  certificate_type varchar(100) not null,
  description text not null default '',
  pic varchar(255) not null default '',
  exp bigint,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  -- available on parent certificate --
  lv2exp jsonb,
  lv int,
  -- available on child certificate --
  knowledge_list jsonb,
  difficulty bigint,
  parent_user_certificate_id bigint references user_certificate(id),

  constraint fk_user_certificate_user_id foreign key (user_id)
  references users (id)
  on delete cascade,

  constraint fk_user_certificate_certificate_id foreign key (certificate_id)
  references certificate (id)
  on delete cascade
);

CREATE INDEX idx_user_certificate_certificate_id ON user_certificate (certificate_id);
CREATE INDEX idx_user_certificate_user_id ON user_certificate (user_id);
CREATE INDEX idx_user_certificate_category ON user_certificate (category);
CREATE INDEX idx_user_certificate_certificate_type ON user_certificate (certificate_type);
CREATE INDEX idx_user_certificate_knowledge_list ON user_certificate USING gin (knowledge_list jsonb_path_ops);
CREATE INDEX idx_user_certificate_difficulty ON user_certificate (difficulty);
CREATE INDEX idx_user_certificate_parent_user_certificate_id ON user_certificate (parent_user_certificate_id);