-- create nda sign records
create table nda_sign_record(
                              id bigserial primary key,
                              nda_id bigint not null references batch_nda(id),
                              batch_id bigint not null references batch(id),
                              user_id bigint not null references users(id),
                              sign_type varchar(20) not null,
                              job_id bigint references job(id),
                              job_invitation_id bigint references job_invitation(id),
                              job_user_id bigint references job_user(id),
                              deleted boolean not null default false,
                              signed_at timestamptz not null default current_timestamp,
                              created_at timestamptz not null default current_timestamp,
                              updated_at timestamptz not null default current_timestamp
);

create index nda_sign_record_nda_sign_type_idx on nda_sign_record(sign_type);

