CREATE TABLE IF NOT EXISTS task_session_review_count
(
    id                          bigserial PRIMARY KEY,
    job_id                      bigint NOT NULL,
    task_session_id             bigint NOT NULL,
    review_count                int NOT NULL default 0,

    deleted                     boolean NOT NULL default false,
    created_at                  timestamptz not null default now(),
    updated_at                  timestamptz not null default now()
);

CREATE INDEX IF NOT EXISTS task_session_review_count_job_idx ON task_session_review_count (job_id);
CREATE INDEX IF NOT EXISTS task_session_review_count_ts_idx ON task_session_review_count (task_session_id);
