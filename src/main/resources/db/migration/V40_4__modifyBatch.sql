alter table batch
  add task_type varchar(20) default 'task' not null;

alter table batch
  add user_type varchar(20) default 'external-labeler';

alter table batch
  add difficulty numeric(20, 6);

alter table batch
  add special_requirement varchar(500);

alter table batch
  add gender varchar(20);

alter table batch
  add accent varchar(20);

alter table batch
  add age integer;

alter table batch
  add final_qa_percentage integer;

alter table batch
  add annotating_min_datapoint integer;

alter table batch
  add annotating_min_datapoint_second integer;

alter table batch
  add reviewing_min_datapoint_second integer;

alter table batch
  add submit_required integer;


alter table batch
  alter column summary drop not null;

alter table batch
  alter column description drop not null;

alter table batch
  alter column extra_info drop not null;

alter table batch
  alter column label_type drop not null;

alter table batch
  alter column label_body drop not null;

alter table batch
  alter column deadline drop not null;

alter table batch
  alter column required_accuracy drop not null;

alter table batch
  alter column required_regions drop not null;

alter table batch
  alter column required_languages drop not null;

alter table batch
  alter column required_skills drop not null;

alter table batch
  alter column labeling_repeat drop not null;

alter table batch
  alter column review_repeat drop not null;

alter table batch
  alter column spot_audit drop not null;



CREATE TABLE batch_access_requirement
(
  id bigserial primary key,
  batch_id bigint not null,
  relation_id bigint not null,
  type varchar(20) NOT NULL,
  deleted boolean not null default false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);


