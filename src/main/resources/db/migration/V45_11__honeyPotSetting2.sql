
CREATE TABLE batch_setting (
    id                              BIGSERIAL       PRIMARY KEY,
    batch_id                        bigint          NOT NULL,
    honey_pot_batch_id              varchar(255)    NOT NULL default '',
    honey_pot_number_per_submit     integer,
    honey_pot_weight                integer,
    honey_pot_accuracy_required     integer,
    deleted                         B<PERSON><PERSON><PERSON>N         NOT NULL DEFAULT false,
    created_at                      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    updated_at                      TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_batch_setting_batch_id ON batch_setting (batch_id);
