-- Create table sync_cursor_from_chain
CREATE TABLE IF NOT EXISTS user_ban_lite
(
    id              BIGSERIAL       PRIMARY KEY,
    user_id         BIGINT          NOT NULL,
    deleted         BOOLEAN         NOT NULL DEFAULT false,
    created_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    updated_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW()
    );
CREATE INDEX IF NOT EXISTS user_ban_lite_uid_idx ON user_ban_lite (user_id) where deleted = false;
