CREATE TABLE job_user_points
(
    id bigserial primary key,
    job_id bigint not null,
    user_id bigint not null,
    price bigint not null default 0,
    session_count bigint not null default 0,
    reward_type varchar(64) NOT NULL default 'annotate',
    status varchar(64) NOT NULL default 'pending',
    deleted boolean not null default false,
    end_count_at timestamp with time zone NOT NULL DEFAULT now(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);
CREATE INDEX idx_job_user_points_job_id ON job_user_points (job_id);
CREATE INDEX idx_job_user_points_user_id ON job_user_points (user_id);