UPDATE season_user_points_detail SET points = CAST(points_legacy AS numeric(20, 6));


-- revert script:
-- ALTER TABLE season_user_points_detail DROP COLUMN points;
-- ALTER TABLE season_user_points_detail RENAME COLUMN points_legacy TO points;



-- <PERSON><PERSON> revert script:
-- ALTER TABLE season_user_points_detail DROP COLUMN points;
-- ALTER TABLE season_user_points_detail RENAME COLUMN points_legacy TO points;
-- ALTER TABLE job_user_points DROP COLUMN price;
-- ALTER TABLE job_user_points RENAME COLUMN price_legacy TO price;
-- ALTER TABLE season_user DROP COLUMN total_points;
-- ALTER TABLE season_user RENAME COLUMN total_points_legacy TO total_points;
-- ALTER TABLE season_user DROP COLUMN season_points;
-- ALTER TABLE season_user RENAME COLUMN season_points_legacy TO season_points;
-- ALTER TABLE batch DROP COLUMN reviewing_price;
-- ALTER TABLE batch RENAME COLUMN reviewing_price_legacy TO reviewing_price;
-- <PERSON><PERSON>R TABLE batch DROP COLUMN annotating_price;
-- <PERSON>TER TABLE batch RENAME COLUMN annotating_price_legacy TO annotating_price;
