

-- create answers table for example and exams
create table question_answer (
                              id bigserial primary key,
                              question_id bigint references task_question(id),
                              task_id bigint references task(id),
                              answer text not null,
                              explanation text not null,
                              owner_id bigint references users(id),
                              deleted boolean default false,
                              created_at timestamptz default now(),
                              updated_at timestamptz default now()
);
