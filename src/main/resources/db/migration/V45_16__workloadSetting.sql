ALTER TABLE batch_setting ADD workload_max_per_job_user INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD workload_max_per_job_user_day INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD workload_max_per_job_user_hour INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD workload_max_per_job_reviewer INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD workload_max_per_job_reviewer_day INTEGER DEFAULT 0;
ALTER TABLE batch_setting ADD workload_max_per_job_reviewer_hour INTEGER DEFAULT 0;

ALTER TABLE batch DROP COLUMN workload_max_per_job_user;
ALTER TABLE batch DROP COLUMN workload_max_per_job_user_day;
ALTER TABLE batch DROP COLUMN workload_max_per_job_user_hour;
ALTER TABLE batch DROP COLUMN workload_max_per_job_reviewer;
ALTER TABLE batch DROP COLUMN workload_max_per_job_reviewer_day;
ALTER TABLE batch DROP COLUMN workload_max_per_job_reviewer_hour;