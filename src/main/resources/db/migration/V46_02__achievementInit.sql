-- -----------------------------------------------------------------------------------------------

INSERT INTO sahara_level(id, level, title, target_exp, strategy)
VALUES (1, 1, '(l) -> "Lv. " + l', 0, '(l) -> l * (l + 1) * 5')
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------

INSERT INTO achievement(id, name, symbol, description, category, level_type, standard, standard_uri, network, feature,
                        sort)
VALUES (1, 'Rising Star', 'RISING_STAR', 'Rising Star Description', 'My Progress', 'disposable', 'ERC 721', '',
        'Sahara Testnet', 'Non-Transferable', 1),
       (2, 'Referral Influencer', 'REFERRAL_INFLUENCER', 'Referral Influencer Description', 'My Progress', 'upgradable',
        'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 2),
       (3, 'Data Contributor', 'DATA_CONTRIBUTOR', 'Data Contributor Description', 'My Progress', 'upgradable',
        'ERC 721',
        '', 'Sahara Testnet', 'Non-Transferable', 3),
       (4, 'Accuracy Expert', 'ACCURACY_EXPERT', 'Accuracy Expert Description', 'My Progress', 'upgradable', 'ERC 721',
        '',
        'Sahara Testnet', 'Non-Transferable', 4),
       (5, 'Task Master', 'TASK_MASTER', 'Task Master Description', 'My Progress', 'upgradable', 'ERC 721', '',
        'Sahara Testnet', 'Non-Transferable', 5),
       (6, 'Dedication Expert', 'DEDICATION_EXPERT', 'Dedication Expert Description', 'My Progress', 'upgradable',
        'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 6),
       (7, 'Pioneer of Progress', 'PIONEER_OF_PROGRESS', 'Pioneer of Progress Description', 'My Progress', 'disposable',
        'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 7),
       (8, 'Guardian of Growth', 'GUARDIAN_OF_GROWTH', 'Guardian of Growth Description', 'My Progress', 'disposable',
        'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 8)
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------

INSERT INTO achievement_level(id, achievement_id, level, exp, behavior_code, requirement, denominator, logo)
VALUES
  -- Rising Star
  (1, 1, 1, 30, '', 'In total 2 requirements', 2, ''),
  -- Referral Influencer
  (2, 2, 1, 10, '', 'Total number of successful referrals reaches 2', 2, ''),
  (3, 2, 2, 50, '', 'Total number of successful referrals reaches 10', 10, ''),
  (4, 2, 3, 250, '', 'Total number of successful referrals reaches 50', 50, ''),
  (5, 2, 4, 350, '', 'Total number of successful referrals reaches 100', 100, ''),
  (6, 2, 5, 500, '', 'Total number of successful referrals reaches 150', 150, ''),
  -- Data Contributor
  (7, 3, 1, 10, '', 'Achieve a total of 50 approved data points from all tasks', 50, ''),
  (8, 3, 2, 20, '', 'Achieve a total of 100 approved data points from all tasks', 100, ''),
  (9, 3, 3, 60, '', 'Achieve a total of 250 approved data points from all tasks', 250, ''),
  (10, 3, 4, 120, '', 'Achieve a total of 500 approved data points from all tasks', 500, ''),
  (11, 3, 5, 150, '', 'Achieve a total of 750 approved data points from all tasks', 750, ''),
  (12, 3, 6, 180, '', 'Achieve a total of 1000 approved data points from all tasks', 1000, ''),
  (13, 3, 7, 220, '', 'Achieve a total of 1250 approved data points from all tasks', 1250, ''),
  -- Accuracy Expert
  (14, 4, 1, 30, '', 'Achieve a total of 5 accuracy bonuses', 5, ''),
  (15, 4, 2, 40, '', 'Achieve a total of 10 accuracy bonuses', 10, ''),
  (16, 4, 3, 100, '', 'Achieve a total of 20 accuracy bonuses', 20, ''),
  (17, 4, 4, 110, '', 'Achieve a total of 30 accuracy bonuses', 30, ''),
  (18, 4, 5, 130, '', 'Achieve a total of 40 accuracy bonuses', 40, ''),
  (19, 4, 6, 260, '', 'Achieve a total of 60 accuracy bonuses', 60, ''),
  (20, 4, 7, 260, '', 'Achieve a total of 80 accuracy bonuses', 80, ''),
  -- Task Master
  (21, 5, 1, 150, '', 'Earn at least 500 SP (Sahara Points) in a minimum of 5 different tasks', 5, ''),
  (22, 5, 2, 150, '', 'Earn at least 500 SP (Sahara Points) in a minimum of 10 different tasks', 10, ''),
  (23, 5, 3, 350, '', 'Earn at least 500 SP (Sahara Points) in a minimum of 20 different tasks', 20, ''),
  (24, 5, 4, 400, '', 'Earn at least 500 SP (Sahara Points) in a minimum of 30 different tasks', 30, ''),
  -- Dedication Expert
  (25, 6, 1, 300, '', 'Earn at least 1600 SP (Sahara Points) in a minimum of 2 different weeks', 2, ''),
  (26, 6, 2, 300, '', 'Earn at least 1600 SP (Sahara Points) in a minimum of 4 different weeks', 4, ''),
  (27, 6, 3, 400, '', 'Earn at least 1600 SP (Sahara Points) in a minimum of 6 different weeks', 6, ''),
  (28, 6, 4, 500, '', 'Earn at least 1600 SP (Sahara Points) in a minimum of 8 different weeks', 8, ''),
  -- Pioneer of Progress
  (29, 7, 1, 150, '', 'In total 1 requirement', 1, ''),
  -- Guardian of Growth
  (30, 8, 1, 450, '', 'In total 1 requirement', 1, '')
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------

INSERT INTO achievement_requirement(id, achievement_id, achievement_level_id, requirement, denominator, behavior_code,
                                    name,
                                    description, logo, action_text, action_passed_text, action_uri, sort)
VALUES
  -- Rising Star - 300 sp
  (1, 1, 1, 'Achieve a total of 300 sahara points', 300, 'SAHARA_POINTS', 'sahara points', '', '', '', '', '', 1),
  -- Rising Star - verify email
  (2, 1, 1, 'Verify your email', 1, 'VERIFY_EMAIL', 'verify email', '', '', 'verify', 'verified', 'VERIFY_EMAIL', 2),
  -- Pioneer of Progress
  (3, 7, 29, 'Complete all required questionnaires by Sahara Labs during the season pilot', 1,
   'SAHARA_LABS_QUESTIONNAIRES',
   'sahara labs questionnaires', '', '', '', '', 'SAHARA_LABS_QUESTIONNAIRES', 1),
  -- Guardian of Growth
  (4, 8, 30, 'Complete all required tasks by Sahara Labs during the season pilot', 1, 'SAHARA_LABS_TASKS',
   'sahara labs tasks', '',
   '', '', '', 'SAHARA_LABS_TASKS', 1)
ON CONFLICT (id) DO NOTHING;
-- -----------------------------------------------------------------------------------------------

UPDATE achievement_level
SET requirement = 'In total 1 requirement',
    denominator = 1
WHERE achievement_id = 1
  AND level = 1;

UPDATE achievement_requirement
SET deleted = true
WHERE behavior_code = 'VERIFY_EMAIL';
-- -----------------------------------------------------------------------------------------------
