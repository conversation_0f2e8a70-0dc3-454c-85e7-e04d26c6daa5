CREATE TABLE task_question_extra (
  id BIGSERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  batch_id BIGINT NOT NULL,
  task_id BIGINT NOT NULL,
  question VA<PERSON>HAR(255) NOT NULL,
  answer VARCHAR(255) NOT NULL,
  question_type VARCHAR(255) NOT NULL,
  details TEXT DEFAULT '',
  used_for SMALLINT NOT NULL DEFAULT 0,
  deleted boolean NOT NULL DEFAULT false,
  created_at timestamptz NOT NULL DEFAULT NOW(),
  updated_at timestamptz NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_task_question_extra_batch_id_task_id_used_for ON task_question_extra(batch_id, task_id, used_for) WHERE deleted = false;