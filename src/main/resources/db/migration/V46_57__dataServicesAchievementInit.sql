UPDATE achievement
SET active= false
WHERE id < 9;

UPDATE achievement
SET deleted= true
WHERE id BETWEEN 9 AND 11;
-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement(id, name, symbol, description, category, level_type, standard, standard_uri, network, feature,
                        sort, start_at, end_at, claim_start_at, claim_end_at, mint_start_at, mint_end_at, active)
VALUES (12, 'Titan’s Vigil', 'TITANS_VIGIL', 'Checked in for 31 days based on UTC time during the Data Services: Early Access Program. (We have 31 days in total in Data Services)
This achievement symbolizes the vigilance of Titans who never falter in their duties, guarding the flame of progress with steadfast loyalty. ',
        'AI Data Services Achievements', 'daily-trickle', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 12,
        '2024-12-18 00:00:00', '2025-01-17 23:59:59.999', '2024-12-18 00:00:00', '2025-01-24 23:59:59.999',
        '2025-01-17 00:00:00', '2025-01-31 23:59:59.999', true),
       (13, 'Forge of Perseverance', 'FORGE_OF_PERSEVERANCE', 'Earn at least 15 Sahara Points daily for 31 days based on UTC time during the Data Services (Early Access Program) (We have 31 days in total in Data Services)
This achievement honors those who labor tirelessly in the forge of innovation, shaping the tools that will light the path to discovery.',
        'AI Data Services Achievements',
        'daily-trickle', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 13, '2024-12-18 00:00:00',
        '2025-01-17 23:59:59.999', '2024-12-18 00:00:00', '2025-01-24 23:59:59.999', '2025-01-17 00:00:00',
        '2025-01-31 23:59:59.999', true),
       (14, 'Oracle of Knowledge', 'ORACLE_OF_KNOWLEDGE',
        'Complete tasks to earn at least 100 Sahara Points in one of the following domains: Prompt Collection, Model Optimization, Persona Emulation. ',
        'AI Data Services Achievements',
        'disposable-active-final', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 14, '2024-12-18 00:00:00',
        '2025-01-17 23:59:59.999', '2024-12-18 00:00:00', '2025-01-24 23:59:59.999', '2024-12-18 00:00:00',
        '2025-01-31 23:59:59.999', true)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_level(id, achievement_id, level, exp, behavior_code, requirement, denominator, logo, note,
                              deleted)
VALUES (37, 12, 1, 0, '',
        'Checked in for 31 days based on UTC time during Data Services Season. (We have 31 days total in Data Services Season)',
        31, 'res:1216750',
        'You will be eligible to claim the NFT which represents the final degree realized in this achievement at the end of Data Services Season 1.',
        false),
       (38, 13, 1, 0, '',
        'Earn at least 20 Sahara Points for 31 days based on UTC time during Data Services Season 1. (We have 31 days total in Data Services Season 1.)',
        31, 'res:1216751',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of Data Services Season 1.',
        false),
       (39, 14, 1, 0, '',
        'Once you earn at least 100 Sahara Points in any one of the three categories: Prompt Collection, Model Optimization, Persona Emulation, you can claim 30 EXP',
        3, 'res:1216752',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of the Data Services Season 1.',
        false),
       (40, 14, 2, 0, '',
        'Once you earn at least 100 Sahara Points in any one of the three categories: Prompt Collection, Model Optimization, Persona Emulation, you can claim 30 EXP',
        3, 'res:1216752',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of the Data Services Season 1.',
        true),
       (41, 14, 3, 0, '',
        'Once you earn at least 100 Sahara Points in any one of the three categories: Prompt Collection, Model Optimization, Persona Emulation, you can claim 30 EXP',
        3, 'res:1216752',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of the Data Services Season 1.',
        true)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_requirement(id, achievement_id, achievement_level_id, requirement, denominator, exp,
                                    behavior_code, name, description, logo, action_text, action_passed_text, action_uri,
                                    sort)
VALUES
  -- Titan’s Vigil
  (45, 12, null, '1', 1, 1, 'TITANS_VIGIL:1', '1', '', '', '', '', '', 1),
  (46, 12, null, '2', 1, 1, 'TITANS_VIGIL:2', '2', '', '', '', '', '', 2),
  (47, 12, null, '3', 1, 1, 'TITANS_VIGIL:3', '3', '', '', '', '', '', 3),
  (48, 12, null, '4', 1, 1, 'TITANS_VIGIL:4', '4', '', '', '', '', '', 4),
  (49, 12, null, '5', 1, 1, 'TITANS_VIGIL:5', '5', '', '', '', '', '', 5),
  (50, 12, null, '6', 1, 1, 'TITANS_VIGIL:6', '6', '', '', '', '', '', 6),
  (51, 12, null, '7', 1, 1, 'TITANS_VIGIL:7', '7', '', '', '', '', '', 7),
  (52, 12, null, '8', 1, 1, 'TITANS_VIGIL:8', '8', '', '', '', '', '', 8),
  (53, 12, null, '9', 1, 1, 'TITANS_VIGIL:9', '9', '', '', '', '', '', 9),
  (54, 12, null, '10', 1, 1, 'TITANS_VIGIL:10', '10', '', '', '', '', '', 10),
  (55, 12, null, '11', 1, 1, 'TITANS_VIGIL:11', '11', '', '', '', '', '', 11),
  (56, 12, null, '12', 1, 1, 'TITANS_VIGIL:12', '12', '', '', '', '', '', 12),
  (57, 12, null, '13', 1, 1, 'TITANS_VIGIL:13', '13', '', '', '', '', '', 13),
  (58, 12, null, '14', 1, 1, 'TITANS_VIGIL:14', '14', '', '', '', '', '', 14),
  (59, 12, null, '15', 1, 1, 'TITANS_VIGIL:15', '15', '', '', '', '', '', 15),
  (60, 12, null, '16', 1, 1, 'TITANS_VIGIL:16', '16', '', '', '', '', '', 16),
  (61, 12, null, '17', 1, 1, 'TITANS_VIGIL:17', '17', '', '', '', '', '', 17),
  (62, 12, null, '18', 1, 1, 'TITANS_VIGIL:18', '18', '', '', '', '', '', 18),
  (63, 12, null, '19', 1, 1, 'TITANS_VIGIL:19', '19', '', '', '', '', '', 19),
  (64, 12, null, '20', 1, 1, 'TITANS_VIGIL:20', '20', '', '', '', '', '', 20),
  (65, 12, null, '21', 1, 1, 'TITANS_VIGIL:21', '21', '', '', '', '', '', 21),
  (66, 12, null, '22', 1, 1, 'TITANS_VIGIL:22', '22', '', '', '', '', '', 22),
  (67, 12, null, '23', 1, 1, 'TITANS_VIGIL:23', '23', '', '', '', '', '', 23),
  (68, 12, null, '24', 1, 1, 'TITANS_VIGIL:24', '24', '', '', '', '', '', 24),
  (69, 12, null, '25', 1, 1, 'TITANS_VIGIL:25', '25', '', '', '', '', '', 25),
  (70, 12, null, '26', 1, 1, 'TITANS_VIGIL:26', '26', '', '', '', '', '', 26),
  (71, 12, null, '27', 1, 1, 'TITANS_VIGIL:27', '27', '', '', '', '', '', 27),
  (72, 12, null, '28', 1, 1, 'TITANS_VIGIL:28', '28', '', '', '', '', '', 28),
  (73, 12, null, '29', 1, 1, 'TITANS_VIGIL:29', '29', '', '', '', '', '', 29),
  (74, 12, null, '30', 1, 1, 'TITANS_VIGIL:30', '30', '', '', '', '', '', 30),
  (75, 12, null, '31', 1, 1, 'TITANS_VIGIL:31', '31', '', '', '', '', '', 31),
  -- Forge of Perseverance
  (76, 13, null, '1', 1, 5, 'FORGE_OF_PERSEVERANCE:1', '1', '', '', '', '', '', 1),
  (77, 13, null, '2', 1, 5, 'FORGE_OF_PERSEVERANCE:2', '2', '', '', '', '', '', 2),
  (78, 13, null, '3', 1, 5, 'FORGE_OF_PERSEVERANCE:3', '3', '', '', '', '', '', 3),
  (79, 13, null, '4', 1, 5, 'FORGE_OF_PERSEVERANCE:4', '4', '', '', '', '', '', 4),
  (80, 13, null, '5', 1, 5, 'FORGE_OF_PERSEVERANCE:5', '5', '', '', '', '', '', 5),
  (81, 13, null, '6', 1, 5, 'FORGE_OF_PERSEVERANCE:6', '6', '', '', '', '', '', 6),
  (82, 13, null, '7', 1, 5, 'FORGE_OF_PERSEVERANCE:7', '7', '', '', '', '', '', 7),
  (83, 13, null, '8', 1, 5, 'FORGE_OF_PERSEVERANCE:8', '8', '', '', '', '', '', 8),
  (84, 13, null, '9', 1, 5, 'FORGE_OF_PERSEVERANCE:9', '9', '', '', '', '', '', 9),
  (85, 13, null, '10', 1, 5, 'FORGE_OF_PERSEVERANCE:10', '10', '', '', '', '', '', 10),
  (86, 13, null, '11', 1, 5, 'FORGE_OF_PERSEVERANCE:11', '11', '', '', '', '', '', 11),
  (87, 13, null, '12', 1, 5, 'FORGE_OF_PERSEVERANCE:12', '12', '', '', '', '', '', 12),
  (88, 13, null, '13', 1, 5, 'FORGE_OF_PERSEVERANCE:13', '13', '', '', '', '', '', 13),
  (89, 13, null, '14', 1, 5, 'FORGE_OF_PERSEVERANCE:14', '14', '', '', '', '', '', 14),
  (90, 13, null, '15', 1, 5, 'FORGE_OF_PERSEVERANCE:15', '15', '', '', '', '', '', 15),
  (91, 13, null, '16', 1, 5, 'FORGE_OF_PERSEVERANCE:16', '16', '', '', '', '', '', 16),
  (92, 13, null, '17', 1, 5, 'FORGE_OF_PERSEVERANCE:17', '17', '', '', '', '', '', 17),
  (93, 13, null, '18', 1, 5, 'FORGE_OF_PERSEVERANCE:18', '18', '', '', '', '', '', 18),
  (94, 13, null, '19', 1, 5, 'FORGE_OF_PERSEVERANCE:19', '19', '', '', '', '', '', 19),
  (95, 13, null, '20', 1, 5, 'FORGE_OF_PERSEVERANCE:20', '20', '', '', '', '', '', 20),
  (96, 13, null, '21', 1, 5, 'FORGE_OF_PERSEVERANCE:21', '21', '', '', '', '', '', 21),
  (97, 13, null, '22', 1, 5, 'FORGE_OF_PERSEVERANCE:22', '22', '', '', '', '', '', 22),
  (98, 13, null, '23', 1, 5, 'FORGE_OF_PERSEVERANCE:23', '23', '', '', '', '', '', 23),
  (99, 13, null, '24', 1, 5, 'FORGE_OF_PERSEVERANCE:24', '24', '', '', '', '', '', 24),
  (100, 13, null, '25', 1, 5, 'FORGE_OF_PERSEVERANCE:25', '25', '', '', '', '', '', 25),
  (101, 13, null, '26', 1, 5, 'FORGE_OF_PERSEVERANCE:26', '26', '', '', '', '', '', 26),
  (102, 13, null, '27', 1, 5, 'FORGE_OF_PERSEVERANCE:27', '27', '', '', '', '', '', 27),
  (103, 13, null, '28', 1, 5, 'FORGE_OF_PERSEVERANCE:28', '28', '', '', '', '', '', 28),
  (104, 13, null, '29', 1, 5, 'FORGE_OF_PERSEVERANCE:29', '29', '', '', '', '', '', 29),
  (105, 13, null, '30', 1, 5, 'FORGE_OF_PERSEVERANCE:30', '30', '', '', '', '', '', 30),
  (106, 13, null, '31', 1, 5, 'FORGE_OF_PERSEVERANCE:31', '31', '', '', '', '', '', 31),
  -- Oracle of Knowledge
  (107, 14, null, 'Prompt Collection', 100, 30, 'ORACLE_OF_KNOWLEDGE:PROMPT_COLLECTION', 'Prompt Collection', '', '',
   '',
   '', '', 1),
  (108, 14, null, 'Model Optimization', 100, 30, 'ORACLE_OF_KNOWLEDGE:MODEL_OPTIMIZATION', 'Model Optimization', '', '',
   '', '', '', 2),
  (109, 14, null, 'Persona Emulation', 100, 30, 'ORACLE_OF_KNOWLEDGE:PERSONA_EMULATION',
   'Persona Emulation', '', '', '',
   '', '', 3)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------
