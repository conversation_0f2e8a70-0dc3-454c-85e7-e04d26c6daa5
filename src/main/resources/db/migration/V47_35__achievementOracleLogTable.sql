-- Create table achievement_oracle_log
CREATE TABLE IF NOT EXISTS achievement_oracle_log
(
  id             BIGSERIAL PRIMARY KEY,
  key            VARCHAR(64) NOT NULL,
  achievement_id BIGINT      NOT NULL, -- new achievement on contract achievementId
  user_id        BIGINT      NOT NULL,
  task_id        VARCHAR(64) NOT NULL DEFAULT '',
  timestamp      BIGINT      NULL,
  progress       BIGINT      NOT NULL,
  deleted        BOOLEAN     NOT NULL DEFAULT false,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at     TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX IF NOT EXISTS achievement_oracle_log_uni_idx_key
  ON achievement_oracle_log USING btree
    (key ASC NULLS LAST);

-- add column sort for achievement_on_contract
ALTER TABLE achievement_on_contract
  ADD COLUMN sort INTEGER NOT NULL DEFAULT 0;

-- add column daily for user_daily_check_in
ALTER TABLE user_daily_check_in
  ADD COLUMN IF NOT EXISTS daily_map JSONB NOT NULL DEFAULT '{}';
