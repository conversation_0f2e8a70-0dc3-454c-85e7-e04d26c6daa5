
-- create user job stat table
create table user_job_stat (
  id bigserial primary key,
  user_id bigint not null references users(id),
  job_id bigint references job(id),
  node_id bigint references node(id), -- node id that user belongs to when job is created
  stat_level varchar(16) not null, -- job/week/year
  year int not null,
  week int not null,
  data_type varchar(30) not null,
  time_used_answer bigint not null default 0,
  answer_count bigint not null default 0,
  accepted_count bigint not null default 0,
  revised_count bigint not null default 0,
  time_used_review bigint not null default 0,
  review_count bigint not null default 0,
  review_accepted_count bigint not null default 0,
  review_revised_count bigint not null default 0,
  time_used_spot bigint not null default 0,
  spot_count bigint not null default 0,
  spot_accepted_count bigint not null default 0,
  spot_revised_count bigint not null default 0,
  job_count bigint not null default 0,
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

create index idx_user_job_stat_stat_level on user_job_stat(stat_level);
create index idx_user_job_stat_data_type on user_job_stat(data_type);
create index idx_user_job_stat_year on user_job_stat(year);

-- create job status table
create table job_stat_status (
  id bigserial primary key,
  job_id bigint not null references job(id),
  status varchar(16) not null, -- pending,processing,completed
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

create index idx_job_stat_status on job_stat_status(job_id);
