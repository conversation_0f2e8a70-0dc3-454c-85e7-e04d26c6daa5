
ALTER TABLE batch RENAME COLUMN reviewing_price TO reviewing_price_legacy;
ALTER TABLE batch ADD COLUMN IF NOT EXISTS reviewing_price numeric(20, 6);
UPDATE batch SET reviewing_price = CASE
  WHEN reviewing_price_legacy is not null THEN CAST(reviewing_price_legacy AS numeric(20, 6))
  ELSE reviewing_price
END;

ALTER TABLE batch RENAME COLUMN annotating_price TO annotating_price_legacy;
ALTER TABLE batch ADD COLUMN IF NOT EXISTS annotating_price numeric(20, 6);
UPDATE batch SET annotating_price = CASE
  WHEN annotating_price_legacy is not null THEN CAST(annotating_price_legacy AS numeric(20, 6))
  ELSE annotating_price
END;



-- revert script:
-- ALTER TABLE batch DROP COLUMN reviewing_price;
-- ALTER TABLE batch RENAME COLUMN reviewing_price_legacy TO reviewing_price;
--
-- ALTER TABLE batch DROP COLUMN annotating_price;
-- ALTER TABLE batch RENAME COLUMN annotating_price_legacy TO annotating_price;
