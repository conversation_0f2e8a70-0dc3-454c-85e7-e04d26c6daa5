CREATE TABLE IF NOT EXISTS job_user_points_pre_calc
(
    id                          bigserial PRIMARY KEY,
    job_id                      bigint NOT NULL,
    user_id                     bigint NOT NULL,
    job_user_id                 bigint NOT NULL,
    session_id                  bigint NOT NULL,

    job_user_role               varchar(64) not null,
    not_settled                 boolean NOT NULL default true,

    deleted                     boolean NOT NULL default false,
    final_judge_at              timestamptz not null default now(),
    created_at                  timestamptz not null default now(),
    updated_at                  timestamptz not null default now()
);

CREATE INDEX IF NOT EXISTS job_user_points_pre_calc_job_user_id_idx
    ON job_user_points_pre_calc (job_user_id) where not_settled;
CREATE INDEX IF NOT EXISTS job_user_points_pre_calc_final_judge_at_idx
    ON job_user_points_pre_calc (final_judge_at) where not_settled;
CREATE INDEX IF NOT EXISTS job_user_points_pre_calc_job_id_idx
    ON job_user_points_pre_calc (job_id) where not_settled;

