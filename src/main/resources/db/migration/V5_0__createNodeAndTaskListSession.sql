
-- fix batch example's created_at and updated_at columns
-- to use timestamptz
ALTER TABLE batch_example
    ALTER COLUMN created_at TYPE TIMESTAMPTZ USING created_at::TIMESTAMPTZ,
    ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING updated_at::TIMESTAMPTZ;

-- create node table
CREATE TABLE node (
    id bigserial PRIMARY KEY,
    name varchar(255) not null,
    region varchar(255) not null,
    country varchar(255) not null,
    languages text not null,
    node_manager_id bigint not null REFERENCES users(id),
    active boolean not null DEFAULT true,
    deleted boolean not null DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- create the task list sessions table
CREATE TABLE task_list_session (
    id bigserial PRIMARY KEY,
    task_list_id bigint not null REFERENCES task_list(id),
    owner_id bigint not null REFERENCES users(id),
    node_id bigint REFERENCES node(id),
    status varchar(20) not null,
    submitted_at TIMESTAMPTZ not null,
    accepted_at TIMESTAMPTZ not null,
    rejected_by bigint REFERENCES users(id),
    rejected_at TIMESTAMPTZ not null,
    deleted boolean not null DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- create the task list session users table
create table task_list_session_user (
    id bigserial PRIMARY KEY,
    task_list_session_id bigint not null REFERENCES task_list_session(id),
    user_id bigint not null REFERENCES users(id),
    role varchar(20) not null,
    active boolean not null DEFAULT true,
    deleted boolean not null DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
