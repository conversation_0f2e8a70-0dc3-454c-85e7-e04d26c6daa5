CREATE INDEX IF NOT EXISTS user_achievement_progress_all_ids_idx
  ON user_achievement_progress (user_id, achievement_id, achievement_level_id, achievement_requirement_id);

DROP INDEX IF EXISTS user_achievement_progress_user_id_achievement_id_idx;

CREATE INDEX IF NOT EXISTS user_achievement_record_all_ids_idx
  ON user_achievement_record (user_id, achievement_id, achievement_level_id, achievement_requirement_id);

CREATE INDEX IF NOT EXISTS user_achievement_record_user_id_claimed_at_increased_exp
  ON user_achievement_record (user_id, claimed_at, increased_exp);

CREATE INDEX IF NOT EXISTS user_sahara_level_user_id_idx
  ON user_sahara_level (user_id);
