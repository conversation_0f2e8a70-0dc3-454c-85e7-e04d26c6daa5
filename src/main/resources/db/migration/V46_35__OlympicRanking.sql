-- Step 1: Create table season_achievement
CREATE TABLE IF NOT EXISTS season_achievement (
    id bigserial NOT NULL,
    season_id int8 NOT NULL,
    achievement_id int8 NOT NULL,
    deleted bool DEFAULT false NOT NULL,
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL,
    CONSTRAINT season_achievement_pkey PRIMARY KEY (id),
    CONSTRAINT fk_season FOREIGN KEY (season_id) REFERENCES season (id) ON DELETE CASCADE,
    CONSTRAINT fk_achievement FOREIGN KEY (achievement_id) REFERENCES achievement (id) ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS season_achievement_season_id_idx ON season_achievement USING btree (season_id);
CREATE INDEX IF NOT EXISTS season_achievement_achievement_id_idx ON season_achievement USING btree (achievement_id);

-- Step 2: Create table season_user_exp_detail
CREATE TABLE IF NOT EXISTS season_user_exp_detail (
    id bigserial NOT NULL,
    season_id int8 NOT NULL,
    user_id int8 NOT NULL,
    achievement_id int8 NOT NULL,
    exp int8 DEFAULT 0 NOT NULL,
    detail json NOT NULL,
    deleted bool DEFAULT false NOT NULL,
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL,
    CONSTRAINT season_user_exp_detail_pkey PRIMARY KEY (id),
    CONSTRAINT fk_season_user_exp_detail_season_id FOREIGN KEY (season_id) REFERENCES season(id) ON DELETE CASCADE,
    CONSTRAINT fk_season_user_exp_detail_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_season_user_exp_detail_achievement_id FOREIGN KEY (achievement_id) REFERENCES achievement (id) ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS season_user_exp_detail_season_id_idx ON season_user_exp_detail USING btree (season_id);
CREATE INDEX IF NOT EXISTS season_user_exp_detail_user_id_idx ON season_user_exp_detail USING btree (user_id);