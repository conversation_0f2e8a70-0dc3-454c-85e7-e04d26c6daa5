CREATE TABLE sybil_job_take_counts (
  id BIGSERIAL PRIMARY KEY,
  take_count BIGINT NOT NULL,
  job_id BIGINT NOT NULL,
  role VARCHAR(20) NOT NULL,
  created_at timestamptz NOT NULL DEFAULT NOW(),
  updated_at timestamptz NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_sybil_job_take_counts_job_id_role ON sybil_job_take_counts(job_id, role);
ALTER TABLE sybil_job_take_counts ADD CONSTRAINT user_job_role_unique UNIQUE (job_id, role);

-- migrate from sybil_user_counts
INSERT INTO sybil_job_take_counts (job_id, role, take_count)
SELECT job_id, role, SUM(take_count)
FROM sybil_user_counts
GROUP BY job_id, role;
