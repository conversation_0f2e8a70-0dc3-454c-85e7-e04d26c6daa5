ALTER TABLE achievement_on_contract
  ADD COLUMN IF NOT EXISTS progress_type VARCHAR(20) DEFAULT 'bar' NOT NULL,
  ADD COLUMN IF NOT EXISTS start_time    TIMESTAMPTZ               NULL,
  ADD COLUMN IF NOT EXISTS end_time      TIMESTAMPTZ               NULL;

ALTER TABLE achievement_oracle_log
  ADD COLUMN IF NOT EXISTS batch_uuid VARCHAR(32) DEFAULT '' NOT NULL;

-- Create table achievement_oracle_batch
CREATE TABLE IF NOT EXISTS achievement_oracle_batch
(
  id            BIGSERIAL PRIMARY KEY,
  uuid          VARCHAR(32) DEFAULT ''        NOT NULL,
  progress_type VARCHAR(20) DEFAULT 'bar'     NOT NULL,
  body          TEXT        DEFAULT ''        NOT NULL,
  status        VARCHAR(20) DEFAULT 'pending' NOT NULL,
  deleted       BOOLEAN                       NOT NULL DEFAULT FALSE,
  created_at    TIMESTAMPTZ                   NOT NULL DEFAULT NOW(),
  updated_at    TIMESTAMPTZ                   NOT NULL DEFAULT NOW()
);
