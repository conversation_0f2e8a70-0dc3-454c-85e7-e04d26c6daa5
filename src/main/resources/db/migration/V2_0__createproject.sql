
-- create a user table in pgsql
create table project (
    id bigserial primary key,
    name varchar(255) not null,
    description text not null default '',
    status int4 not null default 0,
    requester_id bigint not null,
    account_manager_id bigint,
    topic text not null default '',
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now(),
    deleted boolean not null default false,
    constraint fk_project_requester_id foreign key (requester_id) references users (id),
    constraint fk_project_account_manager_id foreign key (account_manager_id) references users (id)
);
