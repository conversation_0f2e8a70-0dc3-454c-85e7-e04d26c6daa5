UPDATE certificate SET deleted = true WHERE certificate_type is null;

INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('course', 'course', 'Saharaa Version', '', '', '2024-03-28 16:28:48.686939 +00:00', '2024-03-28 16:28:48.686939 +00:00', null, null, null, null, 0, false);
INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('course', 'course', 'AI / Web knowledge', '', '', '2024-03-28 16:28:48.686939 +00:00', '2024-03-28 16:28:48.686939 +00:00', null, null, null, null, 0, false);
INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('vetting-test', 'vetting-test', 'Language', '', '', '2024-03-28 16:30:28.752192 +00:00', '2024-03-28 16:30:28.752192 +00:00', null, null, null, null, 0, false);
INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('vetting-test', 'vetting-test', 'Math', '', '', '2024-03-28 16:30:28.752192 +00:00', '2024-03-28 16:30:28.752192 +00:00', null, null, null, null, 0, false);
INSERT INTO certificate (name, category, certificate_type, description, pic, created_at, updated_at, lv2exp, knowledge_list, difficulty, parent_certificate_id, exp, deleted) VALUES ('vetting-test', 'vetting-test', 'Reasoning', '', '', '2024-03-28 16:30:28.752192 +00:00', '2024-03-28 16:30:28.752192 +00:00', null, null, null, null, 0, false);