<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.season.SeasonUserMapper">
  <select id="getUserRank">
    WITH RankedUsers AS (
    SELECT
    user_id,
    season_id,
    total_points,
    RANK() OVER (ORDER BY total_points DESC) AS `rank`
    FROM season_user
    )
    SELECT *
    FROM RankedUsers
    WHERE user_id = #{userId}
    AND deleted = false
    AND season_id = #{seasonId};
  </select>
  <select id="getUserRankList">
    SELECT
    user_id,
    season_id,
    total_points,
    RANK() OVER (ORDER BY total_points DESC) AS `rank`
    FROM season_user
    WHERE deleted = false and season_id = #{seasonId}
    LIMIT #{size} OFFSET #{offset};
  </select>
  <select id="getUserExpRankList">
    SELECT
      user_id,
      season_id,
      total_exp,
      RANK() OVER (ORDER BY total_exp DESC) AS rank
    FROM season_user
    WHERE deleted = false
      AND season_id = #{seasonId}
      AND total_exp > 0;
  </select>
  <select id="getUserPointsAndExpSeasonList" resultType="ai.saharaa.model.season.UserPointsSeason">
    select season.id as id,
    season.name as name,
    season.started_at as started_at,
    season.ended_at as ended_at,
    season.rule_version as rule_version,
    su.user_id as user_id,
    su.total_points as user_points,
    su.total_exp as user_exp,
    su.claimed as claimed
    from season_user su
    join season on su.season_id = season.id
    where su.user_id = ${userId}
    and season.deleted = false
    and su.deleted = false
    order by season.started_at desc;
  </select>
</mapper>
