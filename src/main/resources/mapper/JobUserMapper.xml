<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.JobUserMapper">
  <select id="getNonStatJobUserForJob" resultType="ai.saharaa.model.JobUser">
    select ju.id, ju.task_list_session_id, ju.user_id, ju.role, ju.active,
           ju.disabled, ju.disabled_by, ju.disabled_reason, ju.deleted,  ju.created_at, ju.updated_at
    from job_user ju
    where ju.task_list_session_id = #{jobId} and ju.deleted = false and ju.role not in ('Invitation', 'ndaSigning')
      and not exists(
      select 1 from user_job_stat ujs where ujs.user_id = ju.user_id and ujs.deleted = false
      and ujs.status = 'completed'
      and ujs.stat_level = 'job' and ujs.job_id = #{jobId}
    ) order by ju.id
    limit #{limit};
  </select>
</mapper>