<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.stat.NodeStatMapper">
  <select id="getWeeklyStatForNode" resultType="ai.saharaa.model.stat.NodeStat">
    select 0 as id, 0 as job_id, #{nodeId} as node_id,
            'week' as stat_level,
            #{year} as year, #{week} as week,
            ns.data_type as data_type,
            sum(ns.reviewed_session_count) as reviewed_session_count,
            sum(ns.approved_session_count) as approved_session_count,
            sum(ns.rejected_session_count) as rejected_session_count,
            sum(ns.job_count) as job_count,
            sum(ns.approved_job_count) as approved_job_count,
            array_to_string(array_agg(distinct ns.data_type), ',') as data_types,
            array_to_string(array_agg(distinct ns.topics), ',') as topics,
            'completed':: varchar as status,
            0::boolean as deleted,
            '1999-01-01'::timestamptz as created_at,
            '1999-01-01'::timestamptz as updated_at
    from node_stat ns
    where ns.node_id = #{nodeId} and ns.deleted = false
      and ns.year = #{year} and ns.week = #{week} and
      ns.stat_level = 'job'
    group by ns.data_type;
  </select>
  <select id="getYearlyStatForNode" resultType="ai.saharaa.model.stat.NodeStat">
    select 0 as id, 0 as job_id, #{nodeId} as node_id,
           'year' as stat_level,
           #{year} as year, 0 as week,
           ns.data_type as data_type,
           sum(ns.reviewed_session_count) as reviewed_session_count,
           sum(ns.approved_session_count) as approved_session_count,
           sum(ns.rejected_session_count) as rejected_session_count,
           sum(ns.job_count) as job_count,
           sum(ns.approved_job_count) as approved_job_count,
           array_to_string(array_agg(distinct ns.data_types), ',') as data_types,
           array_to_string(array_agg(distinct ns.topics), ',') as topics,
           'completed':: varchar as status,
           0::boolean as deleted,
           '1999-01-01'::timestamptz as created_at,
           '1999-01-01'::timestamptz as updated_at
    from node_stat ns
    where ns.node_id = #{nodeId} and ns.deleted = false
      and ns.year = #{year}
      and ns.stat_level = 'week'
    group by ns.data_type;
  </select>
  <select id="getStatByNode" resultType="ai.saharaa.model.stat.NodeStat">
    select 0 as id, 0 as job_id, #{nodeId} as node_id,
           'year' as stat_level,
           0 as year, 0 as week,
           'image' as data_type,
           sum(ns.reviewed_session_count) as reviewed_session_count,
           sum(ns.approved_session_count) as approved_session_count,
           sum(ns.rejected_session_count) as rejected_session_count,
           sum(ns.job_count) as job_count,
           sum(ns.approved_job_count) as approved_job_count,
           array_to_string(array_agg(distinct ns.data_types), ',') as data_types,
           array_to_string(array_agg(distinct ns.topics), ',') as topics,
           'completed':: varchar as status,
           0::boolean as deleted,
           '1999-01-01'::timestamptz as created_at,
           '1999-01-01'::timestamptz as updated_at
    from node_stat ns
    where ns.node_id = #{nodeId} and ns.deleted = false
      and ns.stat_level = 'year'
  </select>
</mapper>