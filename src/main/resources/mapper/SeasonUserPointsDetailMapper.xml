<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.season.SeasonUserPointsDetailMapper">
  <select id="sumPointsByProjectKnowledge">
    SELECT COALESCE(SUM(supd.points), 0)
    FROM season_user_points_detail supd
           INNER JOIN job j ON j.id = supd.job_id
    WHERE j.deleted = FALSE
      AND supd.deleted = FALSE
      AND supd.user_id = #{userId}
      AND j.batch_id IN (
        SELECT b.id
        FROM batch b
              INNER JOIN project p ON p.id = b.project_id
        WHERE p.deleted = FALSE
        AND b.deleted = FALSE
        AND LOWER(#{knowledge}) = ANY (STRING_TO_ARRAY(LOWER(p.knowledge), ','))
      )
    <if test="startTime != null">
      AND supd.generation_time &gt;= #{startTime}
    </if>
    <if test="endTime != null">
      AND supd.generation_time &lt;= #{endTime}
    </if>
  </select>

  <select id="sumPointsByJobIds">
    SELECT COALESCE(SUM(supd.points), 0)
    FROM season_user_points_detail supd
    WHERE supd.user_id = #{userId}
      AND supd.job_id IN
      <foreach collection="jobIds" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    <if test="startTime != null">
      AND supd.generation_time &gt;= #{startTime}
    </if>
    <if test="endTime != null">
      AND supd.generation_time &lt;= #{endTime}
    </if>
    AND supd.deleted = FALSE
  </select>
</mapper>
