<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.stat.UserJobStatMapper">
  <select id="getWeeklyStatForUser" resultType="ai.saharaa.model.stat.UserJobStat">
    select  0 as id, #{uid} as user_id, 0 as job_id,
           0 as node_id,
           'week' as stat_level,
           #{year} as year, #{week} as week,
           ujs.data_type as data_type,
           sum(ujs.time_used_answer) as time_used_answer,
           sum(ujs.answer_count) as answer_count,
           sum(ujs.accepted_count) as accepted_count,
           sum(ujs.revised_count) as revised_count,
           sum(ujs.review_count) as review_count,
           sum(ujs.time_used_review) as time_used_review,
           sum(ujs.review_accepted_count) as review_accepted_count,
           sum(ujs.review_revised_count) as review_revised_count,
           sum(ujs.time_used_spot) as time_used_spot,
           sum(ujs.spot_count) as spot_count,
           sum(ujs.spot_accepted_count) as spot_accepted_count,
           sum(ujs.spot_revised_count) as spot_revised_count,
           sum(ujs.job_count) as job_count,
           sum(ujs.job_accepted_count) as job_accepted_count,
            sum(ujs.time_used_exam) as time_used_exam,
            sum(ujs.exam_count) as exam_count,
            sum(ujs.exam_accepted_count) as exam_accepted_count,
            sum(ujs.exam_rejected_count) as exam_rejected_count,
            sum(ujs.feedback_count) as feedback_count,
            'completed':: varchar as status,
            array_to_string(array_agg(ujs.topics), ',') as topics,
            0::boolean as deleted,
            '1999-01-01'::timestamptz as created_at,
            '1999-01-01'::timestamptz as updated_at
    from user_job_stat ujs
    where ujs.user_id = #{uid} and ujs.deleted = false
      and ujs.year = #{year} and ujs.week = #{week} and
      ujs.stat_level = 'job'
    group by ujs.data_type;
  </select>
  <select id="getYearlyStatForUser" resultType="ai.saharaa.model.stat.UserJobStat">
    select 0 as id, #{uid} as user_id, 0 as job_id,
           0 as node_id, 'year' as stat_level, #{year} as year, 0 as week,
           ujs.data_type as data_type,
           sum(ujs.time_used_answer) as time_used_answer,
           sum(ujs.answer_count) as answer_count,
           sum(ujs.accepted_count) as accepted_count,
           sum(ujs.revised_count) as revised_count,
           sum(ujs.time_used_review) as time_used_review,
           sum(ujs.review_count) as review_count,
           sum(ujs.review_accepted_count) as review_accepted_count,
           sum(ujs.review_revised_count) as review_revised_count,
           sum(ujs.time_used_spot) as time_used_spot,
           sum(ujs.spot_count) as spot_count,
           sum(ujs.spot_accepted_count) as spot_accepted_count,
           sum(ujs.spot_revised_count) as spot_revised_count,
           sum(ujs.job_count) as job_count,
           sum(ujs.job_accepted_count) as job_accepted_count,
           sum(ujs.time_used_exam) as time_used_exam,
           sum(ujs.exam_count) as exam_count,
           sum(ujs.exam_accepted_count) as exam_accepted_count,
           sum(ujs.exam_rejected_count) as exam_rejected_count,
           sum(ujs.feedback_count) as feedback_count,
           'completed'::varchar as status,
           array_to_string(array_agg(ujs.topics), ',') as topics,
           false as deleted,
            now() as created_at,
            now() as updated_at
    from user_job_stat ujs
    where ujs.user_id = #{uid} and ujs.deleted = false
      and ujs.year = #{year} and ujs.stat_level = 'week'
    group by ujs.data_type;
  </select>
  <select id="getStatByUser" resultType="ai.saharaa.model.stat.UserJobStat">
    select 0 as id, #{uid} as user_id, 0 as job_id,
           0 as node_id, 'year' as stat_level, 0 as year, 0 as week,
           'image' as data_type,
           sum(ujs.time_used_answer) as time_used_answer,
           sum(ujs.answer_count) as answer_count,
           sum(ujs.accepted_count) as accepted_count,
           sum(ujs.revised_count) as revised_count,
           sum(ujs.time_used_review) as time_used_review,
           sum(ujs.review_count) as review_count,
           sum(ujs.review_accepted_count) as review_accepted_count,
           sum(ujs.review_revised_count) as review_revised_count,
           sum(ujs.time_used_spot) as time_used_spot,
           sum(ujs.spot_count) as spot_count,
           sum(ujs.spot_accepted_count) as spot_accepted_count,
           sum(ujs.spot_revised_count) as spot_revised_count,
           sum(ujs.job_count) as job_count,
           sum(ujs.job_accepted_count) as job_accepted_count,
           sum(ujs.time_used_exam) as time_used_exam,
           sum(ujs.exam_count) as exam_count,
           sum(ujs.exam_accepted_count) as exam_accepted_count,
           sum(ujs.exam_rejected_count) as exam_rejected_count,
           sum(ujs.feedback_count) as feedback_count,
           'completed'::varchar as status,
           array_to_string(array_agg(ujs.topics), ',') as topics,
           false as deleted,
           now() as created_at,
           now() as updated_at
    from user_job_stat ujs
    where ujs.user_id = #{uid} and ujs.deleted = false
      and ujs.stat_level = 'year'
  </select>
  <select id="getWeeklyStatsByUser" resultType="ai.saharaa.model.stat.UserJobStat">
    select 0 as id, #{uid} as user_id, 0 as job_id,
           0 as node_id, 'year' as stat_level, 0 as year, 0 as week,
           'image' as data_type,
           sum(ujs.time_used_answer) as time_used_answer,
           sum(ujs.answer_count) as answer_count,
           sum(ujs.accepted_count) as accepted_count,
           sum(ujs.revised_count) as revised_count,
           sum(ujs.time_used_review) as time_used_review,
           sum(ujs.review_count) as review_count,
           sum(ujs.review_accepted_count) as review_accepted_count,
           sum(ujs.review_revised_count) as review_revised_count,
           sum(ujs.time_used_spot) as time_used_spot,
           sum(ujs.spot_count) as spot_count,
           sum(ujs.spot_accepted_count) as spot_accepted_count,
           sum(ujs.spot_revised_count) as spot_revised_count,
           sum(ujs.job_count) as job_count,
           sum(ujs.job_accepted_count) as job_accepted_count,
           sum(ujs.time_used_exam) as time_used_exam,
           sum(ujs.exam_count) as exam_count,
           sum(ujs.exam_accepted_count) as exam_accepted_count,
           sum(ujs.exam_rejected_count) as exam_rejected_count,
           sum(ujs.feedback_count) as feedback_count,
           'completed'::varchar as status,
           array_to_string(array_agg(ujs.topics), ',') as topics,
           false as deleted,
           now() as created_at,
           now() as updated_at
    from user_job_stat ujs
    where ujs.user_id = #{uid} and ujs.year = #{year} and ujs.week = #{week} and ujs.deleted = false
      and ujs.stat_level = 'week'
  </select>
  <select id="queryStatsByUsers" resultType="ai.saharaa.model.stat.UserJobStat">
    select 0 as id, ujs.user_id as user_id, 0 as job_id,
           0 as node_id, 'year' as stat_level, 0 as year, 0 as week,
           'image' as data_type,
           sum(ujs.time_used_answer) as time_used_answer,
           sum(ujs.answer_count) as answer_count,
           sum(ujs.accepted_count) as accepted_count,
           sum(ujs.revised_count) as revised_count,
           sum(ujs.time_used_review) as time_used_review,
           sum(ujs.review_count) as review_count,
           sum(ujs.review_accepted_count) as review_accepted_count,
           sum(ujs.review_revised_count) as review_revised_count,
           sum(ujs.time_used_spot) as time_used_spot,
           sum(ujs.spot_count) as spot_count,
           sum(ujs.spot_accepted_count) as spot_accepted_count,
           sum(ujs.spot_revised_count) as spot_revised_count,
           sum(ujs.job_count) as job_count,
           sum(ujs.job_accepted_count) as job_accepted_count,
           sum(ujs.time_used_exam) as time_used_exam,
           sum(ujs.exam_count) as exam_count,
           sum(ujs.exam_accepted_count) as exam_accepted_count,
           sum(ujs.exam_rejected_count) as exam_rejected_count,
           sum(ujs.feedback_count) as feedback_count,
           'completed'::varchar as status,
           array_to_string(array_agg(ujs.topics), ',') as topics,
           false as deleted,
           now() as created_at,
           now() as updated_at
    from user_job_stat ujs
    where
    <foreach item="item" index="index" collection="uids"
             open="ujs.user_id in (" separator="," close=")" nullable="true">
      #{item}
    </foreach>
    and ujs.deleted = false
      and ujs.stat_level = 'year'
    group by ujs.user_id;
  </select>
  <select id="queryWeeklyStatsByUsers" resultType="ai.saharaa.model.stat.UserJobStat">
    select ujs.*
    from user_job_stat ujs
    where
    <foreach item="item" index="index" collection="uids"
             open="ujs.user_id in (" separator="," close=")" nullable="true">
      #{item}
    </foreach>
      and ujs.year = #{year} and ujs.week = #{week} and ujs.deleted = false
      and ujs.stat_level = 'week'
  </select>
</mapper>