<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.JobMapper">
  <select id="getOnePendingStatJob" resultType="ai.saharaa.model.Job">
    select job.id, job.name, job.batch_id, job.task_list_id, job.owner_id, job.node_id,
           job.status, job.am_review_status, job.nm_review_status, job.assign_data_volume, job.time_spent_per_task,
           job.review_deadline, job.audit_deadline, job.invite_acceptance_deadline,
           job.submitted_at, job.accepted_at, job.rejected_by, job.rejected_at, job.deleted, job.created_at, job.updated_at
    from job
    where job.deleted = false and job.status in ('committed', 'finished', 'settling', 'dropped') and job.job_type = 'normal'
      and not exists(
      select 1 from job_stat_status jss where jss.job_id = job.id and jss.status = 'completed' and jss.deleted = false
    ) order by job.id limit 1;
  </select>
  <select id="listNotPassedExamJobs" resultType="ai.saharaa.model.Job">
    select job.id, job.name, job.batch_id, job.task_list_id, job.owner_id, job.node_id,
           job.status, job.am_review_status, job.nm_review_status, job.assign_data_volume, job.time_spent_per_task,
           job.review_deadline, job.audit_deadline, job.invite_acceptance_deadline,
           job.submitted_at, job.accepted_at, job.rejected_by, job.rejected_at, job.deleted, job.created_at, job.updated_at
    from job
    LEFT JOIN job_user ju ON job.id = ju.task_list_session_id
    AND ju.deleted = false
    AND ju.user_id = #{userId}
    LEFT JOIN Batch b ON b.id = job.batch_id
    LEFT JOIN Project p ON p.id = b.project_id
    WHERE (#{jobName} IS NULL OR job.name ILIKE '%' || #{jobName} || '%')
      AND job.job_type = 'individual'
      AND ju.role = 'Not_Pass_Exam'
      AND job.deleted = false
      AND b.task_type = 'task'
      AND job.status = 'working'
    ORDER BY
      CASE WHEN #{sortType} = 'oldest' THEN job.created_at END ASC,
      CASE WHEN #{sortType} = 'earliest' THEN job.created_at END DESC,
      CASE WHEN #{sortType} = 'maxEarning' THEN b.est_earnings END DESC,
      CASE WHEN #{sortType} = 'minEarning' THEN b.est_earnings END ASC,
      CASE WHEN #{sortType} = 'endingSoon' THEN job.review_deadline END ASC,
      b.have_certificate ASC,
      b.have_exam ASC,
      CASE WHEN #{sortType} IN ('maxEarning', 'minEarning') THEN job.created_at END DESC;
  </select>
</mapper>