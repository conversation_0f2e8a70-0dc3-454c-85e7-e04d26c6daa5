<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.TaskMapper">
  <select id="getTaskIdsByTaskListId">
    select id from task where task.task_list_id = #{taskListId} and task.deleted = false;
  </select>
  <select id="takeTasksHasNoDefaultQuestionGroup" resultType="ai.saharaa.model.Task">
    SELECT t.id AS id,
           t.task_list_id AS task_list_id,
           t.resource_id AS resource_id,
           t.owner_id AS owner_id,
           t.task_type AS task_type,
           t.origin_id AS origin_id,
           t.sort AS sort,
           t.status AS status,
           t.deleted AS deleted,
           t.is_hybrid AS is_hybrid,
           t.created_at AS created_at,
           t.updated_at AS updated_at
    FROM task_list AS tl
           LEFT JOIN task AS t ON tl.id = t.task_list_id
           LEFT JOIN question_group AS qg ON t.id = qg.task_id
    WHERE tl.list_type = 'label' AND t.deleted = false
    GROUP BY tl.id, t.id
    HAVING COUNT(qg.id) = 0
      limit #{maxCount};
  </select>
</mapper>