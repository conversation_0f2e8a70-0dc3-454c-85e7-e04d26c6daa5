<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.ExamSessionMapper">
  <select id="getExamSessionStatByJobAndUser" resultType="ai.saharaa.model.stat.ExamSessionStat">
    select count(*) as total,
           count(case when es.review_result = 'right' then 1 end) as accepted,
           sum(es.duration) as time_used
    from exam_session es
    where es.job_id = #{jobId} and es.user_id = #{uid} and es.deleted = false;
  </select>
</mapper>