<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.BatchMapper">
  <select id="getAmInProgressBatchDistinct">
    select project.account_manager_id as amId, count(batch.id) as inprogressCount
    from batch inner join project on batch.project_id = project.id
    where
    <foreach item="item" index="index" collection="ids"
             open="project.account_manager_id in (" separator="," close=")" nullable="true">
      #{item}
    </foreach>
      and batch.status not in ('draft', 'finished')
      and batch.deleted = false GROUP BY project.account_manager_id;
  </select>
  <select id="canJoinTask" resultType="java.lang.Long">
    select bar.batch_id from batch_access_requirement bar
                           left join user_certificate uc on bar.certificate_id = uc.certificate_id
                           join batch b on bar.batch_id = b.id
    where uc.user_id = #{userId}
      and batch_id not in (
      select batch_id from job where id in (
        select task_list_session_id from job_user where user_id = #{userId}
                                                    and deleted = false)
    )
      and b.status in ('assignJobs')
      and b.task_type = 'task'
      and uc.certificate_id = #{certificateId}
      and b.deleted = false
      and uc.deleted = false
      and bar.deleted = false
    group by bar.batch_id
    having count(bar.certificate_id) = count(uc.certificate_id) limit 3
  </select>

  <select id="previewCanJoinTaskPreview" resultType="java.lang.Long">
    select batch_id from batch_access_requirement bar
                           join batch b on bar.batch_id = b.id
    where bar.certificate_id = #{certificateId}
      and bar.batch_id not in (
      select batch_id from job where id in (
        select task_list_session_id
        from job_user where user_id = #{userId}
                        and deleted = false)
    )
      and bar.deleted = false
      and b.status = 'assignJobs'
      and b.task_type = 'task' limit 3
  </select>

  <select id="canJoinAcademy" resultType="java.lang.Long">
    select bar.batch_id from batch_access_requirement bar
                           join batch b on bar.batch_id = b.id
    where bar.certificate_id = #{certificateId}
      and bar.batch_id not in (
      select requirement_target_id from user_requirements_status where user_id = #{userId} and deleted = false
    )
      and bar.deleted = false
      and  b.task_type = 'academy' limit 3
  </select>

  <select id="hasLevelUp" resultType="java.lang.Long">
    select bar.batch_id from batch_access_requirement bar
                      join batch b on bar.batch_id = b.id
                      join certificate cer on b.certificate_id = cer.id
    where bar.certificate_id = #{certificateId}
      and bar.batch_id not in (
      select requirement_target_id from user_requirements_status where user_id = #{userId} and deleted = false
    )
      and bar.deleted = false
      and b.task_type = 'academy'
      and cer.category = #{category}
      and cer.certificate_type = #{certificateType}
      limit 10
  </select>

  <select id="availableAcademy" resultType="ai.saharaa.model.Batch">
    SELECT * FROM batch b
    left join user_requirements_status urs on b.id = urs.requirement_target_id and urs.deleted = false and urs.user_id = #{userId}
    WHERE b.id
          not in
               (
      select distinct batch_id from batch_access_requirement where certificate_id not in (
        select user_certificate.certificate_id from user_certificate where user_id = #{userId}
      )
                                                               and type = 'academy'
                                                               and deleted = false
    ) and b.task_type = 'academy'
      <if test="category != null">
        and b.academy_type =  #{category}
      </if>
      <if test="courseType != null">
        AND b.course_type =  #{courseType}
      </if>
      <if test="knowledge != null and knowledge.trim().length > 0">
        and array_length(array(SELECT unnest(string_to_array(b.knowledge, ',')) INTERSECT SELECT unnest(string_to_array(#{knowledge}, ','))), 1) > 0
      </if>
      <if test="name != null and name.trim().length > 0">
        and b.name = #{name}
      </if>
      <if test="courseDifficulty != null and !courseDifficulty.isEmpty()">
        and difficulty in
        <foreach collection="courseDifficulty" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and b.deleted = false
      and (
          urs.status in
          <foreach collection="userStatus" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>

        or urs.status is null
      )
      and b.status = 'preTaskExam'
    order by urs.id is null, b.id desc
    offset #{offset} limit #{size}
  </select>

  <select id="countAvailableAcademy" resultType="java.lang.Long">
    SELECT count(1) FROM batch b
    left join user_requirements_status urs on b.id = urs.requirement_target_id and urs.deleted = false and urs.user_id = #{userId}
    WHERE b.id
    not in
    (
    select distinct batch_id from batch_access_requirement where certificate_id not in (
    select user_certificate.certificate_id from user_certificate where user_id = #{userId}
    )
    and type = 'academy'
    and deleted = false
    ) and b.task_type = 'academy'
    <if test="category != null">
      and b.academy_type =  #{category}
    </if>
    <if test="courseType != null">
      AND b.course_type =  #{courseType}
    </if>
    <if test="knowledge != null and knowledge.trim().length > 0">
      and array_length(array(SELECT unnest(string_to_array(b.knowledge, ',')) INTERSECT SELECT unnest(string_to_array(#{knowledge}, ','))), 1) > 0
    </if>
    <if test="name != null and name.trim().length > 0">
      and b.name = #{name}
    </if>
    <if test="courseDifficulty != null and !courseDifficulty.isEmpty()">
      and difficulty in
      <foreach collection="courseDifficulty" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and b.deleted = false
    and (
    urs.status in
    <foreach collection="userStatus" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>

    or urs.status is null
    )
    and b.status = 'preTaskExam'
  </select>

  <select id="notAvailableAcademy" resultType="ai.saharaa.model.Batch">
    SELECT * FROM batch b
    left join user_requirements_status urs on b.id = urs.requirement_target_id and urs.deleted = false and urs.user_id =  #{userId}
    WHERE b.id in (
    select distinct batch_id from batch_access_requirement
    where certificate_id not in (
    select user_certificate.certificate_id from user_certificate where user_id =  #{userId}
    )
    and type = 'academy'
    and deleted = false
    union
    select b.id from user_requirements_status urs left join batch b on  urs.requirement_target_id = b.id
    where urs.user_id = #{userId} and urs.deleted = false and urs.type = 'academy' and urs.status in
    <foreach collection="userStatus" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and b.course_type =  #{courseType}
    ) and b.task_type = 'academy'
    <if test="category != null">
      and b.academy_type =  #{category}
    </if>
    <if test="courseType != null">
      AND b.course_type =  #{courseType}
    </if>
    <if test="knowledge != null and knowledge.trim().length > 0">
      and array_length(array(SELECT unnest(string_to_array(b.knowledge, ',')) INTERSECT SELECT unnest(string_to_array(#{knowledge}, ','))), 1) > 0
    </if>
    <if test="name != null and name.trim().length > 0">
      and b.name = #{name}
    </if>
    <if test="courseDifficulty != null and !courseDifficulty.isEmpty()">
      and difficulty in
      <foreach collection="courseDifficulty" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and b.deleted = false
    and b.status = 'preTaskExam'
    order by urs.id is not null, b.id desc
    offset #{offset} limit #{size}
  </select>

  <select id="countNotAvailableAcademy" resultType="java.lang.Long">
    SELECT count(1) FROM batch b
    left join user_requirements_status urs on b.id = urs.requirement_target_id and urs.deleted = false and urs.user_id =  #{userId}
    WHERE b.id in (
    select distinct batch_id from batch_access_requirement
    where certificate_id not in (
    select user_certificate.certificate_id from user_certificate where user_id = #{userId}
    )
    and type = 'academy'
    and deleted = false
    union
    select b.id from user_requirements_status urs left join batch b on  urs.requirement_target_id = b.id
    where urs.user_id = #{userId} and urs.deleted = false and urs.type = 'academy' and urs.status in
    <foreach collection="userStatus" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and b.course_type =  #{courseType}
    ) and b.task_type = 'academy'
    <if test="category != null">
      and b.academy_type =  #{category}
    </if>
    <if test="courseType != null">
      AND b.course_type =  #{courseType}
    </if>
    <if test="knowledge != null and knowledge.trim().length > 0">
      and array_length(array(SELECT unnest(string_to_array(b.knowledge, ',')) INTERSECT SELECT unnest(string_to_array(#{knowledge}, ','))), 1) > 0
    </if>
    <if test="name != null and name.trim().length > 0">
      and b.name = #{name}
    </if>
    <if test="courseDifficulty != null and !courseDifficulty.isEmpty()">
      and difficulty in
      <foreach collection="courseDifficulty" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and b.deleted = false
    and b.status = 'preTaskExam'
  </select>

  <select id="jobIdsByProjectKnowledge">
    SELECT j.id
    FROM job j
           INNER JOIN batch b ON j.batch_id = b.id
           INNER JOIN project p ON p.id = b.project_id
    WHERE LOWER(#{knowledge}) = ANY (STRING_TO_ARRAY(LOWER(p.knowledge), ','))
      AND p.deleted = FALSE
      AND b.deleted = FALSE
      AND j.deleted = FALSE
  </select>

  <select id="jobIdsByProjectKnowledges">
    SELECT j.id AS jobId, p.knowledge AS knowledge
    FROM job j
           INNER JOIN batch b ON j.batch_id = b.id
           INNER JOIN project p ON p.id = b.project_id
    WHERE STRING_TO_ARRAY(LOWER(#{knowledges}), ',')  &amp;&amp; STRING_TO_ARRAY(LOWER(p.knowledge), ',')
      AND p.deleted = FALSE
      AND b.deleted = FALSE
      AND j.deleted = FALSE
  </select>
</mapper>