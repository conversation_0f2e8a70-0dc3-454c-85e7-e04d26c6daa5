<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.newTasks.UserTokenTaskRewardsMapper">
  
  <select id="getEarningBreakdownPage" resultType="ai.saharaa.dto.reward.EarningBreakdownDTO">
    SELECT 
      uttr.created_at as date,
      b.name as taskName,
      CASE 
        WHEN uttr.reward_token_type = 0 THEN 'SAH'
        WHEN uttr.reward_token_type = 1 THEN 'USD1'
        WHEN uttr.reward_token_type = 9 THEN rti.token_name
        ELSE 'Unknown'
      END as tokenName,
      CASE 
        WHEN uttr.reward_token_type = 0 THEN '/icons/sah-token.png'
        WHEN uttr.reward_token_type = 1 THEN '/icons/usd1-token.png'
        WHEN uttr.reward_token_type = 9 AND rti.resource_id IS NOT NULL THEN CONCAT('/api/resource/', rti.resource_id, '/download')
        ELSE '/icons/default-token.png'
      END as tokenIconUrl,
      uttr.amount,
      uttr.status,
      uttr.job_id,
      uttr.reward_token_type as rewardTokenType,
      CASE 
        WHEN uttr.reward_token_type = 9 THEN COALESCE(rti.tge_already, false)
        ELSE false
      END as isPartnerTokenTGE
    FROM user_token_task_rewards uttr
    INNER JOIN job j ON uttr.job_id = j.id
    INNER JOIN batch b ON j.batch_id = b.id
    LEFT JOIN reward_token_info rti ON uttr.third_party_token_id = rti.id AND uttr.reward_token_type = 9
    WHERE uttr.user_id = #{userId}
      AND uttr.deleted = false
      AND j.deleted = false
      AND b.deleted = false
      <if test="role != null and role != ''">
        AND EXISTS (
          SELECT 1 FROM job_user ju 
          WHERE ju.task_list_session_id = j.id 
            AND ju.user_id = #{userId} 
            AND ju.role = #{role}
            AND ju.deleted = false
        )
      </if>
      <if test="taskName != null and taskName != ''">
        AND b.name ILIKE CONCAT('%', #{taskName}, '%')
      </if>
      <if test="tokenType != null">
        AND uttr.reward_token_type = #{tokenType}
      </if>
    ORDER BY uttr.created_at DESC
  </select>
  
</mapper>
