<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.JobInvitationMapper">
  <select id="getExpiredInvitations">
    select ji.id, ji.node_id, ji.job_id, ji.status, ji.deleted, ji.created_at, ji.updated_at
    from job_invitation ji
           left join job j on j.id = ji.job_id
    where ji.deleted = false
      and ji.status in ('ndaSigning', 'pending')
    and j.invite_acceptance_deadline &lt; now()
    order by j.invite_acceptance_deadline, ji.id
    limit #{limit};
  </select>
</mapper>