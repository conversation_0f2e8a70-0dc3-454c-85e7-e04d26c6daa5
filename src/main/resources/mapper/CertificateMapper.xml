<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.certificate.CertificateMapper">
  <resultMap id="certificateResultMap" type="ai.saharaa.model.certificate.Certificate">
    <id property="knowledgeList" column="knowledge_list" typeHandler="ai.saharaa.common.typeHandler.JsonTypeHandler"
        jdbcType="OTHER"/>
  </resultMap>
  <select id="getTodoCertificatesByUserIdCertificateType" resultMap="certificateResultMap">
    select * from certificate cert
    join batch on cert.id = batch.certificate_id
    and batch.status = 'preTaskExam'
    and batch.deleted = false
    where cert.id not in (
    select uc.certificate_id from user_certificate uc
    where uc.user_id = #{userId}
    and uc.deleted = false
    and uc.certificate_type = #{certificateType}
    )
    and cert.certificate_type = #{certificateType}
    and cert.deleted = false
    order by cert.created_at desc
    limit #{size} offset #{offset};
  </select>
  <select id="countTodoCertificatesByUserIdCertificateType" resultType="java.lang.Long">
    select count(*) from certificate cert
    join batch on cert.id = batch.certificate_id
    and batch.status = 'preTaskExam'
    and batch.deleted = false
    where cert.id not in (
    select uc.certificate_id from user_certificate uc
    where uc.user_id = #{userId}
    and uc.deleted = false
    and uc.certificate_type = #{certificateType}
    )
    and cert.deleted = false
    and cert.certificate_type = #{certificateType};
  </select>
</mapper>
