<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.achievement.UserAchievementProgressMapper">
  <update id="batchUpdateProgressById">
    UPDATE user_achievement_progress AS uap
    SET
    progress = updates.progress,
    passed = updates.passed,
    on_chain_passed = updates.on_chain_passed,
    passed_at = updates.passed_at::timestamptz,
    on_chain_passed_at = updates.on_chain_passed_at::timestamptz,
    updated_at = updates.updated_at::timestamptz
    FROM (VALUES
    <foreach item="p" index="index" collection="progresses"
             separator="," nullable="true">
      (#{p.id}, #{p.progress}, #{p.passed}, #{p.onChainPassed}, #{p.passedAt}, #{p.onChainPassedAt}, #{p.updatedAt})
    </foreach>
    ) AS updates(id, progress, passed, on_chain_passed, passed_at, on_chain_passed_at, updated_at)
    WHERE
    uap.id = updates.id;
  </update>
</mapper>