<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.SpotSessionMapper">
  <update id="resetPendingReviewSessionsByReviewerId">
update spot_session set deleted = true
where job_id = #{jobId} and status = 'pending' and deleted = false
      and task_session_id in (select ts.id from task_session ts
             join review_session rs on ts.id = rs.task_session_id
            where ts.job_id = #{jobId} and rs.user_id = #{uid}
              and ts.deleted = false and rs.deleted = false and ts.job_id = #{jobId});
  </update>
  <select id="getUserStat" resultType="ai.saharaa.model.stat.SpotSessionUserStat">
    select count(ss.id) as session_count,
           sum(ss.duration) as duration,
           count(case when ss.status = 'finish' and ss.revised = false then 1 end) as approved,
           count(case when ss.status = 'finish' and ss.revised then 1 end) as revised
    from spot_session ss
    where ss.user_id = #{uid} and ss.job_id = #{jobId}
      and ss.deleted = false and ss.status != 'rejected';
  </select>

  <select id="getSpotterTimeTotal" resultType="java.lang.Long">
    select sum(ss.duration) as duration
    from spot_session ss
    where ss.job_id = #{jobId} and ss.user_id = #{uid}
      and ss.deleted = false and ss.status != 'pending';
  </select>
</mapper>