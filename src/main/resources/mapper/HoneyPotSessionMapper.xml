<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.HoneyPotSessionMapper">
  <select id="getCurrentJobUserAvailableQuestionIdList">
    WITH excluded_ids AS (
      SELECT question_id FROM honey_pot_session where job_user_id = #{jobUserId} and deleted = false
      GROUP BY question_id HAVING COUNT(question_id) &gt;= #{count}
    ) select id from task_question where task_id in (${ids}) and deleted = false and id not in (SELECT question_id FROM excluded_ids) limit 10;
  </select>
<!--  <select id="getCurrentJobUserAvailableQuestionIdList2">-->
<!--    SELECT question_id FROM honey_pot_session where job_user_id = #{jobUserId} and deleted = false-->
<!--    GROUP BY question_id HAVING COUNT(question_id) &lt; #{count};-->
<!--  </select>-->
</mapper>