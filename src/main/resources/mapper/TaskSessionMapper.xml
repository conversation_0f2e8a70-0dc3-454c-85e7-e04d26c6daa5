<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.TaskSessionMapper">
    <select id="takeReviewJobForUser">
        SELECT ts.id,
               ts.job_user_id,
               ts.job_id,
               ts.task_id,
               ts.user_id,
               ts.user_role,
               ts.answer,
               ts.feedback,
               ts.status,
               ts.reviewer_status,
               ts.spotter_status,
               ts.nm_review_status,
               ts.am_review_status,
               ts.client_review_status,
               ts.duration,
               ts.deleted,
               ts.created_at,
               ts.updated_at
        FROM task_session ts
                 LEFT JOIN (SELECT task_session_id
                            FROM review_session
                            WHERE job_id = #{jobId}
                              AND deleted = false
                              and status != 'rejected') rs ON ts.id = rs.task_session_id
        WHERE ts.job_id = #{jobId}
          and ts.status = 'pending_review'
          and ts.deleted = false
          AND NOT EXISTS(
                SELECT 1
                FROM review_session
                WHERE job_id = #{jobId}
                  AND task_session_id = ts.id
                  AND user_id = #{uid}
                  And deleted = false
                  AND status != 'rejected'
    )
        GROUP BY ts.id
        HAVING COUNT(rs.task_session_id) &lt; #{maxSession} limit 5;
    </select>

    <!--<select id="takeReviewJobsForUserOld">-->
    <!--  SELECT t.* FROM task_session t-->
    <!--  left join review_session rs on rs.task_session_id = t.id and rs.user_id = #{uid}-->
    <!--  left join skip_task_session skp on skp.task_session_id = t.id and skp.user_id = #{uid} and skp.deleted = false-->
    <!--  left join (select id, task_session_id, user_id, deleted, status, ROW_NUMBER() OVER (PARTITION BY id) as ct from review_session) r-->
    <!--  on t.id = r.task_session_id and r.deleted = false and rs.deleted = false and r.status != 'rejected'-->
    <!--  where t.status = 'pending_review' and t.job_id = #{jobId}-->
    <!--  and rs.id is null-->
    <!--  and skp.id is null-->
    <!--  and t.deleted = false and (r.ct &lt; #{repeatCount} or r.id is null) order by t.id limit #{sessionCount};-->
    <!--</select>-->
    <select id="takeReviewJobsForUser">
        SELECT t.*
        FROM task_session t
        where t.status = 'pending_review'
          and t.job_id = #{jobId}
          and t.id not in
              (select task_session_id
               from review_session
               where user_id = #{uid}
                 and job_id = #{jobId}
                 and deleted = false)
          and t.id not in (select task_session_id
                           from skip_task_session
                           where user_id = #{uid}
                             and job_id = #{jobId}
                             and deleted = false)
          and t.id not in (select task_session_id
                           from task_session_review_count
                           where job_id = #{jobId}
                             and deleted = false
                             and review_count &gt;= #{repeatCount})
          and t.deleted = false
        order by t.id limit #{sessionCount};
    </select>

    <select id="refreshTaskQueue" resultType="java.lang.Long">
        SELECT t.id
        FROM task_session t
        where t.status = 'pending_review'
          and t.job_id = #{jobId}
          and t.id not in (select task_session_id
                           from task_session_review_count
                           where job_id = #{jobId}
                             and deleted = false
                             and review_count &gt;= #{repeatCount})
          and t.deleted = false
        order by t.id limit #{limit};
    </select>

    <select id="countReviewJobsForUser">
        SELECT count(t.id)
        FROM task_session t
        where t.status = 'pending_review'
          and t.job_id = #{jobId}
          and t.id not in
              (select task_session_id
               from review_session
               where user_id = #{uid}
                 and job_id = #{jobId}
                 and deleted = false)
          and t.id not in (select task_session_id
                           from skip_task_session
                           where user_id = #{uid}
                             and job_id = #{jobId}
                             and deleted = false)
          and t.id not in (select task_session_id
                           from task_session_review_count
                           where job_id = #{jobId}
                             and deleted = false
                             and review_count &gt;= #{repeatCount})
          and t.deleted = false;
    </select>

    <select id="takeReviewJobForJobUser">
        SELECT ts.id,
               ts.job_user_id,
               ts.job_id,
               ts.task_id,
               ts.user_id,
               ts.user_role,
               ts.answer,
               ts.feedback,
               ts.status,
               ts.reviewer_status,
               ts.spotter_status,
               ts.nm_review_status,
               ts.am_review_status,
               ts.client_review_status,
               ts.duration,
               ts.deleted,
               ts.created_at,
               ts.updated_at
        FROM task_session ts
                 LEFT JOIN (SELECT task_session_id
                            FROM review_session
                            WHERE job_id = #{jobId}
                              AND deleted = false
                              and status != 'rejected') rs ON ts.id = rs.task_session_id
        WHERE ts.job_id = #{jobId}
          and ts.status = 'pending_review'
          and ts.deleted = false
          AND NOT EXISTS(
                SELECT 1
                FROM review_session
                WHERE job_user_id = #{jobUserId}
                  AND task_session_id = ts.id
                  And deleted = false
                  AND status != 'rejected'
    )
        GROUP BY ts.id
        HAVING COUNT(rs.task_session_id) &lt; #{maxSession} limit 5;
    </select>

    <select id="takeSpotJobForUser">
        SELECT ts.id,
               ts.job_user_id,
               ts.job_id,
               ts.task_id,
               ts.user_id,
               ts.user_role,
               ts.answer,
               ts.feedback,
               ts.status,
               ts.reviewer_status,
               ts.spotter_status,
               ts.nm_review_status,
               ts.am_review_status,
               ts.client_review_status,
               ts.duration,
               ts.deleted,
               ts.created_at,
               ts.updated_at
        FROM task_session ts
                 LEFT JOIN (SELECT task_session_id
                            FROM spot_session
                            WHERE job_id = #{jobId}
                              AND deleted = false
                              and status != 'rejected') ss ON ts.id = ss.task_session_id
        WHERE ts.job_id = #{jobId}
          and ts.status = 'pending_spot'
          and ts.deleted = false
          AND NOT EXISTS(
                SELECT 1
                FROM spot_session
                WHERE job_id = #{jobId}
                  AND task_session_id = ts.id
                  AND user_id = #{uid}
                  And deleted = false
                  AND status != 'rejected'
    )
        GROUP BY ts.id
        HAVING COUNT(ss.task_session_id) &lt; #{maxSession} limit 5;
    </select>
    <select id="getSubmissionCountByBatchId" resultType="java.lang.Long">
        select count(task_session.*)
        from task_session
                 join job on task_session.job_id = job.id
        where job.batch_id = #{batchId}
          and job.deleted = false
          and task_session.deleted = false
          and task_session.status in ('pending_spot', 'finished');
    </select>
    <select id="getRequesterIdByTaskSessionId" resultType="java.lang.Long">
        select batch.owner_id
        from batch
                 join job on batch.id = job.batch_id
                 join task_session ts on ts.job_id = job.id
        where ts.id = #{id} limit 1;
    </select>
    <update id="resetReviewStatusByJobAndReviewerId">
        update task_session
        set reviewer_status = 'none',
            status          = 'pending_review'
        where job_id = #{jobId}
          and deleted = false
          and spotter_status = 'none'
          and id in (select distinct rs.task_session_id
                     from review_session rs
                     where rs.user_id = #{reviewerId}
                       and rs.job_id = #{jobId}
                       and rs.deleted = false
                       and rs.status
            != 'spotted');
    </update>
    <select id="getPendingAuditSessions" resultType="ai.saharaa.model.TaskSession">
        SELECT ts.id,
               ts.job_user_id,
               ts.job_id,
               ts.task_id,
               ts.user_id,
               ts.user_role,
               ts.answer,
               ts.feedback,
               ts.status,
               ts.reviewer_status,
               ts.spotter_status,
               ts.nm_review_status,
               ts.am_review_status,
               ts.client_review_status,
               ts.duration,
               ts.deleted,
               ts.created_at,
               ts.updated_at
        FROM task_session ts
                 left join review_session rs on rs.task_session_id = ts.id
        WHERE ts.job_id = #{jobId}
          and ts.status = 'pending_spot'
          and ts.deleted = false
          and rs.deleted = false
          and rs.status != 'rejected'
      AND NOT EXISTS (
      SELECT 1
      FROM spot_session
      WHERE job_id = #{jobId}
          AND task_session_id = ts.id
          And deleted = false
          and status != 'rejected'
          and status != 'pending'
            )
        order by ts.id
        offset #{offset} limit #{pageSize};
    </select>
    <select id="getPendingAuditSessionCount" resultType="java.lang.Long">
        SELECT count(ts.id)
        FROM task_session ts
        WHERE ts.job_id = #{jobId}
          and ts.status = 'pending_spot'
          and ts.deleted = false
          AND NOT EXISTS(
                SELECT 1
                FROM spot_session
                WHERE job_id = #{jobId}
                  AND task_session_id = ts.id
                  And deleted = false
                  and status != 'rejected'
        and status != 'pending'
    );
    </select>
    <select id="getUserStat" resultType="ai.saharaa.model.stat.TaskSessionUserStat">
        select count(case when ts.status != 'rejected' then 1 end)                                    as session_count,
               sum(case when ts.status != 'rejected' then ts.duration end)                            as duration,
               count(case when ts.status != 'rejected' and ts.reviewer_status = 'approve' then 1 end) as approved,
               count(case when ts.status != 'rejected' and ts.reviewer_status = 'revised' then 1 end) as revised,
               count(case when ts.status != 'rejected' and ts.feedback != '' then 1 end)              as feedback_count,
               count(case when ts.status = 'rejected' then 1 end)                                     as rejected
        from task_session ts
        where ts.user_id = #{uid}
          and ts.job_id = #{jobId}
          and ts.deleted = false;
    </select>
    <select id="getTaskSessionStat" resultType="ai.saharaa.model.stat.TaskSessionUserStat">
        select count(ts.id)                                               as session_count,
               sum(ts.duration)                                           as duration,
               count(case when ts.reviewer_status = 'approve' then 1 end) as approved,
               count(case when ts.reviewer_status = 'revised' then 1 end) as revised,
               count(case when ts.feedback != '' then 1 end)              as feedbackCount
        from task_session ts
        where ts.job_id = #{jobId}
          and ts.deleted = false
          and ts.status != 'pending' and ts.status != 'rejected';
    </select>
    <select id="getNodeStat" resultType="ai.saharaa.model.stat.TaskSessionNodeStat">
        select count(ts.id)                                                     as reviewed_count,
               count(case when ts.client_review_status = 'approved' then 1 end) as approved_count,
               count(case when ts.client_review_status = 'rejected' then 1 end) as rejected_count
        from task_session ts
        where ts.job_id = #{jobId}
          and ts.client_review_status != 'none'
        and ts.deleted = false;
    </select>
    <select id="getLabelerTimeTotal" resultType="java.lang.Long">
        select sum(ts.duration) as duration
        from task_session ts
        where ts.job_id = #{jobId}
          and ts.user_id = #{uid}
          and ts.user_role = 'Labeler'
          and ts.deleted = false
          and ts.status != 'pending';
    </select>

    <select id="getRejectedBySpotterTaskSessions" resultType="ai.saharaa.model.TaskSession">
        select ts.*
        from task_session ts
                 join review_session rs
                      on ts.id = rs.task_session_id
        where ts.job_id = #{jobId}
          and rs.status = 'rejected'
    </select>

    <select id="takeAuditJobForAm" resultType="ai.saharaa.model.TaskSession">
        select ts.*
        from task_session ts
                 left join am_audit_session aas
                           on aas.task_session_id = ts.id and aas.result = 'none'
        where ts.job_id = #{jobId}
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and ts.deleted = false
          and aas.id is null
        order by random() limit 1;
    </select>

    <select id="countApprovedAuditForAm" resultType="java.lang.Long">
        select count(*)
        from task_session ts
                 join am_audit_session aas
                      on aas.task_session_id = ts.id
        where ts.job_id = #{jobId}
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and aas.status = 'approved'
          and aas.result = 'none'
          and aas.deleted = false
    </select>

    <select id="countRejectedAuditForAm" resultType="java.lang.Long">
        select count(*)
        from task_session ts
                 join am_audit_session aas
                      on aas.task_session_id = ts.id
        where ts.job_id = #{jobId}
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and aas.status = 'rejected'
          and aas.result = 'none'
          and aas.deleted = false
    </select>

    <select id="countPendingAuditForAm" resultType="java.lang.Long">
        select count(*)
        from task_session ts
                 left join am_audit_session aas
                           on aas.task_session_id = ts.id and aas.result = 'none'
        where ts.job_id = #{jobId}
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and ts.deleted = false
          and aas.id is null
    </select>

    <select id="takeAuditJobForClient" resultType="ai.saharaa.model.TaskSession">
        select ts.*
        from task_session ts
                 left join client_audit_session cas
                           on cas.task_session_id = ts.id and cas.result = 'none'
        where ts.job_id in
              (select id
               from job
               where batch_id = #{batchId}
                 and deleted = false
                 and status in ('am_audit', 'committed'))
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and ts.deleted = false
          and cas.id is null
        order by random() limit 1;
    </select>

    <select id="countApprovedAuditForClient" resultType="java.lang.Long">
        select count(*)
        from task_session ts
                 join client_audit_session cas
                      on cas.task_session_id = ts.id
                 left join job j on ts.job_id = j.id
        where ts.job_id in (select id
                            from job
                            where batch_id = #{batchId}
                              and deleted = false)
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and cas.status = 'approved'
          and cas.result = 'none'
          and cas.deleted = false
          and j.status != 'dropped'
    </select>

    <select id="countRejectedAuditForClient" resultType="java.lang.Long">
        select count(*)
        from task_session ts
                 join client_audit_session cas
                      on cas.task_session_id = ts.id
                 left join job j on ts.job_id = j.id
        where ts.job_id in (select id
                            from job
                            where batch_id = #{batchId}
                              and deleted = false)
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and cas.status = 'rejected'
          and cas.result = 'none'
          and cas.deleted = false
          and j.status != 'dropped'
    </select>

    <select id="countPendingAuditForClient" resultType="java.lang.Long">
        select count(*)
        from task_session ts
                 left join client_audit_session cas
                           on cas.task_session_id = ts.id and cas.result = 'none'
                 left join job j on ts.job_id = j.id
        where ts.job_id in (select id
                            from job
                            where batch_id = #{batchId}
                              and deleted = false)
          and ts.status in ('finish', 'pending_review', 'pending_spot')
          and ts.deleted = false
          and cas.id is null
          and j.status != 'dropped'
    </select>

    <select id="getTaskIdsInWorking" resultType="java.lang.Long">
        SELECT DISTINCT ts.task_id
        FROM task_session AS ts
        WHERE ts.job_id = #{jobId}
          AND ts.deleted = false
          AND (ts.status = 'pending' OR ts.status = 'pending_machine_review' OR ts.status = 'pending_review')
        ORDER BY ts.task_id
            LIMIT #{maxCount}
    </select>
    <select id="takeReviewJobsForUserAssembled">
        WITH FirstElement AS (SELECT t.job_user_id, t.lot_number
                              FROM task_session t
                              WHERE t.status = 'pending_review'
                                AND t.job_id = #{jobId}
                                AND t.id NOT IN (SELECT task_session_id
                                                 FROM review_session
                                                 WHERE user_id = #{uid}
                                                   AND job_id = #{jobId}
                                                   AND deleted = false)
                                AND t.id NOT IN (SELECT task_session_id
                                                 FROM skip_task_session
                                                 WHERE user_id = #{uid}
                                                   AND job_id = #{jobId}
                                                   AND deleted = false)
                                AND t.id NOT IN (SELECT task_session_id
                                                 FROM review_session
                                                 WHERE job_id = #{jobId}
                                                   AND deleted = false
                                                 GROUP BY task_session_id
                                                 HAVING count(id) &gt;= #{repeatCount})
                                AND t.deleted = false
            LIMIT 1
            )
        SELECT t.*
        FROM task_session t
                 JOIN FirstElement f ON t.job_user_id = f.job_user_id AND t.lot_number = f.lot_number
        WHERE t.status = 'pending_review'
          AND t.job_id = #{jobId}
          AND t.id NOT IN (SELECT task_session_id
                           FROM review_session
                           WHERE user_id = #{uid}
                             AND job_id = #{jobId}
                             AND deleted = false)
          AND t.id NOT IN (SELECT task_session_id
                           FROM skip_task_session
                           WHERE user_id = #{uid}
                             AND job_id = #{jobId}
                             AND deleted = false)
          AND t.id NOT IN (SELECT task_session_id
                           FROM review_session
                           WHERE job_id = #{jobId}
                             AND deleted = false
                           GROUP BY task_session_id
                           HAVING count(id) &gt;= #{repeatCount})
          AND t.deleted = false
        order by t.id limit #{sessionCount};
    </select>

    <select id="takeReviewJobsForUserAssembledByJobUserId">
        SELECT t.*
        FROM task_session t
        WHERE t.status = 'pending_review'
          AND t.job_id = #{jobId}
          AND t.job_user_id = #{jobUserId}
          AND t.lot_number = #{lotNumber}
          AND t.id NOT IN (SELECT task_session_id
                           FROM review_session
                           WHERE user_id = #{uid}
                             AND job_id = #{jobId}
                             AND deleted = false)
          AND t.id NOT IN (SELECT task_session_id
                           FROM skip_task_session
                           WHERE user_id = #{uid}
                             AND job_id = #{jobId}
                             AND deleted = false)
          AND t.id NOT IN (SELECT task_session_id
                           FROM review_session
                           WHERE job_id = #{jobId}
                             AND deleted = false
                           GROUP BY task_session_id
                           HAVING count(id) &gt;= #{repeatCount})
          AND t.deleted = false
        order by t.id limit #{sessionCount};
    </select>
</mapper>
