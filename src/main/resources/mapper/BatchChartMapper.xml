<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.BatchChartMapper">
  <select id="numberOfApprovedDataPoints">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM task_session ts
                    JOIN job j ON ts.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND ts.deleted = FALSE
               AND ts.status IN ('pending_spot', 'finished')
               AND ts.updated_at &lt; #{startTime}) + SUM(dd.y) OVER (ORDER BY dd.x)) AS y
    FROM (SELECT DATE_BIN('${interval}', ts.updated_at, TIMESTAMP '2022-01-01') x, COUNT(1) y
          FROM task_session ts
                 JOIN job j ON ts.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND ts.deleted = FALSE
            AND ts.status IN ('pending_spot', 'finished')
            AND ts.updated_at &gt;= #{startTime}
            AND ts.updated_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="numberOfDataPointsSubmittedByAnnotator">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM task_session ts
                    JOIN job j ON ts.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND ts.deleted = FALSE
               AND ts.submit_at &lt; #{startTime}) + SUM(dd.y) OVER (ORDER BY dd.x)) AS y
    FROM (SELECT DATE_BIN('${interval}', ts.submit_at, TIMESTAMP '2022-01-01') x, COUNT(1) y
          FROM task_session ts
                 JOIN job j ON ts.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND ts.deleted = FALSE
            AND ts.submit_at &gt;= #{startTime}
            AND ts.submit_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="numberOfDataPointsSubmittedByReviewer">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM review_session rs
                    JOIN job j ON rs.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND rs.deleted = FALSE
               AND rs.submit_at &lt; #{startTime}) + SUM(dd.y) OVER (ORDER BY dd.x)) AS y
    FROM (SELECT DATE_BIN('${interval}', rs.submit_at, TIMESTAMP '2022-01-01') x, COUNT(1) y
          FROM review_session rs
                 JOIN job j ON rs.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND rs.deleted = FALSE
            AND rs.submit_at &gt;= #{startTime}
            AND rs.submit_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="numberOfAnnotator">
    SELECT DATE_BIN('${interval}', ju.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT ju.user_id) y
    FROM job_user ju
           JOIN job j ON ju.task_list_session_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.active = TRUE
      AND ju.role = 'Labeler'
      AND ju.updated_at &gt;= #{startTime}
      AND ju.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfReviewer">
    SELECT DATE_BIN('${interval}', ju.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT ju.user_id) y
    FROM job_user ju
           JOIN job j ON ju.task_list_session_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.active = TRUE
      AND ju.role = 'Reviewer'
      AND ju.updated_at &gt;= #{startTime}
      AND ju.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="averageAccuracyOfAnnotator">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM task_session ts
                    JOIN job j ON ts.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND (ts.status = 'pending_spot' AND ts.deleted = FALSE)
               AND ts.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
            SUM(dd.y) OVER (ORDER BY dd.x)) /
           NULLIF(((SELECT COUNT(1)
                    FROM task_session ts
                           JOIN job j ON ts.job_id = j.id
                    WHERE j.batch_id = #{batchId}
                      AND j.deleted = FALSE
                      AND (ts.status = 'pending_spot' AND ts.deleted = FALSE OR ts.status = 'rejected')
                      AND ts.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
                   SUM(dd.z) OVER (ORDER BY dd.x)), 0) * 100 AS y
    FROM (SELECT DATE_BIN('${interval}', ts.updated_at, TIMESTAMP '2022-01-01')                               x,
                 COUNT((ts.status = 'pending_spot' AND ts.deleted = FALSE) OR NULL)                           y,
                 COUNT((ts.status = 'pending_spot' AND ts.deleted = FALSE OR ts.status = 'rejected') OR NULL) z
          FROM task_session ts
                 JOIN job j ON ts.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND ts.updated_at &gt;= #{startTime}
            AND ts.updated_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) AS dd
  </select>
  <select id="averageAccuracyOfReviewer">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM review_session rs
                    JOIN job j ON rs.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND rs.deleted = FALSE
               AND rs.status = 'spotted'
               AND rs.wrong_review = FALSE
               AND rs.submit_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
            SUM(dd.y) OVER (ORDER BY dd.x)) /
           NULLIF(((SELECT COUNT(1)
                    FROM review_session rs
                           JOIN job j ON rs.job_id = j.id
                    WHERE j.batch_id = #{batchId}
                      AND j.deleted = FALSE
                      AND rs.deleted = FALSE
                      AND rs.status = 'spotted'
                      AND rs.submit_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
                   SUM(dd.z) OVER (ORDER BY dd.x)), 0) * 100 AS y
    FROM (SELECT DATE_BIN('${interval}', rs.submit_at, TIMESTAMP '2022-01-01') x,
                 COUNT(rs.wrong_review = FALSE OR NULL)                        y,
                 COUNT(1)                                                      z
          FROM review_session rs
                 JOIN job j ON rs.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND rs.deleted = FALSE
            AND rs.status = 'spotted'
            AND rs.submit_at &gt;= #{startTime}
            AND rs.submit_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) AS dd
  </select>
  <select id="averageHoneyPotAccuracyOfAnnotator">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM honey_pot_session hps
                    JOIN task_session ts ON hps.task_session_id = ts.id
                    JOIN job j ON ts.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND ts.deleted = FALSE
               AND hps.deleted = FALSE
               AND hps.status = 1
               AND hps.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
            SUM(dd.y) OVER (ORDER BY dd.x)) /
           NULLIF(((SELECT COUNT(1)
                    FROM honey_pot_session hps
                           JOIN task_session ts ON hps.task_session_id = ts.id
                           JOIN job j ON ts.job_id = j.id
                    WHERE j.batch_id = #{batchId}
                      AND j.deleted = FALSE
                      AND ts.deleted = FALSE
                      AND hps.deleted = FALSE
                      AND (hps.status = 1 OR hps.status = 2)
                      AND hps.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
                   SUM(dd.z) OVER (ORDER BY dd.x)), 0) * 100 AS y
    FROM (SELECT DATE_BIN('${interval}', hps.updated_at, TIMESTAMP '2022-01-01') x,
                 COUNT(hps.status = 1 OR NULL)                                   y,
                 COUNT(hps.status = 1 OR hps.status = 2 OR NULL)                 z
          FROM honey_pot_session hps
                 JOIN task_session ts ON hps.task_session_id = ts.id
                 JOIN job j ON ts.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND ts.deleted = FALSE
            AND hps.deleted = FALSE
            AND hps.updated_at &gt;= #{startTime}
            AND hps.updated_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="averageHoneyPotAccuracyOfReviewer">
    SELECT dd.x,
           ((SELECT COUNT(1)
             FROM honey_pot_review_session hprs
                    JOIN review_session rs ON hprs.review_session_id = rs.id
                    JOIN job j ON rs.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND rs.deleted = FALSE
               AND hprs.deleted = FALSE
               AND hprs.status = 1
               AND hprs.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
            SUM(dd.y) OVER (ORDER BY dd.x)) /
           NULLIF(((SELECT COUNT(1)
                    FROM honey_pot_review_session hprs
                           JOIN review_session rs ON hprs.review_session_id = rs.id
                           JOIN job j ON rs.job_id = j.id
                    WHERE j.batch_id = #{batchId}
                      AND j.deleted = FALSE
                      AND rs.deleted = FALSE
                      AND hprs.deleted = FALSE
                      AND (hprs.status = 1 OR hprs.status = 2)
                      AND hprs.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
                   SUM(dd.z) OVER (ORDER BY dd.x)), 0) * 100 AS y
    FROM (SELECT DATE_BIN('${interval}', hprs.updated_at, TIMESTAMP '2022-01-01') x,
                 COUNT(hprs.status = 1 OR NULL)                                   y,
                 COUNT(hprs.status = 1 OR hprs.status = 2 OR NULL)                z
          FROM honey_pot_review_session hprs
                 JOIN review_session rs ON hprs.review_session_id = rs.id
                 JOIN job j ON rs.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND rs.deleted = FALSE
            AND hprs.deleted = FALSE
            AND hprs.updated_at &gt;= #{startTime}
            AND hprs.updated_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="averageHoneyPotAccuracyOverall">
    SELECT COALESCE(dd_a.x, dd_b.x)                                                                   AS x,
           ((SELECT COUNT(1)
             FROM honey_pot_session hps
                    JOIN task_session ts ON hps.task_session_id = ts.id
                    JOIN job j ON ts.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND ts.deleted = FALSE
               AND hps.deleted = FALSE
               AND hps.status = 1
               AND hps.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
            (SELECT COUNT(1)
             FROM honey_pot_review_session hprs
                    JOIN review_session rs ON hprs.review_session_id = rs.id
                    JOIN job j ON rs.job_id = j.id
             WHERE j.batch_id = #{batchId}
               AND j.deleted = FALSE
               AND rs.deleted = FALSE
               AND hprs.deleted = FALSE
               AND hprs.status = 1
               AND hprs.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
            SUM(dd_a.y) OVER (ORDER BY dd_a.x) + SUM(dd_b.y) OVER (ORDER BY dd_b.x)) /
           NULLIF(((SELECT COUNT(1)
                    FROM honey_pot_session hps
                           JOIN task_session ts ON hps.task_session_id = ts.id
                           JOIN job j ON ts.job_id = j.id
                    WHERE j.batch_id = #{batchId}
                      AND j.deleted = FALSE
                      AND ts.deleted = FALSE
                      AND hps.deleted = FALSE
                      AND (hps.status = 1 OR hps.status = 2)
                      AND hps.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
                   (SELECT COUNT(1)
                    FROM honey_pot_review_session hprs
                           JOIN review_session rs ON hprs.review_session_id = rs.id
                           JOIN job j ON rs.job_id = j.id
                    WHERE j.batch_id = #{batchId}
                      AND j.deleted = FALSE
                      AND rs.deleted = FALSE
                      AND hprs.deleted = FALSE
                      AND (hprs.status = 1 OR hprs.status = 2)
                      AND hprs.updated_at &lt; DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01')) +
                   SUM(dd_a.z) OVER (ORDER BY dd_a.x) + SUM(dd_b.z) OVER (ORDER BY dd_b.x)), 0) * 100 AS y
    FROM (SELECT DATE_BIN('${interval}', hps.updated_at, TIMESTAMP '2022-01-01') x,
                 COUNT(hps.status = 1 OR NULL)                                   y,
                 COUNT(hps.status = 1 OR hps.status = 2 OR NULL)                 z
          FROM honey_pot_session hps
                 JOIN task_session ts ON hps.task_session_id = ts.id
                 JOIN job j ON ts.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND ts.deleted = FALSE
            AND hps.deleted = FALSE
            AND hps.updated_at &gt;= #{startTime}
            AND hps.updated_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd_a
           FULL JOIN (SELECT DATE_BIN('${interval}', hprs.updated_at, TIMESTAMP '2022-01-01') x,
                             COUNT(hprs.status = 1 OR NULL)                                   y,
                             COUNT(hprs.status = 1 OR hprs.status = 2 OR NULL)                z
                      FROM honey_pot_review_session hprs
                             JOIN review_session rs ON hprs.review_session_id = rs.id
                             JOIN job j ON rs.job_id = j.id
                      WHERE j.batch_id = #{batchId}
                        AND j.deleted = FALSE
                        AND rs.deleted = FALSE
                        AND hprs.deleted = FALSE
                        AND hprs.updated_at &gt;= #{startTime}
                        AND hprs.updated_at &lt;= #{endTime}
                      GROUP BY x
                      ORDER BY x) dd_b ON dd_a.x = dd_b.x
  </select>
  <select id="numberOfUsersThatAcquiredABonus">
    SELECT DATE_BIN('${interval}', jup.end_count_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT jup.user_id) y
    FROM job_user_points jup
           JOIN job j ON jup.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND jup.deleted = FALSE
      AND jup.session_count != 0
      AND jup.end_count_at &gt;= #{startTime}
      AND jup.end_count_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="totalBonusPointsDistributed">
    SELECT DATE_BIN('${interval}', jup.end_count_at, TIMESTAMP '2022-01-01') x, COALESCE(SUM(jup.price), 0) y
    FROM job_user_points jup
           JOIN job j ON jup.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND jup.deleted = FALSE
      AND jup.session_count != 0
      AND jup.reward_type IN ('annotate_bonus', 'review_bonus')
      AND jup.end_count_at &gt;= #{startTime}
      AND jup.end_count_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="amountOfDistributedPointsToAnnotator">
    SELECT DATE_BIN('${interval}', jup.end_count_at, TIMESTAMP '2022-01-01') x,
           COALESCE(SUM(CASE jup.reward_type
                          WHEN 'annotate' THEN jup.price * jup.session_count
                          WHEN 'annotate_bonus' THEN jup.price
                          ELSE 0
             END), 0)                                                        y
    FROM job_user_points jup
           JOIN job j ON jup.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND jup.deleted = FALSE
      AND jup.session_count != 0
      AND jup.end_count_at &gt;= #{startTime}
      AND jup.end_count_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="amountOfDistributedPointsToReviewer">
    SELECT DATE_BIN('${interval}', jup.end_count_at, TIMESTAMP '2022-01-01') x,
           COALESCE(SUM(CASE jup.reward_type
                          WHEN 'review' THEN jup.price * jup.session_count
                          WHEN 'review_bonus' THEN jup.price
                          ELSE 0
             END), 0)                                                        y
    FROM job_user_points jup
           JOIN job j ON jup.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND jup.deleted = FALSE
      AND jup.session_count != 0
      AND jup.end_count_at &gt;= #{startTime}
      AND jup.end_count_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="amountOfDistributedPointsOverall">
    SELECT DATE_BIN('${interval}', jup.end_count_at, TIMESTAMP '2022-01-01') x,
           COALESCE(SUM(CASE jup.reward_type
                          WHEN 'annotate' THEN jup.price * jup.session_count
                          WHEN 'review' THEN jup.price * jup.session_count
                          WHEN 'annotate_bonus' THEN jup.price
                          WHEN 'review_bonus' THEN jup.price
                          ELSE 0
             END), 0)                                                        y
    FROM job_user_points jup
           JOIN job j ON jup.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND jup.deleted = FALSE
      AND jup.end_count_at &gt;= #{startTime}
      AND jup.end_count_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="averageTimeSpentOfAnnotator">
    SELECT dd.x,
           (COALESCE((SELECT SUM(ts.duration)
                      FROM task_session ts
                             JOIN job j ON ts.job_id = j.id
                      WHERE j.batch_id = #{batchId}
                        AND j.deleted = FALSE
                        AND ts.deleted = FALSE
                        AND ts.submit_at &lt; #{startTime}), 0) + SUM(dd.y) OVER (ORDER BY dd.x)) /
           (SELECT COUNT(DISTINCT ts.user_id)
            FROM task_session ts
                   JOIN job j ON ts.job_id = j.id
            WHERE j.batch_id = #{batchId}
              AND j.deleted = FALSE
              AND ts.deleted = FALSE
              AND ts.submit_at &lt; (dd.x + '${interval}'::INTERVAL)) / 1000 AS y
    FROM (SELECT DATE_BIN('${interval}', ts.submit_at, TIMESTAMP '2022-01-01') x,
                 COALESCE(SUM(ts.duration), 0)                                 y
          FROM task_session ts
                 JOIN job j ON ts.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND ts.deleted = FALSE
            AND ts.submit_at &gt;= #{startTime}
            AND ts.submit_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="averageTimeSpentOfReviewer">
    SELECT dd.x,
           (COALESCE((SELECT SUM(rs.duration)
                      FROM review_session rs
                             JOIN job j ON rs.job_id = j.id
                      WHERE j.batch_id = #{batchId}
                        AND j.deleted = FALSE
                        AND rs.deleted = FALSE
                        AND rs.submit_at &lt; #{startTime}), 0) + SUM(dd.y) OVER (ORDER BY dd.x)) /
           (SELECT COUNT(DISTINCT rs.user_id)
            FROM review_session rs
                   JOIN job j ON rs.job_id = j.id
            WHERE j.batch_id = #{batchId}
              AND j.deleted = FALSE
              AND rs.deleted = FALSE
              AND rs.submit_at &lt; (dd.x + '${interval}'::INTERVAL)) / 1000 AS y
    FROM (SELECT DATE_BIN('${interval}', rs.submit_at, TIMESTAMP '2022-01-01') x,
                 COALESCE(SUM(rs.duration), 0)                                 y
          FROM review_session rs
                 JOIN job j ON rs.job_id = j.id
          WHERE j.batch_id = #{batchId}
            AND j.deleted = FALSE
            AND rs.deleted = FALSE
            AND rs.submit_at &gt;= #{startTime}
            AND rs.submit_at &lt;= #{endTime}
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="averageTimeSpentOverall">
    SELECT dd.x,
           ((COALESCE((SELECT SUM(ts.duration)
                       FROM task_session ts
                              JOIN job j ON ts.job_id = j.id
                       WHERE j.batch_id = #{batchId}
                         AND j.deleted = FALSE
                         AND ts.deleted = FALSE
                         AND ts.submit_at &lt; #{startTime}), 0) +
             COALESCE((SELECT SUM(rs.duration)
                       FROM review_session rs
                              JOIN job j ON rs.job_id = j.id
                       WHERE j.batch_id = #{batchId}
                         AND j.deleted = FALSE
                         AND rs.deleted = FALSE
                         AND rs.submit_at &lt; #{startTime}), 0) +
             SUM(dd.y) OVER (ORDER BY dd.x)) /
            (SELECT COUNT(DISTINCT userall.u)
             FROM (SELECT ts.user_id u
                   FROM task_session ts
                          JOIN job j ON ts.job_id = j.id
                   WHERE j.batch_id = #{batchId}
                     AND j.deleted = FALSE
                     AND ts.deleted = FALSE
                     AND ts.submit_at &lt; (dd.x + '${interval}'::INTERVAL)
                   UNION ALL
                   SELECT rs.user_id u
                   FROM review_session rs
                          JOIN job j ON rs.job_id = j.id
                   WHERE j.batch_id = #{batchId}
                     AND j.deleted = FALSE
                     AND rs.deleted = FALSE
                     AND rs.submit_at &lt; (dd.x + '${interval}'::INTERVAL)) as userall)) / 1000 AS y
    FROM (SELECT DATE_BIN('${interval}', overall.submit_at, TIMESTAMP '2022-01-01') x,
                 COALESCE(SUM(overall.duration), 0)                                 y
          FROM (SELECT ts.submit_at, ts.duration
                FROM task_session ts
                       JOIN job j ON ts.job_id = j.id
                WHERE j.batch_id = #{batchId}
                  AND j.deleted = FALSE
                  AND ts.deleted = FALSE
                  AND ts.submit_at &gt;= #{startTime}
                  AND ts.submit_at &lt;= #{endTime}
                UNION ALL
                SELECT rs.submit_at, rs.duration
                FROM review_session rs
                       JOIN job j ON rs.job_id = j.id
                WHERE j.batch_id = #{batchId}
                  AND j.deleted = FALSE
                  AND rs.deleted = FALSE
                  AND rs.submit_at &gt;= #{startTime}
                  AND rs.submit_at &lt;= #{endTime}) AS overall
          GROUP BY x
          ORDER BY x) dd
  </select>
  <select id="timeSpentPerDatapointOfAnnotator">
    SELECT DATE_BIN('${interval}', ts.submit_at, TIMESTAMP '2022-01-01') x,
           COALESCE(SUM(ts.duration), 0)::FLOAT / COUNT(ts.id) / 1000    y
    FROM task_session ts
           JOIN job j ON ts.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ts.deleted = FALSE
      AND ts.submit_at &gt;= #{startTime}
      AND ts.submit_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="timeSpentPerDatapointOfReviewer">
    SELECT DATE_BIN('${interval}', rs.submit_at, TIMESTAMP '2022-01-01') x,
           COALESCE(SUM(rs.duration), 0)::FLOAT / COUNT(rs.id) / 1000    y
    FROM review_session rs
           JOIN job j ON rs.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND rs.deleted = FALSE
      AND rs.submit_at &gt;= #{startTime}
      AND rs.submit_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanUsers">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanUsersInAccuracyHoneypotBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'accuracy_honeypot(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanAnnotatorsInAccuracyHoneypotBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
           JOIN job_user ju ON j.id = ju.task_list_session_id AND ju.user_id = bur.user_id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.role = 'Labeler'
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'accuracy_honeypot(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanReviewersInAccuracyHoneypotBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
           JOIN job_user ju ON j.id = ju.task_list_session_id AND ju.user_id = bur.user_id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.role = 'Reviewer'
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'accuracy_honeypot(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanUsersInHoneypotBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'honeypot(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanAnnotatorsInHoneypotBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
           JOIN job_user ju ON j.id = ju.task_list_session_id AND ju.user_id = bur.user_id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.role = 'Labeler'
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'honeypot(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanReviewersInHoneypotBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
           JOIN job_user ju ON j.id = ju.task_list_session_id AND ju.user_id = bur.user_id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.role = 'Reviewer'
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'honeypot(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanUsersInReviewAccuracyBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'accuracy(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanAnnotatorsInReviewAccuracyBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
           JOIN job_user ju ON j.id = ju.task_list_session_id AND ju.user_id = bur.user_id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.role = 'Labeler'
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'accuracy(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="numberOfBanReviewersInReviewAccuracyBan">
    SELECT DATE_BIN('${interval}', bur.updated_at, TIMESTAMP '2022-01-01') x, COUNT(DISTINCT bur.user_id) y
    FROM ban_user_reason bur
           JOIN job j ON bur.job_id = j.id
           JOIN job_user ju ON j.id = ju.task_list_session_id AND ju.user_id = bur.user_id
    WHERE j.batch_id = #{batchId}
      AND j.deleted = FALSE
      AND ju.deleted = FALSE
      AND ju.disabled = FALSE
      AND ju.role = 'Reviewer'
      AND bur.deleted = FALSE
      AND bur.banned = TRUE
      AND bur.reason ILIKE 'accuracy(%'
      AND bur.updated_at &gt;= #{startTime}
      AND bur.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="passRateOfPreTaskExam">
    SELECT DATE_BIN('${interval}', urs.updated_at, TIMESTAMP '2022-01-01')                                     x,
           COUNT(urs.status = 1 OR NULL)::NUMERIC / NULLIF(COUNT(urs.status = 1 OR urs.status = 2 OR NULL), 0) y
    FROM user_requirements_status urs
    WHERE urs.requirement_target_id = #{batchId}
      AND urs.type = 'exam'
      AND urs.deleted = FALSE
      AND urs.updated_at &gt;= #{startTime}
      AND urs.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="avgTimeSpentOnPreTaskExam">
    SELECT DATE_BIN('${interval}', urs.updated_at, TIMESTAMP '2022-01-01')                                      x,
           COALESCE(SUM(DATE_PART('SECOND', urs.updated_at - urs.created_at)), 0) / COUNT(DISTINCT urs.user_id) y
    FROM user_requirements_status urs
    WHERE urs.requirement_target_id = #{batchId}
      AND urs.type = 'exam'
      AND urs.deleted = FALSE
      AND urs.status = 1
      AND urs.updated_at &gt;= #{startTime}
      AND urs.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="averageScoreOfPreTaskExam">
    SELECT DATE_BIN('${interval}', urs.updated_at, TIMESTAMP '2022-01-01')          x,
           COALESCE(SUM(urs.content::FLOAT / 100), 0) / COUNT(DISTINCT urs.user_id) y
    FROM user_requirements_status urs
    WHERE urs.requirement_target_id = #{batchId}
      AND urs.type = 'exam'
      AND urs.deleted = FALSE
      AND (urs.status = 1 OR urs.status = 2)
      AND urs.updated_at &gt;= #{startTime}
      AND urs.updated_at &lt;= #{endTime}
    GROUP BY x
    ORDER BY x
  </select>
  <select id="passScoreOfPreTaskExam">
    SELECT CASE
             WHEN #{startTime} = DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01') THEN #{startTime}
             ELSE DATE_BIN('${interval}', #{startTime}, TIMESTAMP '2022-01-01') + '${interval}'::INTERVAL
             END                  AS x,
           COALESCE(b.exam_numerator, 0)::FLOAT /
           NULLIF(CASE
                    WHEN b.exam_type = 'qa'
                      THEN (SELECT COUNT(1)
                            FROM task_question tq
                            WHERE tq.deleted = FALSE
                              AND tq.task_id =
                                  (SELECT t.id
                                   FROM task_list tl
                                          JOIN task t ON tl.id = t.task_list_id
                                   WHERE tl.batch_id = #{batchId}
                                     AND tl.list_type = 'label'
                                     AND tl.deleted = false
                                     AND t.deleted = false
                                     AND t.task_type = 'task'
                                   ORDER BY tl.id, t.id
                                   LIMIT 1))
                    ELSE (SELECT COUNT(1)
                          FROM task_list tl
                                 JOIN task t ON tl.id = t.task_list_id
                          WHERE tl.batch_id = #{batchId}
                            AND tl.list_type = 'exam'
                            AND tl.deleted = false
                            AND t.deleted = false
                            AND t.task_type = 'exam')
                    END, 0) * 100 AS y
    FROM batch b
    WHERE b.id = #{batchId}
      AND b.deleted = FALSE
  </select>
</mapper>
