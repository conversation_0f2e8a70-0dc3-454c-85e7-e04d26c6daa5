<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.achievement.AchievementOracleLogMapper">
  <select id="sumTimeProgress">
    SELECT user_id, achievement_id, task_id, timestamp, SUM(progress) AS progress
    FROM achievement_oracle_log
    WHERE achievement_id = #{achievementId}
    AND user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    <if test="startTime != null">
      AND timestamp &gt;= #{startTime}
    </if>
    <if test="endTime != null">
      AND timestamp &lt;= #{endTime}
    </if>
    GROUP BY user_id, achievement_id, task_id, timestamp;
  </select>
  <select id="sumBarProgress">
    SELECT user_id, achievement_id, task_id, SUM(progress) AS progress
    FROM achievement_oracle_log
    WHERE achievement_id = #{achievementId}
    AND user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    <if test="startTime != null">
      AND timestamp &gt;= #{startTime}
    </if>
    <if test="endTime != null">
      AND timestamp &lt;= #{endTime}
    </if>
    GROUP BY user_id, achievement_id, task_id;
  </select>
</mapper>
