<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.JobTaskMapper">
    <resultMap id="jobTaskResultMap" type="ai.saharaa.model.JobTask">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <id property="jobId" column="job_id" jdbcType="BIGINT"/>
        <id property="taskId" column="task_id" jdbcType="BIGINT"/>
        <id property="deleted" column="deleted" jdbcType="BOOLEAN"/>
        <id property="createdAt" column="created_at" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
        <id property="updatedAt" column="updated_at" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
    </resultMap>
    <select id="takeJobForUser">
        SELECT jt.id, jt.job_id, jt.task_id, jt.deleted, jt.created_at, jt.updated_at
        FROM job_task jt
                 LEFT JOIN (SELECT task_id, job_id
                            FROM task_session
                            WHERE job_id = #{jobId}
                              and deleted = false
                              and status != 'rejected') ts ON jt.task_id = ts.task_id
        WHERE jt.job_id = #{jobId}
          AND jt.deleted = false
          AND NOT EXISTS(
                SELECT 1
                FROM task_session
                WHERE job_id = #{jobId}
                  AND task_id = jt.task_id
                  AND user_id = #{uid}
                  and user_role = #{role}
                  and deleted = false
            )
        GROUP BY jt.id, jt.task_id
        HAVING COUNT(ts.task_id) &lt; #{maxSession} limit 5;
    </select>
    <select id="takeJobForJobUser">
        select jt.*
        from job_task jt
        where jt.job_id = #{jobId}
          and jt.deleted = false
          and jt.task_id not in (SELECT task_id
                                 FROM task_session
                                 WHERE job_id = #{jobId}
                                   and user_role = #{role}
                                   and deleted = false
                                   and status
            != 'rejected'
            ) limit 5
    </select>

    <select id="countMyAvailableLeftTask" resultType="java.lang.Long">
        WITH excluded_tasks AS (SELECT task_id
                                FROM task_session
                                WHERE user_id = #{userId}
                                  AND job_id = #{jobId}
                                  AND deleted = false
                                UNION
                                SELECT task_id
                                FROM skip_task_session
                                WHERE user_id = #{userId}
                                  AND job_id = #{jobId}
                                  AND deleted = false
                                UNION
                                SELECT task_id
                                FROM task_session
                                WHERE job_id = #{jobId}
                                  AND deleted = false
                                GROUP BY task_id
                                HAVING COUNT(task_id) &gt;= #{limit})
        SELECT count(*)
        FROM job_task
        WHERE deleted = false
          AND job_id = #{jobId}
          AND task_id NOT IN (SELECT task_id FROM excluded_tasks);
    </select>
    <select id="takeJobForJobUserAvoidSkipped" resultType="ai.saharaa.model.JobTask">
        WITH excluded_tasks AS (SELECT task_id
                                FROM task_session
                                WHERE user_id = #{userId}
                                  AND job_id = #{jobId}
                                  AND deleted = false
                                UNION
                                SELECT task_id
                                FROM skip_task_session
                                WHERE user_id = #{userId}
                                  AND job_id = #{jobId}
                                  AND deleted = false
                                UNION
                                SELECT task_id
                                FROM task_session_dis_count
                                WHERE job_id = #{jobId}
                                  AND deleted = false
                                  AND dis_count &gt;= #{limit})
        SELECT jt.*
        FROM job_task jt
        WHERE jt.deleted = false
          AND jt.job_id = #{jobId}
          AND jt.task_id NOT IN (SELECT task_id FROM excluded_tasks)
        ORDER BY jt.task_id ASC LIMIT #{count};
    </select>


    <select id="refreshTaskQueue" resultType="java.lang.Long">
        WITH excluded_tasks AS (SELECT task_id
                                FROM task_session_dis_count
                                WHERE job_id = #{jobId}
                                  AND deleted = false
                                  AND dis_count &gt;= #{repeatCount})
        SELECT jt.task_id
        FROM job_task jt
        WHERE jt.deleted = false
          AND jt.job_id = #{jobId}
          AND jt.task_id NOT IN (SELECT task_id FROM excluded_tasks)
        ORDER BY jt.task_id ASC LIMIT #{limit};
    </select>

    <select id="unreviewedTaskCount" resultType="java.lang.Long">
        select count(*)
        from job_task jt
        where jt.deleted = false
          and jt.job_id = #{jobId}
          and not exists(
                select *
                from task_session ts
                where ts.deleted = false
                  and ts.job_id = jt.job_id
                  and ts.task_id = jt.task_id
                  and ts.reviewer_status in ('approve', 'revised'))
    </select>
    <select id="getJobTaskPagesByJobId" resultMap="jobTaskResultMap">
        SELECT jt.id, jt.job_id, jt.task_id, jt.deleted, jt.created_at, jt.updated_at
        FROM job_task jt
        WHERE jt.job_id = #{jobId}
          AND jt.deleted = false LIMIT #{size}
        OFFSET #{offset}
    </select>
    <update id="deleteJobTasksInWorkingByJobId">
        UPDATE job_task
        SET deleted    = true,
            updated_at = now()
        WHERE job_id = #{jobId}
          AND deleted = false
          AND task_id NOT IN (SELECT DISTINCT ts.task_id
                              FROM task_session AS ts
                              WHERE ts.job_id = #{jobId}
                                AND ts.deleted = false
                                AND (ts.status = 'pending_spot' OR ts.status = 'finish'))
    </update>
</mapper>
