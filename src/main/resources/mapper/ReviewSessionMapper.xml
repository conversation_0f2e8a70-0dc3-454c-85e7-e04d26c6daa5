<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.ReviewSessionMapper">
  <update id="resetPendingReviewSessionsByLabelerId">
     update review_session set deleted = true
      where job_id = #{jobId} and status = 'pending'
          and task_session_id in (select ts.id from task_session ts
                 where ts.job_id = #{jobId} and ts.user_id = #{uid} and ts.deleted = false)
  </update>
  <select id="getUserStat" resultType="ai.saharaa.model.stat.ReviewSessionUserStat">
    select count(case when rs.status != 'rejected' then 1 end) as session_count,
           sum(case when rs.status != 'rejected' then rs.duration end) as duration,
           count(case when rs.status = 'spotted' and rs.revised = false then 1 end) as approved,
           count(case when rs.status != 'rejected' and rs.revised then 1 end) as revised,
           count(case when rs.status = 'rejected' then 1 end) as rejected
    from review_session rs
    where rs.user_id = #{uid} and rs.job_id = #{jobId}
      and rs.deleted = false;
  </select>
  <select id="getReviewerTimeTotal" resultType="java.lang.Long">
    select sum(rs.duration) as duration
    from review_session rs
    where rs.job_id = #{jobId} and rs.user_id = #{uid}
      and rs.deleted = false and rs.status != 'pending';
  </select>
</mapper>