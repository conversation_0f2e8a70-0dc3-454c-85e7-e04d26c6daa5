<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.TaskQuestionMapper">
  <select id="getQuestionRandomlyByMultiTask">
    SELECT * FROM task_question where task_id in (${ids}) and deleted = false
    OFFSET floor(random() * (SELECT COUNT(*)
    FROM task_question where task_id in (${ids}) and deleted = false)) limit #{count};
  </select>
  <select id="getQuestionRandomlyByMultiTask2">
    SELECT * FROM task_question where task_id in (${ids}) and deleted = false
    and id in (#{range})
    OFFSET floor(random() * (SELECT COUNT(*)
    FROM task_question where task_id in (${ids}) and deleted = false)) limit #{count};
  </select>
</mapper>