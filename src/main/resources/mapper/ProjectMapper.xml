<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.ProjectMapper">
  <select id="getProjectsByNodeManagerId">
    SELECT p.*
    FROM project AS p
    WHERE p.deleted = false
      AND id IN (SELECT DISTINCT b.project_id
                 FROM batch AS b
                        LEFT JOIN job AS j ON b.id = j.batch_id
                        LEFT JOIN node AS n ON n.id = j.node_id
                 WHERE b.deleted = false
                   AND j.deleted = false
                   AND n.deleted = false
                   AND n.node_manager_id = #{nodeManagerId})
  </select>
</mapper>