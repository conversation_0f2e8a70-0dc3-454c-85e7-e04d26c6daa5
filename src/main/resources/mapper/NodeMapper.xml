<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.saharaa.mappers.NodeMapper">
  <select id="getNodeIdsByTaskSessions">
    select node.id node_id, ts.id session_id from node
        join job on job.node_id = node.id
        join task_session ts on ts.job_id  = job.id
    <where>
      <foreach item="item" index="index" collection="taskSessionIds"
               open="ts.id in (" separator="," close=")" nullable="true">
        #{item}
      </foreach>
    </where>
  </select>

  <select id="getNodeIdsByProjectId">
    SELECT DISTINCT j.node_id
    FROM batch AS b
           LEFT JOIN job AS j ON b.id = j.batch_id
    WHERE b.deleted = false
      AND j.deleted = false
      AND b.project_id = #{projectId}
  </select>
</mapper>