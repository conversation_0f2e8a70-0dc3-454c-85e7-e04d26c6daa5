openapi: 3.0.3
info:
  title: Machine Review API
  version: 1.0.0
paths:
  /machine-review/api/v1/sim_check/add_and_check:
    post:
      summary: Perform similarity check and add data points to database
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                project_id:
                  type: integer
                  format: int64
                submission_id:
                  type: integer
                  format: int64
                author_id:
                  type: integer
                  format: int64
                task_id:
                  type: integer
                  format: int64
                threshold:
                  type: number
                  format: double
                question_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                texts:
                  type: array
                  items:
                    type: string
              required:
                - project_id
                - submission_id
                - author_id
                - task_id
                - question_ids
                - texts
      responses:
        '200':
          description: Similarity check results
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      similarity_check_passed:
                        type: boolean
                      author_sim_check_passed:
                        type: boolean
                      project_sim_check_passed:
                        type: boolean
                      similar_data_points:
                        type: object
                        additionalProperties:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                format: int64
                              project_id:
                                type: integer
                                format: int64
                              submission_id:
                                type: integer
                                format: int64
                              author_id:
                                type: integer
                                format: int64
                              question_id:
                                type: integer
                                format: int64
                              task_id:
                                type: integer
                                format: int64
                              author_check_rejected:
                                type: boolean
                              project_check_rejected:
                                type: boolean
                              rejected:
                                type: boolean
                              created_at_ts_ms:
                                type: integer
                                format: int64
                              updated_at_ts_ms:
                                type: integer
                                format: int64
                              embedding_info:
                                type: object
                                properties:
                                  distance:
                                    type: number

  /machine-review/api/v1/sim_check/mark_reject:
    post:
      summary: Mark a submission as rejected
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                project_id:
                  type: integer
                  format: int64
                submission_id:
                  type: integer
                  format: int64
              required:
                - project_id
                - submission_id
      responses:
        '200':
          description: Successfully marked submission as rejected
