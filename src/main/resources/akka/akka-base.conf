akka {


  # Logger config for Akka internals and classic actors, the new API relies
  # directly on SLF4J and your config for the logger backend.

  # Loggers to register at boot time (akka.event.Logging$DefaultLogger logs
  # to STDOUT)
  loggers = ["akka.event.slf4j.Slf4jLogger"]


  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # NOTE: turn this on in prod
  coordinated-shutdown.exit-jvm = on


  actor {
    provider = "cluster"

    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      custom-json = "ai.saharaa.config.cluster.CustomJsonSerializer"
    }

    serialization-bindings {
      "ai.saharaa.model.SimpleResult" = custom-json
      "ai.saharaa.model.IShardMessage" = jackson-json
      "ai.saharaa.model.TraceableMessage" = jackson-json
      "java.lang.Record" = jackson-json
    }

    default-dispatcher {
      # Throughput for default Dispatcher, set to 1 for as fair as possible
      throughput = 10
    }

    blocking-io-dispatcher {
      type = Dispatcher
      executor = "thread-pool-executor"
      thread-pool-executor {
        fixed-pool-size = 16
      }
      throughput = 1
    }
  }

  remote.artery {
    canonical {
      hostname = "127.0.0.1"
      port = 2551
      transport = tcp
    }
  }

  cluster {
    downing-provider-class = "akka.cluster.sbr.SplitBrainResolverProvider"
    seed-nodes = [
      "akka://ai-dev-cluster@127.0.0.1:2551",
      "akka://ai-dev-cluster@127.0.0.1:2552",
    ]

    # roles might be used for splitting cluster into different groups
    roles = ["ai-platform", "ai-db", "ai-api", "ai-processor"]

    sharding {
      number-of-shards = 1000

      # Automatic entity passivation settings.
      passivation {
        strategy = "default-idle-strategy"
        default-idle-strategy {
          idle-entity.timeout = 120s
        }

        default-strategy {
          # Default limit of 10k active entities in a shard region (in a cluster node).
          active-entity-limit = 10000
        }
      }
    }
  }
}
