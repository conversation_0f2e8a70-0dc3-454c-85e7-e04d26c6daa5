
include "akka-base.conf"

akka {
  actor {
    serializers {
      jackson-json = "akka.serialization.jackson.JacksonJsonSerializer"
      custom-json = "ai.saharaa.config.cluster.CustomJsonSerializer"
    }

    serialization-bindings {
      "ai.saharaa.model.SimpleResult" = custom-json
      "ai.saharaa.model.IShardMessage" = jackson-json
      "ai.saharaa.model.TraceableMessage" = jackson-json
    }
  }

  coordinated-shutdown {
    exit-jvm = on
  }

  extensions = [
    "akka.rollingupdate.kubernetes.PodDeletionCost",
    "akka.rollingupdate.kubernetes.AppVersionRevision"]

  cluster {
    shutdown-after-unsuccessful-join-seed-nodes = 600s
    seed-nodes = []
  }

  actor.blocking-io-dispatcher.thread-pool-executor.fixed-pool-size = 32

  remote.artery {
    canonical {
      hostname = "<getHostAddress>"
    }
  }

  discovery {
    kubernetes-api {
      pod-label-selector = "actor-system-name=%s"
      use-raw-ip = true
    }
  }

  management {
    cluster.bootstrap {
      new-cluster-enabled = on
      contact-point-discovery {
        discovery-method = kubernetes-api
        required-contact-point-nr = ${?REQUIRED_CONTACT_POINT_NR}
        stable-margin = 5s
      }
    }
  }
}
