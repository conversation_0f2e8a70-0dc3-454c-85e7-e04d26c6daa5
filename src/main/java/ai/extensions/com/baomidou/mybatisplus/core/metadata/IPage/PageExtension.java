package ai.extensions.com.baomidou.mybatisplus.core.metadata.IPage;

import ai.saharaa.model.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import manifold.ext.rt.api.Extension;
import manifold.ext.rt.api.This;

@Extension
public class PageExtension {
  public static <T> PageResult<T> toPageResult(@This IPage<T> thiz) {
    return PageResult.<T>builder()
        .data(thiz.getRecords())
        .pages(thiz.getPages())
        .current(thiz.getCurrent())
        .size(thiz.getSize())
        .total(thiz.getTotal())
        .build();
  }

  public static <T, K> PageResult<K> toPageResultWithData(@This IPage<T> thiz, List<K> data) {
    return PageResult.<K>builder()
        .data(data)
        .pages(thiz.getPages())
        .current(thiz.getCurrent())
        .size(thiz.getSize())
        .total(thiz.getTotal())
        .build();
  }
}
