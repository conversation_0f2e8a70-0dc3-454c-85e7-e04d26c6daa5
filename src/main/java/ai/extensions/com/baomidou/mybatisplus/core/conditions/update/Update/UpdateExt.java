package ai.extensions.com.baomidou.mybatisplus.core.conditions.update.Update;

import ai.saharaa.common.typeHandler.PgJacksonTypeHandler;
import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import manifold.ext.rt.api.Extension;
import manifold.ext.rt.api.This;

@Extension
public class UpdateExt {
  public static <Children, R> Children setJson(
      @This Update<Children, R> thiz, R column, Object val) {
    return thiz.set(column, val, SqlScriptUtils.mappingTypeHandler(PgJacksonTypeHandler.class));
  }

  public static <Children, R> Children setJson(
      @This Update<Children, R> thiz, boolean condition, R column, Object val) {
    return thiz.set(
        condition, column, val, SqlScriptUtils.mappingTypeHandler(PgJacksonTypeHandler.class));
  }
}
