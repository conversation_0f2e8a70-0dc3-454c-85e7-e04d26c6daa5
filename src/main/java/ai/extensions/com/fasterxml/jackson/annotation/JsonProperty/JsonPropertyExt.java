package ai.extensions.com.fasterxml.jackson.annotation.JsonProperty;

import com.google.errorprone.annotations.Keep;
import manifold.ext.rt.api.Extension;

/**
 * Annotate all @JsonProperty fields with @Keep to prevent ErrorProne from incorrectly flagging them
 * as unused. We appreciate the gracious support of the Manifold library author, who agreed to
 * implement this feature for us.
 *
 * <p><a href="https://github.com/manifold-systems/manifold/issues/611">...</a>
 */
@Extension
@Keep
public class JsonPropertyExt {}
