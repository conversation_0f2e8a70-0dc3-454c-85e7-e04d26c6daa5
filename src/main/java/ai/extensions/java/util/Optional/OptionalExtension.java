package ai.extensions.java.util.Optional;

import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.Supplier;
import manifold.ext.rt.api.Extension;
import manifold.ext.rt.api.Self;
import manifold.ext.rt.api.This;

@Extension
public class OptionalExtension {
  public static <T, X extends Throwable> @Self Optional<T> orThrow(
      @This Optional<T> thiz, Supplier<? extends X> exceptionSupplier) {
    if (thiz.isPresent()) {
      return thiz;
    }
    throw exceptionSupplier.get();
  }

  public static <T, X extends Throwable> void ensurePresent(
      @This Optional<T> thiz, Supplier<? extends X> exceptionSupplier) {
    if (thiz.isEmpty()) {
      throw exceptionSupplier.get();
    }
  }

  public static <T> @Self Optional<T> filterIf(
      @This Optional<T> thiz, Predicate<T> preCond, Predicate<T> cond) {
    if (thiz.isEmpty()) {
      return thiz;
    }

    if (!preCond.test(thiz.get())) {
      return thiz;
    }
    return thiz.filter(cond);
  }
}
