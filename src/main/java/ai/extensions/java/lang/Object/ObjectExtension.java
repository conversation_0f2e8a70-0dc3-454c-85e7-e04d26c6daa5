package ai.extensions.java.lang.Object;

import ai.saharaa.dto.ApiResult;
import java.util.Optional;
import manifold.ext.rt.api.Extension;
import manifold.ext.rt.api.Self;
import manifold.ext.rt.api.This;

@Extension
public class ObjectExtension {
  public static ApiResult<@Self Object> toApiResult(@This Object thiz) {
    return ApiResult.success(thiz);
  }

  public static Optional<@Self Object> asOpt(@This Object thiz) {
    return Optional.ofNullable(thiz);
  }
}
