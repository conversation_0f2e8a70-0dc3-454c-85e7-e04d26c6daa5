package ai.saharaa.distribution;

import java.util.Map;

public interface TaskCounter {

  void cancelRemoveJobData(Long jobId);

  void removeJobData(Long jobId);

  Object getByKey(String key);

  Long getJobCount(Long jobId, String subKey);

  Long setJobCount(Long jobId, String subKey, Long value);

  void resetJobCount(Long jobId, String subKey);

  Long hgetJobCount(Long jobId, String subKey, String hashKey);

  Long hgetJobHashSize(Long jobId, String subKey);

  void hsetJobCount(Long jobId, String subKey, String hashKey, Long value);

  void hsetAllJobCount(Long jobId, String subKey, Map<String, Long> values);

  Map<Object, Object> hgetAllJobCount(Long jobId, String subKey);

  void hIncrementJobCount(Long jobId, String subKey, String hashKey, Integer value);

  void hDecreaseJobCount(Long jobId, String subKey, String hashKey, Integer value);

  void setJobCountIncrement(Long jobId, String subKey, Long value);

  void setJobCountDecrease(Long jobId, String subKey, Long value);

  Long getJobUserCount(Long jobId, Long userId, String subKey);

  Long setJobUserCount(Long jobId, Long userId, String subKey, Long value);

  void resetJobUserCount(Long jobId, Long userId, String subKey);

  void setJobUserCountIncrement(Long jobId, Long userId, String subKey, Long value);

  void setJobUserCountDecrease(Long jobId, Long userId, String subKey, Long value);
}
