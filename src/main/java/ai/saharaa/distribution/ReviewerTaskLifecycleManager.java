package ai.saharaa.distribution;

import static ai.saharaa.model.JobUser.JobUserRole.REVIEWER;
import static java.util.stream.Collectors.toMap;

import ai.saharaa.config.LabelingMetricsController;
import ai.saharaa.daos.TaskSessionDao;
import ai.saharaa.distribution.workload.WorkloadContext;
import ai.saharaa.distribution.workload.WorkloadSession;
import ai.saharaa.dto.job.*;
import ai.saharaa.enums.WorkloadType;
import ai.saharaa.exception.AppException;
import ai.saharaa.model.*;
import ai.saharaa.model.HoneyPot.HoneyPotReviewSession;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Component
public class ReviewerTaskLifecycleManager
    implements TaskLifecycleManager,
        TaskDistributor<ReviewSession>,
        TaskSubmitter<SubmitReviewsDTO, Boolean>,
        TypeProvider,
        TaskVisitor {
  private static final String key = "taskQueue:review:";

  private final TaskSessionDao taskSessionDao;
  private final TaskQueue taskQueue;
  private final JobTaskService jobTaskService;
  private final WorkloadLimitService workloadService;
  private final TaskSessionService taskSessionService;
  private final ReviewSessionService reviewSessionService;
  private final HoneyPotService honeyPotService;
  private final LabelingMetricsController labelingMetricsController;
  private final CommonCounterManager commonCounterManager;

  public ReviewerTaskLifecycleManager(
      TaskSessionDao taskSessionDao,
      TaskQueue taskQueue,
      JobTaskService jobTaskService,
      WorkloadLimitService workloadService,
      TaskSessionService taskSessionService,
      ReviewSessionService reviewSessionService,
      HoneyPotService honeyPotService,
      LabelingMetricsController labelingMetricsController,
      CommonCounterManager commonCounterManager) {
    this.taskSessionDao = taskSessionDao;
    this.taskQueue = taskQueue;
    this.jobTaskService = jobTaskService;
    this.workloadService = workloadService;
    this.taskSessionService = taskSessionService;
    this.reviewSessionService = reviewSessionService;
    this.honeyPotService = honeyPotService;
    this.labelingMetricsController = labelingMetricsController;
    this.commonCounterManager = commonCounterManager;
  }

  @Override
  @Transactional
  public List<ReviewSession> assignTasks(
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      String ipAddress,
      Boolean isTester) {

    if (isTester) ipAddress = null;
    boolean sybilFlag = workloadService.isSybilUser(jobUser.getUserId());
    WorkloadContext workloadContext = WorkloadContext.builder()
        .batch(batch)
        .batchSetting(batchSetting)
        .job(job)
        .role(REVIEWER)
        .sybilCount(workloadService.getSybilCount(job.getId(), REVIEWER))
        .sybil(sybilFlag)
        .dailyUserLimit(workloadService.getPlatformUserDailyWorkload())
        .commonCounterManager(commonCounterManager)
        .build();
    List<WorkloadSession> jobWorkloadSessions =
        workloadService.getJobWorkloadSessions(REVIEWER, jobUser);
    List<WorkloadSession> userWorkloadSessions = workloadService.getUserWorkloadSessions(jobUser);
    var workload = workloadService.checkReviewerLimit(
        workloadContext, jobWorkloadSessions, userWorkloadSessions, isTester);

    List<ReviewSession> pendingTasks =
        this.jobTaskService.getOngoingReviewSessionsForJobUser(jobUser.getId());

    if (workload.getKey() == WorkloadType.NO_WORKLOAD) {
      var remainingQuotas =
          workload.getValue().values().stream().min(Integer::compareTo).orElse(Integer.MAX_VALUE);
      var batchAnnotationBatchSize = batch.getReviewingRequiredDatapoint();
      var limit = Math.min(batchAnnotationBatchSize - pendingTasks.size(), remainingQuotas);
      List<ReviewSession> reviewSessions = this.getCacheTask(job, jobUser, (int) limit);
      if (!pendingTasks.isEmpty() && !reviewSessions.isEmpty()) {
        if (batchSetting.getHoneyPotSettingForReviewer().equals(BatchSetting.HpSettingType.NORMAL)
            && CollectionUtils.isNotEmpty(batchSetting.getHoneyPotReviewBatches())) {
          honeyPotService.adaptHpReviewSessionByReviewSessionIds(
              reviewSessions.mapToList(ReviewSession::getId), batch, jobUser, false);
        } else if (batchSetting
            .getHoneyPotSettingForReviewer()
            .equals(BatchSetting.HpSettingType.DP_BOUND)) {
          honeyPotService.adaptHpReviewSessionByReviewSessionIdsDpBound(
              reviewSessions, batch, jobUser, batchSetting);
        }
      }
      pendingTasks.addAll(reviewSessions);
      if (sybilFlag) {
        workloadService.upsertSybilUserCounts(
            reviewSessions.size(), JobUser.JobUserRole.REVIEWER, jobUser, job.getId());
      }

      return pendingTasks;
    }
    if (CollectionUtils.isNotEmpty(pendingTasks)) {
      return pendingTasks;
    }
    throw ControllerUtils.badRequest(
        "User workloads exceeded, workload: " + workload.getKey().getValue());
  }

  @Override
  public Boolean submitTasks(
      SubmitReviewsDTO reviews,
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      Boolean isTester) {

    var reviewSessionIds =
        reviews.getReviews().stream().map(SubmitReviewDTO::getReviewSessionId).toList();
    var sessions = this.reviewSessionService.getReviewSessionByIdList(reviewSessionIds).stream()
        .filter(s -> s.getUserId().equals(jobUser.getUserId()))
        .filter(s -> s.getStatus().equals(ReviewSession.ReviewSessionStatus.PENDING))
        .toList();
    if (CollectionUtils.isEmpty(sessions) && CollectionUtils.isNotEmpty(reviewSessionIds)) {
      throw ControllerUtils.notFound(
          "You are not allowed to approve these submissions, please reload");
    }
    if (sessions.size() < reviewSessionIds.size()) {
      throw ControllerUtils.notFound("Review session may expired, please reload");
    }
    var taskSessionIds =
        reviews.getReviews().stream().map(SubmitReviewDTO::getTaskSessionId).toList();

    var taskSessionsEffectIdList =
        this.taskSessionService.getTaskSessionByIdList(taskSessionIds).stream()
            .filter(s -> !s.getDeleted())
            .filter(s -> s.getStatus().equals(TaskSession.TaskSessionStatus.PendingReview))
            .map(TaskSession::getId)
            .toList();
    if (CollectionUtils.isEmpty(taskSessionsEffectIdList)
        && CollectionUtils.isNotEmpty(taskSessionIds)) {
      reviews.getReviews().forEach(r -> reviewSessionService.deleteById(r.getReviewSessionId()));
      reviews
          .getHpReviews()
          .forEach(r -> honeyPotService.deleteReviewHPSessionById(
              r.getHoneyPotReviewSession().getId()));
      return Boolean.FALSE;
      //      throw ControllerUtils.notFound("You are not allowed to approve these sessions");
    }
    if (taskSessionsEffectIdList.size() < reviewSessionIds.size()) {
      reviews.getReviews().removeIf(r -> {
        if (!taskSessionsEffectIdList.contains(r.getTaskSessionId())) {
          reviewSessionService.deleteById(r.getReviewSessionId());
          return true;
        }
        return false;
      });
      var filteredReviewId =
          reviews.getReviews().map(SubmitReviewDTO::getReviewSessionId).collect(Collectors.toSet());
      reviews.getHpReviews().removeIf(h -> {
        if (!filteredReviewId.contains(h.getHoneyPotReviewSession().getReviewSessionId())) {
          honeyPotService.deleteReviewHPSessionById(h.getHoneyPotReviewSession().getId());
          return true;
        }
        return false;
      });
      //      throw ControllerUtils.notFound("task session may expired, please reload");
    }

    var hpReviews = Objects.isNull(reviews.getHpReviews())
        ? List.<SubmitHoneyPotReviewsItemDTO>of()
        : reviews.getHpReviews();

    var hpSessions = this.honeyPotService.getSessionByReviewSessionIdList(reviewSessionIds).stream()
        .filter(s -> !s.getDeleted())
        .toList();
    if (reviewSessionIds.size() < hpSessions.size()) {
      throw ControllerUtils.notFound("Invalid data.");
    }
    var id2HpReviewSession = hpSessions.stream()
        .collect(toMap(HoneyPotReviewSession::getReviewSessionId, Function.identity()));
    var notIncluded = hpReviews.stream()
        .filter(
            r -> !id2HpReviewSession.containsKey(r.getHoneyPotReviewSession().getReviewSessionId()))
        .findAny();
    if (notIncluded.isPresent()) {
      throw ControllerUtils.notFound("Invalid data.");
    }
    var rejectIdList = honeyPotService.submitReviewAndJudge(
        hpReviews, jobUser, isTester, batchSetting.getHoneyPotSettingForReviewer());

    if (rejectIdList.getRight()) {
      return Boolean.valueOf(false);
    }

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);

    labelingMetricsController.reviewAnnotate(reviews.getReviews().size());
    reviewSessionService.handleSubmitReviews(
        reviews.getReviews(), batch, job, jobUser.getUserId(), rejectIdList.getLeft());
    workloadService.updateJobUserWorkloads(
        job.getId(),
        jobUser.getUserId(),
        (long) reviews.getReviews().size(),
        JobUser.JobUserRole.REVIEWER,
        ip);

    return Boolean.TRUE;
  }

  //  @Override
  //  public TaskSession submitTasks(
  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
  //    return null;
  //  }

  @Override
  public void refreshTaskQueue(Job job, Integer repeatCount) {
    Long length = this.taskQueue.length(key.concat(job.getId().toString()));
    if (length > 0) {
      return;
    }

    var pendingTaskIds = taskSessionDao
        .getTaskIdsByJobIdAndStatus(job.getId(), TaskSession.TaskSessionStatus.PendingReview)
        .map(TaskSession::getId)
        .map(Objects::toString)
        .toList();

    if (!pendingTaskIds.isEmpty()) {
      var refreshTaskDistributionStatus =
          taskSessionDao
              .refreshTaskQueueForReview(job.getId(), String.join(",", pendingTaskIds))
              .stream()
              .toMap(
                  v -> v.getTaskSessionId().toString(),
                  GroupByTaskSessionIdAndCountResultDTO::getCount);

      for (int i = 0; i < repeatCount; i++) {
        int finalI = i;
        var taskIds = pendingTaskIds
            .filter(r -> !refreshTaskDistributionStatus.containsKey(r)
                || (refreshTaskDistributionStatus.get(r) + finalI) < repeatCount)
            .map(Long::valueOf)
            .toList();
        if (!taskIds.isEmpty()) {
          this.taskQueue.pushAll(key.concat(job.getId().toString()), taskIds);
        }
      }
    }
  }

  @Override
  public void initializeTaskQueue(Batch batch, BatchSetting batchSetting) {}

  @Override
  public List<BatchSetting.DistributeType> getDistributionTypes() {
    return List.of(
        BatchSetting.DistributeType.RAW,
        BatchSetting.DistributeType.SINGLE,
        BatchSetting.DistributeType.ASSEMBLED);
  }

  @Override
  public List<JobUser.JobUserRole> getRoles() {
    return List.of(JobUser.JobUserRole.REVIEWER);
  }

  @SuppressWarnings("unused")
  private void checkIfWorkloadLimitExceeded(Job job, JobUser jobUser, String ipAddress) {
    workloadService.checkIfWorkloadLimitExceeded(
        job.getId(), jobUser.getUserId(), JobUser.JobUserRole.REVIEWER, ipAddress);
  }

  private List<ReviewSession> getCacheTask(Job job, JobUser jobUser, int limit) {

    Set<Long> notIncluded = Stream.concat(
            this.taskSessionService
                .getReviewSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
                .stream(),
            this.taskSessionService
                .getSkipTaskSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
                .stream()
                .map(SkipTaskSession::getTaskSessionId))
        .toSet();

    Set<Long> taskSessionIds = new HashSet<>(limit);
    var maxTry = Math.max(Math.min((job.getAssignDataVolume() - notIncluded.size()), 50), 20);
    int retryCount = 0;
    while (limit > 0 && retryCount < maxTry) {
      retryCount++;
      Long taskSessionId = this.taskQueue.pop(key.concat(job.getId().toString()));
      if (Objects.equals(taskSessionId, Long.MIN_VALUE)) {
        limit = 0;
        continue;
      }

      if (!notIncluded.contains(taskSessionId) && !taskSessionIds.contains(taskSessionId)) {
        limit -= 1;
        taskSessionIds.add(taskSessionId);
        //      } else {
        //        this.taskQueue.push(key.concat(job.getId().toString()), taskSessionId);
      }
    }
    if (taskSessionIds.isEmpty()) return Collections.EMPTY_LIST;
    List<TaskSession> taskSessions = this.taskSessionService.getTaskSessionByIdList(taskSessionIds);
    taskSessions.stream().filter(TaskSession::getDeleted).findAny().ifPresent(taskSession -> {
      throw new AppException("No more tasks available, please try again later");
    });

    taskSessions.stream()
        .filter(ts -> !Objects.equals(ts.getStatus(), TaskSession.TaskSessionStatus.PendingReview))
        .findAny()
        .ifPresent(taskSession -> {
          throw new AppException("No more tasks available, please try again later");
        });

    List<ReviewSession> reviewSessions = taskSessions.stream()
        .map(j -> this.reviewSessionService.createReviewSession(jobUser, j))
        .toList();

    return reviewSessions;
  }

  @Override
  public TaskSubmitter taskSubmitter() {
    return this;
  }

  @Override
  public TaskDistributor taskDistributor() {
    return this;
  }

  @Override
  public TaskLifecycleManager taskLifecycleManager() {
    return this;
  }
}
