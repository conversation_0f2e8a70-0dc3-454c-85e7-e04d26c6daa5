package ai.saharaa.distribution;

import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.*;
import static ai.saharaa.model.JobUser.JobUserRole.LABELER;

import ai.saharaa.config.LabelingMetricsController;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.daos.*;
import ai.saharaa.distribution.workload.WorkloadContext;
import ai.saharaa.distribution.workload.WorkloadSession;
import ai.saharaa.dto.job.GroupByTaskIdAndCountResultDTO;
import ai.saharaa.dto.job.SubmitAnswersDTO;
import ai.saharaa.enums.WorkloadType;
import ai.saharaa.model.*;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class LabelerRawTaskLifecycleManager extends AbstractTaskLifecycleManager
    implements TaskLifecycleManager, TaskDistributor<TaskSession>, TypeProvider, TaskVisitor {
  private static final String key = "taskQueue:";
  private final TaskQueue taskQueue;
  private final CommonCounterManager counterManager;
  private final JobTaskService jobTaskService;
  private final HoneyPotService honeyPotService;
  private final JobTaskDao jobTaskDao;
  private final TaskSessionService taskSessionService;

  @SuppressWarnings("unused")
  private final TaskSessionDao taskSessionDao;

  private final ProjectService projectService;
  private final JobService jobService;
  private final BatchService batchService;
  private final WorkloadLimitService workloadService;

  public LabelerRawTaskLifecycleManager(
      TaskQueue taskQueue,
      CommonCounterManager counterManager,
      JobTaskService jobTaskService,
      JobTaskDao jobTaskDao,
      TaskSessionDao taskSessionDao,
      TaskSessionService taskSessionService,
      WorkloadLimitService workloadService,
      ClusterConfiguration clusterConfiguration,
      CaptchaService captchaService,
      IndividualsService individualsService,
      JobUserService jobUserService,
      TaskAndReviewSessionDao taskAndReviewSessionDao,
      TaskQuestionDao taskQuestionDao,
      ResourceDao resourceDao,
      HoneyPotService honeyPotService,
      LabelingMetricsController labelingMetricsController,
      NotificationService notificationService,
      ProjectService projectService,
      JobService jobService,
      BatchService batchService) {

    super(
        clusterConfiguration,
        captchaService,
        individualsService,
        jobUserService,
        honeyPotService,
        taskSessionService,
        labelingMetricsController,
        taskSessionDao,
        taskAndReviewSessionDao,
        workloadService,
        taskQuestionDao,
        resourceDao,
        notificationService);
    this.taskQueue = taskQueue;
    this.counterManager = counterManager;
    this.jobTaskService = jobTaskService;
    this.honeyPotService = honeyPotService;
    this.jobTaskDao = jobTaskDao;
    this.taskSessionService = taskSessionService;
    this.taskSessionDao = taskSessionDao;
    this.workloadService = workloadService;
    this.projectService = projectService;
    this.jobService = jobService;
    this.batchService = batchService;
  }

  @Override
  @Transactional
  public List<TaskSession> assignTasks(
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      String ipAddress,
      Boolean isTester) {

    if (isTester) ipAddress = null;

    boolean sybilFlag = workloadService.isSybilUser(jobUser.getUserId());
    WorkloadContext workloadContext =
        WorkloadContext.builder()
            .batch(batch)
            .batchSetting(batchSetting)
            .job(job)
            .role(LABELER)
            .sybilCount(workloadService.getSybilCount(job.getId(), LABELER))
            .sybil(sybilFlag)
            .dailyUserLimit(workloadService.getPlatformUserDailyWorkload())
            .commonCounterManager(counterManager)
            .build();

    List<WorkloadSession> jobWorkloadSessions =
        workloadService.getJobWorkloadSessions(LABELER, jobUser);
    List<WorkloadSession> userWorkloadSessions = workloadService.getUserWorkloadSessions(jobUser);
    var workload = workloadService.checkLabelerLimit(
        workloadContext, jobWorkloadSessions, userWorkloadSessions, isTester);
    log.info(
        "jobId: {}, userId: {}, job workload session size: {}, user workload session size: {}, workload: {}",
        job.getId(),
        jobUser.getUserId(),
        jobWorkloadSessions.size(),
        userWorkloadSessions,
        workload.toString());
    var pendingTaskSessions = this.getPendingTaskSessions(jobUser);
    log.info("pending task size: {}", pendingTaskSessions.size());
    if (workload.getKey() == WorkloadType.NO_WORKLOAD) {
      var remainingQuotas =
          workload.getValue().values().stream().min(Integer::compareTo).orElse(Integer.MAX_VALUE);
      log.info("remaining quotas: {}", remainingQuotas);
      if (pendingTaskSessions.isEmpty()) {
        var reviseTaskSessions = this.getReviseTaskSessions(batch, jobUser);
        if (CollectionUtils.isNotEmpty(reviseTaskSessions)) {
          return reviseTaskSessions;
        }
      }
      var limit = Math.min(
          remainingQuotas, batch.getAnnotatingSubmitRequired() - pendingTaskSessions.size());
      log.info("limit: {}", limit);
      var appendedPendingTaskSessions =
          this.getEnoughPendingTaskSessions(job, batch, jobUser, batchSetting, limit);
      log.info("appended pending task sessions: {}", appendedPendingTaskSessions);
      if (CollectionUtils.isNotEmpty(appendedPendingTaskSessions) && sybilFlag) {
        workloadService.upsertSybilUserCounts(
            appendedPendingTaskSessions.size(), LABELER, jobUser, job.getId());
      }
      pendingTaskSessions = Stream.concat(
              pendingTaskSessions.stream(), appendedPendingTaskSessions.stream())
          .toList();
      log.info("concated pending task session size: {}", pendingTaskSessions.size());
      if (CollectionUtils.isNotEmpty(pendingTaskSessions)) {
        return pendingTaskSessions;
      }

      var result = getTaskSessions(job, batch, batchSetting, jobUser);
      if (sybilFlag) {
        workloadService.upsertSybilUserCounts(result.size(), LABELER, jobUser, job.getId());
      }
      return result;
    }

    if (CollectionUtils.isNotEmpty(pendingTaskSessions)) {
      return pendingTaskSessions;
    }
    throw ControllerUtils.badRequest(
        "User workloads exceeded, workload: " + workload.getKey().getValue());
  }

  @Override
  @Transactional
  public Boolean submitTasks(
      SubmitAnswersDTO answers,
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      Boolean isTester) {
    return super.submitTasks(answers, job, batch, batchSetting, jobUser, isTester);
  }

  //  @Override
  //  @Transactional
  //  public TaskSession submitTasks(
  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
  //    return super.submitTasks(answer, taskSessionId, userId, isTester);
  //  }

  @Override
  public void refreshTaskQueue(Job job, Integer repeatCount) {
    Long length = this.taskQueue.length(key.concat(job.getId().toString()));
    if (length > 0) {
      return;
    }
    var taskIdList = jobTaskDao.selectTasksInJobInLimit(job.getId(), job.getAssignDataVolume());
    List<GroupByTaskIdAndCountResultDTO> refreshTaskDistributionStatus =
        jobTaskDao.refreshTaskDistributionStatus(job.getId());
    var refreshTaskDistributionStatusMap = refreshTaskDistributionStatus.toMap(
        GroupByTaskIdAndCountResultDTO::getTaskId, GroupByTaskIdAndCountResultDTO::getCount);

    Map<String, Long> taskIdMap = refreshTaskDistributionStatus.stream()
        .collect(Collectors.toMap(
            k -> k.getTaskId().toString(), GroupByTaskIdAndCountResultDTO::getCount));
    if (taskIdList.size() > taskIdMap.size()) {
      taskIdList.forEach(tid -> {
        if (!taskIdMap.containsKey(tid.toString())) {
          taskIdMap.put(tid.toString(), 0L);
        }
      });
    }

    counterManager.hsetAllJobCount(job.getId(), TASK_COUNTS.value, taskIdMap);

    for (int i = 0; i < repeatCount; i++) {
      int finalI = i;
      var taskIds = taskIdList
          .filter(id -> !refreshTaskDistributionStatusMap.containsKey(id)
              || (refreshTaskDistributionStatusMap.get(id) + finalI) < repeatCount)
          .toList();
      if (!taskIds.isEmpty()) {
        this.taskQueue.pushAll(key.concat(job.getId().toString()), taskIds);
      }
    }
  }

  @Override
  @Transactional
  public void initializeTaskQueue(Batch batch, BatchSetting batchSetting) {

    projectService
        .getProjectById(batch.getProjectId())
        .filter(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
        .ifPresent(p -> jobService.assignJobToExternalBatchJob(batch));

    List<Job> jobs = this.jobService.listJobsByBatchId(batch.getId());
    var jobId = jobs.get(0).getId();

    var taskIdList =
        jobTaskDao.selectTasksInJobInLimit(jobs.get(0).getId(), jobs.get(0).getAssignDataVolume());

    for (int i = 0; i < batchSetting.getAnnotatingTimesAnnotationPerDatapoint(); i++) {
      this.taskQueue.pushAll(key.concat(jobs.get(0).getId().toString()), taskIdList);
    }
    counterManager.setJobCount(jobId, COMPLETE_COUNT.value, 0L);
    Map<String, Long> taskIdMap =
        taskIdList.stream().collect(Collectors.toMap(Object::toString, v -> 0L));
    counterManager.hsetAllJobCount(jobId, TASK_COUNTS.value, taskIdMap);

    this.batchService.update(
        null,
        new UpdateWrapper<Batch>()
            .lambda()
            .eq(Batch::getId, batch.getId())
            .set(Batch::getStatus, Batch.BatchStatus.ASSIGN_JOBS)
            .set(Batch::getUpdatedAt, DateUtils.now()));
  }

  @Override
  public List<BatchSetting.DistributeType> getDistributionTypes() {
    return Arrays.asList(BatchSetting.DistributeType.RAW, BatchSetting.DistributeType.ASSEMBLED);
  }

  @Override
  public List<JobUser.JobUserRole> getRoles() {
    return List.of(LABELER);
  }

  private List<TaskSession> getPendingTaskSessions(JobUser jobUser) {
    var pendingTaskSession =
        jobTaskService.getOngoingTaskSessionsForJobUser(jobUser.getId(), LABELER);
    if (CollectionUtils.isEmpty(pendingTaskSession)) {
      return Collections.EMPTY_LIST;
    }
    return pendingTaskSession;
  }

  private List<TaskSession> getEnoughPendingTaskSessions(
      Job job, Batch batch, JobUser jobUser, BatchSetting batchSetting, int limit) {
    List<TaskSession> taskSessions = this.getCacheTaskId(job, batch, jobUser, batchSetting, limit);
    if (!taskSessions.isEmpty()) {
      if (batchSetting.getHoneyPotSettingForLabeler().equals(BatchSetting.HpSettingType.NORMAL)
          && CollectionUtils.isNotEmpty(batchSetting.getHoneyPotBatches())) {
        honeyPotService.adaptHoneyPotSessionWithSessionIds(
            taskSessions.mapToList(TaskSession::getId), batch, jobUser, false);
      } else if (batchSetting
          .getHoneyPotSettingForLabeler()
          .equals(BatchSetting.HpSettingType.DP_BOUND)) {
        honeyPotService.createHoneyPotsByQuestionIdListAndTaskSessionIdListForDpBound(
            taskSessions, batch, jobUser);
      }
    }
    return taskSessions;
  }

  private List<TaskSession> getTaskSessions(
      Job job, Batch batch, BatchSetting batchSetting, JobUser jobUser) {
    return this.getCacheTaskId(
        job, batch, jobUser, batchSetting, batch.getAnnotatingSubmitRequired());
  }

  private List<TaskSession> getReviseTaskSessions(Batch batch, JobUser jobUser) {
    var reviseLimitBatchSize = batch.getAnnotatingSubmitRequired();
    if (reviseLimitBatchSize > 0) {
      var reviseTasks = jobTaskService.getReviseTaskSessionsForJobUser(
          jobUser.getId(), LABELER, reviseLimitBatchSize);
      jobTaskService.clearWaitingReverseSession(jobUser.getId(), LABELER);
      if (CollectionUtils.isNotEmpty(reviseTasks)) {
        return reviseTasks
            .map(taskSessionService::reCreateTaskSessionWithRevisedRecord)
            .toList();
      }
    }

    return Collections.EMPTY_LIST;
  }

  @SuppressWarnings("unused")
  private List<TaskSession> getCacheTaskId(
      Job job, Batch batch, JobUser jobUser, BatchSetting batchSetting, int limit) {
    Set<Long> notIncluded = Stream.concat(
            this.taskSessionService
                .getTaskSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
                .stream(),
            this.taskSessionService
                .getSkipTaskSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
                .stream()
                .map(SkipTaskSession::getTaskId))
        .toSet();

    Set<Long> jobTaskIds = new HashSet<>(limit);
    var maxTry = Math.max((job.getAssignDataVolume() - notIncluded.size()), 20);
    int retryCount = 0;
    while (limit > 0 && retryCount < maxTry) { // todo: use popList to take by batch
      retryCount++;
      Long taskId = this.taskQueue.pop(key.concat(job.getId().toString()));
      if (Objects.equals(taskId, Long.MIN_VALUE)) {
        limit = 0;
        continue;
      }

      if (!notIncluded.contains(taskId) && !jobTaskIds.contains(taskId)) {
        limit -= 1;
        jobTaskIds.add(taskId);
        counterManager.hIncrementJobCount(job.getId(), TASK_COUNTS.value, taskId.toString(), 1);
        //      } else {
        //        this.taskQueue.push(key.concat(job.getId().toString()), taskId);
      }
    }

    List<TaskSession> taskSessions = this.createTaskSessionCanDuplicate(
        jobUser,
        jobTaskIds.toSet(),
        LABELER,
        DateUtils.now(),
        batchSetting,
        batchSetting.getDistributeType());

    return taskSessions;
  }

  private List<TaskSession> createTaskSessionCanDuplicate(
      JobUser jobUser,
      Collection<Long> jobTasks,
      JobUser.JobUserRole role,
      Timestamp timestamp,
      BatchSetting batchSetting,
      BatchSetting.DistributeType distributeType) {
    return switch (distributeType) {
      case RAW -> jobTasks
          .map(taskId -> taskSessionService.createTaskSessionCanDuplicate(
              jobUser,
              taskId,
              role,
              timestamp,
              batchSetting.getAnnotatingTimesPerDatapointPerUser() + 1))
          .toList();
      case ASSEMBLED, SINGLE -> Collections.EMPTY_LIST;
    };
  }

  @Override
  public TaskSubmitter taskSubmitter() {
    return this;
  }

  @Override
  public TaskDistributor taskDistributor() {
    return this;
  }

  @Override
  public TaskLifecycleManager taskLifecycleManager() {
    return this;
  }
}
