package ai.saharaa.distribution;

import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
import static java.util.stream.Collectors.toMap;

import ai.saharaa.actors.jobs.IndividualJobOtherProcessActor;
import ai.saharaa.config.LabelingMetricsController;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.daos.*;
import ai.saharaa.dto.job.SubmitAnswersDTO;
import ai.saharaa.dto.job.SubmitAnswersItemDTO;
import ai.saharaa.model.*;
import ai.saharaa.model.HoneyPot.HoneyPotSession;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.ObjectUtils;
import akka.actor.ActorRef;
import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public abstract class AbstractTaskLifecycleManager
    implements TaskSubmitter<SubmitAnswersDTO, Boolean> {

  private final ClusterConfiguration clusterConfiguration;
  private final CaptchaService captchaService;
  private final IndividualsService individualsService;
  private final JobUserService jobUserService;
  private final HoneyPotService honeyPotService;
  private final TaskSessionService taskSessionService;
  private final LabelingMetricsController labelingMetricsController;

  @SuppressWarnings("unused")
  private final TaskSessionDao taskSessionDao;

  private final TaskAndReviewSessionDao taskAndReviewSessionDao;

  private final WorkloadLimitService workloadService;

  private final TaskQuestionDao taskQuestionDao;
  private final ResourceDao resourceDao;
  private final NotificationService notificationService;

  protected AbstractTaskLifecycleManager(
      ClusterConfiguration clusterConfiguration,
      CaptchaService captchaService,
      IndividualsService individualsService,
      JobUserService jobUserService,
      HoneyPotService honeyPotService,
      TaskSessionService taskSessionService,
      LabelingMetricsController labelingMetricsController,
      TaskSessionDao taskSessionDao,
      TaskAndReviewSessionDao taskAndReviewSessionDao,
      WorkloadLimitService workloadService,
      TaskQuestionDao taskQuestionDao,
      ResourceDao resourceDao,
      NotificationService notificationService) {
    this.clusterConfiguration = clusterConfiguration;
    this.captchaService = captchaService;
    this.individualsService = individualsService;
    this.jobUserService = jobUserService;
    this.honeyPotService = honeyPotService;
    this.taskSessionService = taskSessionService;
    this.labelingMetricsController = labelingMetricsController;
    this.taskSessionDao = taskSessionDao;
    this.taskAndReviewSessionDao = taskAndReviewSessionDao;
    this.workloadService = workloadService;
    this.taskQuestionDao = taskQuestionDao;
    this.resourceDao = resourceDao;
    this.notificationService = notificationService;
  }

  //  @Override
  //  public TaskSession submitTasks(
  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
  //
  //    TaskSession taskSession =
  //        this.taskSessionService
  //            .getTaskSessionById(taskSessionId)
  //            .orElseThrow(() -> ControllerUtils.badRequest("task session not found"));
  //
  //    this.checkTaskSession(taskSession, userId);
  //
  //    var hpAnswerSubmitted = answer.getSubmitHPAnswer();
  //    var rejectIdList = new ArrayList<Long>();
  //    Boolean bannedInHp = false;
  //    if (hpAnswerSubmitted != null) {
  //      if
  // (!hpAnswerSubmitted.getHoneyPotSession().getTaskSessionId().equals(taskSession.getId())) {
  //        throw ControllerUtils.forbidden("invalid data");
  //      }
  //      var jobUser =
  //          jobUserService
  //              .getById(taskSession.getJobUserId())
  //              .asOpt()
  //              .orElseThrow(() -> ControllerUtils.notFound("job user not found"));
  //      var hpJudgeRes =
  //          honeyPotService.submitAnswerAndJudgeCorrection(
  //              List.of(hpAnswerSubmitted), jobUser, isTester);
  //      rejectIdList.addAll(hpJudgeRes.getLeft());
  //      bannedInHp = hpJudgeRes.getRight();
  //    }
  //    if (bannedInHp) {
  //      return taskSession;
  //    }
  //
  //    taskSession = this.handleSubmitAnswer(answer, taskSession, rejectIdList, isTester);
  //
  //    if (answer.getFeedback() != null && !answer.getFeedback().isBlank()) {
  //      notificationService.noticeNMLabelerJobFeedback(
  //          ControllerUtils.currentUid(),
  //          taskSession.getJobId(),
  //          taskSession.getTaskId(),
  //          answer.getFeedback());
  //    }
  //
  //    return taskSession;
  //  }

  @Override
  public Boolean submitTasks(
      SubmitAnswersDTO answers,
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      Boolean isTester) {

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ip = Web2AuthService.getClientIP(request);

    this.checkFrontendHoneyPot(answers, jobUser.getUserId(), job.getId(), isTester);
    this.checkCaptcha(answers, isTester, jobUser, ip);
    this.checkBanned(jobUser, isTester);

    Pair<List<Long>, Boolean> rejectIdList =
        this.checkHoneypot(answers, jobUser, isTester, batchSetting.getHoneyPotSettingForLabeler());
    //    Pair<List<Long>, Boolean> rejectIdList = this.checkHoneypot(answers, jobUser, isTester,
    // batchSetting, batch);
    if (rejectIdList.getRight()) {
      return Boolean.valueOf(false);
    }

    List<TaskSession> taskSessions = taskSessionService.getTaskSessionByIdList(
        answers.getSubmitAnswers().stream().map(a -> a.getTaskSession().getId()).toList());

    taskSessions.forEach(ts -> this.checkTaskSession(ts, jobUser.getUserId()));
    this.submitAnswers(answers, rejectIdList, batch, job, jobUser, ip, batchSetting, isTester);
    this.updateResourcesStatus(answers.getSubmitAnswers());

    answers.getSubmitAnswers().stream()
        .filter(a -> !a.getSubmitAnswer().getFeedback().isBlank())
        .findFirst()
        .ifPresent(firstFeedBack -> {
          notificationService.noticeAMLabelerJobFeedback(
              jobUser.getUserId(),
              job.getOwnerId(),
              firstFeedBack.getTaskSession().getJobId(),
              firstFeedBack.getTaskSession().getTaskId(),
              firstFeedBack.getSubmitAnswer().getFeedback());
        });
    return Boolean.TRUE;
  }

  private List<TaskSession> submitAnswers(
      SubmitAnswersDTO answers,
      Pair<List<Long>, Boolean> rejectIdList,
      Batch batch,
      Job job,
      JobUser jobUser,
      String ip,
      BatchSetting batchSetting,
      Boolean isTester) {
    if (!isTester) {
      workloadService.filterAnswersByWorkloadLimit(
          answers.getSubmitAnswers(), job.getId(), jobUser.getUserId(), ip);
    }
    if (answers.getSubmitAnswers().isEmpty()) {
      return Collections.EMPTY_LIST;
    }

    labelingMetricsController.handleAnnotate(answers.getSubmitAnswers().size());

    List<TaskSession> result = taskSessionService.submitAnswers(
        answers.getSubmitAnswers(), batch, rejectIdList.getLeft(), batchSetting, isTester);

    if (!batch.getReviewerRequired()) {
      taskAndReviewSessionDao.closeNoReviewRequiredJobIfNeeded(batch, job);
    }
    workloadService.updateJobUserWorkloads(
        job.getId(),
        jobUser.getUserId(),
        answers.getSubmitAnswers().stream()
            .filter(s -> !TaskSession.TaskSessionReviewStatus.REVISED.equals(
                s.getTaskSession().getSpotterStatus()))
            .count(),
        LABELER,
        ip);
    return result;
  }

  private void updateResourcesStatus(List<SubmitAnswersItemDTO> submitAnswers) {
    // handle if question type is image-upload, will set cloud storage deleted to false
    CompletableFuture.allOf(submitAnswers.stream()
            .map(submitAnswer -> CompletableFuture.runAsync(() -> {
              submitAnswer.getSubmitAnswer().getAnswers().forEach(answerDetailsDTO -> {
                var questionId = answerDetailsDTO.getQuestionId();
                TaskQuestion taskQuestion =
                    taskQuestionDao.getTaskQuestionById(questionId).orElse(null);
                if (taskQuestion != null
                    && TaskQuestion.QuestionType.IMAGE_UPLOAD.equals(
                        taskQuestion.getQuestionType())) {
                  resourceDao.unDeleteTaskQuestionByResourceId(
                      Long.parseLong(answerDetailsDTO.getAnswer().replaceAll("\"", "")));
                  answerDetailsDTO.setAnswerImageUploaded(true);
                }
              });
            }))
            .toArray(CompletableFuture[]::new))
        .join();
  }

  private void checkTaskSession(TaskSession taskSession, Long userId) {
    if (taskSession.getDeleted()) {
      throw ControllerUtils.badRequest("task session not found");
    }
    if (!Objects.equals(taskSession.getStatus(), TaskSession.TaskSessionStatus.PENDING)) {
      throw ControllerUtils.badRequest("task session not pending");
    }

    if (!Objects.equals(taskSession.getUserId(), userId)) {
      throw ControllerUtils.badRequest(
          "you are not allowed to submit answer for this task session");
    }
  }

  //  private Pair<List<Long>, Boolean> checkHoneypotPre(
  //      SubmitAnswersDTO answers, JobUser jobUser, Boolean isTester, BatchSetting batchSetting,
  // Batch batch) {
  //    if (!batchSetting.getHoneyPotSettingForLabeler().equals(BatchSetting.HpSettingType.NORMAL))
  //      return this.checkHoneypotDpBound(answers, jobUser, isTester, batch, batchSetting);
  //    return this.checkHoneypot(answers, jobUser, isTester);
  //  }
  //  private Pair<List<Long>, Boolean> checkHoneypotDpBound(
  //      SubmitAnswersDTO answers, JobUser jobUser, Boolean isTester, Batch batch, BatchSetting
  // batchSetting) {
  //    if (isTester) {
  //      return Pair.of(List.<Long>of(), false);
  //    }
  //    var basicQuestionIdSet = taskService.getBasicLabelingQuestions(batch.getId(),
  // TaskList.TaskListType.LABEL).map(TaskQuestion::getId).collect(Collectors.toSet());
  //    var taskIdList = answers.getSubmitAnswers().map(ans ->
  // ans.getTaskSession().getTaskId()).collect(Collectors.toSet());
  //    var extraQuestionMap = taskQuestionExtraDao.getQuestions(batch.getId(),
  // TaskQuestionExtra.ExtraTaskQuestionUsage.HP, taskIdList).toMap(TaskQuestionExtra::getTaskId,
  // Function.identity());
  //    var res = new ArrayList<Long>();
  //    var hpAnswerList = answers.getSubmitAnswers();
  //    var rejectIdList =
  //        honeyPotService.submitAnswerAndJudgeCorrectionForDpBounded(hpAnswerList,
  // extraQuestionMap, jobUser, isTester);
  //    return rejectIdList;
  //  }
  private Pair<List<Long>, Boolean> checkHoneypot(
      SubmitAnswersDTO answers,
      JobUser jobUser,
      Boolean isTester,
      BatchSetting.HpSettingType honeyPotSettingForLabeler) {
    var answerList = answers.getSubmitAnswers();
    var taskSessionIdList =
        answerList.stream().map(a -> a.getTaskSession().getId()).toList();
    var hpSessions = honeyPotService.getSessionByTaskSessionIdList(taskSessionIdList);
    if (taskSessionIdList.size() < hpSessions.size()) {
      throw ControllerUtils.badRequest("invalid data"); // hp session submitted are not enough.
    }
    var hpSessionsMap =
        hpSessions.stream().collect(toMap(HoneyPotSession::getTaskSessionId, Function.identity()));

    var hpAnswerList = answers.getSubmitHPAnswers();
    var notIncludeRec = hpAnswerList.stream()
        .filter(ha -> !hpSessionsMap.containsKey(ha.getTaskSessionId()))
        .findAny();
    if (notIncludeRec.isPresent()) {
      throw ControllerUtils.badRequest("invalid data"); // wrong hp session submitted.
    }
    var rejectIdList = honeyPotService.submitAnswerAndJudgeCorrection(
        hpAnswerList, jobUser, isTester, honeyPotSettingForLabeler);
    return rejectIdList;
  }

  private void checkBanned(JobUser jobUser, Boolean isTester) {
    if (individualsService.checkUserBannedInPlatform(jobUser.getUserId())) {
      if (isTester) {
        jobUserService.removeUserBanningInPlatform(jobUser.getUserId());
      } else {
        throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(jobUser.getUserId()));
      }
    }
  }

  private void checkFrontendHoneyPot(
      SubmitAnswersDTO answers, Long userId, Long jobId, Boolean isTester) {
    if (isTester) {
      return;
    }

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

    var submitTimeInHeader = request.getHeader("X-Submit-Time");
    StringBuilder stringBuilder = new StringBuilder();
    try (BufferedReader reader = request.getReader()) {
      String line;
      while ((line = reader.readLine()) != null) {
        stringBuilder.append(line).append('\n');
      }
    } catch (IOException e) {
      throw ControllerUtils.notFound("Job user not found");
    }

    String requestBody = stringBuilder.toString();
    var reqBodyMap = ObjectUtils.fromJsonToMapBasic(requestBody);
    if (reqBodyMap.isPresent()) {
      var newDetailString = String.join(",", reqBodyMap.get().keySet());
      this.clusterConfiguration
          .getIndividualJobOtherProcessActor()
          .tell(
              new IndividualJobOtherProcessActor.OpSubmitAnswersCheckRisk(
                  jobId,
                  userId,
                  newDetailString,
                  submitTimeInHeader,
                  answers
                      .getSubmitAnswers()
                      .get(answers.getSubmitAnswers().size() - 1)
                      .getTaskSession()
                      .getCreatedAt()),
              ActorRef.noSender());
    }
  }

  private void checkCaptcha(
      SubmitAnswersDTO answers, Boolean isTester, JobUser jobUser, String ip) {
    var firstSessionOpt = answers.getSubmitAnswers().stream().findFirst();
    if (firstSessionOpt.isEmpty()) return;
    var firstSessionId = firstSessionOpt.get().getTaskSession().getId();
    if (!isTester && captchaService.isRecaptchaRequired(firstSessionId, jobUser.getUserId())) {
      captchaService.verifyRecaptchaToken(answers.getCaptchaToken(), ip);
    }
  }

  //  private TaskSession handleSubmitAnswer(
  //      SubmitAnswerDTO answer, TaskSession taskSession, List<Long> rejectIdList, Boolean
  // isTester) {
  //
  //    var j = jobService.getJobById(taskSession.getJobId()).get();
  //    var b = batchService.getBatchById(j.getBatchId()).get();
  //    labelingMetricsController.handleAnnotate(1);
  //    var submit = SubmitAnswersItemDTO.builder()
  //        .taskSession(taskSession)
  //        .submitAnswer(SubmitAnswerDTO.builder()
  //            .answers(answer.getAnswers())
  //            .feedback(answer.getFeedback())
  //            .build())
  //        .time(DateUtils.diff(DateUtils.now(), taskSession.getCreatedAt()))
  //        .build();
  //    if (b.getReviewerRequired()) {
  //      return taskSessionService
  //          .submitAnswers(List.of(submit), b.getProjectId(), rejectIdList, isTester)
  //          .first();
  //    } else {
  //
  //      var session = taskSessionService
  //          .submitNoReviewAnswers(List.of(submit), b.getProjectId(), rejectIdList, isTester)
  //          .first();
  //      taskAndReviewSessionDao.closeNoReviewRequiredJobIfNeeded(b, j);
  //      return session;
  //    }
  //  }
}
