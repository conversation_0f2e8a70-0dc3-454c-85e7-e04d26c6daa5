package ai.saharaa.distribution.Contants;

import ai.saharaa.enums.BaseEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

public class Constants {

  public enum CACHE_KEY_FOR_JOB {
    COMPLETE_COUNT("COMPLETE_COUNT"),
    //    EFFECT_COUNT("EFFECT_COUNT"),
    TASK_COUNTS("TASK_COUNTS"), // hash in redis, only for raw
    TASK_ID("TASK_ID"); // only for single

    @EnumValue
    @JsonValue
    public final String value;

    CACHE_KEY_FOR_JOB(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }
  }

  public enum CACHE_KEY_FOR_JOB_USER implements BaseEnum {
    SUBMITTED_COUNT("SUBMITTED_COUNT"),
    USER_RIGHT_COUNT("USER_RIGHT_COUNT"),
    COMPLETE_COUNT("COMPLETE_COUNT"),
    PENDING_PIPELINE_COUNT("PENDING_PIPELINE_COUNT");
    //    COMPLETE_COUNT("COMPLETE_COUNT"),
    //    COMPLETE_LIST("COMPLETE_LIST"),
    //    SKIP_LIST("SKIP_LIST");

    @EnumValue
    @JsonValue
    public final String value;

    CACHE_KEY_FOR_JOB_USER(String value) {
      this.value = value;
    }

    @Override
    public String getValue() {
      return value;
    }
  }
}
