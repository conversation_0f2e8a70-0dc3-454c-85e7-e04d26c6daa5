package ai.saharaa.distribution;

import ai.saharaa.model.*;
import ai.saharaa.services.*;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DefaultTaskVisitorProvider implements TaskVisitorProvider, ApplicationContextAware {

  private ApplicationContext applicationContext;

  @Override
  public TaskVisitor getTaskVisitor(
      BatchSetting.DistributeType distributeType, JobUser.JobUserRole role) {

    Map<String, TypeProvider> beansOfType =
        this.applicationContext.getBeansOfType(TypeProvider.class);

    List<TypeProvider> typeProviders =
        beansOfType.values().stream().filter(x -> x.getRoles().contains(role)).toList();

    if (CollectionUtils.isEmpty(typeProviders)) {
      throw new IllegalStateException("No distribution strategy found for role: " + role);
    }

    TypeProvider typeProvider = typeProviders.stream()
        .filter(x -> x.getDistributionTypes().contains(distributeType))
        .findAny()
        .orElseThrow(() -> new IllegalStateException(
            "No distribution strategy found for type: " + distributeType));

    Class<TaskVisitor> aClass = TaskVisitor.class;
    return aClass.cast(typeProvider);
  }

  //  @Override
  //  @SuppressWarnings("TypeParameterUnusedInFormals")
  //  public <T> T submitTasks(
  //      SubmitAnswersDTO answers, Long userId, Boolean isTester, String submitTimeInHeader) {
  //
  //    if (CollectionUtils.isEmpty(answers.getSubmitAnswers())) {
  //      throw ControllerUtils.badRequest("No answers submitted");
  //    }
  //
  //    var jobId = answers.getSubmitAnswers().stream().findAny().get().getTaskSession().getJobId();
  //    Job job = this.getAndCheckJob(jobId);
  //    BatchDetailsDTO batch = this.getAndCheckBatch(job.getBatchId());
  //    JobUser jobUser = this.getJobUser(jobId, userId);
  //    if (Objects.equals(jobUser.getActive(), Boolean.FALSE)) {
  //      if (!isTester) {
  //        throw ControllerUtils.badRequest(
  //            "You've been banned from this task because we've detected malicious activity.");
  //      } else {
  //        jobUserService.unbanInJob(userId, jobId);
  //      }
  //    }
  //
  //    TaskSubmitter taskSubmitter =
  //        this.getDistribution(
  //            batch.getBatchSetting().getDistributeType(), jobUser.getRole(),
  // TaskSubmitter.class);
  //
  //    Object o =
  //        taskSubmitter.submitTasks(
  //            answers, job, batch.getBatch(), batch.getBatchSetting(), jobUser, isTester);
  //
  //    return (T) o;
  //  }

  //  @Override
  //  public TaskSession submitTasks(
  //      SubmitAnswerDTO answer,
  //      Long taskSessionId,
  //      Long userId,
  //      Boolean isTester,
  //      JobUser.JobUserRole role) {
  //
  //    // There is no need to distinguish between types
  //    return this.getDistribution(BatchSetting.DistributeType.SINGLE, role, TaskSubmitter.class)
  //        .submitTasks(answer, taskSessionId, userId, isTester);
  //  }
  @Override
  public void setApplicationContext(
      org.springframework.context.ApplicationContext applicationContext) throws BeansException {
    this.applicationContext = applicationContext;
  }
}
