package ai.saharaa.distribution.workload;

import org.springframework.stereotype.Component;

@Component
public class WorkloadManager {

  //  public WorkloadStrategy getWorkloadStrategy(JobUser.JobUserRole role, WorkloadContext context)
  // {
  //    if (JobUser.JobUserRole.LABELER.equals(role)) {
  //      return new LabelerWorkloadStrategy(context);
  //    }
  //
  //    if (JobUser.JobUserRole.REVIEWER.equals(role)) {
  //      return new ReviewerWorkloadStrategy(context);
  //    }
  //
  //    throw new IllegalArgumentException("Invalid role: " + role);
  //  }
}
