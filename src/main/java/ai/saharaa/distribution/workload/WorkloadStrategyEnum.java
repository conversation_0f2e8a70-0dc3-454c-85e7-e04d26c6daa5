package ai.saharaa.distribution.workload;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;

public enum WorkloadStrategyEnum {
  JOB((context, sessions) -> {
    if (context.getWorkloadMaxPerUser() == null || context.getWorkloadMaxPerUser() == 0) {
      return Integer.MAX_VALUE;
    }
    return context.getWorkloadMaxPerUser() - sessions.size();
  }),

  HOURLY((context, sessions) -> {
    if (context.getWorkloadMaxPerUserHour() == null || context.getWorkloadMaxPerUserHour() == 0) {
      return Integer.MAX_VALUE;
    }
    return context.getWorkloadMaxPerUserHour()
        - (int) sessions.stream()
            .filter(session -> session
                .getCreatedAt()
                .after(Timestamp.valueOf(LocalDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"))
                    .truncatedTo(ChronoUnit.HOURS)
                    .minusNanos(1))))
            .count();
  }),

  DAILY((context, sessions) -> {
    if (context.getWorkloadMaxPerUserDay() == null || context.getWorkloadMaxPerUserDay() == 0) {
      return Integer.MAX_VALUE;
    }
    return context.getWorkloadMaxPerUserDay()
        - (int) sessions.stream()
            .filter(session -> session
                .getCreatedAt()
                .after(Timestamp.valueOf(LocalDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"))
                    .truncatedTo(ChronoUnit.DAYS)
                    .minusNanos(1))))
            .count();
  }),
  DAILY_TASK_LIMIT((context, sessions) -> context.getWorkloadPlatformPerUser()
      - (int) sessions.stream()
          .filter(session -> session
              .getCreatedAt()
              .after(Timestamp.valueOf(LocalDateTime.ofInstant(Instant.now(), ZoneId.of("UTC"))
                  .truncatedTo(ChronoUnit.DAYS)
                  .minusNanos(1))))
          .count()),

  SYBIL((context, sessions) -> {
    if (!context.getSybil()) {
      return Integer.MAX_VALUE;
    }
    return context.getSybilThreshold();
  });

  private final WorkloadStrategy strategy;

  WorkloadStrategyEnum(WorkloadStrategy strategy) {
    this.strategy = strategy;
  }

  public int getQuota(WorkloadContext context, List<WorkloadSession> sessions) {
    return strategy.canTakeTask(context, sessions);
  }
}
