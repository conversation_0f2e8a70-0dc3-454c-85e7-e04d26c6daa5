package ai.saharaa.distribution.workload;

import ai.saharaa.distribution.CommonCounterManager;
import ai.saharaa.distribution.Contants.Constants;
import ai.saharaa.model.Batch;
import ai.saharaa.model.BatchSetting;
import ai.saharaa.model.Job;
import ai.saharaa.model.JobUser;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Builder
@Data
@Slf4j
public class WorkloadContext {
  private Batch batch;
  private BatchSetting batchSetting;
  private Job job;
  private JobUser.JobUserRole role;
  private Long sybilCount;
  private Boolean sybil;
  private Integer dailyUserLimit;
  private CommonCounterManager commonCounterManager;

  public Integer getSybilThreshold() {
    var jobUserSybilPercent = batchSetting.getWorkloadMaxSybilJobUserPercent();
    var jobReviewerSybilPercent = batchSetting.getWorkloadMaxSybilJobReviewerPercent();
    var jobCompletionCount = getJobCompletionCount();
    var sybilCount = this.sybilCount.intValue();

    if (Objects.equals(this.role, JobUser.JobUserRole.LABELER)) {
      if (jobUserSybilPercent == null || jobUserSybilPercent == 10000) {
        return Integer.MAX_VALUE;
      }
      int totalThreshold = jobUserSybilPercent * getTaskTotalCount() / 10000 - sybilCount;
      int dynamicThreshold = jobCompletionCount * jobUserSybilPercent / 10000 - sybilCount;
      return Math.min(totalThreshold, dynamicThreshold);
    }
    if (jobReviewerSybilPercent == null || jobReviewerSybilPercent == 10000) {
      return Integer.MAX_VALUE;
    }
    int totalThreshold = jobReviewerSybilPercent * getTaskTotalCount() / 10000 - sybilCount;
    int dynamicThreshold = jobCompletionCount
            * jobReviewerSybilPercent
            * batch.getReviewingTimesReviewPerDatapoint()
            / 10000
        - sybilCount;
    return Math.min(totalThreshold, dynamicThreshold);
  }

  public Integer getJobCompletionCount() {
    return Math.toIntExact(this.commonCounterManager.getJobCount(
        job.getId(), Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value));
  }

  public Integer getTaskTotalCount() {
    if (Objects.equals(this.role, JobUser.JobUserRole.LABELER)) {
      return this.job.getAssignDataVolume()
          * this.batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    }

    if (Objects.equals(this.role, JobUser.JobUserRole.REVIEWER)) {
      return this.job.getAssignDataVolume()
          * this.batchSetting.getAnnotatingTimesAnnotationPerDatapoint()
          * this.batch.getReviewingTimesReviewPerDatapoint();
    }

    throw new IllegalArgumentException("Invalid role: " + role);
  }

  public Integer getWorkloadMaxPerUser() {
    if (Objects.equals(this.role, JobUser.JobUserRole.LABELER)) {
      return batchSetting.getWorkloadMaxPerJobUser() == null
          ? Integer.MAX_VALUE
          : batchSetting.getWorkloadMaxPerJobUser();
    } else {
      return batchSetting.getWorkloadMaxPerJobReviewer() == null
          ? Integer.MAX_VALUE
          : batchSetting.getWorkloadMaxPerJobReviewer();
    }
  }

  public Integer getWorkloadMaxPerUserHour() {
    if (Objects.equals(this.role, JobUser.JobUserRole.LABELER)) {
      return batchSetting.getWorkloadMaxPerJobUserHour() == null
          ? Integer.MAX_VALUE
          : batchSetting.getWorkloadMaxPerJobUserHour();
    } else {
      return batchSetting.getWorkloadMaxPerJobReviewerHour() == null
          ? Integer.MAX_VALUE
          : batchSetting.getWorkloadMaxPerJobReviewerHour();
    }
  }

  public Integer getWorkloadMaxPerUserDay() {
    if (Objects.equals(this.role, JobUser.JobUserRole.LABELER)) {
      return batchSetting.getWorkloadMaxPerJobUserDay() == null
          ? Integer.MAX_VALUE
          : batchSetting.getWorkloadMaxPerJobUserDay();
    } else {
      return batchSetting.getWorkloadMaxPerJobReviewerDay() == null
          ? Integer.MAX_VALUE
          : batchSetting.getWorkloadMaxPerJobReviewerDay();
    }
  }

  public Integer getWorkloadPlatformPerUser() {
    return dailyUserLimit;
  }
}
