package ai.saharaa.distribution;

import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class DefaultTaskQueue implements TaskQueue {

  private final RedisTemplate<String, Object> taskQueue;

  public DefaultTaskQueue(
      @Qualifier("redisTemplateForTaskQueue") RedisTemplate<String, Object> taskQueue) {
    this.taskQueue = taskQueue;
  }

  @Override
  public RedisTemplate<String, Object> get() {
    return this.taskQueue;
  }

  @Override
  public Long push(String key, Long value) {
    return this.taskQueue.opsForList().rightPush(key, value.toString());
  }

  @Override
  public Long pushAll(String key, List<Long> value) {
    return this.taskQueue.opsForList().rightPushAll(key, value.toArray());
  }

  @Override
  public Long pop(String key) {
    Object obj = this.taskQueue.opsForList().leftPop(key);
    return Objects.isNull(obj) ? Long.MIN_VALUE : Long.valueOf(obj.toString());
  }

  @Override
  public List<Long> popList(String key, Long count) {
    List<Object> objs = this.taskQueue.opsForList().leftPop(key, count);
    return objs.isEmpty()
        ? List.of()
        : objs.map(obj -> Long.valueOf(obj.toString())).toList();
  }

  @Override
  public Long length(String key) {
    return this.taskQueue.opsForList().size(key);
  }
}
