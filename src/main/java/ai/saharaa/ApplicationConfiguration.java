package ai.saharaa;

import ai.saharaa.config.UserWalletExtractor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.daos.season.SeasonDao;
import ai.saharaa.extensions.SpringExtension;
import ai.saharaa.model.season.Season;
import ai.saharaa.services.season.RuleService;
import ai.saharaa.utils.DateUtils;
import akka.actor.ActorSystem;
import akka.management.cluster.bootstrap.ClusterBootstrap;
import akka.management.javadsl.AkkaManagement;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.logging.LoggingMeterRegistry;
import io.micrometer.core.instrument.logging.LoggingRegistryConfig;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AdviceMode;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.aspectj.EnableSpringConfigured;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.filter.ShallowEtagHeaderFilter;
import org.zalando.logbook.attributes.AttributeExtractor;
import org.zalando.logbook.autoconfigure.LogbookAutoConfiguration;
import org.zalando.logbook.core.attributes.CompositeAttributeExtractor;

@EnableAsync
@Configuration
@EnableScheduling
@EnableSpringConfigured
@EnableTransactionManagement(mode = AdviceMode.ASPECTJ)
class ApplicationConfiguration {
  private final Logger logger = LoggerFactory.getLogger(ApplicationConfiguration.class);

  @Value("${akka.cluster.name}")
  private String clusterName;

  private final ApplicationContext applicationContext;
  private final String env;
  private final String akkaMode;

  public ApplicationConfiguration(
      ApplicationContext applicationContext,
      @Value("${ai.saharaa.platform.env}") String env,
      @Value("${ai.saharaa.akka.mode}") String akkaMode) {
    this.applicationContext = applicationContext;
    this.env = env;
    this.akkaMode = akkaMode;
  }

  @Bean(destroyMethod = "terminate")
  public ActorSystem actorSystem(SpringExtension springExtension, SeasonDao seasonDao) {
    var cfgFile = "akka/akka-${akkaMode}.conf";
    logger.info("loading akka config: {}", cfgFile);
    Config cfg = ConfigFactory.load(cfgFile);
    logger.info("akka configuration: {}", cfg);
    ActorSystem actorSystem =
        ActorSystem.create(clusterName, cfg, applicationContext.getClassLoader());
    springExtension.initialize(applicationContext);
    logger.info("actor system started, cluster name: {}", clusterName);

    actorSystem.actorOf(springExtension.props("simpleClusterListener"), "ClusterListener");

    // Akka Management hosts the HTTP routes used by bootstrap
    AkkaManagement.get(actorSystem).start();

    // Starting the bootstrap process needs to be done explicitly
    ClusterBootstrap.get(actorSystem).start();

    var seasonName = env.trim().equalsIgnoreCase("dev") ? "alpha-dev" : "alpha";

    Optional<Season> season = seasonDao.getByName(seasonName);
    if (season.isEmpty()) {
      logger.info("create season ${seasonName} for dev env");
      seasonDao.create(Season.builder()
          .name(seasonName)
          .ruleVersion(RuleService.Version.MARCH)
          .startedAt(DateUtils.add(DateUtils.now(), Duration.ofHours(-1)))
          .endedAt(DateUtils.add(DateUtils.now(), Duration.ofDays(200)))
          .build());
    }

    return actorSystem;
  }

  @Bean
  public ClusterConfiguration clusterConfiguration(ActorSystem actorSystem) {
    return new ClusterConfiguration(actorSystem, applicationContext);
  }

  @Bean
  public FilterRegistrationBean<ShallowEtagHeaderFilter> shallowEtagHeaderFilter() {
    logger.info("register etag  header filter");
    FilterRegistrationBean<ShallowEtagHeaderFilter> filterRegistrationBean =
        new FilterRegistrationBean<>(new ShallowEtagHeaderFilter());
    filterRegistrationBean.addUrlPatterns("/api/resources/download/**");
    filterRegistrationBean.setName("etagFilter");
    return filterRegistrationBean;
  }

  // Configure Micrometer to log only the @Timed annotated methods. Everything else are still
  // published to prometheus.
  @Bean
  public MeterRegistry loggingMeterRegistry() {
    var registry = new LoggingMeterRegistry(LoggingRegistryConfig.DEFAULT, Clock.SYSTEM);
    registry.config().meterFilter(MeterFilter.denyUnless(a -> a.getName()
        .equals(TimedAspect.DEFAULT_METRIC_NAME)));
    return registry;
  }

  // Inject user wallet address into request logging. BeanPostProcessor might be a better way to do
  // this.
  @Bean
  public AttributeExtractor getAttributeExtractor(
      LogbookAutoConfiguration logbookAutoConfiguration, ObjectMapper objectMapper) {
    var extractor = logbookAutoConfiguration.getAttributeExtractor(objectMapper);
    return new CompositeAttributeExtractor(List.of(extractor, new UserWalletExtractor()));
  }
}
