package ai.saharaa.controllers;

import static ai.saharaa.utils.Constants.*;

import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.cloudstorage.CreateCloudStorageDTO;
import ai.saharaa.dto.cloudstorage.GetUploadLinkDTO;
import ai.saharaa.dto.cloudstorage.GetUploadLinkResultDTO;
import ai.saharaa.model.CloudStorage;
import ai.saharaa.model.PageResult;
import ai.saharaa.services.ICloudStorageService;
import ai.saharaa.services.UserService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import java.util.Arrays;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/cloud-storage")
public class CloudStorageController {
  private final ICloudStorageService cloudStorageService;
  private final UserService userService;

  public CloudStorageController(ICloudStorageService cloudStorageService, UserService userService) {
    this.cloudStorageService = cloudStorageService;
    this.userService = userService;
  }

  @RequestMapping("/list")
  public ApiResult<PageResult<CloudStorage>> getUserCloudStorage(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var pageData = ControllerUtils.safePageRange(page, size);
    var r = cloudStorageService.listUserCloudStorages(
        ControllerUtils.currentUid(), pageData.getFirst(), pageData.getSecond());
    return r.toPageResult().toApiResult();
  }

  @RequestMapping("/{id}")
  public ApiResult<CloudStorage> getCloudStorageById(@PathVariable Long id) {
    var res = cloudStorageService
        .getCloudStorageById(id)
        .filter(r -> !r.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Resource not found"))
        .filter(r -> r.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this resource"));
    return res.toApiResult();
  }

  @PostMapping
  public ApiResult<CloudStorage> createStorage(@Valid @RequestBody CreateCloudStorageDTO storage) {
    var st = CloudStorage.builder()
        .provider(storage.getProvider())
        .ownerId(ControllerUtils.currentUid())
        .authAccount(storage.getAuthAccount())
        .bucket(storage.getBucket())
        .prefix(storage.getPrefix())
        .build();
    return cloudStorageService.createCloudStorage(st).toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteStorage(@PathVariable Long id) {
    var st = cloudStorageService
        .getCloudStorageById(id)
        .filter(r -> !r.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Resource not found"))
        .filter(r -> r.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this resource"));
    cloudStorageService.deleteCloudStorageById(st.getId());
  }

  @PostMapping("/getUploadLink")
  public ApiResult<GetUploadLinkResultDTO> getUploadLink(@RequestBody GetUploadLinkDTO dto) {
    var uid = ControllerUtils.currentUid();
    userService
        .getUserById(uid)
        .filter(
            u -> Arrays.asList(ROLE_ADMIN, ROLE_REQUESTER, ROLE_ACCOUNT_MANAGER, ROLE_QUEUE_MANAGER)
                .contains(u.getRole()))
        .ensurePresent(() -> ControllerUtils.notFound("invalid user"));
    return cloudStorageService.getUploadLink(dto).toApiResult();
  }
}
