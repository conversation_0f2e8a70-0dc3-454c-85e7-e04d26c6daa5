package ai.saharaa.controllers;

import static ai.saharaa.utils.ActorUtils.askWithDefault;

import ai.saharaa.actors.PingActor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.model.SimpleResult;
import ai.saharaa.model.TaskSession;
import java.sql.*;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/hello")
public class HelloController {

  private final ClusterConfiguration clusterConfiguration;
  private Logger log = LoggerFactory.getLogger(HelloController.class);

  public HelloController(ClusterConfiguration clusterConfiguration) {
    this.clusterConfiguration = clusterConfiguration;
  }

  @GetMapping("greet")
  public Optional<TaskSession> greet(
      @RequestParam("name") String name, @RequestParam("id") long id) {
    CompletableFuture<SimpleResult<PingActor.Pong>> result =
        askWithDefault(clusterConfiguration.getPingActor(), new PingActor.Cmd(id, name, "haha"));
    var r = result.get();
    log.info("haha, got");
    return r.getResult().data;
  }
}
