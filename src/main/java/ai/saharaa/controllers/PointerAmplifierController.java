package ai.saharaa.controllers;

import ai.saharaa.dto.ApiResult;
import ai.saharaa.model.UserPointAmplifier;
import ai.saharaa.services.PointAmplifierService;
import ai.saharaa.utils.ControllerUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/pointAmplifier")
public class PointerAmplifierController {
  private final PointAmplifierService pointAmplifierService;

  public PointerAmplifierController(PointAmplifierService pointAmplifierService) {
    this.pointAmplifierService = pointAmplifierService;
  }

//  @PostMapping("/activate")
//  public ApiResult<Boolean> activatePointAmplifier(@RequestParam() Long userId) {
//    var uid = ControllerUtils.currentUid();
//    if (!uid.equals(userId)) {
//      return ApiResult.fail("User id does not match the current user id.");
//    }
//    var userPointAmplifier = pointAmplifierService.getUnActivatedPointAmplifierByUserId(uid);
//    if (!userPointAmplifier.isPresent()) {
//      return ApiResult.fail(
//          "User point amplifier not found, maybe the user has not participated in the Olympic season.");
//    }
//    return ApiResult.success(pointAmplifierService.activatePointAmplifier(uid));
//  }

  @GetMapping("/get")
  public ApiResult<UserPointAmplifier> getUserPointAmplifierByUserId() {
    var uid = ControllerUtils.currentUid();
    var userPointAmplifier = pointAmplifierService.getUserPointAmplifierByUserId(uid);
    if (!userPointAmplifier.isPresent()) {
      return ApiResult.fail(
        "User point amplifier not found, maybe the user has not participated in the Olympic season.");
    }
    return ApiResult.success(userPointAmplifier.get());
  }
}
