package ai.saharaa.controllers;

import ai.saharaa.dto.ApiResult;
import ai.saharaa.model.DataTask;
import ai.saharaa.services.DataTaskService;
import ai.saharaa.utils.ControllerUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Objects;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/data-tasks")
@Tag(name = "Data Task Service", description = "Data Task service API")
public class DataTaskController {
  private final DataTaskService dataTaskService;

  public DataTaskController(DataTaskService dataTaskService) {
    this.dataTaskService = dataTaskService;
  }

  @GetMapping("/{id}")
  public ApiResult<DataTask> getDataTaskById(@PathVariable Long id) {
    var b = dataTaskService
        .getDataTaskById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Data Task not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid())
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's data task"));

    return b.toApiResult();
  }
}
