package ai.saharaa.controllers;

import ai.saharaa.config.ChainConfig;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.services.WorkloadLimitService;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/configs")
public class ConfigController {
  private final ChainConfig chainConfig;

  private final WorkloadLimitService workloadLimitService;

  public ConfigController(ChainConfig chainConfig, WorkloadLimitService workloadLimitService) {
    this.chainConfig = chainConfig;
    this.workloadLimitService = workloadLimitService;
  }

  @GetMapping("/chain")
  public ApiResult<ChainConfig.JsonChainConfig> getChainConfig() {
    return ApiResult.success(chainConfig.getJson());
  }

  @GetMapping("/workload/platform-user-daily")
  public ApiResult<Integer> getPlatformUserDailyWorkload() {
    return ApiResult.success(workloadLimitService.getPlatformUserDailyWorkload());
  }

  @PutMapping("/workload/platform-user-daily")
  @IsAdmin
  public ApiResult<Boolean> setPlatformUserDailyWorkload(@RequestParam Integer workload) {
    workloadLimitService.setPlatformUserDailyWorkload(workload);
    return ApiResult.success(true);
  }
}
