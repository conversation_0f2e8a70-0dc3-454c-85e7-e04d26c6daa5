package ai.saharaa.controllers;

import ai.saharaa.actors.achievement.AchievementRouter;
import ai.saharaa.actors.settings.ArraySettingsActor;
import ai.saharaa.actors.settings.SingleValueSettingsActor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.AvailabilityDTO;
import ai.saharaa.dto.SimpleDataDTO;
import ai.saharaa.dto.cloudstorage.CloudUploadSettingDTO;
import ai.saharaa.model.SimpleResult;
import ai.saharaa.model.User.RegisterType;
import ai.saharaa.model.UserSetting;
import ai.saharaa.services.AwsService;
import ai.saharaa.services.VaultService;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.Constants.OlympusViewType;
import ai.saharaa.utils.ControllerUtils;
import akka.actor.ActorRef;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/user-settings")
public class UserSettingsController {
  private final Logger log = org.slf4j.LoggerFactory.getLogger(UserSettingsController.class);

  private final ClusterConfiguration clusterConfiguration;
  private AwsService awsService;
  private final VaultService vaultService;

  public UserSettingsController(
      ClusterConfiguration clusterConfiguration,
      AwsService awsService,
      VaultService vaultService,
      AchievementRouter achievementRouter) {
    this.clusterConfiguration = clusterConfiguration;
    this.awsService = awsService;
    this.vaultService = vaultService;
  }

  @GetMapping("/topics")
  public ApiResult<List<String>> getTopics() {
    return readSettings(this.clusterConfiguration.getUserTopicsActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/topics")
  public ApiResult<List<String>> updateTopics(
      @Valid @RequestBody SimpleDataDTO<List<String>> topics) {
    return updateSettings(this.clusterConfiguration.getUserTopicsActor(), topics)
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @DeleteMapping("/topics")
  public void deleteTopic(@Valid @RequestBody SimpleDataDTO<String> topic) {
    var writeOp = new ArraySettingsActor.OpDel(ControllerUtils.currentUid(), topic.getData());
    var r = ActorUtils.<SimpleResult<Boolean>, ArraySettingsActor.OpDel>askWithDefault(
        this.clusterConfiguration.getUserTopicsActor(), writeOp);
    var result = r.get();
    if (!result.getResult()) {
      throw ControllerUtils.notFound("topic does not exist");
    }
  }

  @GetMapping("/regions")
  public ApiResult<List<String>> getRegions() {
    return readSettings(this.clusterConfiguration.getUserRegionsActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/regions")
  public ApiResult<List<String>> updateRegions(
      @Valid @RequestBody SimpleDataDTO<List<String>> topic) {
    return updateSettings(this.clusterConfiguration.getUserRegionsActor(), topic)
        .map(v -> v.map(UserSetting::getContent).toList());
  }

  @GetMapping("/tutorial")
  public ApiResult<List<String>> getTutorials() {
    return readSettings(this.clusterConfiguration.getUserTutorialActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/tutorial")
  public ApiResult<List<String>> updateTutorials(
      @Valid @RequestBody SimpleDataDTO<List<String>> tutorial) {
    if (tutorial.getData().size() > 20) {
      throw ControllerUtils.badRequest("tutorials should not exceed 20");
    }

    var tutorialData = readSettings(this.clusterConfiguration.getUserTutorialActor())
        .map(r -> r.map(UserSetting::getContent).toList());
    var lastTutorials = tutorialData.getData();

    Set<String> set = new HashSet<>(lastTutorials);
    set.addAll(tutorial.getData());

    if (set.size() > 20) {
      throw ControllerUtils.badRequest("tutorials should not exceed 20");
    }
    return updateSettings(
            this.clusterConfiguration.getUserTutorialActor(),
            SimpleDataDTO.<List<String>>builder().data(set.toList()).build())
        .map(v -> v.map(UserSetting::getContent).toList());
  }

  @DeleteMapping("/regions")
  public void deleteRegion(@Valid @RequestBody SimpleDataDTO<String> topic) {
    var writeOp = new ArraySettingsActor.OpDel(ControllerUtils.currentUid(), topic.getData());
    var r = ActorUtils.<SimpleResult<Boolean>, ArraySettingsActor.OpDel>askWithDefault(
        this.clusterConfiguration.getUserRegionsActor(), writeOp);
    var result = r.get();
    if (!result.getResult()) {
      throw ControllerUtils.notFound("region does not exist");
    }
  }

  @GetMapping("/languages")
  public ApiResult<List<String>> getLanguages() {
    return readSettings(this.clusterConfiguration.getUserLanguagesActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  private ApiResult<List<UserSetting>> readSettings(ActorRef settingsActor)
      throws InterruptedException, ExecutionException {
    var readOp = new ArraySettingsActor.OpRead(ControllerUtils.currentUid());
    var r = ActorUtils.<SimpleResult<List<UserSetting>>, ArraySettingsActor.OpRead>askWithDefault(
        settingsActor, readOp);
    var result = r.get();
    return result.asApiResult();
  }

  @PostMapping("/languages")
  public ApiResult<List<String>> updateLanguages(
      @Valid @RequestBody SimpleDataDTO<List<String>> langs) {
    return updateSettings(this.clusterConfiguration.getUserLanguagesActor(), langs)
        .map(v -> v.map(UserSetting::getContent).toList());
  }

  @DeleteMapping("/languages")
  public void deleteLang(@Valid @RequestBody SimpleDataDTO<String> lang) {
    var writeOp = new ArraySettingsActor.OpDel(ControllerUtils.currentUid(), lang.getData());
    var r = ActorUtils.<SimpleResult<Boolean>, ArraySettingsActor.OpDel>askWithDefault(
        this.clusterConfiguration.getUserLanguagesActor(), writeOp);
    var result = r.get();
    if (!result.getResult()) {
      throw ControllerUtils.notFound("language does not exist");
    }
  }

  @GetMapping("/accents")
  public ApiResult<List<String>> getAccents() {
    return readSettings(this.clusterConfiguration.getUserAccentsActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/accents")
  public ApiResult<List<String>> updateAccents(
      @Valid @RequestBody SimpleDataDTO<List<String>> accents) {
    return updateSettings(this.clusterConfiguration.getUserAccentsActor(), accents)
        .map(v -> v.map(UserSetting::getContent).toList());
  }

  @DeleteMapping("/accents")
  public void deleteAccents(@Valid @RequestBody SimpleDataDTO<String> accents) {
    var writeOp = new ArraySettingsActor.OpDel(ControllerUtils.currentUid(), accents.getData());
    var r = ActorUtils.<SimpleResult<Boolean>, ArraySettingsActor.OpDel>askWithDefault(
        this.clusterConfiguration.getUserAccentsActor(), writeOp);
    var result = r.get();
    if (!result.getResult()) {
      throw ControllerUtils.notFound("accent does not exist");
    }
  }

  @GetMapping("/skills")
  public ApiResult<List<String>> getSkills() {
    return readSettings(this.clusterConfiguration.getUserSkillsActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/skills")
  public ApiResult<List<String>> updateSkills(
      @Valid @RequestBody SimpleDataDTO<List<String>> skills) {
    return updateSettings(this.clusterConfiguration.getUserSkillsActor(), skills)
        .map(v -> v.map(UserSetting::getContent).toList());
  }

  @GetMapping("/cloud-s3")
  public ApiResult<CloudUploadSettingDTO> getCloudS3() {
    return readSingleSetting(this.clusterConfiguration.getUserCloudUploadS3Actor())
        .map(this::parseCloudConfig);
  }

  @PostMapping("/cloud-s3")
  public ApiResult<CloudUploadSettingDTO> updateCloudS3(
      @Valid @RequestBody SimpleDataDTO<CloudUploadSettingDTO> data) {
    if (data.getData() == null) {
      throw ControllerUtils.badRequest("data should not be empty");
    }
    var checkResult = awsService.checkAWSS3Config(data.getData(), ControllerUtils.currentUid());
    if (checkResult != null) {
      var mapper = new ObjectMapper();
      data.getData().setType("Success");
      var setting = mapper.writeValueAsString(data.getData());
      return updateSingleSettings(this.clusterConfiguration.getUserCloudUploadS3Actor(), setting)
          .map(this::parseCloudConfig);
    }
    return ApiResult.fail("AWS not configured correctly.");
  }

  @GetMapping("/task-types")
  public ApiResult<List<String>> getTaskTypes() {
    return readSettings(this.clusterConfiguration.getUserTaskTypesActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/task-types")
  public ApiResult<List<String>> updateTaskTypes(
      @Valid @RequestBody SimpleDataDTO<List<String>> taskTypes) {
    return updateSettings(this.clusterConfiguration.getUserTaskTypesActor(), taskTypes)
        .map(v -> v.map(r -> r.getContent()).toList());
  }

  @GetMapping("/interests")
  public ApiResult<List<String>> getInterests() {
    return readSettings(this.clusterConfiguration.getUserInterestsActor())
        .map(r -> r.map(UserSetting::getContent).toList());
  }

  @PostMapping("/interests")
  public ApiResult<List<String>> updateInterests(
      @Valid @RequestBody SimpleDataDTO<List<String>> interests) {
    var data = interests.getData();
    var topicLeast = 5;
    if (data == null) {
      throw ControllerUtils.badRequest("data should not be empty");
    }
    if (data.size() < topicLeast) {
      throw ControllerUtils.badRequest("should have at least 5 topics");
    }
    var result = updateSettings(this.clusterConfiguration.getUserInterestsActor(), interests)
        .map(v -> v.map(UserSetting::getContent).toList());

    if (RegisterType.OLYMPIC.equals(ControllerUtils.currentUserDetail().getRegisterType())) {
      //  just for olympic, will not give user points
      if (!vaultService.markLogin(
          ControllerUtils.currentUid(), this.clusterConfiguration.getUserLoginHistoryActor())) {
        throw ControllerUtils.badRequest("mark login error");
      }
      updateSingleSettings(
          this.clusterConfiguration.getUserViewOlympusStatusActor(), OlympusViewType.NEED_TO_VIEW);
    } else {
      var markStatus =
          vaultService.markLogin(
              ControllerUtils.currentUid(), this.clusterConfiguration.getUserLoginHistoryActor());
      if (!markStatus) {
        log.error("mark login failed for user {}", ControllerUtils.currentUid());
        throw ControllerUtils.badRequest("mark login failed");
      }
    }

    return result;
  }

  @GetMapping("/availabilities")
  public ApiResult<AvailabilityDTO> getAvailability() {
    return readSingleSetting(this.clusterConfiguration.getUserAvailabilityActor())
        .map(this::parseAvailability);
  }

  private AvailabilityDTO parseAvailability(UserSetting s) {
    if (s == null) {
      return null;
    }
    try {
      // parseJson
      var mapper = new ObjectMapper();
      return mapper.readValue(s.getContent(), AvailabilityDTO.class);
    } catch (Throwable ex) {
      log.error("failed to read availability", ex);
      return null;
    }
  }

  private CloudUploadSettingDTO parseCloudConfig(UserSetting s) {
    if (s == null) {
      return null;
    }
    try {
      // parseJson
      var mapper = new ObjectMapper();
      return mapper.readValue(s.getContent(), CloudUploadSettingDTO.class);
    } catch (Throwable ex) {
      log.error("failed to read cloud config", ex);
      return null;
    }
  }

  @PostMapping("/availabilities")
  public ApiResult<AvailabilityDTO> updateAvailabilities(
      @Valid @RequestBody SimpleDataDTO<AvailabilityDTO> avail) {
    if (avail.getData() == null) {
      throw ControllerUtils.badRequest("data should not be empty");
    }
    var mapper = new ObjectMapper();
    var availStr = mapper.writeValueAsString(avail.getData());
    return updateSingleSettings(this.clusterConfiguration.getUserAvailabilityActor(), availStr)
        .map(this::parseAvailability);
  }

  private ApiResult<UserSetting> readSingleSetting(ActorRef settingsActor)
      throws InterruptedException, ExecutionException {
    var readOp = new SingleValueSettingsActor.OpRead(ControllerUtils.currentUid());
    var r = ActorUtils.<SimpleResult<UserSetting>, SingleValueSettingsActor.OpRead>askWithDefault(
        settingsActor, readOp);
    var result = r.get();
    return result.asApiResult();
  }

  public Boolean checkUserFirstLogin(Long userId) {
    var readMsg = new SingleValueSettingsActor.OpRead(userId);
    var userLoginHistory =
        ActorUtils
            .<SimpleResult<SimpleResult<UserSetting>>, SingleValueSettingsActor.OpRead>
                askWithDefault(this.clusterConfiguration.getUserLoginHistoryActor(), readMsg)
            .get();
    return userLoginHistory.getResult().asOpt().isEmpty();
  }

  public Boolean checkUserViewOlympus() {
    var userSettingApiResult =
        this.readSingleSetting(this.clusterConfiguration.getUserViewOlympusStatusActor());
    if (!userSettingApiResult.isSuccess()) {
      return true;
    }
    var datas = userSettingApiResult.getData();

    return datas == null || datas.getContent().equals(OlympusViewType.VIEWED);
  }

  @GetMapping("/firstTimeLogin")
  public ApiResult<Boolean> getFirstTimeLogin() {
    var firstLogin = checkUserFirstLogin(ControllerUtils.currentUid());

    return ApiResult.success(firstLogin);
  }

  private ApiResult<UserSetting> updateSingleSettings(ActorRef settingsActor, String v)
      throws InterruptedException, ExecutionException {
    var setOp = new SingleValueSettingsActor.OpSet(ControllerUtils.currentUid(), v);
    var r = ActorUtils.<SimpleResult<UserSetting>, SingleValueSettingsActor.OpSet>askWithDefault(
        settingsActor, setOp);
    var result = r.get();
    return result.asApiResult();
  }

  private ApiResult<List<UserSetting>> updateSettings(
      ActorRef settingsActor, SimpleDataDTO<List<String>> skills)
      throws InterruptedException, ExecutionException {
    var setOp = new ArraySettingsActor.OpSet(ControllerUtils.currentUid(), skills.getData());
    var r = ActorUtils.<SimpleResult<List<UserSetting>>, ArraySettingsActor.OpSet>askWithDefault(
        settingsActor, setOp);
    var result = r.get();

    return result.asApiResult();
  }

  @GetMapping("/all-topics")
  public ApiResult<List<String>> allCommonTopics() {
    return ApiResult.success(Arrays.asList(Constants.allCommonTopics));
  }

  @GetMapping("/all-languages")
  public ApiResult<List<String>> allCommonLanguage() {
    return ApiResult.success(Arrays.asList(Constants.allCommonLanguages));
  }

  @GetMapping("/all-regions")
  public ApiResult<List<String>> allCommonRegions() {
    return ApiResult.success(Arrays.asList(Constants.allCommonRegions));
  }

  @GetMapping("/all-accents")
  public ApiResult<List<String>> allCommonAccents() {
    return ApiResult.success(Arrays.asList(Constants.allCommonAccents));
  }
}
