package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.res.CloudSyncS3DTO;
import ai.saharaa.dto.res.CreateResourceDTO;
import ai.saharaa.dto.res.UpdateResourceTextDataDTO;
import ai.saharaa.model.AwsS3SyncTask;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.Resource;
import ai.saharaa.services.AwsS3SyncTaskService;
import ai.saharaa.services.AwsService;
import ai.saharaa.services.ICloudStorageService;
import ai.saharaa.services.ResourceService;
import ai.saharaa.services.ReviewSessionService;
import ai.saharaa.services.TaskSessionService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.InputStream;
import java.net.URI;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/resources")
public class ResourceController {
  private final Logger logger = LoggerFactory.getLogger(ResourceController.class);

  private final ResourceService resourceService;
  private final AwsService awsService;
  private final AwsS3SyncTaskService awsS3SyncTaskService;
  private final ICloudStorageService cloudStorageService;
  private final ReviewSessionService reviewSessionService;
  private final TaskSessionService taskSessionService;

  public ResourceController(
      ResourceService resourceService,
      AwsService awsService,
      ReviewSessionService reviewSessionService,
      TaskSessionService taskSessionService,
      ICloudStorageService cloudStorageService,
      AwsS3SyncTaskService awsS3SyncTaskService) {
    this.resourceService = resourceService;
    this.reviewSessionService = reviewSessionService;
    this.taskSessionService = taskSessionService;
    this.cloudStorageService = cloudStorageService;
    this.awsService = awsService;
    this.awsS3SyncTaskService = awsS3SyncTaskService;
  }

  // api for creating resource from cloud storage
  // TODO: implement an api for create resource by uploading file
  @PostMapping
  public ApiResult<Resource> createResource(@Valid @RequestBody CreateResourceDTO res) {
    cloudStorageService
        .getCloudStorageById(res.getCloudStorageId())
        .filter(r -> !r.getDeleted())
        .orThrow(() -> ControllerUtils.badRequest("Cloud storage not found"))
        .filter(r -> r.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(
            () -> ControllerUtils.forbidden("You are not the owner of this cloud storage"));

    var r = Resource.builder()
        .name(res.getName())
        .cloudStorageId(res.getCloudStorageId())
        .fileType(res.getFileType())
        .suffix(res.getSuffix())
        .ownerId(ControllerUtils.currentUid())
        .size(0L)
        .contentDetection("")
        .visibility(Resource.Visibility.TASK)
        .build();
    return resourceService.createResource(r).toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<Resource> getResourceById(@PathVariable Long id) {
    var r = resourceService
        .getResourceById(id)
        .filter(res -> !res.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Resource not found"))
        .filter(res -> res.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this resource"));

    return r.toApiResult();
  }

  @IsAccountManager
  @PutMapping("/{id}")
  public ApiResult<Resource> updateResourceTextDataById(
      @PathVariable Long id, @RequestBody UpdateResourceTextDataDTO dto) {
    return ApiResult.success(resourceService.updateResourceFromText(dto.getData(), id));
  }

  @GetMapping("/by-ids")
  public ApiResult<List<Resource>> getResourceByIds(@RequestParam List<Long> ids) {
    ids = ids.toSet().toList();
    if (ids.isEmpty()) {
      throw ControllerUtils.badRequest("Invalid ids");
    }
    if (ids.size() > 1000) {
      throw ControllerUtils.badRequest("too many ids");
    }

    var reses = resourceService.getResourceByIds(ids);
    Long curUid = ControllerUtils.currentUid();
    if (!ControllerUtils.isAdmin()
        && !ControllerUtils.isAccountManager()
        && reses.anyMatch(res -> !res.getOwnerId().equals(curUid))) {
      throw ControllerUtils.badRequest("You are not the owner of some resources");
    }

    return reses.toApiResult();
  }

  @GetMapping("/download/{id}")
  public ResponseEntity<Object> downloadFile(
      @PathVariable Long id,
      HttpServletResponse httpServletResponse,
      @RequestParam(defaultValue = "false") Boolean cloudFrontMode) {

    if (!resourceService.currentUserCanDownloadResource(id)) {
      logger.warn(
          "User {} is not allowed to download resource {}", ControllerUtils.currentUid(), id);
      return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }

    return handleFileDownload(id, httpServletResponse, cloudFrontMode);
  }

  @GetMapping("/download/public/{id}")
  public ResponseEntity<Object> downloadPublicFile(@PathVariable Long id) {
    if (!resourceService.currentResourceIsPublic(id)) {
      return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }
    return handlePublicFileDownload(id);
  }

  @GetMapping("/review/{taskSessionId}/{resourceId}")
  public ResponseEntity<Object> reviewImageFile(
      @PathVariable Long taskSessionId,
      @PathVariable Long resourceId,
      HttpServletResponse httpServletResponse) {
    var currentUserId = ControllerUtils.currentUid();
    var myTaskSession = taskSessionService
        .getTaskSessionById(taskSessionId)
        .filter(t -> t.getDeleted().equals(false) && t.getUserId().equals(currentUserId))
        .orElse(null);
    if (null == myTaskSession) {
      reviewSessionService
          .getMyReviewSessionByTaskSessionId(taskSessionId, currentUserId)
          .asOpt()
          .ensurePresent(() -> ControllerUtils.forbidden("no perm"));
    }
    return handleFileDownload(resourceId, httpServletResponse, false, true);
  }

  @GetMapping("/download/{resourceId}/for-task/{taskId}")
  public ResponseEntity<Object> downloadFileForTask(
      @PathVariable Long resourceId,
      @PathVariable Long taskId,
      HttpServletResponse httpServletResponse,
      @RequestParam(name = "job", required = false) Long jobId,
      @RequestParam(defaultValue = "false") Boolean cloudFrontMode) {

    if (resourceService.currentUserCanDownloadResource(
        resourceId, Optional.of(taskId), Optional.ofNullable(jobId))) {
      return handleFileDownload(resourceId, httpServletResponse, cloudFrontMode);
    } else {
      logger.warn(
          "User {} is not allowed to download resource {}. Task id {}",
          ControllerUtils.currentUid(),
          resourceId,
          taskId);
      return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }
  }

  public ResponseEntity<Object> handlePublicFileDownload(Long id) {
    var r = resourceService
        .getResourceById(id)
        .filter(res -> !res.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Resource not found"));
    String preSignedUrl = resourceService.tryToGetPreSignedUrl(r, false);
    return ResponseEntity.status(HttpStatus.TEMPORARY_REDIRECT)
        .location(URI.create(preSignedUrl))
        .build();
  }

  public ResponseEntity<Object> handleFileDownload(
      Long id,
      HttpServletResponse httpServletResponse,
      Boolean cloudFrontMode,
      Boolean ignoreDelete) {
    var r = resourceService
        .getResourceById(id)
        .filter(res -> !res.getDeleted() || ignoreDelete)
        .orElseThrow(() -> ControllerUtils.notFound("Resource not found"));

    MediaType contentType = MediaType.parseMediaType(r.getContentDetection());
    String preSignedUrl = resourceService.tryToGetPreSignedUrl(r, cloudFrontMode);
    if (StringUtils.isNotBlank(preSignedUrl)) {
      return ResponseEntity.status(HttpStatus.TEMPORARY_REDIRECT)
          .location(URI.create(preSignedUrl))
          .build();
    }
    InputStream in = resourceService.downloadFile(r);
    return ResponseEntity.ok()
        .contentType(contentType)
        .cacheControl(CacheControl.maxAge(Duration.ofHours(2)))
        .body(new InputStreamResource(in));
  }

  public ResponseEntity<Object> handleFileDownload(
      Long id, HttpServletResponse httpServletResponse, Boolean cloudFrontMode) {
    return handleFileDownload(id, httpServletResponse, cloudFrontMode, false);
  }

  @GetMapping("/{id}/pre-signed-url")
  public ResponseEntity<Object> preSignedUrl(
      @PathVariable Long id,
      HttpServletResponse httpServletResponse,
      @RequestParam(defaultValue = "false") Boolean cloudFrontMode) {
    // TODO: check if user has permission to download this file
    var r = resourceService
        .getResourceById(id)
        .filter(res -> !res.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Resource not found"));

    String preSignedUrl = resourceService.tryToGetPreSignedUrl(r, cloudFrontMode);
    return ResponseEntity.status(HttpStatus.TEMPORARY_REDIRECT)
        .location(URI.create(preSignedUrl))
        .build();
  }

  @PostMapping("/cloud-s3/sync")
  public ApiResult<Boolean> doCloudSync(@RequestBody @Validated CloudSyncS3DTO body) {
    var currentUserId = ControllerUtils.currentUid();
    var userS3Session = awsService.getRoleS3CloudSessionContent(currentUserId);
    if (userS3Session == null) {
      return ApiResult.success(false);
    }
    return awsS3SyncTaskService
        .accessBucketItems(userS3Session, body, currentUserId)
        .toApiResult();
  }

  @GetMapping("/cloud-s3/{id}/task-list")
  @CheckPageParam()
  public ApiResult<PageResult<AwsS3SyncTask>> getS3CloudSyncList(
      @PathVariable Long id,
      @RequestParam(required = false, defaultValue = "-1") Integer status,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    return awsS3SyncTaskService
        .getS3CloudSyncList(id, page, limit, status)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/from-file")
  public ApiResult<Resource> uploadFromFile(@RequestParam MultipartFile file) {
    var res = resourceService.createResourceFromFile(
        ControllerUtils.currentUid(), file, Resource.Visibility.PUBLIC);
    return res.toApiResult();
  }

  @DeleteMapping
  public ApiResult<Boolean> deletePreSignedUrlUploaded(@RequestParam Long resourceId) {
    return ApiResult.success(
        resourceService.deleteResourceById(ControllerUtils.currentUid(), resourceId));
  }
}
