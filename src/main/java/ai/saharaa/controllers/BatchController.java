package ai.saharaa.controllers;

import static ai.saharaa.config.AuditLogInterceptor.REQUEST_USER_AGENT;
import static ai.saharaa.model.Batch.BatchStatus.ASSIGN_JOBS;
import static ai.saharaa.model.Batch.UserType.EXTERNAL_LABELER;
import static ai.saharaa.model.Batch.beforeLaunchBatchStatusList;
import static ai.saharaa.model.TaskList.TaskListType.LABEL;
import static ai.saharaa.utils.Constants.EXPORT_BATCH_WHITE_LIST_ADDRESSES;

import ai.saharaa.actors.batch.DataTaskScheduler;
import ai.saharaa.config.AuditLogInterceptor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.config.perms.IsQueueManager;
import ai.saharaa.daos.*;
import ai.saharaa.daos.newTasks.BatchRewardRecordDao;
import ai.saharaa.distribution.TaskVisitorProvider;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.Chart2;
import ai.saharaa.dto.SimpleDataDTO;
import ai.saharaa.dto.batch.*;
import ai.saharaa.dto.job.AmAuditJobDTO;
import ai.saharaa.dto.job.JobAmAuditStatDTO;
import ai.saharaa.dto.job.TakeJobResultDTO;
import ai.saharaa.dto.sign.BatchRequesterAcceptSignMessageDTO;
import ai.saharaa.dto.sign.EIP712Message;
import ai.saharaa.dto.sign.SignatureDTO;
import ai.saharaa.dto.task.TaskInfoDTO;
import ai.saharaa.dto.task.TaskListDetailsDTO;
import ai.saharaa.enums.BatchChartType;
import ai.saharaa.model.*;
import ai.saharaa.model.newTasks.RewardTokenInfo;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.CryptoUtils;
import ai.saharaa.utils.DateUtils;
import akka.actor.ActorRef;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.web3j.utils.Numeric;

@RestController
@RequestMapping("/api/batches")
@Tag(name = "Batch Service", description = "Batch service API")
public class BatchController {
  private final Logger log = org.slf4j.LoggerFactory.getLogger(BatchController.class);
  private final BatchService batchService;
  private final BatchRewardRecordDao batchRewardRecordDao;
  private final BatchDao batchDao;
  private final ProjectService projectService;

  private final BatchSampleService batchSampleService;
  private final BatchDataService batchDataService;
  private final BatchExampleDao batchExampleDao;
  private final TaskListDao taskListDao;
  private final TaskService taskService;
  private final TaskSessionService taskSessionService;
  private final JobService jobService;
  private final JobTaskService jobTaskService;
  private final BatchNDADao batchNDADao;
  private final UserService userService;
  private final DataTaskService dataTaskService;
  private final ResourceService resourceService;
  private final ObjectMapper objectMapper;
  private final PreTaskService preTaskService;
  private final NotificationService notificationService;
  private final VaultService vaultService;
  private final TaskQuestionDao taskQuestionDao;
  private final BatchChartService batchChartService;
  private final ClusterConfiguration clusterConfiguration;
  private final TaskVisitorProvider taskVisitorProvider;

  public BatchController(
      ClusterConfiguration clusterConfiguration,
      BatchService batchService,
      BatchRewardRecordDao batchRewardRecordDao,
      BatchDao batchDao,
      ProjectService projectService,
      BatchSampleService batchSampleService,
      BatchExampleDao batchExampleDao,
      TaskListDao taskListDao,
      TaskService taskService,
      BatchDataService batchDataService,
      TaskSessionService taskSessionService,
      JobService jobService,
      JobTaskService jobTaskService,
      BatchNDADao batchNDADao,
      UserService userService,
      DataTaskService dataTaskService,
      ResourceService resourceService,
      ObjectMapper objectMapper,
      Web2AuthService web2AuthService,
      PreTaskService preTaskService,
      NotificationService notificationService,
      VaultService vaultService,
      TaskQuestionDao taskQuestionDao,
      BatchChartService batchChartService,
      TaskVisitorProvider taskVisitorProvider) {
    this.clusterConfiguration = clusterConfiguration;
    this.batchDao = batchDao;
    this.batchService = batchService;
    this.batchRewardRecordDao = batchRewardRecordDao;
    this.projectService = projectService;
    this.batchSampleService = batchSampleService;
    this.batchExampleDao = batchExampleDao;
    this.taskListDao = taskListDao;
    this.taskService = taskService;
    this.batchDataService = batchDataService;
    this.taskSessionService = taskSessionService;
    this.jobService = jobService;
    this.jobTaskService = jobTaskService;
    this.batchNDADao = batchNDADao;
    this.userService = userService;
    this.dataTaskService = dataTaskService;
    this.resourceService = resourceService;
    this.objectMapper = objectMapper;
    this.preTaskService = preTaskService;
    this.notificationService = notificationService;
    this.vaultService = vaultService;
    this.taskQuestionDao = taskQuestionDao;
    this.batchChartService = batchChartService;
    this.taskVisitorProvider = taskVisitorProvider;
  }

  @PostMapping("/v2")
  public ApiResult<BatchDetailsDTO> createBatchV2(
      @Valid @RequestBody CreateBatchDTO createBatchDTO) {
    if (!Objects.equals(createBatchDTO.getTaskType(), Batch.TaskType.ACADEMY)
        && Objects.isNull(createBatchDTO.getAcademyType())) {
      projectService
          .getProjectById(createBatchDTO.getProjectId())
          .filter(p -> !p.getDeleted())
          .orThrow(() -> ControllerUtils.notFound("Project not found"))
          .filter(p -> Objects.equals(p.getAccountManagerId(), ControllerUtils.currentUid())
              || ControllerUtils.isAccountManager())
          .ensurePresent(
              () -> ControllerUtils.forbidden("You are not the AccountManager of this project"));
    }
    if (createBatchDTO.getCutoffTime() != null
        && createBatchDTO.getCutoffTime().after(createBatchDTO.getDeadline())) {
      throw ControllerUtils.badRequest("Cut off time must be before deadline");
    }
    var deadline = createBatchDTO.getDeadline();
    if (deadline == null) {
      deadline = DateUtils.TIMESTAMP_NEVER;
    } else {
      if (deadline.before(DateUtils.now())) {
        throw ControllerUtils.badRequest("Deadline must be in the future");
      }
    }

    var mapper = new ObjectMapper();
    Batch batch = Batch.builder()
        .ownerId(ControllerUtils.currentUid())
        .name(createBatchDTO.getName())
        .projectId(createBatchDTO.getProjectId())
        .summary(createBatchDTO.getSummary())
        .summaryForAnnotator(createBatchDTO.getSummaryForAnnotator())
        .summaryForReviewer(createBatchDTO.getSummaryForReviewer())
        .description(createBatchDTO.getDescription())
        .extraInfo(createBatchDTO.getExtraInfo())
        .deadline(deadline)
        .taskType(createBatchDTO.getTaskType())
        .userType(createBatchDTO.getUserType())
        .examType(createBatchDTO.getExamType())
        .reviewerRequired(createBatchDTO.getReviewerRequired())
        .difficulty(createBatchDTO.getDifficulty())
        .deadline(createBatchDTO.getDeadline())
        .requiredRegions(mapper.writeValueAsString(createBatchDTO.getRequiredRegions()))
        .requiredLanguages(mapper.writeValueAsString(createBatchDTO.getRequiredLanguages()))
        .gender(createBatchDTO.getGender())
        .accent(createBatchDTO.getAccent())
        .ageMin(createBatchDTO.getAgeMin())
        .ageMax(createBatchDTO.getAgeMax())
        .specialRequirement(createBatchDTO.getSpecialRequirement())
        .requiredAchievementSymbol(createBatchDTO.getRequiredAchievementSymbol())
        .academyType(createBatchDTO.getAcademyType())
        .dataType(createBatchDTO.getDataType())
        .labelType(
            Objects.isNull(createBatchDTO.getLabelType())
                ? Batch.BatchLabelType.from(createBatchDTO.getDataType())
                : createBatchDTO.getLabelType())
        .version(2)
        .build();

    var batchDetail =
        this.batchService.createBatchV2(batch, createBatchDTO.getAccessRequirements());
    var bs = batchDetail.getBatchSetting();
    createBatchDTO.getAnnotationMethod().asOpt().ifPresent(bs::setAnnotationMethod);
    createBatchDTO.getRequiredUserLevel().asOpt().ifPresent(bs::setRequiredUserLevel);
    createBatchDTO.getTaskPaymentType().asOpt().ifPresent(bs::setTaskPaymentType);
    createBatchDTO.getTotalBudgetPoints().asOpt().ifPresent(bs::setTotalBudgetPoints);

    if (createBatchDTO.getTaskType().equals(Batch.TaskType.TASK)) {
      if (Boolean.TRUE.equals(createBatchDTO.getReviewerRequired())) {
        bs.setCutoffTime(createBatchDTO.getCutoffTime());
      }
    }
    batchDao.updateBatchSettingById(bs);
    return batchDetail.toApiResult();
  }

  @PostMapping
  public ApiResult<Batch> createBatch(@Valid @RequestBody CreateBatchDTO createBatchDTO) {
    if (Objects.isNull(createBatchDTO.getDataType())) {
      throw ControllerUtils.badRequest("dataType not null");
    }
    projectService
        .getProjectById(createBatchDTO.getProjectId())
        .filter(p -> !p.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(p -> Objects.equals(p.getRequesterId(), ControllerUtils.currentUid()))
        .ensurePresent(
            () -> ControllerUtils.forbidden("You are not the requester of this project"));

    var deadline = createBatchDTO.getDeadline();
    if (deadline == null) {
      deadline = DateUtils.TIMESTAMP_NEVER;
    } else {
      if (deadline.before(DateUtils.now())) {
        throw ControllerUtils.badRequest("Deadline must be in the future");
      }
    }
    var mapper = new ObjectMapper();
    // TODO: validate label type

    var b = Batch.builder()
        .ownerId(ControllerUtils.currentUid())
        .projectId(createBatchDTO.getProjectId())
        .name(createBatchDTO.getName())
        .summary(createBatchDTO.getSummary())
        .summaryForAnnotator(createBatchDTO.getSummaryForAnnotator())
        .summaryForReviewer(createBatchDTO.getSummaryForReviewer())
        .description(createBatchDTO.getDescription())
        .extraInfo(createBatchDTO.getExtraInfo())
        .labelType(createBatchDTO.getLabelType())
        .labelBody(createBatchDTO.getLabelBody())
        .deadline(deadline)
        .dataType(createBatchDTO.getDataType())
        .requiredAccuracy(createBatchDTO.getRequiredAccuracy())
        .requiredReviewAccuracy(createBatchDTO.getRequiredReviewAccuracy())
        .spotAudit(createBatchDTO.getSpotAudit())
        .requiredRegions(mapper.writeValueAsString(createBatchDTO.getRequiredRegions()))
        .requiredLanguages(mapper.writeValueAsString(createBatchDTO.getRequiredLanguages()))
        .requiredSkills(mapper.writeValueAsString(createBatchDTO.getRequiredSkills()))
        .labelingRepeat(createBatchDTO.getLabelingRepeat())
        .reviewRepeat(createBatchDTO.getReviewRepeat())
        .version(1)
        .build();
    b = batchService.createBatch(b);
    return b.toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteBatch(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(v -> beforeLaunchBatchStatusList.contains(v.getStatus()))
        .orThrow(() -> ControllerUtils.notFound("can't delete launched batch"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't delete other user's batch"));
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ua = (AuditLogInterceptor.RequestUserAgent) request.getAttribute(REQUEST_USER_AGENT);

    var userDetail = ControllerUtils.currentUserDetail();

    batchService.deleteBatchById(id, ua, userDetail);
  }

  @GetMapping("/list")
  @CheckPageParam()
  public ApiResult<PageResult<Batch>> getUserBatches(
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var curId = ControllerUtils.currentUid();
    var pageInfo = ControllerUtils.safePageRange(page, size);
    var batches = batchService.getUserBatches(curId, pageInfo.getFirst(), pageInfo.getSecond());
    return batches.toPageResult().toApiResult();
  }

  @GetMapping("/v2/list")
  @CheckPageParam()
  public ApiResult<PageResult<BatchWithStatDTO>> getUserBatchesByProject(
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(defaultValue = "") Long projectId,
      @RequestParam Batch.TaskType type) {
    var curId = ControllerUtils.currentUid();
    var batches = batchService.getUserBatchesByProject(curId, page, size, projectId, type);

    return PageResult.<BatchWithStatDTO>builder()
        .pages(batches.getPages())
        .size(batches.getSize())
        .current(batches.getCurrent())
        .total(batches.getTotal())
        .data(batches
            .getRecords()
            .map(batch -> {
              var batchAccessRequirements = batchService.getBatchAccessRequirements(batch.getId());
              var jobs = jobService.listJobsByBatchId(batch.getId());
              return BatchWithStatDTO.builder()
                  .batch(batch)
                  .job(jobs.firstOrNull())
                  .stat(getBatchStatInfo(batch))
                  .accessRequirements(batchAccessRequirements)
                  .build();
            })
            .toList())
        .build()
        .toApiResult();
  }

  @GetMapping("/v2/list/for-am")
  @IsAccountManager
  @CheckPageParam(maxSize = 1000)
  public ApiResult<PageResult<BatchWithStatDTO>> getAmBatchesByProject(
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(defaultValue = "") Long projectId,
      @RequestParam Batch.TaskType type) {
    var batches = batchService.getUserBatchesByProject(null, page, size, projectId, type);

    return PageResult.<BatchWithStatDTO>builder()
        .pages(batches.getPages())
        .size(batches.getSize())
        .current(batches.getCurrent())
        .total(batches.getTotal())
        .data(batches
            .getRecords()
            .map(batch -> {
              var batchAccessRequirements = batchService.getBatchAccessRequirements(batch.getId());
              var jobs = jobService.listJobsByBatchId(batch.getId());
              var records = batchService.getBatchRewardRecords(batch.getId());
              return BatchWithStatDTO.builder()
                  .batch(batch)
                  .stat(getBatchStatInfo(batch))
                  .accessRequirements(batchAccessRequirements)
                  .job(jobs.firstOrNull())
                  .rewards(records)
                  .build();
            })
            .toList())
        .build()
        .toApiResult();
  }

  @GetMapping("/v2/certificates")
  @CheckPageParam()
  public ApiResult<PageResult<BatchDetailsDTO>> getUserBatchesDetailsCertificates(
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var curId = ControllerUtils.currentUid();
    var batches =
        batchService.getUserBatchesByProject(curId, page, size, null, Batch.TaskType.ACADEMY);
    var batchDetails = batches
        .getRecords()
        .map(batch -> BatchDetailsDTO.builder()
            .batch(batch)
            .certificate(
                Objects.isNull(batch.getCertificateId())
                    ? null
                    : this.vaultService.getCertificateById(batch.getCertificateId()))
            .build())
        .toList();
    return PageResult.<BatchDetailsDTO>builder()
        .pages(batches.getPages())
        .size(batches.getSize())
        .current(batches.getCurrent())
        .total(batches.getTotal())
        .data(batchDetails)
        .build()
        .toApiResult();
  }

  @GetMapping("/v2/certificates/{id}")
  public ApiResult<Batch> getBatchDetailByCertificateId(@PathVariable Long id) {
    return batchService.getBatchByCertificateId(id).toApiResult();
  }

  @GetMapping("/for-queue-manager")
  @IsQueueManager
  @CheckPageParam()
  public ApiResult<PageResult<BatchWithStatDTO>> getBatchesByQueueManager(
      @RequestParam(required = false) String searchParam,
      @RequestParam(required = false) List<String> status,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {

    return batchService
        .getBatchesByQueueManager(searchParam, status, page, size)
        .convert(b -> {
          BatchStatDTO batchStatInfo = getBatchStatInfo(b);
          return BatchWithStatDTO.builder()
              .batch(b)
              .job(jobService.listJobsByBatchId(b.getId()).firstOrNull())
              .stat(batchStatInfo)
              .project(getProjectById(b))
              .qmBatchStatus(this.preTaskService.getBatchStatusByQm(b, batchStatInfo))
              .build();
        })
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<Batch> getBatchById(@PathVariable Long id) {
    var b = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));

    return b.toApiResult();
  }

  @GetMapping("/{id}/for-queue-manager")
  public ApiResult<Batch> getBatchByIdForQueueManager(@PathVariable Long id) {
    var b = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));

    return b.toApiResult();
  }

  @GetMapping("/{id}/unallocated-tasks/for-queue-manager")
  public ApiResult<Long> countUnallocatedTask(@PathVariable Long id) {
    return this.batchService.countUnAllocatedTask(id).toApiResult();
  }

  @GetMapping("/{id}/samples")
  public ApiResult<List<BatchSampleDetailsDTO>> getSamplesByBatchId(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));

    return batchDataService.getBatchSampleDetails(batch).toApiResult();
  }

  @GetMapping("/v2/{id}/details")
  public ApiResult<BatchDetailsDTO> getBatchDetailsV2(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));

    BatchDetailsDTO r = this.batchService.getBatchDetails(id);
    var labelTaskList = taskListDao.listTaskListsByBatchIdAndType(batch.getId(), LABEL);
    var labelTasks =
        taskService.listTasksByTaskListId(batch, labelTaskList.first().getId());
    r.setTasks(labelTasks);
    var jobs = jobService.listJobsByBatchId(batch.getId());
    r.setJob(jobs.firstOrNull());

    Optional<Long> inviteAcceptanceDeadline = this.jobService
        .listJobsByBatchId(id)
        .filter(j -> j.getStatus() == Job.JobStatus.PRE_TASK)
        .map(j -> j.getInviteAcceptanceDeadline().getTime())
        .max(Long::compareTo);

    inviteAcceptanceDeadline.ifPresent(i -> r.setInviteAcceptanceDeadline(DateUtils.from(i)));

    return r.toApiResult();
  }

  @GetMapping("/tokenRewards")
  @IsAccountManager
  public ApiResult<List<RewardTokenInfo>> getTokenRewards() {
    return ApiResult.success(batchService.getRewardTokenInfoList());
  }

  @GetMapping("/{id}/details")
  public ApiResult<BatchDetailsDTO> getBatchDetails(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));
    var samples = batchSampleService.listBatchSamplesByBatch(batch);
    var r = BatchDetailsDTO.builder().batch(batch).samples(samples).build();
    var labelTaskList = taskListDao.listTaskListsByBatchIdAndType(batch.getId(), LABEL);
    var labelTasks =
        taskService.listTasksByTaskListId(batch, labelTaskList.first().getId());
    r.setTasks(labelTasks);
    var jobs = jobService.listJobsByBatchId(batch.getId());
    r.setJob(jobs.firstOrNull());

    Optional<Long> inviteAcceptanceDeadline = this.jobService
        .listJobsByBatchId(id)
        .filter(j -> j.getStatus() == Job.JobStatus.PRE_TASK)
        .map(j -> j.getInviteAcceptanceDeadline().getTime())
        .max(Long::compareTo);

    inviteAcceptanceDeadline.ifPresent(i -> r.setInviteAcceptanceDeadline(DateUtils.now()));

    return r.toApiResult();
  }

  @GetMapping("/{batchId}/label-sample-detail")
  public ApiResult<BatchSampleDetailsDTO> getLabelSampleDetail(@PathVariable Long batchId) {
    var batch = batchService
        .getBatchById(batchId)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));

    var labelSample = batchService.getLabelSample(batch);
    if (Objects.isNull(labelSample)) {
      var isBatchForIndividuals = this.projectService
          .getProjectById(batch.getProjectId())
          .map(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
          .orElse(Boolean.FALSE);
      labelSample = batchService.createLabelSample(batch, isBatchForIndividuals);
    }
    if (Objects.isNull(labelSample)) {
      throw ControllerUtils.notFound("label sample not found");
    }

    var details = resourceService
        .getResourceById(labelSample.getResourceId())
        .orElseThrow(() -> ControllerUtils.notFound("resource not found"));
    return ApiResult.success(
        BatchSampleDetailsDTO.builder().sample(labelSample).resource(details).build());
  }

  @GetMapping("/{batchId}/instruction-histories")
  public ApiResult<List<BatchInstructionHistories>> getBatchInstructionHistories(
      @PathVariable Long batchId,
      @RequestParam BatchInstructionHistories.BatchInstructionType type) {
    return this.batchService.getBatchInstructionHistories(batchId, type, 5).toApiResult();
  }

  @PostMapping("/v2/{id}")
  public ApiResult<BatchDetailsDTO> updateBatchV2(
      @PathVariable Long id, @RequestBody UpdateBatchDTO data) {
    Batch batchDB = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't update other user's batch"));

    if (Objects.equals(batchDB.getTaskType(), Batch.TaskType.TASK)) {
      if (!Arrays.asList(
                  Batch.BatchStatus.DRAFT,
                  Batch.BatchStatus.SAMPLE_UPLOADED,
                  Batch.BatchStatus.TASK_CREATED,
                  Batch.BatchStatus.INSTRUCTION_CREATED)
              .contains(batchDB.getStatus())
          && batchDB.getDeadline().before(DateUtils.now())) {
        throw ControllerUtils.badRequest("Batch deadline has passed");
      }
    }

    if (data.getCutoffTime() != null && data.getCutoffTime().after(data.getDeadline())) {
      throw ControllerUtils.badRequest("Cut off time must be before deadline");
    }

    // TODO: validate status if it's not null
    batchService.updateBatchDataV2(id, data, batchDB);
    return this.batchService.getBatchDetails(id).toApiResult();
  }

  @PostMapping("/{id}")
  public ApiResult<BatchDetailsDTO> updateBatch(
      @PathVariable Long id, @RequestBody UpdateBatchDTO data) {

    Batch batchDB = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't update other user's batch"));

    // TODO: validate status if it's not null
    batchService.updateBatchData(id, data, batchDB);
    notificationService.noticeRequesterBatchCalibration(
        ControllerUtils.currentUid(), batchDB.getProjectId(), batchDB.getId());

    var batch = batchService.getBatchById(id).filter(v -> !v.getDeleted()).get();
    var samples = batchSampleService.listBatchSamplesByBatch(batch);
    var r = BatchDetailsDTO.builder().batch(batch).samples(samples).build();

    return r.toApiResult();
  }

  @GetMapping("/{id}/can-submit-am-review")
  public ApiResult<Boolean> canSubmitAmReview(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted()
            && (v.getStatus() == Batch.BatchStatus.AM_REVIEW
                || v.getStatus() == Batch.BatchStatus.CLIENT_REJECTED))
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't submit other user's batch"));

    return batchService.canSubmitAmReview(batch).toApiResult();
  }

  @PostMapping("/{id}/submit-am-review")
  public ApiResult<BatchDetailsDTO> submitAmReview(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted()
            && (v.getStatus() == Batch.BatchStatus.AM_REVIEW
                || v.getStatus() == Batch.BatchStatus.CLIENT_REJECTED))
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't submit other user's batch"));

    if (!batchService.canSubmitAmReview(batch)) {
      throw ControllerUtils.badRequest("Not all tasks completed yet");
    }
    batchService.submitAmReview(id);
    return this.batchService.getBatchDetails(id).toApiResult();
  }

  @PostMapping("/{id}/pre-task")
  public ApiResult<BatchDetailsDTO> preTask(@PathVariable Long id) {

    Batch batchDB = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't update other user's batch"));

    UpdateBatchDTO status =
        UpdateBatchDTO.builder().status(Batch.BatchStatus.PRE_TASK_EXAM).build();
    batchService.updateBatchDataV2(id, status, batchDB);

    return this.batchService.getBatchDetails(id).toApiResult();
  }

  @PostMapping("/{id}/pre-task-passed")
  @IsAccountManager()
  @Operation(summary = "Account Manager modifies the status of the batch to Pass Exam")
  public ApiResult<BatchDetailsDTO> preTaskPassed(@PathVariable Long id) {

    this.preTaskService.passedBatch(id, ControllerUtils.currentUid());
    return this.batchService.getBatchDetails(id).toApiResult();
  }

  @GetMapping("/{id}/labeling-tasks")
  public ApiResult<List<TaskListDetailsDTO>> getLabelingTasks(@PathVariable Long id) {
    var taskListDetails = taskService.getTaskListDetails(id, LABEL, false);
    if (!ControllerUtils.isAccountManager()) {
      taskListDetails.forEach(
          tld -> tld.getTasks().forEach(t -> t.getQuestions().forEach(q -> q.setAnswer(""))));
    }
    return taskListDetails.toApiResult();
  }

  @GetMapping("/{id}/good-examples")
  public ApiResult<List<TaskListDetailsDTO>> getGoodExampleTasks(@PathVariable Long id) {
    return taskService
        .getTaskListDetails(id, TaskList.TaskListType.GOOD_EXAMPLE, false)
        .toApiResult();
  }

  @GetMapping("/{id}/bad-examples")
  public ApiResult<List<TaskListDetailsDTO>> getBadExampleTasks(@PathVariable Long id) {
    return taskService
        .getTaskListDetails(id, TaskList.TaskListType.BAD_EXAMPLE, false)
        .toApiResult();
  }

  @GetMapping("/{id}/exams")
  public ApiResult<List<TaskListDetailsDTO>> getExams(@PathVariable Long id) {
    return taskService.getTaskListDetails(id, TaskList.TaskListType.EXAM, false).toApiResult();
  }

  @GetMapping("/{batchId}/annotation-tasks")
  @CheckPageParam()
  public ApiResult<PageResult<TaskInfoDTO>> getAnnotationTasks(
      @PathVariable Long batchId,
      @RequestParam(defaultValue = "1") Long page,
      @RequestParam(defaultValue = "", required = false) String name,
      @RequestParam(defaultValue = "10") Long size) {
    var batch = batchService
        .getBatchById(batchId)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));
    var taskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .first();
    if (StringUtils.isBlank(name)) {
      return taskService
          .getTaskInfosByTaskListId(taskList.getId(), page, size)
          .toPageResult()
          .toApiResult();
    }
    return taskService
        .getTaskInfosByTaskListIdAndSearchName(
            taskList.getId(),
            page,
            size,
            name,
            Batch.BatchLabelType.HYBRID.equals(batch.getLabelType()))
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/action-logs")
  public ApiResult<List<String>> getActionLogs(@PathVariable Long id) {
    return List.<String>of().toApiResult();
  }

  @PostMapping("/{id}/copy")
  public ApiResult<BatchDetailsDTO> copy(@PathVariable Long id, @RequestParam Long originalId) {
    if (id.equals(originalId)) {
      throw ControllerUtils.badRequest("can't copy batch from itself");
    }
    var batch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));
    var originalBatch = batchService
        .getBatchById(originalId)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Original batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't copy other user's batch"));
    if (!Objects.equals(batch.getProjectId(), originalBatch.getProjectId())) {
      throw ControllerUtils.badRequest("can't copy batch from different project");
    }
    if (!List.of(
            Batch.BatchStatus.DRAFT,
            Batch.BatchStatus.SAMPLE_UPLOADED,
            Batch.BatchStatus.TASK_CREATED,
            Batch.BatchStatus.INSTRUCTION_CREATED,
            Batch.BatchStatus.EXAM_CREATED,
            Batch.BatchStatus.CHECKING,
            Batch.BatchStatus.CALIBRATING)
        .contains(batch.getStatus())) {
      throw ControllerUtils.badRequest("Batch is not between draft and calibrating status");
    }

    return batchService.copyBatch(batch, originalBatch).toApiResult();
  }

  @PostMapping("/v2/{id}/copy")
  public ApiResult<BatchDetailsDTO> copyV2(@PathVariable Long id) {
    var originalBatch = batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Original batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't copy other user's batch"));
    // if (!List.of(Batch.BatchStatus.DRAFT, Batch.BatchStatus.SAMPLE_UPLOADED,
    // Batch.BatchStatus.TASK_CREATED, Batch.BatchStatus.INSTRUCTION_CREATED,
    // Batch.BatchStatus.EXAM_CREATED, Batch.BatchStatus.CHECKING,
    // Batch.BatchStatus.CALIBRATING)
    // .contains(originalBatch.getStatus())) {
    // throw ControllerUtils.badRequest("Batch is not between draft and calibrating status");
    // }

    return batchService.copyBatchV2(originalBatch).toApiResult();
  }

  @GetMapping("/{id}/examples")
  public ApiResult<List<BatchExample>> getBatchExamples(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't get other user's batch"));

    var examples = batchExampleDao.getBatchExampleByBatchId(id);
    return examples.toApiResult();
  }

  @GetMapping("/{id}/stat")
  @Cacheable(value = "batchStat", cacheManager = "apiCacheManager")
  public ApiResult<BatchStatDTO> getBatchStat(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));
    var res = getBatchStatInfo(batch);
    if (ControllerUtils.isAccountManager() && batch.getStatus() == Batch.BatchStatus.AM_REVIEW) {
      var amReviewData = batchService.getAMReviewData(id);
      if (amReviewData != null) {
        res.setAmReviewApprove(amReviewData.getFirst());
        res.setAmReviewRevise(amReviewData.getSecond());
      }
    }
    if (ControllerUtils.isRequester() && batch.getStatus() == Batch.BatchStatus.CLIENT_REVIEW) {
      var requesterReviewData = batchService.getRequesterReviewData(id);
      if (requesterReviewData != null) {
        res.setRequesterReviewApprove(requesterReviewData.getFirst());
        res.setRequesterReviewRevise(requesterReviewData.getSecond());
      }
    }
    return res.toApiResult();
  }

  private BatchStatDTO getBatchStatInfo(Batch batch) {
    var id = batch.getId();
    var tlAnnotation = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .first();
    var dataCount = taskService.getTaskCountInList(tlAnnotation.getId());
    var tlExam = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.EXAM)
        .firstOrNull();
    var examCount = tlExam
        .asOpt()
        .map(TaskList::getId)
        .map(taskService::countTasksByTaskListId)
        .orElse(0L);
    var tlLabel =
        taskListDao.listTaskListsByBatchIdAndType(batch.getId(), LABEL).firstOrNull();
    var questionCount = tlLabel
        .asOpt()
        .map(TaskList::getId)
        .map(tli -> taskService.getTasksByTaskListId(tli).firstOrNull())
        .map(Task::getId)
        .map(taskQuestionDao::countQuestionsByTaskId)
        .orElse(0L);
    var taskCount = jobTaskService.countJobTasksByBatchId(id);
    var submission = taskSessionService.getSubmissionCountByBatchId(id);
    var targetDataCount = dataCount
        * Math.max(1, Objects.isNull(batch.getLabelingRepeat()) ? 1 : batch.getLabelingRepeat());
    return BatchStatDTO.builder()
        .id(id)
        .dataCount(dataCount)
        .taskCount(targetDataCount)
        .assignedCount(taskCount)
        .waitingCount(Math.max(0, targetDataCount - submission))
        .submissions(submission)
        .examCount(examCount)
        .questionCount(questionCount)
        .build();
  }

  private Project getProjectById(Batch batch) {
    return this.projectService.getProjectById(batch.getProjectId()).get();
  }

  private Job preCheckBatchExists(Long id, Collection<Job.JobStatus> status) {
    batchService.getBatchById(id).ensurePresent(() -> ControllerUtils.notFound("Batch not found"));
    var jobs = jobService.listJobsByBatchId(id);
    if (jobs.isEmpty()) throw ControllerUtils.forbidden("the task not launched yet.");
    return jobs.first()
        .asOpt()
        .filter(j -> status.contains(j.getStatus()))
        .orElseThrow(() -> ControllerUtils.forbidden("wrong status"));
  }

  @PostMapping("/{id}/pause")
  @IsAccountManager
  public ApiResult<Batch> pauseBatch(@PathVariable Long id) {
    var job = preCheckBatchExists(id, List.of(Job.JobStatus.WORKING, Job.JobStatus.COMMITTED));
    this.jobService.pauseTask(job.getId());
    return batchService.getById(id).toApiResult();
  }

  @PostMapping("/{id}/resume")
  @IsAccountManager
  public ApiResult<Batch> resumeBatch(@PathVariable Long id) {
    var job = preCheckBatchExists(id, List.of(Job.JobStatus.PAUSING));
    this.jobService.resumeBatch(job.getId());
    return batchService.getById(id).toApiResult();
  }

  @PostMapping("/{id}/terminal")
  @IsAccountManager
  public ApiResult<Batch> terminalBatch(@PathVariable Long id) {
    var job = preCheckBatchExists(
        id, List.of(Job.JobStatus.PAUSING, Job.JobStatus.WORKING, Job.JobStatus.COMMITTED));
    this.jobService.terminalBatch(job.getId());
    return batchService.getById(id).toApiResult();
  }

  @GetMapping("/{id}/edit-history")
  @IsAccountManager
  public ApiResult<List<TaskEditHistoryDTO>> getEditHistoryList(@PathVariable Long id) {
    // TODO: mork data for now.
    List<TaskEditHistoryDTO> res = new ArrayList<>();
    var now = DateUtils.now();
    var now1 = DateUtils.add(now, Duration.ofHours(-1));
    var now2 = DateUtils.add(now, Duration.ofHours(-2));
    var now3 = DateUtils.add(now, Duration.ofHours(-3));
    res.add(TaskEditHistoryDTO.builder()
        .batchId(id)
        .id(1L)
        .deleted(false)
        .detail("some text ...")
        .createdAt(now1)
        .updatedAt(now1)
        .build());
    res.add(TaskEditHistoryDTO.builder()
        .batchId(id)
        .id(2L)
        .deleted(false)
        .detail("some text ......")
        .createdAt(now2)
        .updatedAt(now2)
        .build());
    res.add(TaskEditHistoryDTO.builder()
        .batchId(id)
        .id(3L)
        .deleted(false)
        .detail(
            "some long text: In the heart of the bustling city, amidst the towering skyscrapers and bustling streets, there lies a hidden oasis of tranquility. A small park, enveloped by lush greenery and vibrant flowers, provides a sanctuary from the chaos of urban life. The gentle rustling of leaves in the breeze, the melodious chirping of birds, and the soft murmur of a nearby stream create a symphony of nature's sounds.")
        .createdAt(now3)
        .updatedAt(now3)
        .build());
    return res.toApiResult();
  }

  @PostMapping("/{id}/launch")
  public ApiResult<Batch> launchBatch(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("no permission"));

    BatchSetting setting = this.batchService.getBatchSettingByBatchId(id);

    if (batch.getTaskType().equals(Batch.TaskType.TASK)) {
      checkTaskDataIsReady(batch);
      this.taskVisitorProvider
          .getTaskVisitor(setting.getDistributeType(), JobUser.JobUserRole.LABELER)
          .taskLifecycleManager()
          .initializeTaskQueue(batch, setting);
    } else if (batch.getTaskType().equals(Batch.TaskType.EXAM)) {
      this.batchService.update(
          null,
          new UpdateWrapper<Batch>()
              .lambda()
              .eq(Batch::getId, batch.getId())
              .set(Batch::getStatus, ASSIGN_JOBS)
              .set(Batch::getUpdatedAt, DateUtils.now()));
    }
    var res = this.batchService.getBatchById(id);
    if (res.isEmpty()) throw ControllerUtils.notFound("launch failed");
    return res.get().toApiResult();
  }

  private void checkTaskDataIsReady(Batch batch) {
    // check rewardToken is set
    var rewardRecords = batchRewardRecordDao.listByBatchId(batch.getId());
    if (rewardRecords.size() <= 0) {
      throw ControllerUtils.forbidden("Reward Pool not set yet.");
    }
  }

  @GetMapping("/{id}/job")
  public ApiResult<Job> getJobByBatchId(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b ->
            Batch.TaskType.TASK.equals(b.getTaskType()) && EXTERNAL_LABELER.equals(b.getUserType()))
        .ensurePresent(() -> ControllerUtils.forbidden("invalid data"));
    var jobs = jobService.listJobsByBatchId(id);
    if (jobs.isEmpty()) throw ControllerUtils.forbidden("invalid data");
    return jobs.first().toApiResult();
  }

  @GetMapping("/{id}/jobs-for-am-audit")
  @IsAccountManager
  @Operation(summary = "Get jobs for account manager audit")
  public ApiResult<List<AmAuditJobDTO>> getJobsForAMAudit(@PathVariable Long id) {
    var jobs = jobService.getAmAuditJobsByBatchId(id);
    var details = batchDataService.getAuditJobDetails(jobs);
    return details.toApiResult();
  }

  @GetMapping("/{id}/jobs")
  @Operation(summary = "Get jobs for a job")
  public ApiResult<List<AmAuditJobDTO>> getJobsByBatch(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            b -> Objects.equals(b.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't get other user's batch"));

    var jobs = jobService.listJobsByBatchId(id);
    var details = batchDataService.getAuditJobDetails(jobs);
    return details.toApiResult();
  }

  // @GetMapping("/{id}/stats-for-am-audit")
  // @IsAccountManager
  // @Operation(summary = "Get stats for account manager audit")
  // public ApiResult<AmJobAuditStats> getAmAuditStats(@PathVariable Long id) {
  // return taskSessionService.getAmAuditStatsByBatchId(id)
  // .toApiResult();
  // }

  @GetMapping("/{id}/submissions")
  @CheckPageParam()
  public ApiResult<PageResult<TaskSession>> getAllSubmissions(
      @PathVariable Long id,
      @RequestParam(required = false) String status,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam Integer size) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            b -> Objects.equals(b.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't get other user's batch"));

    return taskSessionService
        .getSubmissionsByBatchId(id, status, page, size)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/submission-stats")
  public ApiResult<JobAmAuditStatDTO> getSubmissionStats(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            b -> Objects.equals(b.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't get other user's batch"));

    return taskSessionService.getSubmissionStatsByBatch(id).toApiResult();
  }

  @PostMapping("/{id}/take-client-audit-job")
  public ApiResult<Optional<TakeJobResultDTO>> takeClientAuditJob(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            b -> Objects.equals(b.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't get other user's batch"));
    return taskSessionService
        .takeAuditJobForClient(id)
        .map(v -> TakeJobResultDTO.builder()
            .taskSession(v)
            .resource(taskService.getResourceByTaskId(v.getTaskId()).orElse(null))
            .build())
        .toApiResult();
  }

  @GetMapping("/{id}/ndas")
  public ApiResult<List<BatchNDA>> getBatchNdas(@PathVariable Long id) {
    // TODO: permission
    return batchNDADao.getNDAsByBatch(id).toApiResult();
  }

  @PostMapping("/{id}/ndas")
  public ApiResult<List<BatchNDA>> updateNdas(
      @PathVariable Long id, @RequestBody SimpleDataDTO<List<BatchNDA>> ndas) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            b -> Objects.equals(b.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't get other user's batch"));
    var currentNdaList = batchNDADao.getNDAsByBatch(id);
    var currentNdaMap = currentNdaList.toMap(v -> v.getId(), v -> v);
    ndas.getData().forEachIndexed((idx, v) -> {
      v.setSort(idx);
      v.setOwnerId(ControllerUtils.currentUid());
    });
    var newNdasMap = ndas.getData().filter(v -> v.getId() != null).toMap(v -> v.getId(), v -> v);
    var deletedNdas = currentNdaList.stream()
        .filter(v -> v.getId() != null && !newNdasMap.containsKey(v.getId()))
        .map(v -> v.getId())
        .toList();
    var newNdas = ndas.getData().stream().filter(v -> v.getId() == null).toList();
    var updatedNdas = ndas.getData().stream()
        .filter(v -> v.getId() != null && currentNdaMap.containsKey(v.getId()))
        .toList();
    batchNDADao.batchUpdateNda(id, deletedNdas, updatedNdas, newNdas);
    return batchNDADao.getNDAsByBatch(id).toApiResult();
  }

  @PostMapping("/{id}/confirm-payment")
  public ApiResult<Batch> confirmPayment(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(
            b -> Objects.equals(b.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("can't get other user's batch"));
    if (batch.getStatus() != Batch.BatchStatus.SETTLING) {
      throw ControllerUtils.forbidden("batch status is not SETTLING");
    }
    var newBatch = Batch.builder()
        .id(id)
        .status(Batch.BatchStatus.FINISHED)
        .updatedAt(DateUtils.now())
        .build();
    batchService.updateById(newBatch);
    newBatch = batchService.getBatchById(id).get();
    var uid = ControllerUtils.currentUid();
    log.info("batch ${id} status updated to ${newBatch.getStatus()} by ${uid}");
    return newBatch.toApiResult();
  }

  @GetMapping("/{id}/requester-accept-sign-message")
  public ApiResult<BatchRequesterAcceptSignMessageDTO> getRequesterAcceptSignMessage(
      @PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));
    if (batch.getStatus() != Batch.BatchStatus.CLIENT_REVIEW) {
      throw ControllerUtils.badRequest("batch status is not CLIENT_REVIEW");
    }

    // FIXME: enforce requester check
    var requester = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("requester not found"));

    var committedJobIds =
        jobService.getCommittedJobIdsByBatchId(batch.getId()).map(Job::getId).toList();
    if (committedJobIds.isEmpty()) {
      ControllerUtils.badRequest("am hasn't committed to the batch yet");
    }

    return taskSessionService
        .getRequesterAcceptSignMessage(id, committedJobIds, requester)
        .toApiResult();
  }

  @PostMapping("/{id}/requester-reject")
  public ApiResult<Boolean> requesterReject(@PathVariable Long id) {
    var batch = batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't reject other user's batch"));
    if (batch.getStatus() != Batch.BatchStatus.CLIENT_REVIEW) {
      throw ControllerUtils.badRequest("batch status is not CLIENT_REVIEW");
    }
    var committedJobIds =
        jobService.getCommittedJobIdsByBatchId(batch.getId()).map(Job::getId).toList();

    batchService.requesterReject(id, committedJobIds);
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/requester-accept")
  public ApiResult<Boolean> requesterAccept(
      @PathVariable Long id, @Validated @RequestBody SignatureDTO payload) {
    if (Web2AuthService.isWeb3Login() && payload.getSignature().isEmpty()) {
      throw ControllerUtils.badRequest("missing signature");
    }

    var batch = batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("can't approve other user's batch"));
    if (batch.getStatus() != Batch.BatchStatus.CLIENT_REVIEW) {
      throw ControllerUtils.badRequest("batch status is not CLIENT_REVIEW");
    }

    // FIXME: enforce requester check
    var requester = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("requester not found"));
    var committedJobIds =
        jobService.getCommittedJobIdsByBatchId(batch.getId()).map(Job::getId).toList();

    if (payload.getSignature().isPresent()) {
      var signMessage =
          taskSessionService.getRequesterAcceptSignMessage(id, committedJobIds, requester);
      var eip712msg = new EIP712Message(signMessage.getDomain(), signMessage.getData());
      var sigAddress =
          CryptoUtils.recoverAddress(eip712msg.hashMsg(), payload.getSignature().get());
      if (0
          != StringUtils.compareIgnoreCase(
              Numeric.cleanHexPrefix(requester.getWalletAddress()),
              Numeric.cleanHexPrefix(sigAddress))) {
        throw ControllerUtils.badRequest("signature mismatch");
      }
    }

    batchService.requesterAccept(id, committedJobIds);

    return ApiResult.success(true);
  }

  @PostMapping("/{id}/export")
  public ApiResult<ExportBatchResultDTO> exportBatch(
      @PathVariable Long id, @RequestBody BatchExportQueryDTO query) {
    var uid = ControllerUtils.currentUid();
    var user = userService
        .getUserById(uid)
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("user not found"));
    var fileType = query.getFileType();
    if (Objects.isNull(fileType)) {
      throw ControllerUtils.badRequest("illegal fileType");
    }
    var batch = batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("batch not found"))
        .filter(b -> List.of(
                ASSIGN_JOBS,
                Batch.BatchStatus.AM_REVIEW,
                Batch.BatchStatus.CLIENT_REVIEW,
                Batch.BatchStatus.COMMITTED,
                Batch.BatchStatus.SETTLING,
                Batch.BatchStatus.CLIENT_REJECTED,
                Batch.BatchStatus.FINISHED)
            .contains(b.getStatus()))
        .orThrow(() -> ControllerUtils.notFound("batch has no labeling"))
        .filter(b -> EXPORT_BATCH_WHITE_LIST_ADDRESSES.contains(user.getWalletAddress())
            || Objects.equals(uid, b.getOwnerId())
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("you are not allowed to export this batch"));
    var qs = objectMapper.convertValue(query, new TypeReference<Map<String, Object>>() {});
    var keysHash = qs.entrySet()
        .filter(q -> Boolean.TRUE.equals(q.getValue()))
        .map(Map.Entry::getKey)
        .sorted()
        .collect(Collectors.joining(";"))
        .hashCode();
    var config = objectMapper.writeValueAsString(query);
    var taskKey = "batch:${id}:${fileType}:${keysHash}";
    var dataTask = DataTask.builder()
        .ownerId(uid)
        .taskType(DataTask.TaskType.BATCH_EXPORT)
        .config(config)
        .taskKey(taskKey)
        .status(DataTask.TaskStatus.PENDING)
        .build();
    dataTaskService.createDataTask(dataTask);
    this.clusterConfiguration
        .getBatchExportActor()
        .tell(new DataTaskScheduler.CheckPendingDataTask(), ActorRef.noSender());

    log.info("batch ${id} ${batch} export ${dataTask} by ${uid}");
    return ExportBatchResultDTO.builder()
        .dataTaskId(dataTask.getId())
        .resourceId(dataTask.getResourceId())
        .status(dataTask.getStatus())
        .build()
        .toApiResult();
  }

  @GetMapping("/{id}/export-download/{dataTaskId}")
  public ResponseEntity<InputStreamSource> downloadFile(
      @PathVariable Long id, @PathVariable Long dataTaskId) {
    var uid = ControllerUtils.currentUid();
    var user = userService
        .getUserById(uid)
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("user not found"));
    var batch = batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("batch not found"))
        .filter(b -> EXPORT_BATCH_WHITE_LIST_ADDRESSES.contains(user.getWalletAddress())
            || Objects.equals(uid, b.getOwnerId())
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("you are not allowed to export this batch"));
    var dataTask = dataTaskService
        .getDataTaskById(dataTaskId)
        .filter(dt -> !dt.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("data_task not found"))
        .filter(dt -> DataTask.TaskStatus.SUCCESS.equals(dt.getStatus()))
        .orThrow(() -> ControllerUtils.notFound("data_task not success yet"))
        .filter(dt -> dt.getTaskKey().startsWith("batch:${batch.getId()}"))
        .orThrow(() -> ControllerUtils.notFound("data_task not match this batch"))
        .filter(dt -> EXPORT_BATCH_WHITE_LIST_ADDRESSES.contains(user.getWalletAddress())
            || Objects.equals(uid, dt.getOwnerId())
            || ControllerUtils.isAccountManager())
        .orElseThrow(
            () -> ControllerUtils.forbidden("you are not allowed to download this data_task"));

    var r = resourceService
        .getResourceById(dataTask.getResourceId())
        .filter(res -> !res.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Resource not found"));

    MediaType contentType = MediaType.TEXT_PLAIN;
    InputStream in = resourceService.downloadFile(r);
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=${r.getName()}")
        .contentType(contentType)
        .cacheControl(CacheControl.maxAge(Duration.ofMinutes(2)))
        .body(new InputStreamResource(in));
  }

  @GetMapping("/{id}/charts")
  @Cacheable(value = "batchCharts", cacheManager = "apiCacheManager")
  public ApiResult<Map<BatchChartType, List<Chart2<ZonedDateTime, Object>>>> batchCharts(
      @PathVariable Long id,
      @RequestParam Long startTime,
      @RequestParam Long endTime,
      @RequestParam(defaultValue = "1") Integer intervalCount,
      @RequestParam(defaultValue = "DAYS") ChronoUnit intervalUnit,
      @RequestParam List<BatchChartType> types) {
    if (CollectionUtils.isEmpty(types) || startTime > endTime) {
      return ApiResult.success(Map.of());
    }
    return ApiResult.success(batchChartService.batchChart(
        id,
        Timestamp.from(Instant.ofEpochMilli(startTime)),
        Timestamp.from(Instant.ofEpochMilli(endTime)),
        intervalCount,
        intervalUnit,
        types));
  }

  private boolean passForManagers() {
    // TODO: check for project account manager
    return ControllerUtils.isAdmin()
        || ControllerUtils.isAccountManager()
        || ControllerUtils.isQueueManager()
        || ControllerUtils.isNodeManager();
  }
}
