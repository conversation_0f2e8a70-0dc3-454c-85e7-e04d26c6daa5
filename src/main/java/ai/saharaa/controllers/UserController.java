package ai.saharaa.controllers;

import static ai.saharaa.config.AuditLogInterceptor.REQUEST_USER_AGENT;
import static ai.saharaa.utils.Constants.ROLE_EXTERNAL_USER;
import static akka.pattern.Patterns.ask;

import ai.saharaa.actors.UserRegisterActor;
import ai.saharaa.config.AuditLogInterceptor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.config.perms.IsApiKeyClient;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.LoginResultDTO;
import ai.saharaa.dto.UserRegisterDTO;
import ai.saharaa.dto.achievement.OffChainUserLevelUpVerifyDTO;
import ai.saharaa.dto.achievement.OnChainUserLevelUpSuccessDTO;
import ai.saharaa.dto.node.JoinNodeDTO;
import ai.saharaa.dto.node.NodeDetailDTO;
import ai.saharaa.dto.stat.UserStatDetailsDTO;
import ai.saharaa.dto.user.*;
import ai.saharaa.dto.web2email.Web2EmailChangePasswordDTO;
import ai.saharaa.dto.web2email.Web2EmailResetPasswordDTO;
import ai.saharaa.dto.web2email.Web2EmailSendResetPasswordTokenDTO;
import ai.saharaa.dto.web2email.Web2EmailSignupDTO;
import ai.saharaa.mappers.UserMapper;
import ai.saharaa.model.*;
import ai.saharaa.model.api.ApiKeys;
import ai.saharaa.model.stat.UserJobStat;
import ai.saharaa.services.*;
import ai.saharaa.services.achievement.AchievementService;
import ai.saharaa.services.achievement.SaharaLevelService;
import ai.saharaa.services.stat.UserJobStatService;
import ai.saharaa.services.user.TokenValidationService;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import com.google.common.base.Ascii;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@RestController
@RequestMapping("/api/users")
public class UserController {
  private final Logger log = org.slf4j.LoggerFactory.getLogger(UserController.class);
  private final ClusterConfiguration clusterConfiguration;

  private final UserService userService;
  private final CaptchaService captchaService;
  private final NodeService nodeService;
  private final UserJobStatService userJobStatService;
  private final Web2AuthService web2AuthService;
  private final JwtTokenService jwtTokenService;
  private final AuditLogService auditLogService;
  private final UserSettingService userSettingService;
  private final RestTemplate restTemplate;
  private final SaharaLevelService saharaLevelService;
  private final AchievementService achievementService;
  private final WorkloadLimitService workloadLimitService;
  private final TokenValidationService tokenValidationService;

  public UserController(
      ClusterConfiguration clusterConfiguration,
      UserService userService,
      UserMapper userMapper,
      ResourceService resourceService,
      NodeService nodeService,
      UserJobStatService userJobStatService,
      Web2AuthService web2AuthService,
      JwtTokenService jwtTokenService,
      CaptchaService captchaService,
      AuditLogService auditLogService,
      UserSettingService userSettingService,
      RestTemplate restTemplate,
      SaharaLevelService saharaLevelService,
      AchievementService achievementService,
      WorkloadLimitService workloadLimitService,
      TokenValidationService tokenValidationService) {
    this.clusterConfiguration = clusterConfiguration;
    this.userService = userService;
    this.nodeService = nodeService;
    this.userJobStatService = userJobStatService;
    this.web2AuthService = web2AuthService;
    this.jwtTokenService = jwtTokenService;
    this.captchaService = captchaService;
    this.auditLogService = auditLogService;
    this.userSettingService = userSettingService;
    this.restTemplate = restTemplate;
    this.saharaLevelService = saharaLevelService;
    this.achievementService = achievementService;
    this.workloadLimitService = workloadLimitService;
    this.tokenValidationService = tokenValidationService;
  }

  @PostMapping("/register")
  public Future<ApiResult<User>> register(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
    // TODO: pre validation
    var reg = UserRegisterActor.RegisterUser.builder()
        .walletAddress(Ascii.toLowerCase(userRegisterDTO.getAddress()))
        .firstName(userRegisterDTO.getFirstName())
        .lastName(userRegisterDTO.getLastName())
        .role(0)
        .build();
    var result = ask(clusterConfiguration.getUserRegisterActor(), reg, Duration.ofSeconds(10L));
    return result.toCompletableFuture().thenApply(v -> ((SimpleResult<User>) v).asApiResult());
  }

  @PostMapping("/email-signup")
  public ApiResult<LoginResultDTO> signup(
      HttpServletRequest request, @Valid @RequestBody Web2EmailSignupDTO payload) {
    if (!web2AuthService.isEnabled(request).join()) {
      throw ControllerUtils.notFound("unknown api");
    }

    var role = payload.getRole();
    if (role != Constants.ROLE_REQUESTER
        && role != Constants.ROLE_USER
        && role != ROLE_EXTERNAL_USER
        && role != Constants.ROLE_NODE_MANAGER) {
      throw ControllerUtils.badRequest("Unexpected role");
    }

    Boolean captchaResult = captchaService.verifyCaptchaToken(payload.getCaptcha(), restTemplate);
    if (!captchaResult) {
      throw ControllerUtils.badRequest("please retry");
    }

    try {
      var user = web2AuthService.signup(payload);
      var token = jwtTokenService.buildToken(payload.getEmail());
      return LoginResultDTO.builder()
          .token(token)
          .user(user)
          .accountID(user.getId())
          .build()
          .toApiResult();
    } catch (Throwable e) {
      throw ControllerUtils.badRequest(e.getMessage());
    }
  }

  @PostMapping("/email-send-reset-password")
  public Future<ApiResult<Void>> sendEmailResetPassword(
      HttpServletRequest request, @Valid @RequestBody Web2EmailSendResetPasswordTokenDTO payload) {
    if (!web2AuthService.isEnabled(request).join()) {
      throw ControllerUtils.notFound("unknown api");
    }

    Boolean captchaResult = captchaService.verifyCaptchaToken(payload.getCaptcha(), restTemplate);
    if (!captchaResult) {
      throw ControllerUtils.badRequest("please retry");
    }
    try {
      return web2AuthService
          .sendResetPasswordEmail(payload.getEmail())
          .thenApply(v -> ApiResult.success(null));
    } catch (Throwable e) {
      throw ControllerUtils.badRequest(e.getMessage());
    }
  }

  @PostMapping("/email-reset-password/{token}")
  public ApiResult<Void> sendEmailResetPassword(
      HttpServletRequest request,
      @PathVariable String token,
      @Validated @RequestBody Web2EmailResetPasswordDTO payload) {
    if (!web2AuthService.isEnabled(request).join()) {
      throw ControllerUtils.notFound("unknown api");
    }

    try {
      var ua = (AuditLogInterceptor.RequestUserAgent) request.getAttribute(REQUEST_USER_AGENT);

      return ApiResult.success(web2AuthService.resetPassword(token, payload, ua));
    } catch (Throwable e) {
      throw ControllerUtils.badRequest(e.getMessage());
    }
  }

  @PostMapping("/change-password")
  public ApiResult<Boolean> changePassword(@Valid @RequestBody Web2EmailChangePasswordDTO payload) {
    if (Web2AuthService.isWeb3Login()) {
      throw ControllerUtils.notFound("unknown api");
    }

    var user = userService
        .getUserById(ControllerUtils.currentUid())
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.badRequest("user not found"));

    try {
      return web2AuthService.changePassword(user, payload).toApiResult();
    } catch (Throwable e) {
      throw ControllerUtils.badRequest(e.getMessage());
    }
  }

  @GetMapping("/self")
  @ResponseBody
  public ApiResult<UserDTO> self() {
    var r = userService.getUserById(ControllerUtils.currentUid()).get();
    return ApiResult.success(r);
  }

  @GetMapping("/{id}")
  @IsAccountManager
  public ApiResult<UserDetailsDTO> getUserProfile(@PathVariable Long id) {
    UserDTO user = tokenValidationService
        .getUserById(id)
        .orElseThrow(() -> ControllerUtils.notFound("User not found"));
    List<UserSetting> userSettings = this.userSettingService.getUserSettingByUserId(id);
    return ApiResult.success(
        UserDetailsDTO.builder().userSettings(userSettings).user(user).build());
  }

  @GetMapping("/by-address/{address}")
  public ApiResult<Optional<UserDTO>> getUserByAddress(@PathVariable String address) {
    if (!ControllerUtils.isAdmin() && !ControllerUtils.isAccountManager()) {
      throw ControllerUtils.forbidden("no permission");
    }
    var u = userService.findUserByAddress(address);
    return ApiResult.success(u);
  }

  @PostMapping("/profile/avatar")
  @ResponseBody
  public ApiResult<User> setAvatar() {
    // todo:
    return ApiResult.success(null);
  }

  @GetMapping("/query-profiles")
  @IsAccountManager
  public ApiResult<List<UserWalletAddressDTO>> queryUserProfiles(
      @RequestParam("ids") List<Long> ids) {
    ids = ids.toSet().toList();
    if (ids.size() > 1000) {
      throw ControllerUtils.badRequest("ids size must be less than 1000");
    }
    var users = tokenValidationService.getUsersByIds(ids);

    return users.toApiResult().getData();
  }

  @GetMapping("/nodes")
  @CheckPageParam()
  public ApiResult<PageResult<NodeDetailDTO>> getNodesList(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "") String name,
      @RequestParam(defaultValue = "10") Integer limit) {
    return nodeService.getNodesList(page, limit, name).toPageResult().toApiResult();
  }

  @PostMapping("/nodes/{id}/join")
  public ApiResult<NodeInvitation> joinNodeRequest(
      @PathVariable Long id, @RequestBody JoinNodeDTO data) {
    var note = data == null ? "" : data.getNote().asOpt().orElse("");

    var nodeInvitation =
        nodeService.joinNodeRequest(id, ControllerUtils.currentUid(), note).toApiResult();

    var userOpt = tokenValidationService.getUserById(ControllerUtils.currentUid());
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ua = (AuditLogInterceptor.RequestUserAgent) request.getAttribute(REQUEST_USER_AGENT);
    var currentUser = userOpt
        .filter((user) -> !user.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("cannot find current user"));
    auditLogService.userJoinNode(currentUser, "request to join node", ua);
    return nodeInvitation;
  }

  @GetMapping("/{id}/stats")
  public ApiResult<UserJobStat> getUserStats(@PathVariable Long id) {
    return userJobStatService.getUserStatByUserId(id).toApiResult();
  }

  @GetMapping("/query-stats")
  public ApiResult<List<UserJobStat>> getUserStats(@RequestParam List<Long> uids) {
    return userJobStatService.queryUserStats(uids).toApiResult();
  }

  @GetMapping("/{id}/last-week-stats")
  public ApiResult<UserJobStat> getUserLastWeekStats(@PathVariable Long id) {
    return userJobStatService.getUserStatLastWeek(id).toApiResult();
  }

  @GetMapping("/{id}/detailed-stats")
  public ApiResult<UserStatDetailsDTO> getUserDetailedSTats(@PathVariable Long id) {
    var historical = userJobStatService.getUserStatByUserId(id);
    var lastWeek = userJobStatService.getUserStatLastWeek(id);
    return UserStatDetailsDTO.builder()
        .historical(historical)
        .lastWeek(lastWeek)
        .build()
        .toApiResult();
  }

  @GetMapping("/query-last-week-stats")
  public ApiResult<List<UserJobStat>> getUserLastWeekStats(@RequestParam List<Long> uids) {
    return userJobStatService.queryUserStatsLastWeek(uids).toApiResult();
  }

  @GetMapping("/query-all")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<User>> queryAllUsers(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam Optional<String> address,
      @RequestParam Optional<String> query,
      @RequestParam Optional<Integer> role,
      @RequestParam(defaultValue = "true") Boolean deleted,
      @RequestParam(defaultValue = "id") UserFields sortBy,
      @RequestParam(defaultValue = "false") Boolean desc) {
    return userService
        .queryUsers(address, query, role, page, size, deleted, sortBy, desc)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/offchain-levelup-verify")
  @IsApiKeyClient(ApiKeys.Type.ONCHAIN)
  public ApiResult<OffChainUserLevelUpVerifyDTO> offVerify(
      @RequestBody OffChainUserLevelUpVerifyDTO dto) {
    var user = userService
        .getUserByWalletAddress(dto.getWalletAddress())
        .orElseThrow(() -> ControllerUtils.notFound("user not found"));
    var saharaLevel = saharaLevelService.getByUserId(user.getId());
    var level = Objects.isNull(saharaLevel) ? 0 : saharaLevel.getOnChainLevel() - 1;
    var exp = Objects.isNull(saharaLevel) ? 0L : saharaLevel.getClaimedExp();

    if (dto.getLevel() < level) {
      throw ControllerUtils.forbidden("level can't be lower than current level");
    } else if (dto.getLevel().equals(level)) {
      if (dto.getExp() <= exp) {
        return ApiResult.success(dto);
      }
      Boolean verified = achievementService.offChainVerifyClaimExp(
          dto.getLevel(), dto.getExp() - exp, user.getId());
      if (!verified) {
        throw ControllerUtils.forbidden("not eligible to claim exp");
      }
    } else {
      Boolean verified = saharaLevelService.offChainUserLevelUpVerify(
          dto.getLevel(), dto.getExp(), dto.getWalletAddress());
      if (!verified) {
        throw ControllerUtils.forbidden("not eligible to level up");
      }
    }

    return ApiResult.success(dto);
  }

  @PostMapping("/onchain-levelup-success")
  @IsApiKeyClient(ApiKeys.Type.ONCHAIN)
  public ApiResult<OnChainUserLevelUpSuccessDTO> onSuccess(
      @RequestBody OnChainUserLevelUpSuccessDTO dto) {
    var levelAndExp = saharaLevelService.fetchUserSaharaLevelAndExp(dto.getWalletAddress());
    log.info("onchain-levelup-success levelAndExp ${levelAndExp}");
    var level = levelAndExp.getT1();
    var exp = levelAndExp.getT2();
    if (!dto.getLevel().equals(level) || !dto.getExp().equals(exp)) {
      log.info(
          "onchain-levelup-success error level ${dto.getLevel()}:${level}, exp ${dto.getExp()}:${exp}");
      return ApiResult.success(dto);
    }

    var saharaLevel = saharaLevelService.getByWalletAddress(dto.getWalletAddress());
    if (Objects.isNull(saharaLevel)) {
      log.info("onchain-levelup-success saharaLevel not found ${dto.getWalletAddress()}");
      return ApiResult.success(dto);
    }

    if (dto.getLevel() < saharaLevel.getOnChainLevel() - 1) {
      log.info("onchain-levelup-success level can't be lower than current level");
    } else if (dto.getLevel().equals(saharaLevel.getOnChainLevel() - 1)) {
      try {
        var success = achievementService.onChainSuccessClaimExp(
            dto.getLevel(), dto.getExp(), dto.getWalletAddress());
        if (!success) {
          log.info("onchain-levelup-success level claim exp not success");
        }
      } catch (HttpClientErrorException e) {
        log.info("onchain-levelup-success onChainSuccessClaimExp error ${e}");
      }
    } else {
      if (!Objects.equals(dto.getExp(), saharaLevel.getClaimedExp())) {
        log.info("onchain-levelup-success exp changed");
        return ApiResult.success(dto);
      }
      Boolean success =
          saharaLevelService.onChainUserLevelUpSuccess(dto.getLevel(), dto.getExp(), saharaLevel);
      if (!success) {
        log.info("onchain-levelup-success not success");
      }
    }

    return ApiResult.success(dto);
  }

  @PostMapping("/sync-levelup")
  public ApiResult<Boolean> syncLevelUp() {
    var curId = ControllerUtils.currentUid();
    Boolean success = saharaLevelService.syncLevelUpStatus(curId);
    if (!success) {
      throw ControllerUtils.forbidden("not eligible to level up");
    }
    return ApiResult.success(success);
  }

  @GetMapping("/email-exists")
  public ApiResult<Boolean> emailExists(@RequestParam String email) {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(userService
        .getUserByEmail(email)
        .filter(user -> !Objects.equals(curId, user.getId()))
        .isPresent());
  }

  @GetMapping("/registered")
  @IsApiKeyClient({ApiKeys.Type.GALXE, ApiKeys.Type.DATA_SERVICE})
  @CrossOrigin
  public ApiResult<Boolean> addressRegistered(@RequestParam String address) {
    if (StringUtils.isBlank(address)) {
      return ApiResult.success(Boolean.FALSE);
    }
    return ApiResult.success(
        userService.getUserByWalletAddress(address.toLowerCase()).isPresent());
  }

  @GetMapping("/whitelisted")
  @IsApiKeyClient({ApiKeys.Type.GALXE, ApiKeys.Type.DATA_SERVICE})
  @CrossOrigin
  public ApiResult<Boolean> addressWhitelisted(@RequestParam String address) {
    if (StringUtils.isBlank(address)) {
      return ApiResult.success(Boolean.FALSE);
    }
    return ApiResult.success(userService.isWhiteListed(address.toLowerCase()));
  }

  @GetMapping("/count-by-completed-beginner-guide")
  @IsApiKeyClient(ApiKeys.Type.DATA_SERVICE)
  public ApiResult<Long> getCountByCompletedBeginnerGuide() {
    Long count = userService.getCountByCompletedBeginnerGuide();
    return ApiResult.success(count);
  }

  @GetMapping("/workload")
  public ApiResult<Integer> getUserLeftWorkload() {
    return ApiResult.success(
        workloadLimitService.getUserLeftWorkload(ControllerUtils.currentUid()));
  }
}
