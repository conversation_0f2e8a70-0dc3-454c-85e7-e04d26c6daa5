package ai.saharaa.controllers;

import static ai.saharaa.model.Batch.beforeLaunchBatchStatusList;

import ai.saharaa.actors.batch.BatchContentActor;
import ai.saharaa.actors.tasks.BatchSampleActor;
import ai.saharaa.actors.tasks.TaskActor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.config.perms.IsLabeler;
import ai.saharaa.daos.QuestionAnswerDao;
import ai.saharaa.daos.QuestionGroupDao;
import ai.saharaa.daos.TaskListDao;
import ai.saharaa.daos.TaskQuestionDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.SimpleDataDTO;
import ai.saharaa.dto.res.HybridResSortDTO;
import ai.saharaa.dto.task.AnswerDetailsDTO;
import ai.saharaa.dto.task.BatchUpdateTaskQuestionDTO;
import ai.saharaa.dto.task.CreateQuestionGroupDTO;
import ai.saharaa.dto.task.CreateTaskDTO;
import ai.saharaa.dto.task.HybridLabelingResourcesDTO;
import ai.saharaa.dto.task.HybridResourceDTO;
import ai.saharaa.dto.task.PreSignedUrlUploadDTO;
import ai.saharaa.dto.task.PreSignedUrlUploadResultDTO;
import ai.saharaa.dto.task.SubmitTaskExampleDTO;
import ai.saharaa.dto.task.TaskDetailsDTO;
import ai.saharaa.dto.task.TaskInfoDTO;
import ai.saharaa.dto.task.UpdateQuestionDTO;
import ai.saharaa.dto.task.UpdateQuestionGroupDTO;
import ai.saharaa.model.*;
import ai.saharaa.services.BatchSampleService;
import ai.saharaa.services.BatchService;
import ai.saharaa.services.HybridTaskResourceService;
import ai.saharaa.services.ICloudStorageService;
import ai.saharaa.services.JobService;
import ai.saharaa.services.ResourceService;
import ai.saharaa.services.TaskQuestionService;
import ai.saharaa.services.TaskService;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.google.common.collect.Streams;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/tasks")
public class TaskController {
  private final TaskService taskService;
  private final ICloudStorageService cloudStorageService;
  private final JobService jobService;
  private final TaskListDao taskListDao;
  private final HybridTaskResourceService hybridTaskResourceService;
  private final ResourceService resourceService;
  private final TaskQuestionService taskQuestionService;
  private final TaskQuestionDao taskQuestionDao;
  private final QuestionGroupDao questionGroupDao;
  private final BatchService batchService;

  private final BatchSampleService batchSampleService;

  private final QuestionAnswerDao questionAnswerDao;
  private final ClusterConfiguration clusterConfiguration;

  public TaskController(
      ClusterConfiguration clusterConfiguration,
      TaskService taskService,
      TaskListDao taskListDao,
      ResourceService resourceService,
      TaskQuestionService taskQuestionService,
      TaskQuestionDao taskQuestionDao,
      BatchService batchService,
      BatchSampleService batchSampleService,
      QuestionAnswerDao questionAnswerDao,
      HybridTaskResourceService hybridTaskResourceService,
      JobService jobService,
      QuestionGroupDao questionGroupDao,
      ICloudStorageService cloudStorageService) {
    this.clusterConfiguration = clusterConfiguration;
    this.taskService = taskService;
    this.cloudStorageService = cloudStorageService;
    this.taskListDao = taskListDao;
    this.resourceService = resourceService;
    this.taskQuestionService = taskQuestionService;
    this.taskQuestionDao = taskQuestionDao;
    this.batchService = batchService;
    this.batchSampleService = batchSampleService;
    this.questionAnswerDao = questionAnswerDao;
    this.jobService = jobService;
    this.hybridTaskResourceService = hybridTaskResourceService;
    this.questionGroupDao = questionGroupDao;
  }

  @PostMapping
  public ApiResult<Task> createTask(@Valid @RequestBody CreateTaskDTO createTaskDTO) {
    var tl = taskListDao
        .getTaskListById(createTaskDTO.getTaskListId())
        .filter(t -> !t.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Task list not found"));
    if (!tl.getOwnerId().equals(ControllerUtils.currentUid())) {
      throw ControllerUtils.forbidden("You are not the owner of this task list");
    }
    var res = resourceService
        .getResourceById(createTaskDTO.getResourceId())
        .filter(r -> !r.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Resource not found"));
    if (!res.getOwnerId().equals(ControllerUtils.currentUid())) {
      throw ControllerUtils.forbidden("You are not the owner of this resource");
    }
    // validate parent task
    var sort = taskService.getMaxSortByTaskListId(createTaskDTO.getTaskListId());
    // TODO: validate task type

    var task = Task.builder()
        .taskListId(createTaskDTO.getTaskListId())
        .resourceId(createTaskDTO.getResourceId())
        .ownerId(ControllerUtils.currentUid())
        .sort(sort)
        .build();
    return taskService.createTask(task).toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<Task> getTaskById(@PathVariable Long id) {
    var task = taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Task not found"));
    if (!task.getOwnerId().equals(ControllerUtils.currentUid())
        && !ControllerUtils.isAdmin()
        && !ControllerUtils.isAccountManager()
        && !ControllerUtils.isNodeManager()) {
      throw ControllerUtils.forbidden("You are not the owner of this task");
    }

    return task.toApiResult();
  }

  @GetMapping("/{id}/details")
  public ApiResult<TaskDetailsDTO> getTaskDetailsById(@PathVariable Long id) {
    var task = taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Task not found"));
    if (!task.getOwnerId().equals(ControllerUtils.currentUid())
        && !ControllerUtils.isAdmin()
        && !ControllerUtils.isAccountManager()
        && !ControllerUtils.isNodeManager()) {
      throw ControllerUtils.forbidden("You are not the owner of this task");
    }
    TaskDetailsDTO res = taskService.getTaskDetails(task);
    var taskList = taskListDao
        .getTaskListById(res.getTask().getTaskListId())
        .filter(t -> !t.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("TaskList not found"));
    var batch = batchService
        .getBatchById(taskList.getBatchId())
        .filter(t -> !t.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));
    if (batch.getLabelType() == Batch.BatchLabelType.HYBRID) {
      Optional<List<HybridResourceDTO>> hybridResourceList = taskService.getHybridTaskResource(id);
      hybridResourceList.ifPresent(res::setHybridTaskResources);
    }

    return res.toApiResult();
  }

  @GetMapping("/getHybridLabelingTaskDetail/byBatchId/{id}")
  public ApiResult<HybridLabelingResourcesDTO> getHybridTaskDetail(@PathVariable Long id) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted() && b.getLabelType() == Batch.BatchLabelType.HYBRID)
        .ensurePresent(() -> ControllerUtils.notFound("batch not found"));
    var taskList = taskListDao.listTaskListsByBatchIdAndType(id, TaskList.TaskListType.LABEL);

    var normalTask = taskService.getTasksByTaskListIds(
        Collections.singletonList(taskList.first().getId()));

    var normalResources = resourceService.getResourceByIds(normalTask.stream()
        .filter(t -> !t.getIsHybrid())
        .map(Task::getResourceId)
        .toList());

    var task = taskService.getOnlyHybridSampleTaskByTaskListId(taskList.get(0).getId());
    if (task.isPresent()) {
      return HybridLabelingResourcesDTO.builder()
          .normalResources(normalResources)
          .hybridResources(
              hybridTaskResourceService.listResourcesByTaskId(task.get().getId()))
          .build()
          .toApiResult();
    }
    List<Resource> emptyRes = new ArrayList<>();
    return HybridLabelingResourcesDTO.builder()
        .normalResources(normalResources)
        .hybridResources(emptyRes)
        .build()
        .toApiResult();
  }

  @GetMapping("/getHybridLabelingTaskDetail/byTaskId/{id}")
  public ApiResult<HybridLabelingResourcesDTO> getHybridTaskDetailByTaskId(@PathVariable Long id) {
    taskService
        .getTaskById(id)
        .filter(b -> !b.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("task not found"));

    return HybridLabelingResourcesDTO.builder()
        .normalResources(hybridTaskResourceService.getNormalResourceByTaskId(id))
        .hybridResources(hybridTaskResourceService.listResourcesByTaskId(id))
        .build()
        .toApiResult();
  }

  @PostMapping("/getHybridLabelingTaskDetail/byBatchId/{id}/updateSort")
  public ApiResult<List<Resource>> getHybridLabelingTaskDetailUpdateSort(
      @PathVariable Long id, @RequestBody List<HybridResSortDTO> body) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted() && b.getLabelType() == Batch.BatchLabelType.HYBRID)
        .ensurePresent(() -> ControllerUtils.notFound("batch not found"));
    var taskList = taskListDao.listTaskListsByBatchIdAndType(id, TaskList.TaskListType.LABEL);
    var task = taskService.getOnlyHybridSampleTaskByTaskListId(taskList.get(0).getId());
    var sortMap = body.toMap(HybridResSortDTO::getResId, HybridResSortDTO::getSort);
    hybridTaskResourceService.updateResourceSort(task.get().getId(), sortMap);
    batchService.resetBatchStep(id, Batch.BatchStatus.SAMPLE_UPLOADED);
    return hybridTaskResourceService.listResourcesByTaskId(task.get().getId()).toApiResult();
  }

  @PostMapping("/getHybridLabelingTaskDetail/byTaskId/{id}/updateSort")
  public ApiResult<List<Resource>> getHybridTaskDetailUpdateSort(
      @PathVariable Long id, @RequestBody List<HybridResSortDTO> body) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    var sortMap = body.toMap(HybridResSortDTO::getResId, HybridResSortDTO::getSort);
    hybridTaskResourceService.updateResourceSort(id, sortMap);
    return hybridTaskResourceService.listResourcesByTaskId(id).toApiResult();
  }

  @PostMapping("/getHybridLabelingTaskDetail/byBatchId/{id}/removeOrAddRes/{resId}")
  public ApiResult<List<Resource>> getHybridLabelingTaskDetailRemoveOrAddRes(
      @PathVariable Long id, @PathVariable Long resId, @RequestParam Boolean isDelete) {
    batchService
        .getBatchById(id)
        .filter(b -> !b.getDeleted() && b.getLabelType() == Batch.BatchLabelType.HYBRID)
        .ensurePresent(() -> ControllerUtils.notFound("batch not found"));
    var taskList = taskListDao.listTaskListsByBatchIdAndType(id, TaskList.TaskListType.LABEL);
    var task = taskService.getOnlyHybridSampleTaskByTaskListId(taskList.get(0).getId());
    hybridTaskResourceService.removeOrAddResFromHybridSample(
        task.get().getTaskListId(), task.get().getId(), resId, isDelete);
    batchService.resetBatchStep(id, Batch.BatchStatus.SAMPLE_UPLOADED);
    return hybridTaskResourceService.listResourcesByTaskId(task.get().getId()).toApiResult();
  }

  @PostMapping("/getHybridLabelingTaskDetail/byTaskId/{id}/removeOrAddRes/{resId}")
  public ApiResult<List<Resource>> getHybridTaskDetailRemoveOrAddRes(
      @PathVariable Long id, @PathVariable Long resId, @RequestParam Boolean isDelete) {
    var task = taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    hybridTaskResourceService.removeOrAddResFromHybridTask(task.getId(), resId, isDelete);
    return hybridTaskResourceService.listResourcesByTaskId(task.getId()).toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteTaskById(
      @PathVariable Long id, @RequestParam(required = false) Boolean withSample) {
    var task = taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    // TODO: if this task is a batch sample task, remove it from batch sample
    // TODO: more validation
    taskService.deleteTaskById(id);

    if (task.getTaskType() == Task.TaskType.ExampleTask) {
      taskListDao.getTaskListById(task.getTaskListId()).ifPresent(taskList -> {
        batchService.resetBatchStep(taskList.getBatchId(), Batch.BatchStatus.TASK_CREATED);
        if (Boolean.TRUE.equals(withSample)) {
          batchSampleService
              .getBatchSampleByResourceId(taskList.getBatchId(), task.getResourceId())
              .ifPresent(batchSampleService::deleteBatchSample);
        }
      });
    } else if (task.getTaskType() == Task.TaskType.Exam) {
      taskListDao
          .getTaskListById(task.getTaskListId())
          .ifPresent(taskList -> batchService.resetBatchStep(
              taskList.getBatchId(), Batch.BatchStatus.INSTRUCTION_CREATED));
    }
  }

  @PostMapping("/{id}/question/orders")
  public void updateSubTaskOrders(
      @PathVariable Long id, @Valid @RequestBody SimpleDataDTO<List<Long>> questionIds) {
    var idsDistinct = questionIds.getData().stream().distinct().toSet();
    if (idsDistinct.size() != questionIds.getData().size()) {
      throw ControllerUtils.badRequest("question ids must be unique");
    }

    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var questions = taskQuestionDao.getQuestionsByTaskId(id);
    if (questions.size() != questionIds.getData().size()) {
      throw ControllerUtils.badRequest("Number of question is not correct");
    }
    var sIds = questions.stream().map(TaskQuestion::getId).toList();
    if (!idsDistinct.containsAll(sIds)) {
      throw ControllerUtils.badRequest("question ids are match with the sub tasks");
    }

    taskQuestionDao.updateQuestionsSort(questionIds.getData());
  }

  @GetMapping("/{id}/groups")
  public ApiResult<List<QuestionGroup>> getGroupsByTaskId(@PathVariable Long id) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    return questionGroupDao.getQuestionGroupsByTaskId(id).toApiResult();
  }

  @PostMapping("/{id}/groups")
  public ApiResult<QuestionGroup> createGroup(
      @PathVariable Long id, @Valid @RequestBody CreateQuestionGroupDTO dto) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var sort = questionGroupDao.getMaxSortByTaskId(id);

    var questionGroup = QuestionGroup.builder()
        .taskId(id)
        .name(dto.getName())
        .pageSize(dto.getPageSize())
        .sort(sort)
        .deleted(false)
        .createdAt(DateUtils.now())
        .updatedAt(DateUtils.now())
        .build();
    return questionGroupDao.createQuestionGroup(questionGroup).toApiResult();
  }

  @PutMapping("/{id}/groups/{groupId}")
  public ApiResult<QuestionGroup> updateGroup(
      @PathVariable Long id,
      @PathVariable Long groupId,
      @Valid @RequestBody UpdateQuestionGroupDTO dto) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    questionGroupDao
        .getQuestionGroupById(groupId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Question group not found"))
        .filter(t -> t.getTaskId().equals(id))
        .ensurePresent(
            () -> ControllerUtils.forbidden("This Task are not the owner of this group"));

    var questionGroup = QuestionGroup.builder()
        .taskId(id)
        .name(dto.getName())
        .pageSize(dto.getPageSize())
        .build();
    return questionGroupDao.updateQuestionGroupById(questionGroup).toApiResult();
  }

  @DeleteMapping("/{id}/groups/{groupId}")
  public void deleteGroupById(@PathVariable Long id, @PathVariable Long groupId) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    questionGroupDao
        .getQuestionGroupById(groupId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Question group not found"))
        .filter(t -> t.getTaskId().equals(id))
        .ensurePresent(
            () -> ControllerUtils.forbidden("This Task are not the owner of this group"));
    questionGroupDao.deleteQuestionGroupById(id);
  }

  @PostMapping("/{id}/groups/orders")
  public void updateGroupOrders(
      @PathVariable Long id, @Valid @RequestBody SimpleDataDTO<List<Long>> groupIds) {
    var idsDistinct = groupIds.getData().stream().distinct().toSet();
    if (idsDistinct.size() != groupIds.getData().size()) {
      throw ControllerUtils.badRequest("group ids must be unique");
    }

    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var groups = questionGroupDao.getQuestionGroupsByTaskId(id);
    if (groups.size() != groupIds.getData().size()) {
      throw ControllerUtils.badRequest("Number of group is not correct");
    }
    var gIds = groups.stream().map(QuestionGroup::getId).toList();
    if (!idsDistinct.containsAll(gIds)) {
      throw ControllerUtils.badRequest("group ids are match with the groups");
    }

    questionGroupDao.updateGroupsSort(groupIds.getData());
  }

  @GetMapping("/{id}/groups/{groupId}/questions")
  public ApiResult<List<TaskQuestion>> getQuestionsByGroupId(
      @PathVariable Long id, @PathVariable Long groupId) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    questionGroupDao
        .getQuestionGroupById(groupId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Question group not found"))
        .filter(t -> t.getTaskId().equals(id))
        .ensurePresent(
            () -> ControllerUtils.forbidden("This Task are not the owner of this group"));
    return taskQuestionDao.getQuestionsByGroupId(groupId).toApiResult();
  }

  @PostMapping("/{id}/groups/{groupId}/questions")
  public ApiResult<List<TaskQuestion>> updateQuestionsBatchInGroup(
      @PathVariable Long id,
      @PathVariable Long groupId,
      @RequestBody BatchUpdateTaskQuestionDTO data) {
    var task = taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    questionGroupDao
        .getQuestionGroupById(groupId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Question group not found"))
        .filter(t -> t.getTaskId().equals(id))
        .ensurePresent(
            () -> ControllerUtils.forbidden("This Task are not the owner of this group"));

    var questions = taskQuestionDao.getQuestionsByTaskId(id);
    var existingQuestionIds = questions.stream().toMap(TaskQuestion::getId, q -> q);
    // clean up question ids
    // TODO: validate question data
    var updates = taskQuestionService.mergeWithExistingQuestions(
        id, ControllerUtils.currentUid(), data.getQuestions(), existingQuestionIds, groupId);

    var updatedIds = data.getQuestions().stream().map(UpdateQuestionDTO::getId).toSet();

    var deleted = existingQuestionIds
        .entrySet()
        .filter(e -> groupId.equals(e.getValue().getGroupId()))
        .map(Map.Entry::getKey)
        .filter(k -> !updatedIds.contains(k))
        .toList();

    if (!updates.isEmpty() || !deleted.isEmpty()) {
      taskQuestionDao.batchUpdateTaskListQuestions(updates, deleted);
    }
    taskListDao
        .getTaskListById(task.getTaskListId())
        .ifPresent(taskList ->
            batchService.resetBatchStep(taskList.getBatchId(), Batch.BatchStatus.SAMPLE_UPLOADED));
    return taskQuestionDao.getQuestionsByGroupId(groupId).toApiResult();
  }

  // helper method for lazy programmers
  @GetMapping("/{id}/questions")
  public ApiResult<List<TaskQuestion>> getQuestionsByTaskId(@PathVariable Long id) {
    taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    return taskQuestionDao.getQuestionsByTaskId(id).toApiResult();
  }

  // helper method for lazy programmers
  @PostMapping("/{id}/questions")
  public ApiResult<List<TaskQuestion>> updateQuestionsBatch(
      @PathVariable Long id, @RequestBody BatchUpdateTaskQuestionDTO data) {
    var task = taskService
        .getTaskById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var questions = taskQuestionDao.getQuestionsByTaskId(id);
    var existingQuestionIds = questions.stream().toMap(TaskQuestion::getId, q -> q);
    // clean up question ids
    // TODO: validate question data
    var questionGroup = questionGroupDao.findOrCreateDefaultGroupForTask(task);
    var updates = taskQuestionService.mergeWithExistingQuestions(
        id,
        ControllerUtils.currentUid(),
        data.getQuestions(),
        existingQuestionIds,
        Objects.isNull(questionGroup) ? null : questionGroup.getId());

    var updatedIds = data.getQuestions().stream().map(UpdateQuestionDTO::getId).toSet();

    var deleted = existingQuestionIds.keySet().stream()
        .filter(k -> !updatedIds.contains(k))
        .toList();

    if (!updates.isEmpty() || !deleted.isEmpty()) {
      taskQuestionDao.batchUpdateTaskListQuestions(updates, deleted);
    }
    taskListDao
        .getTaskListById(task.getTaskListId())
        .ifPresent(taskList ->
            batchService.resetBatchStep(taskList.getBatchId(), Batch.BatchStatus.SAMPLE_UPLOADED));
    return taskQuestionDao.getQuestionsByTaskId(id).toApiResult();
  }

  @GetMapping("/copy-for-batch-sample/{sampleId}")
  public ApiResult<TaskDetailsDTO> getClonedTask(
      @PathVariable Long sampleId, @RequestParam String type) {
    if (!type.equals("good") && !type.equals("bad")) {
      throw ControllerUtils.badRequest("invalid type");
    }

    batchSampleService
        .getBatchSampleById(sampleId)
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager()
            || ControllerUtils.isNodeManager())
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var t = type.equals("good")
        ? TaskList.TaskListType.GOOD_EXAMPLE
        : TaskList.TaskListType.BAD_EXAMPLE;
    var message = new BatchSampleActor.OpCloneAsExample(sampleId, t);
    var future = ActorUtils.<SimpleResult<Task>, BatchSampleActor.OpCloneAsExample>askWithDefault(
        this.clusterConfiguration.getBatchSampleActor(), message);
    var result = future.get();
    if (result.isSuccess()) {
      var task = result.getResult();
      var resource = resourceService.getResourceById(task.getResourceId()).get();
      var questions = taskQuestionDao.getQuestionsByTaskId(task.getId());
      var groups = questionGroupDao.getQuestionGroupsByTaskId(task.getId());
      return TaskDetailsDTO.builder()
          .task(task)
          .resource(resource)
          .questions(questions)
          .groups(groups)
          .build()
          .toApiResult();
    } else {
      return ApiResult.fail(result.getReason());
    }
  }

  @GetMapping("/copy-for-batch-sample/taskId/{taskId}")
  public ApiResult<TaskDetailsDTO> getClonedTaskByTaskId(
      @PathVariable Long taskId, @RequestParam String type) {
    if (!type.equals("good") && !type.equals("bad")) {
      throw ControllerUtils.badRequest("invalid type");
    }
    taskService
        .getTaskById(taskId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager()
            || ControllerUtils.isNodeManager())
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));
    var t = type.equals("good")
        ? TaskList.TaskListType.GOOD_EXAMPLE
        : TaskList.TaskListType.BAD_EXAMPLE;
    var message = new TaskActor.OpCloneTaskAsExample(taskId, t);
    var future = ActorUtils.<SimpleResult<Task>, TaskActor.OpCloneTaskAsExample>askWithDefault(
        this.clusterConfiguration.getTaskActor(), message);
    var result = future.get();
    if (result.isSuccess()) {
      var task = result.getResult();
      var resource = resourceService.getResourceById(task.getResourceId()).get();
      var questions = taskQuestionDao.getQuestionsByTaskId(task.getId());
      var groups = questionGroupDao.getQuestionGroupsByTaskId(task.getId());
      var res = TaskDetailsDTO.builder()
          .task(task)
          .resource(resource)
          .questions(questions)
          .groups(groups)
          .build();
      if (task.getIsHybrid()) {
        Optional<List<HybridResourceDTO>> hybridResourceList =
            taskService.getHybridTaskResource(task.getId());
        hybridResourceList.ifPresent(res::setHybridTaskResources);
      }
      return res.toApiResult();
    } else {
      return ApiResult.fail(result.getReason());
    }
  }

  @PostMapping("/{taskId}/submit-answer")
  public ApiResult<List<QuestionAnswer>> submitAnswer(
      @PathVariable Long taskId, @RequestBody SubmitTaskExampleDTO data) {
    var task = taskService
        .getTaskById(taskId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        // only example/exam tasks can't set answers
        .filter(t ->
            t.getTaskType() == Task.TaskType.ExampleTask || t.getTaskType() == Task.TaskType.Exam)
        .orThrow(() -> ControllerUtils.badRequest("Task is not an example task"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager()
            || ControllerUtils.isNodeManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var tl = taskListDao.getTaskListById(task.getTaskListId()).get();
    var labelingTask = taskService
        .getTaskListDetails(tl.getBatchId(), TaskList.TaskListType.LABEL, true)
        .first();
    var questions = labelingTask.getTasks().first().getQuestions();

    if (questions.size() != data.getAnswers().size()) {
      throw ControllerUtils.badRequest("Number of questions is not correct");
    }
    var questionIds = data.getAnswers().toMap(AnswerDetailsDTO::getQuestionId, a -> a);
    if (questions.anyMatch(q -> !questionIds.containsKey(q.getId()))) {
      throw ControllerUtils.badRequest("Question ids are not correct");
    }

    var op = new TaskActor.OpUpdateAnswers(taskId, ControllerUtils.currentUid(), data.getAnswers());
    var answers =
        ActorUtils.<SimpleResult<List<QuestionAnswer>>, TaskActor.OpUpdateAnswers>askWithDefault(
            this.clusterConfiguration.getTaskActor(), op);

    var r = answers.get();
    batchService
        .getBatchById(tl.getBatchId())
        .map(Batch::getStatus)
        .filter(beforeLaunchBatchStatusList::contains)
        .ifPresent(s -> {
          if (task.getTaskType() == Task.TaskType.ExampleTask) {
            batchService.resetBatchStep(tl.getBatchId(), Batch.BatchStatus.TASK_CREATED);
          } else if (task.getTaskType() == Task.TaskType.Exam) {
            batchService.resetBatchStep(tl.getBatchId(), Batch.BatchStatus.INSTRUCTION_CREATED);
          }
        });
    if (r.isSuccess()) {
      return r.getResult().toApiResult();
    } else {
      return ApiResult.fail(r.getReason());
    }
  }

  @GetMapping("/{taskId}/answers")
  public ApiResult<List<QuestionAnswer>> getAnswersByTask(@PathVariable Long taskId) {
    taskService
        .getTaskById(taskId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t ->
            t.getTaskType() == Task.TaskType.ExampleTask || t.getTaskType() == Task.TaskType.Exam)
        .orThrow(() -> ControllerUtils.badRequest("Task is not an example or exam task"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAccountManager()
            || ControllerUtils.isAdmin()
            || ControllerUtils.isNodeManager())
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    return questionAnswerDao.getExampleAnswerByTaskId(taskId).toApiResult();
  }

  @DeleteMapping("/{taskId}/answers")
  public void deleteSubmittedAnswers(@PathVariable Long taskId) {
    var task = taskService
        .getTaskById(taskId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t ->
            t.getTaskType() == Task.TaskType.ExampleTask || t.getTaskType() == Task.TaskType.Exam)
        .orThrow(() -> ControllerUtils.badRequest("Task is not an example task"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var tl = taskListDao
        .getTaskListById(task.getTaskListId())
        .orElseThrow(() -> ControllerUtils.forbidden("task list not found"));
    var op = new TaskActor.OpDeleteAnswers(taskId, ControllerUtils.currentUid());
    ActorUtils.<SimpleResult<Integer>, TaskActor.OpDeleteAnswers>askWithDefault(
            this.clusterConfiguration.getTaskActor(), op)
        .get();

    if (task.getTaskType() == Task.TaskType.ExampleTask) {
      batchService.resetBatchStep(tl.getBatchId(), Batch.BatchStatus.TASK_CREATED);
    } else if (task.getTaskType() == Task.TaskType.Exam) {
      batchService.resetBatchStep(tl.getBatchId(), Batch.BatchStatus.INSTRUCTION_CREATED);
    }
  }

  @GetMapping("/{taskId}/answers-for-job/{jobId}")
  public ApiResult<List<QuestionAnswer>> getAnswersByTaskAndJob(
      @PathVariable Long taskId, @PathVariable Long jobId) {
    var job = jobService.validateJobPermission(
        jobId, ControllerUtils.currentUid(), ControllerUtils.isAccountManager());
    var task = taskService
        .getTaskById(taskId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t ->
            t.getTaskType() == Task.TaskType.ExampleTask || t.getTaskType() == Task.TaskType.Exam)
        .orElseThrow(() -> ControllerUtils.badRequest("Task is not an example or exam task"));
    taskListDao
        .getTaskListById(task.getTaskListId())
        .filter(t -> t.getBatchId().equals(job.getBatchId()))
        .ensurePresent(() -> ControllerUtils.forbidden("no permission"));

    return questionAnswerDao.getExampleAnswerByTaskId(taskId).toApiResult();
  }

  @GetMapping("/{taskId}/answers-for-batch/{batchId}")
  @IsLabeler
  public ApiResult<List<QuestionAnswer>> getAnswersByTaskAndBatch(
      @PathVariable Long taskId, @PathVariable Long batchId) {
    var task = taskService
        .getTaskById(taskId)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t ->
            t.getTaskType() == Task.TaskType.ExampleTask || t.getTaskType() == Task.TaskType.Exam)
        .orElseThrow(() -> ControllerUtils.badRequest("Task is not an example or exam task"));
    taskListDao
        .getTaskListById(task.getTaskListId())
        .filter(t ->
            t.getBatchId().equals(batchId) && !t.getListType().equals(TaskList.TaskListType.EXAM))
        .ensurePresent(() -> ControllerUtils.forbidden("no permission"));

    return questionAnswerDao.getExampleAnswerByTaskId(taskId).toApiResult();
  }

  // @PostMapping("/{taskId}/update-exam-answer")
  // public ApiResult<TaskDetailsDTO> updateExamAnswer(@PathVariable Long taskId, @RequestBody
  // SubmitTaskExampleDTO data){
  // taskService.getTaskById(taskId)
  // .filter(t -> !t.getDeleted())
  // .orThrow(() -> ControllerUtils.notFound("Task not found"))
  // .filter(t ->t.getTaskType().equals(Constants.TaskType.Exam))
  // .orThrow(()-> ControllerUtils.badRequest("Task is not an exam task"))
  // .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
  // .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this
  // task"));

  // var questions = taskQuestionService.getQuestionsByTaskId(taskId);
  // if (questions.size() != data.getAnswers().size()) {
  // throw ControllerUtils.badRequest("Number of questions is not correct");
  // }
  // // todo: validate answers
  // var questionAndAnswers =
  // Streams.zip(questions.stream(), data.getAnswers().stream(), (q, d) -> Pair.make(q.getId(),
  // d)).toList();

  // taskService.submitTaskAnswer(taskId, questionAndAnswers);
  // return taskService.getTaskDetailsById(taskId).get().toApiResult();
  // }

  @PostMapping("/upload/for/{batchId}")
  public ApiResult<List<TaskDetailsDTO>> createTaskFromUpload(
      @PathVariable Long batchId, @RequestParam MultipartFile[] files) {
    // create exams for batch
    ensureFilesValid(files);

    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    // always have an exam task list
    var examTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.EXAM)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .first();
    var examCount = taskService.getTaskCountInList(examTaskList.getId());
    if (examCount >= Constants.MAX_EXAM_COUNT) {
      return List.<TaskDetailsDTO>of().toApiResult();
    }
    // always have a labeling task list
    var labelingTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    var task =
        taskService.listTasksByTaskListId(batch, labelingTaskList.getId()).firstOrNull();
    Long fromTaskId = task == null ? null : task.getId();

    var resources = List.<Resource>of();
    if (batch.getLabelType() == Batch.BatchLabelType.TEXT
        || batch.getLabelType() == Batch.BatchLabelType.URL) {
      var txtResources = Arrays.stream(files)
          .flatMap(f -> resourceService
              .getTextResourcesFromFile(
                  ControllerUtils.currentUid(),
                  f,
                  Constants.MAX_EXAM_COUNT - examCount,
                  Resource.Visibility.TASK)
              .stream())
          .toList();
      resources = resourceService.batchCreateResources(txtResources);
    } else {
      resources = Arrays.stream(files)
          .map(f -> {
            if (f.getContentType() != null && f.getContentType().startsWith("text")) {
              return resourceService.createResource(resourceService.getTextResourceFromFile(
                  ControllerUtils.currentUid(), f, Resource.Visibility.TASK));
            } else {
              return resourceService.createResourceFromFile(
                  ControllerUtils.currentUid(), f, Resource.Visibility.TASK);
            }
          })
          .toList();
    }

    var resourcesToCreate = resources;

    CompletableFuture<SimpleResult<List<Task>>> result = ActorUtils.askWithDefault(
        this.clusterConfiguration.getBatchContentActor(),
        new BatchContentActor.OpCreateExams(
            batchId, examTaskList.getId(), fromTaskId, ControllerUtils.currentUid(), resources));

    var tasks = result.get();
    return tasks.asApiResult().map(ts -> Streams.zip(
            resourcesToCreate.stream(), ts.stream(), (r, t) -> {
              t.setDeleted(false);
              return TaskDetailsDTO.builder().task(t).resource(r).build();
            })
        .toList());
  }

  @PostMapping("/hybridExam/create/{batchId}")
  public ApiResult<TaskDetailsDTO> hybridExamTaskCreate(@PathVariable Long batchId) {
    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));
    var examTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.EXAM)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .first();
    var examResCount = taskService.getTaskCountInListNonHybrid(examTaskList.getId());
    if (examResCount <= 0) {
      throw ControllerUtils.forbidden("You need to upload resource first.");
    }
    var labelingTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    var task =
        taskService.listTasksByTaskListId(batch, labelingTaskList.getId()).firstOrNull();
    Long fromTaskId = task == null ? null : task.getId();
    CompletableFuture<SimpleResult<Task>> result = ActorUtils.askWithDefault(
        this.clusterConfiguration.getBatchContentActor(),
        new BatchContentActor.OpCreateHybridExams(
            batchId, examTaskList.getId(), fromTaskId, ControllerUtils.currentUid()));
    var tasks = result.get();
    Optional<List<HybridResourceDTO>> hybridResourceList =
        taskService.getHybridTaskResource(tasks.getResult().getId());
    return TaskDetailsDTO.builder()
        .task(tasks.getResult())
        .hybridTaskResources(hybridResourceList.get())
        .build()
        .toApiResult();
  }

  @PostMapping("/upload-annotation/presigned-url/{batchId}")
  public ApiResult<PreSignedUrlUploadResultDTO> createAnnotationTaskWithPresignedUrl(
      @PathVariable Long batchId, @RequestBody PreSignedUrlUploadDTO dto) {
    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));
    var annotationTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    if (annotationTaskList == null) {
      throw ControllerUtils.internal("Annotation task list not found");
    }

    var resources = resourceService.preCreateResourceForPreSignedUpload(
        ControllerUtils.currentUid(), dto, Resource.Visibility.TASK);
    if (resources == null) {
      throw ControllerUtils.internal("failed to create resource");
    }

    var labelingTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    var task =
        taskService.listTasksByTaskListId(batch, labelingTaskList.getId()).firstOrNull();
    var fromTaskId = task == null ? null : task.getId();
    var maxSort = taskService.getMaxSortByTaskListId(annotationTaskList.getId());

    var result = batchCreateTask(
        resources,
        annotationTaskList,
        fromTaskId,
        maxSort,
        Task.TaskType.TASK,
        Task.TaskStatus.READY,
        false);
    var uploadUrl = cloudStorageService.generatePresignedUploadUrl(dto);
    return PreSignedUrlUploadResultDTO.builder()
        .taskDetails(result)
        .preSignedUrl(uploadUrl)
        .build()
        .toApiResult();
  }

  @PostMapping("/upload-annotation/presigned-url/{batchId}/mark-complete/{taskId}")
  public ApiResult<Boolean> markAnnotationTaskWithPresignedUploadComplete(
      @PathVariable Long batchId, @PathVariable Long taskId) {
    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));
    var annotationTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    if (annotationTaskList == null) {
      throw ControllerUtils.internal("Annotation task list not found");
    }
    return taskService.undeleteTaskWhenPresignedUploadComplete(taskId).toApiResult();
  }

  @PostMapping("/upload-annotation/for/{batchId}")
  public ApiResult<List<TaskInfoDTO>> createAnnotationTask(
      @PathVariable Long batchId, @RequestParam MultipartFile[] files) {
    ensureFilesValid(files);
    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    // create an annotation task for a resource
    // no need to copy the questions as they should be the same as the labeling task
    var annotationTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    if (annotationTaskList == null) {
      throw ControllerUtils.internal("Annotation task list not found");
    }

    var resources = List.<Resource>of();
    if (batch.getLabelType() == Batch.BatchLabelType.TEXT
        || batch.getLabelType() == Batch.BatchLabelType.URL) {
      var txtResources =
          // we just read the first 10000 lines, it's pretty huge!
          Arrays.stream(files)
              .flatMap(f -> resourceService
                  .getTextResourcesFromFile(
                      ControllerUtils.currentUid(), f, 10000L, Resource.Visibility.TASK)
                  .stream())
              .toList();
      resources = resourceService.batchCreateResources(txtResources);
    } else {
      resources = Arrays.stream(files)
          .map(f -> resourceService.createResourceFromFile(
              ControllerUtils.currentUid(), f, Resource.Visibility.TASK))
          .toList();
    }

    var labelingTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .firstOrNull();
    var task =
        taskService.listTasksByTaskListId(batch, labelingTaskList.getId()).firstOrNull();
    var fromTaskId = task == null ? null : task.getId();
    var maxSort = taskService.getMaxSortByTaskListId(annotationTaskList.getId());

    var result = batchCreateTasks(
        resources,
        annotationTaskList,
        fromTaskId,
        maxSort,
        Task.TaskType.TASK,
        Task.TaskStatus.READY,
        false);

    return result.map(TaskInfoDTO::fromTaskDetailsDTO).toList().toApiResult();
  }

  private TaskDetailsDTO batchCreateTask(
      Resource resource,
      TaskList taskList,
      Long fromTaskId,
      Integer maxSort,
      Task.TaskType taskType,
      Task.TaskStatus status,
      boolean copyQuestions) {
    var task = Task.builder()
        .taskListId(taskList.getId())
        .resourceId(resource.getId())
        .ownerId(ControllerUtils.currentUid())
        .taskType(taskType)
        .status(status)
        .originId(fromTaskId)
        .sort(maxSort)
        .deleted(true)
        .build();

    var r = taskService.batchCopyTasks(fromTaskId, Collections.singletonList(task), copyQuestions);
    return TaskDetailsDTO.builder()
        .task(r.first().getT1())
        .questions(r.first().getT2())
        .groups(r.first().getT3())
        .resource(resource)
        .build();
  }

  private List<TaskDetailsDTO> batchCreateTasks(
      List<Resource> resources,
      TaskList taskList,
      Long fromTaskId,
      Integer maxSort,
      Task.TaskType taskType,
      Task.TaskStatus status,
      boolean copyQuestions) {
    var tasks = resources.mapIndexed((idx, r) -> Task.builder()
        .taskListId(taskList.getId())
        .resourceId(r.getId())
        .ownerId(ControllerUtils.currentUid())
        .taskType(taskType)
        .status(status)
        .originId(fromTaskId)
        .sort(idx + maxSort)
        .deleted(true)
        .build());

    var r = taskService.batchCopyTasks(fromTaskId, tasks, copyQuestions);
    taskService.commitBatchCopyTasks(r);
    return Streams.zip(resources.stream(), r.stream(), (res, t) -> {
          t.getT1().setDeleted(false);
          t.getT2().forEach(q -> q.setDeleted(false));
          t.getT3().forEach(g -> g.setDeleted(false));
          return TaskDetailsDTO.builder()
              .task(t.getT1())
              .questions(t.getT2())
              .groups(t.getT3())
              .resource(res)
              .build();
        })
        .toList();
  }

  private static void ensureFilesValid(MultipartFile[] files) {
    if (files.length == 0) {
      throw ControllerUtils.badRequest("No files uploaded");
    }
    if (Arrays.stream(files).anyMatch(f -> f.isEmpty())) {
      throw ControllerUtils.badRequest("Empty file");
    }
  }
}
