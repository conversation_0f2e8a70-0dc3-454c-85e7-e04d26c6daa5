package ai.saharaa.controllers;

import ai.saharaa.dto.ApiResult;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/statisticses")
public class StatisticController {

  // TODO: fix it
  @RequestMapping("/labeler/submissions")
  public ApiResult<SubmissionStat> getLabelerSubmissions() {
    return ApiResult.success(SubmissionStat.builder().submissions(1).build());
  }

  @Data
  @Builder
  public static class SubmissionStat {
    public long submissions;
  }
}
