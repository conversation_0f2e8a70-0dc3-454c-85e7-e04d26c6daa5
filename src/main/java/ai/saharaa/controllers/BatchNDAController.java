package ai.saharaa.controllers;

import ai.saharaa.daos.BatchNDADao;
import ai.saharaa.utils.ControllerUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/batch-ndas")
public class BatchNDAController {

  private final BatchNDADao batchNDADao;

  public BatchNDAController(BatchNDADao batchNDADao) {
    this.batchNDADao = batchNDADao;
  }

  @DeleteMapping("/{id}")
  public void deleteBatchNDA(@PathVariable Long id) {
    batchNDADao
        .getById(id)
        .asOpt()
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("not found"))
        .filter(v -> v.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("no permission"));
    batchNDADao.deleteById(id);
  }
}
