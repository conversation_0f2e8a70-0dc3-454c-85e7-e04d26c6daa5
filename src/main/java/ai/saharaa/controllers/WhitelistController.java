package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.*;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.WalletAddressWhitelist;
import ai.saharaa.model.WhitelistDropPointsRecord;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/whitelist")
public class WhitelistController {
  private final WhitelistManagementService whiteListManagementService;
  private final WalletAddressWhitelistService walletAddressWhitelistService;
  private final WhitelistWalletAddressMappingService whiteListWalletAddressMappingService;
  private final WhitelistDropPointsRecordService whitelistDropPointsRecordService;
  private final DropPointsByFileService dropPointsByFileService;

  public WhitelistController(
      WhitelistManagementService whiteListManagementService,
      WalletAddressWhitelistService walletAddressWhitelistService,
      WhitelistWalletAddressMappingService whiteListWalletAddressMappingService,
      WhitelistDropPointsRecordService whitelistDropPointsRecordService,
      DropPointsByFileService dropPointsByFileService) {
    this.whiteListManagementService = whiteListManagementService;
    this.walletAddressWhitelistService = walletAddressWhitelistService;
    this.whiteListWalletAddressMappingService = whiteListWalletAddressMappingService;
    this.whitelistDropPointsRecordService = whitelistDropPointsRecordService;
    this.dropPointsByFileService = dropPointsByFileService;
  }

  @PostMapping("/createWhitelist")
  @IsAdmin
  public ApiResult<Boolean> createWhitelist(@RequestParam String whitelistName) {
    Boolean createWhitelist = whiteListManagementService.createWhitelist(whitelistName);
    return ApiResult.success(createWhitelist);
  }

  @GetMapping("/listWhitelists")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<WhitelistDTO>> listWhitelists(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam() Optional<String> whiteListName) {
    return ApiResult.success(
        whiteListManagementService.getWhitelistsPage(page, limit, whiteListName).toPageResult());
  }

  @GetMapping("/listWalletAddresses")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<WhitelistWalletAddressDTO>> listWalletAddresses(
      @RequestParam(defaultValue = "1", name = "page") Integer page,
      @RequestParam(defaultValue = "10", name = "limit") Integer limit,
      @RequestParam("whitelistId") Long whitelistId,
      @RequestParam("walletAddress") Optional<String> walletAddress) {
    return ApiResult.success(whiteListWalletAddressMappingService
        .getWhitelistedAddresses(page, limit, whitelistId, walletAddress)
        .toPageResult());
  }

  @PostMapping("/checkUpload")
  @IsAdmin
  public ApiResult<WhitelistUploadedTextDTO> checkUpload(
      @RequestBody WhitelistWalletAddressDTO whitelistWalletAddressDTO) {
    var checkedWhiteListUploadedTextDTO =
        whiteListWalletAddressMappingService.countUploadedWalletAddresses(
            whitelistWalletAddressDTO);
    return ApiResult.success(checkedWhiteListUploadedTextDTO);
  }

  @PostMapping("/uploadWallets")
  @IsAdmin
  public ApiResult<WhitelistWalletAddressDTO> uploadWalletsToWhitelist(
      @RequestBody WhitelistWalletAddressDTO whiteListWalletAddressDTO) {
    var uploadedWhitelistWalletAddressDTO =
        whiteListWalletAddressMappingService.addWalletAddressToWhitelist(whiteListWalletAddressDTO);
    return ApiResult.success(uploadedWhitelistWalletAddressDTO);
  }

  @PostMapping("/onchain")
  @IsAdmin
  public ApiResult<WhitelistWalletAddressDTO> onchain() {
    var page = 1;

    while (true) {
      var walletAddresses =
          walletAddressWhitelistService
              .getWhitelistedAddressesPage(page, 1000, "", "")
              .getRecords()
              .map(WalletAddressWhitelist::getWalletAddress)
              .toList();
      page++;
      if (walletAddresses.isEmpty()) {
        break;
      }
//      whiteListWalletAddressMappingService.sendWalletAddressToKafka(walletAddresses);
    }

    return ApiResult.success(null);
  }

  @PostMapping("/updateStatus")
  @IsAdmin
  public ApiResult<Boolean> updateWhitelistStatus(
      @RequestBody WhitelistWalletAddressDTO whiteListWalletAddressDTO) {
    var updated = whiteListWalletAddressMappingService.updateStatus(whiteListWalletAddressDTO);
    return ApiResult.success(updated);
  }

  @PostMapping("/updateWalletAddress")
  @IsAdmin
  public ApiResult<Boolean> updateWalletAddress(
      @RequestBody WhitelistWalletAddressDTO whiteListWalletAddressDTO) {
    var updated =
        whiteListWalletAddressMappingService.updateWalletAddressType(whiteListWalletAddressDTO);
    return ApiResult.success(updated);
  }

  @PostMapping("/removeWalletAddress")
  @IsAdmin
  public ApiResult<Boolean> removeWalletAddress(
      @RequestBody WhitelistWalletAddressDTO whiteListWalletAddressDTO) {
    whiteListWalletAddressMappingService.removeWalletAddress(whiteListWalletAddressDTO);
    whiteListWalletAddressDTO
        .getWalletAddressWhitelistDTOList()
        .forEach(walletAddressWhitelistDTO -> walletAddressWhitelistService.removeWalletAddress(
            walletAddressWhitelistDTO.getWalletAddressId()));

    return ApiResult.success(true);
  }

  @PostMapping("/dropPoints")
  @IsAdmin
  public ApiResult<DroppedPointsWalletAddressDTO> dropPoints(
      @RequestBody WhitelistDropPointsDTO whiteListDropPointsDTO) {

    var drop = whiteListWalletAddressMappingService.dropPoints(whiteListDropPointsDTO);
    return ApiResult.success(drop);
  }

  @PostMapping("/v2/dropPoints")
  @IsAdmin
  public ApiResult<Boolean> dropPointsV2(
      @RequestBody WhitelistDropPointsDTO whiteListDropPointsDTO) {
    var drop = whiteListWalletAddressMappingService.dropPointsV2(whiteListDropPointsDTO);
    return ApiResult.success(drop);
  }

  @PostMapping("/dropPointsIndividually")
  @IsAdmin
  public ApiResult<Boolean> dropPointsIndividually(
      @RequestBody WhitelistDropPointsIndividuallyDTO whitelistDropPointsIndividuallyDTO) {
    var drop = whiteListWalletAddressMappingService.dropPointsIndividually(
        whitelistDropPointsIndividuallyDTO);
    return ApiResult.success(drop);
  }

  @PostMapping("/dropPointsByFile")
  @IsAdmin
  public ApiResult<DropPointsByFileRecordDTO> dropPointsByFile(
      @RequestParam("file") MultipartFile file,
      @RequestParam("source") String source,
      @RequestParam("reason") String reason) {

    return ApiResult.success(dropPointsByFileService.dropPointsByFile(file, source, reason));
  }

  @PostMapping("/add")
  @IsAdmin
  public ApiResult<WalletAddressWhitelist> addWhitelist(
      @RequestBody WalletAddressWhitelistDTO walletAddressWhitelistDTO) {
    var walletAddressWhitelist = walletAddressWhitelistService.addWalletAddress(
        walletAddressWhitelistDTO.getWalletAddress(), walletAddressWhitelistDTO.getType());
    return ApiResult.success(walletAddressWhitelist);
  }

  @PostMapping("/batch/add")
  @IsAdmin
  public ApiResult<Boolean> batchAddWhitelist(
      @RequestBody List<WalletAddressWhitelistDTO> walletAddressWhitelistDTO) {
    walletAddressWhitelistService.addWalletAddressBatch(walletAddressWhitelistDTO);
    return ApiResult.success(true);
  }

  @PostMapping("/remove")
  @IsAdmin
  public ApiResult<Boolean> removeWhitelist(@RequestBody SimpleDataDTO<Long> id) {
    walletAddressWhitelistService.removeWalletAddress(id.getData());
    return ApiResult.success(true);
  }

  @GetMapping("/list")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<WalletAddressWhitelist>> listWhitelist(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam() String address,
      @RequestParam() String type) {
    return ApiResult.success(walletAddressWhitelistService
        .getWhitelistedAddressesPage(page, limit, address, type)
        .toPageResult());
  }

  @GetMapping("/listDropPoints")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<RecordDropPointsDTO>> listDropPoints(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam() Optional<String> adminAccount) {
    return ApiResult.success(
        whiteListWalletAddressMappingService.listDropPoints(page, limit, adminAccount));
  }

  @GetMapping("/listDropPointsAddressDetail")
  @CheckPageParam()
  public ApiResult<PageResult<DropPointsAddressDetailDTO>> listDropPointsAddressDetail(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam() Optional<String> id) {
    if (id.isEmpty()) {
      throw ControllerUtils.badRequest("ID is empty");
    }
    return ApiResult.success(
        whiteListWalletAddressMappingService.listDropPointsAddressDetail(page, limit, id));
  }

  @PostMapping("/v3/dropPoints")
  @IsAdmin
  public ApiResult<WhitelistDropPointsRecord> dropPointsV3(
      @RequestBody WhitelistDropPointsDTO whiteListDropPointsDTO) {
    var drop = whiteListWalletAddressMappingService.dropPointsV3(whiteListDropPointsDTO);
    return ApiResult.success(drop);
  }

  @GetMapping("/listUnregisteredWalletAddresses")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<DropPointsAddressDetailDTO>> listUnregisteredWalletAddresses(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam() Long recordId) {
    return ApiResult.success(whitelistDropPointsRecordService
        .getDropPointsRecordPage(page, limit, recordId)
        .toPageResult());
  }

  @GetMapping("/downloadUnregisteredAddresses")
  @IsAdmin
  public ResponseEntity<Object> downloadUnregisteredAddresses(@RequestParam() Long recordId) {
    List<String> unregisteredWalletAddresses =
        whitelistDropPointsRecordService.getUnregisteredWalletAddress(recordId);

    String fileContent = unregisteredWalletAddresses.stream().collect(Collectors.joining("\n"));

    byte[] contentBytes = fileContent.getBytes(StandardCharsets.UTF_8);

    HttpHeaders headers = new HttpHeaders();
    headers.add(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=unregistered_wallet_addresses.txt");
    headers.setContentType(MediaType.TEXT_PLAIN);

    return ResponseEntity.ok()
        .headers(headers)
        .cacheControl(CacheControl.maxAge(Duration.ofHours(2)))
        .body(new ByteArrayResource(contentBytes));
  }
}
