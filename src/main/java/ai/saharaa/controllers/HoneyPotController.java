package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.model.*;
import ai.saharaa.model.HoneyPot.*;
import ai.saharaa.services.HoneyPotService;
import ai.saharaa.utils.ControllerUtils;
import java.util.List;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/hp")
public class HoneyPotController {
  private final HoneyPotService honeyPotService;

  public HoneyPotController(HoneyPotService honeyPotService) {
    this.honeyPotService = honeyPotService;
  }

  @GetMapping("/project")
  public ApiResult<Project> getHPProject() {
    var uid = ControllerUtils.currentUid();
    return ApiResult.success(honeyPotService.getHPProject(uid));
  }

  @GetMapping("/batches")
  public ApiResult<List<HPBatchDetailDTO>> getBatches(
      @RequestParam(required = false) BatchSetting.HoneypotType type,
      @RequestParam(required = false) BatchSetting.HpTargetUserType notType,
      @RequestParam(required = false) BatchSetting.HpTargetUserType userType) {
    var uid = ControllerUtils.currentUid();
    return ApiResult.success(honeyPotService.getHPBatches(uid, type, notType, userType));
  }

  @GetMapping("/batch/{id}")
  public ApiResult<HPBatchDetailDTO> getBatchDetail(@PathVariable Long id) {
    return ApiResult.success(honeyPotService.getBatchDetail(id));
  }

  @GetMapping("/batches/page")
  @CheckPageParam()
  public ApiResult<PageResult<HPBatchDetailDTO>> getBatchesByPage(
      @RequestParam(required = false) BatchSetting.HoneypotType type,
      @RequestParam(required = false) BatchSetting.HpTargetUserType userType,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var uid = ControllerUtils.currentUid();
    return ApiResult.success(
        honeyPotService.getBatchesByPage(uid, page, size, type, userType).toPageResult());
  }

  @DeleteMapping("/batch/{id}")
  public ApiResult<Boolean> deleteHPDatabase(@PathVariable Long id) {
    return ApiResult.success(honeyPotService.deleteHPDatabase(id));
  }

  @PostMapping("/batch")
  public ApiResult<HPBatchDetailDTO> createNewBatch(@RequestBody HPCreateBatchDTO body) {
    var uid = ControllerUtils.currentUid();
    return honeyPotService.createNewBatch(body, uid).toApiResult();
  }

  @PostMapping("/batch/update")
  public ApiResult<HPBatchDetailDTO> updateBatch(@RequestBody HPUpdateBatchDTO body) {
    return honeyPotService.updateBatch(body).toApiResult();
  }

  @GetMapping("/{id}/questions")
  @CheckPageParam()
  public ApiResult<PageResult<TaskQuestion>> getQuestionsFromBatch(
      @PathVariable Long id,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "") String name,
      @RequestParam(defaultValue = "10") Integer size) {
    return honeyPotService
        .getQuestionsFromBatch(id, page, size, name)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/summary")
  public ApiResult<List<HoneyPotDatabaseSummaryDTO>> getQuestionsSummaryFromBatch(
      @PathVariable Long id) {
    return honeyPotService.getQuestionsSummaryFromBatch(id).toApiResult();
  }

  @PostMapping("/{id}/question")
  public ApiResult<Boolean> createQuestionForBatch(
      @PathVariable Long id, @RequestBody HPCreateQuestionsDTO body) {
    honeyPotService.createQuestionForBatch(id, body.getData());
    return ApiResult.success(true);
  }

  @DeleteMapping("/{id}/question")
  public ApiResult<Boolean> deleteQuestionForBatch(@PathVariable Long id, @RequestBody String ids) {
    return ApiResult.success(honeyPotService.deleteQuestionForBatch(id, ids));
  }

  @PostMapping("/{id}/question/dp-bound")
  public ApiResult<Boolean> createQuestionsForDpBound(
      @PathVariable Long id, @RequestBody HPCreateDpBoundQuestionsDTO body) {
    honeyPotService.createQuestionsForDpBound(id, body);
    return ApiResult.success(true);
  }

  @GetMapping("/{id}/questions/dp-bound")
  public ApiResult<PageResult<TaskQuestionExtra>> getQuestionsExtraFromDpBound(
      @PathVariable Long id,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "0") Integer usedFor,
      @RequestParam(defaultValue = "10") Integer size) {
    return honeyPotService
        .getQuestionsExtraFromDpBound(id, page, size, usedFor)
        .toPageResult()
        .toApiResult();
  }
}
