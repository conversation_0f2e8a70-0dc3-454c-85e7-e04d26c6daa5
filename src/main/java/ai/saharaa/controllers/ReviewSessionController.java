package ai.saharaa.controllers;

import ai.saharaa.actors.jobs.IndividualJobOtherProcessActor;
import ai.saharaa.actors.jobs.JobSessionActor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.distribution.TaskSubmitter;
import ai.saharaa.distribution.TaskVisitorProvider;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.job.PreSignedUrlTaskSessionResultDTO;
import ai.saharaa.dto.job.SubmitReviewsDTO;
import ai.saharaa.model.*;
import ai.saharaa.services.*;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import ai.saharaa.utils.ObjectUtils;
import akka.actor.ActorRef;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@RestController
@RequestMapping("/api/review-sessions")
@Tag(name = "Review Session Service")
public class ReviewSessionController {
  private final ReviewSessionService reviewSessionService;
  private final IndividualsService individualsService;
  private final TaskSessionService taskSessionService;
  private final CaptchaService captchaService;
  private final JobUserService jobUserService;
  private final ClusterConfiguration clusterConfiguration;
  private final TaskVisitorProvider taskDistributor;
  private final BatchService batchService;
  private final JobService jobService;

  public ReviewSessionController(
      ReviewSessionService reviewSessionService,
      IndividualsService individualsService,
      TaskSessionService taskSessionService,
      JobUserService jobUserService,
      CaptchaService captchaService,
      ClusterConfiguration clusterConfiguration,
      TaskVisitorProvider taskDistributor,
      BatchService batchService,
      JobService jobService) {
    this.reviewSessionService = reviewSessionService;
    this.captchaService = captchaService;
    this.individualsService = individualsService;
    this.taskSessionService = taskSessionService;
    this.jobUserService = jobUserService;
    this.clusterConfiguration = clusterConfiguration;
    this.taskDistributor = taskDistributor;
    this.batchService = batchService;
    this.jobService = jobService;
  }

  @PostMapping("/{id}/approve")
  public ApiResult<ReviewSession> approveSubmission(@PathVariable Long id) {
    var session = reviewSessionService
        .getReviewSessionById(id)
        .filter(s -> !s.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Review session not found"))
        .filter(s -> ReviewSession.ReviewSessionStatus.PENDING.equals(s.getStatus()))
        .orThrow(() -> ControllerUtils.notFound("Review session not pending"))
        .filter(s -> s.getUserId().equals(ControllerUtils.currentUid()))
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not allowed to approve this submission"));
    var taskSession = taskSessionService
        .getTaskSessionById(session.getTaskSessionId())
        .filter(s -> !s.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task session not found"))
        .filter(s -> TaskSession.TaskSessionStatus.PendingReview.equals(s.getStatus()))
        .orElseThrow(() -> ControllerUtils.notFound("Task session not pending review"));

    CompletableFuture<SimpleResult<ReviewSession>> r = ActorUtils.askWithDefault(
        this.clusterConfiguration.getJobSessionActor(),
        new JobSessionActor.OpSubmitReview(
            session, Optional.empty(), taskSession, DateUtils.now(), new ArrayList<>()));
    return r.get().asApiResult();
  }

  @PostMapping("/submit-revisions")
  public ApiResult<Boolean> submitRevisions(@Valid @RequestBody SubmitReviewsDTO reviews) {
    var curId = ControllerUtils.currentUid();
    var isTester = ControllerUtils.isTesterUser();

    if (individualsService.checkUserBannedInPlatform(curId)) {
      if (isTester) {
        jobUserService.removeUserBanningInPlatform(curId);
      } else {
        throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
      }
    }

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var jobUser = jobUserService
        .getJobUserByJobAndUserId(reviews.getReviews().first().getJobId(), curId)
        .filter(ju -> !ju.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Job user not found"));

    if (!isTester) {
      var submitTimeInHeader = request.getHeader("X-Submit-Time");
      StringBuilder stringBuilder = new StringBuilder();
      try (BufferedReader reader = request.getReader()) {
        String line;
        while ((line = reader.readLine()) != null) {
          stringBuilder.append(line).append('\n');
        }
      } catch (IOException e) {
        throw ControllerUtils.notFound("Job user not found");
      }
      String requestBody = stringBuilder.toString();
      var reqBodyMap = ObjectUtils.fromJsonToMapBasic(requestBody);
      if (reqBodyMap.isPresent()) {
        var newDetailString = String.join(",", reqBodyMap.get().keySet());
        this.clusterConfiguration
            .getIndividualJobOtherProcessActor()
            .tell(
                new IndividualJobOtherProcessActor.OpSubmitReviewsCheckRisk(
                    reviews.getReviews().first().getJobId(),
                    curId,
                    newDetailString,
                    submitTimeInHeader,
                    reviews.getReviews().last().getReviewSessionId()),
                ActorRef.noSender());
      }
    }
    var ip = Web2AuthService.getClientIP(request);
    var firstSessionId = reviews.getReviews().first().getTaskSessionId();
    if (!isTester && captchaService.isRecaptchaRequired(firstSessionId, jobUser.getUserId())) {
      captchaService.verifyRecaptchaToken(reviews.getCaptchaToken(), ip);
    }

    Job job = this.jobService
        .getJobById(jobUser.getTaskListSessionId())
        .orElseThrow(() -> ControllerUtils.notFound("Job not found"));

    if (job.getStatus().equals(Job.JobStatus.PAUSING)) {
      throw ControllerUtils.badRequest(
          "The task has been paused. We'll notify you when the task resumes.");
    }

    Batch batch = this.batchService
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));

    BatchSetting batchSetting = this.batchService.getBatchSettingByBatchId(batch.getId());

    TaskSubmitter<SubmitReviewsDTO, Boolean> taskSubmitter = this.taskDistributor
        .getTaskVisitor(batchSetting.getDistributeType(), jobUser.getRole())
        .taskSubmitter();

    taskSubmitter.submitTasks(reviews, job, batch, batchSetting, jobUser, isTester);

    return ApiResult.success(true);
  }

  //  @PostMapping("/{id}/submit-revision")
  //  public ApiResult<ReviewSession> submitRevision(
  //      @PathVariable Long id, @Valid @RequestBody SubmitAnswerDTO answer) {
  //    var isTester = ControllerUtils.isTesterUser();
  //    var hpSubmittedReview = answer.getSubmitHPReview();
  //    var session = reviewSessionService
  //        .getReviewSessionById(id)
  //        .filter(s -> !s.getDeleted())
  //        .orThrow(() -> ControllerUtils.notFound("Review session not found"))
  //        .filter(s -> ReviewSession.ReviewSessionStatus.PENDING.equals(s.getStatus()))
  //        .orThrow(() -> ControllerUtils.notFound("Review session not pending"))
  //        .filter(s -> s.getUserId().equals(ControllerUtils.currentUid()))
  //        .orElseThrow(
  //            () -> ControllerUtils.forbidden("You are not allowed to approve this submission"));
  //    var rejectIdList = new ArrayList<Long>();
  //    Boolean bannedByHp = false;
  //    if (hpSubmittedReview != null) {
  //      if (!hpSubmittedReview
  //          .getHoneyPotReviewSession()
  //          .getReviewSessionId()
  //          .equals(session.getId())) {
  //        throw ControllerUtils.forbidden("invalid data");
  //      }
  //      var jobUser = jobUserService
  //          .getById(session.getJobUserId())
  //          .asOpt()
  //          .orElseThrow(() -> ControllerUtils.notFound("job user not found"));
  //      //      honeyPotService.submitReviewAndJudge(List.of(hpSubmittedReview), jobUser);
  //      var hpJudgeResult =
  //          honeyPotService.submitReviewAndJudge(List.of(hpSubmittedReview), jobUser, isTester);
  //      rejectIdList.addAll(hpJudgeResult.getLeft());
  //      bannedByHp = hpJudgeResult.getRight();
  //    }
  //    if (bannedByHp) {
  //      return session.toApiResult();
  //    }
  //    var taskSession = taskSessionService
  //        .getTaskSessionById(session.getTaskSessionId())
  //        .filter(s -> !s.getDeleted())
  //        .orThrow(() -> ControllerUtils.notFound("Task session not found"))
  //        .filter(s -> TaskSession.TaskSessionStatus.PendingReview.equals(s.getStatus()))
  //        .orElseThrow(() -> ControllerUtils.notFound("Task session not pending review"));
  //
  //    var answerStr = ObjectUtils.toJson(answer.getAnswers());
  //
  //    CompletableFuture<SimpleResult<ReviewSession>> r = ActorUtils.askWithDefault(
  //        this.clusterConfiguration.getJobSessionActor(),
  //        new JobSessionActor.OpSubmitReview(
  //            session, answerStr.asOpt(), taskSession, DateUtils.now(), rejectIdList));
  //    return r.get().asApiResult();
  //  }

  @GetMapping("/{id}/images/list")
  public ApiResult<List<PreSignedUrlTaskSessionResultDTO>> getReviewSessionImages(
      @PathVariable Long id) {
    return ApiResult.success(reviewSessionService.getReviewSessionImages(id));
  }
}
