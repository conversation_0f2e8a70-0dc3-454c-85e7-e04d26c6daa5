package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.job.*;
import ai.saharaa.model.Batch;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.User;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/job-user")
public class JobUserController {
  private final BatchService batchService;
  private final JobUserService jobUserService;
  private final JobSessionService jobSessionService;
  private final JobService jobService;
  private final WorkloadLimitService workloadLimitService;

  public JobUserController(
    BatchService batchService,
    JobUserService jobUserService,
    JobSessionService jobSessionService,
    JobService jobService, WorkloadLimitService workloadLimitService) {
    this.batchService = batchService;
    this.jobUserService = jobUserService;
    this.jobSessionService = jobSessionService;
    this.jobService = jobService;
    this.workloadLimitService = workloadLimitService;
  }

  @GetMapping("/{jobId}")
  public ApiResult<JobUser> getInvitableWorkers(@PathVariable Long jobId) {
    var curId = ControllerUtils.currentUid();
    return jobUserService
        .getJobUserByJobAndUserId(jobId, curId)
        .orThrow(() -> ControllerUtils.notFound("Task list session not found"))
        .get()
        .toApiResult();
  }

  @GetMapping("/{id}/invitable-workers/by-node-manager")
  @CheckPageParam()
  public ApiResult<PageResult<User>> getInvitableWorkers(
      @PathVariable Long id,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    var curId = ControllerUtils.currentUid();
    return jobUserService
        .getExamInvitableWorkers(id, page, limit, curId)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/available-workers/by-node-manager")
  @CheckPageParam()
  public ApiResult<PageResult<User>> getAvailableWorkers(
      @PathVariable Long id,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    return jobUserService
        .getAvailableWorkers(id, page, limit, ControllerUtils.currentUid())
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/{id}/joined-workers/by-node-manager")
  @CheckPageParam()
  public ApiResult<PageResult<User>> getJoinedWorkers(
      @PathVariable Long id,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(defaultValue = "UnAllocated") JobUser.JobUserRole role) {
    var curId = ControllerUtils.currentUid();
    return jobUserService
        .getJoinedWorkers(curId, id, page, limit, role)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/invite")
  public ApiResult<Boolean> doInviteUserToJob(@RequestBody JobUserInviteDTO body) {

    var job = jobService
        .getJobById(body.getJobId())
        .filter(j -> !j.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("job not found"));

    this.batchService
        .getBatchById(job.getBatchId())
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("batch not found"))
        .filter(b -> b.getStatus().equals(Batch.BatchStatus.PRE_TASK_EXAM))
        .ensurePresent(() -> ControllerUtils.notFound("batch not in PRE_TASK_EXAM status"));

    var resp = jobUserService
        .doInviteUserToJob(body, null, ControllerUtils.currentUid())
        .toApiResult();
    return resp;
  }

  @GetMapping("/my-invites")
  @CheckPageParam()
  public ApiResult<PageResult<JobInviteDTO>> getMyInvites(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    var curId = ControllerUtils.currentUid();
    return jobUserService.getMyInvites(page, limit, curId).toPageResult().toApiResult();
  }

  @PostMapping("/invite/{id}/accept")
  public ApiResult<Boolean> acceptInviteExam(
      @PathVariable Long id, @RequestParam(defaultValue = "true") Boolean accept) {
    return jobSessionService
        .acceptInviteExam(id, ControllerUtils.currentUid(), accept)
        .toApiResult();
  }

  @PostMapping("/{id}/assign-worker-role/by-node-manager")
  public ApiResult<List<JobUser>> assignWorkerRoles(
      @PathVariable Long id, @RequestBody List<JobUserRoleAssignDTO> dto) {
    var resp =
        jobUserService.assignWorkerRoles(id, ControllerUtils.currentUid(), dto).toApiResult();
    return resp;
  }

  @GetMapping("/banned/platform")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<User>> getPlatformBanning(
      @RequestParam(required = false) Integer userId,
      @RequestParam(required = false) String address,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    return jobUserService
        .getPlatformBanning(page, limit, userId, address)
        .toPageResult()
        .toApiResult();
  }

  @GetMapping("/banned/permanent")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<User>> getPermanentBanning(
      @RequestParam(required = false) Integer userId,
      @RequestParam(required = false) String address,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    return jobUserService
        .getPermanentBanning(page, limit, userId, address)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/unban/platform")
  @IsAdmin
  public ApiResult<Boolean> unbanPlatform(@RequestParam Long userId) {
    return jobUserService.removeUserBanningInPlatform(userId).toApiResult();
  }

  @GetMapping("/banned/in-job")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<JobUserDetailDTO>> getInJobBanning(
      @RequestParam(required = false) Long jobId,
      @RequestParam(required = false) String address,
      @RequestParam(required = false) Integer userId,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    return jobUserService
        .getInJobBanning(page, limit, jobId, userId, address)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/unban/in-job")
  @IsAdmin
  public ApiResult<Boolean> unbanInJob(
      @RequestParam Long userId, @RequestParam(required = false) Long jobId) {
    return jobUserService.unbanInJob(userId, jobId).toApiResult();
  }

  @GetMapping("/{jobId}/workload")
  public ApiResult<Integer> getJobUserLeftWorkload(@PathVariable Long jobId) {
    var curId = ControllerUtils.currentUid();
    return workloadLimitService.getJobUserLeftQuota(jobId, curId).toApiResult();
  }
}
