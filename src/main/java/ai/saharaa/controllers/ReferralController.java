package ai.saharaa.controllers;

import static ai.saharaa.utils.OtherUtils.numberToRoman;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.CustomInviteCodeDTO;
import ai.saharaa.dto.ReferralListDTO;
import ai.saharaa.dto.achievement.AchievementActivitiesDTO;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.Referral;
import ai.saharaa.model.User;
import ai.saharaa.model.achievement.Achievement;
import ai.saharaa.services.ReferralService;
import ai.saharaa.services.achievement.AchievementService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import java.util.Objects;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/referral")
public class ReferralController {

  private final ReferralService referralService;
  private final AchievementService achievementService;

  public ReferralController(
      ReferralService referralService, AchievementService achievementService) {
    this.referralService = referralService;
    this.achievementService = achievementService;
  }

  @GetMapping("/code")
  // @HasPermissions(value = {"referral", "code"}, logical = Logical.AND)
  public ApiResult<Referral> getReferralCode() {
    var userId = ControllerUtils.currentUid();

    return ApiResult.success(referralService.genOrGetSystemReferral(userId));
  }

  @GetMapping("/invite/status")
  public ApiResult<Boolean> checkUserIsInvited() {
    var userId = ControllerUtils.currentUid();
    return ApiResult.success(referralService.checkUserIsInvited(userId));
  }

  @PostMapping("/custom/code")
  public ApiResult<Referral> customReferralCode(
      @RequestBody @Valid CustomInviteCodeDTO customCode) {
    var customInviteCode = customCode.getInviteCode();
    var userId = ControllerUtils.currentUid();
    return referralService.genCustomReferral(userId, customInviteCode).toApiResult();
  }

  @GetMapping("/info/inviter")
  public ApiResult<User> getUserInfoByInviterCode(@RequestParam String inviteCode) {
    return referralService.getUserInfoByInviterCode(inviteCode).toApiResult();
  }

  @GetMapping("/check/inviteCodeRepeat")
  public ApiResult<Boolean> checkInviteCodeRepeat(@RequestParam String inviteCode) {
    return ApiResult.success(
        referralService.checkInviteCodeRepeat(inviteCode, ControllerUtils.currentUid()));
  }

  @GetMapping("/list")
  @CheckPageParam()
  public ApiResult<PageResult<ReferralListDTO>> getReferralList(
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    var userId = ControllerUtils.currentUid();
    return referralService.getReferralList(userId, page, limit).toPageResult().toApiResult();
  }

  @GetMapping("/activities")
  public ApiResult<AchievementActivitiesDTO> getActivities() {
    var friendInvitations = referralService.countFriendInvitations(ControllerUtils.currentUid());
    var achievementDto = achievementService.getAchievementDtoBySymbol(
        Achievement.Symbol.REFERRAL_INFLUENCER, ControllerUtils.currentUid());
    var level = Objects.isNull(achievementDto.getUserAchievement())
        ? 1
        : achievementDto.getUserAchievement().getLevel();
    var nextLevelInvitationRequirement = achievementDto
        .getAchievementLevels()
        .filter(l -> Objects.equals(l.getLevel(), level))
        .findFirst()
        .map(l -> l.getDenominator() - friendInvitations)
        .orElse(0L);
    var claimedLevel = Objects.isNull(achievementDto.getUserAchievement())
        ? 0
        : achievementDto.getUserAchievement().getOnChainPassed() ? level : level - 1;
    var referralAchievement = claimedLevel < 1
        ? "--"
        : String.format(
            "%s %s", achievementDto.getAchievement().getName(), numberToRoman(claimedLevel));

    return ApiResult.success(AchievementActivitiesDTO.builder()
        .friendInvitations(friendInvitations)
        .referralAchievement(referralAchievement)
        .nextLevelInvitationRequirement(Math.max(0, nextLevelInvitationRequirement))
        .build());
  }

  @GetMapping("/status")
  public ApiResult<Boolean> getReferralStatus() {
    var uid = ControllerUtils.currentUid();
    return ApiResult.success(referralService.getReferralStatus(uid));
  }
}
