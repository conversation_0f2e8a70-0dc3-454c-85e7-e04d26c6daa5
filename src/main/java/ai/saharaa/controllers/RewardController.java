package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.reward.*;
import ai.saharaa.enums.SortType;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.newTasks.RewardTokenInfo;
import ai.saharaa.model.newTasks.UserTokenTaskRewards;
import ai.saharaa.services.JobUserService;
import ai.saharaa.services.newTasks.NewRewardService;
import ai.saharaa.utils.ControllerUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/rewards")
@RequiredArgsConstructor
@Slf4j
public class RewardController {

  private final NewRewardService newRewardService;

  private final JobUserService jobUserService;

  @GetMapping("/my-earnings")
  public ApiResult<MyEarningsDTO> getMyEarnings() {
    return ApiResult.success(newRewardService.getMyEarnings(ControllerUtils.currentUid()));
  }

  @GetMapping("/earning-breakdown")
  @CheckPageParam
  public ApiResult<PageResult<EarningBreakdownDTO>> getEarningBreakdown(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(required = false) Integer coinType,
      @RequestParam(defaultValue = "") String taskName,
      @RequestParam(defaultValue = "false") Boolean claimed,
      @RequestParam(defaultValue = "createdAtDesc") SortType sortType,
      @RequestParam(defaultValue = "") String role) {
    return ApiResult.success(newRewardService
        .getEarningBreakdown(
            ControllerUtils.currentUid(), page, size, coinType, taskName, role, sortType, claimed)
        .toPageResult());
  }

  @GetMapping("/request-claim")
  public ApiResult<List<UserTokenTaskRewards>> requestClaim(@RequestParam Integer tokenType) {
    return ApiResult.success(
        newRewardService.requestClaim(ControllerUtils.currentUid(), tokenType));
  }

  @GetMapping("/request-claim/checking")
  public ApiResult<Boolean> requestClaimCheckAfter() {
    return ApiResult.success(newRewardService.requestClaimCheckAfter(ControllerUtils.currentUid()));
  }

  @GetMapping("/request-claim/just-claimed")
  public ApiResult<String> requestClaimCheckJustClaimed(@RequestParam String claimedIds) {
    newRewardService.requestClaimJustDid(ControllerUtils.currentUid(), claimedIds);
    return ApiResult.success("ok");
  }

  @GetMapping("/token-info")
  public ApiResult<RewardTokenInfo> getTokenInfo(@RequestParam Integer tokenType) {
    return ApiResult.success(newRewardService.getTokenInfo(tokenType));
  }

  @PostMapping("/finalize-job/{jobId}")
  @IsAccountManager
  public ResponseEntity<String> finalizeJob(@PathVariable Long jobId) {
    try {
      jobUserService.finalizeJobOnChain(jobId);
      return ResponseEntity.ok(
          "Finalization process for job " + jobId + " has been triggered successfully.");
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Failed to finalize job: " + e.getMessage());
    }
  }

  @PostMapping("/{batchId}/task_status")
  public ResponseEntity<String> updateTaskStatus(
      @PathVariable Long batchId, @RequestParam Integer taskStatus) {
    try {
      jobUserService.updateTaskStatus(batchId, taskStatus);
      return ResponseEntity.ok("ok");
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Failed to updateTaskStatus job: " + e.getMessage());
    }
  }
}
