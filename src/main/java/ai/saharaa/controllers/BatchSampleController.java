package ai.saharaa.controllers;

import static ai.saharaa.utils.Constants.DEFAULT_LABELING_RESOURCE_NAME;

import ai.saharaa.actors.batch.BatchContentActor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.daos.TaskListDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.batch.BatchSampleDetailsDTO;
import ai.saharaa.dto.batch.CreateBatchSampleDTO;
import ai.saharaa.dto.res.UpdateResourceTextDataDTO;
import ai.saharaa.model.*;
import ai.saharaa.services.*;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import com.google.common.collect.Streams;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/batch-samples")
public class BatchSampleController {
  private final BatchSampleService batchSampleService;
  private final BatchService batchService;
  private final ResourceService resourceService;
  private final TaskListDao taskListDao;

  private final ClusterConfiguration clusterConfiguration;

  public BatchSampleController(
      BatchSampleService batchSampleService,
      BatchService batchService,
      ResourceService resourceService,
      TaskListDao taskListDao,
      ClusterConfiguration clusterConfiguration) {
    this.batchSampleService = batchSampleService;
    this.batchService = batchService;
    this.resourceService = resourceService;
    this.taskListDao = taskListDao;
    this.clusterConfiguration = clusterConfiguration;
  }

  @PostMapping
  public ApiResult<BatchSample> createBatchSample(
      @Valid @RequestBody CreateBatchSampleDTO createBatchSampleDTO) {
    batchService
        .getBatchById(createBatchSampleDTO.getBatchId())
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    resourceService
        .getResourceById(createBatchSampleDTO.getResourceId())
        .filter(r -> !r.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Resource not found"))
        .filter(r -> r.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this resource"));

    var bs = BatchSample.builder()
        .batchId(createBatchSampleDTO.getBatchId())
        .resourceId(createBatchSampleDTO.getResourceId())
        .ownerId(ControllerUtils.currentUid())
        .build();
    batchService.resetBatchStep(createBatchSampleDTO.getBatchId(), Batch.BatchStatus.DRAFT);
    return batchSampleService.createBatchSample(bs).toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<BatchSample> getBatchSampleById(@PathVariable Long id) {
    return batchSampleService
        .getBatchSampleById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch sample not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch sample"))
        .toApiResult();
  }

  @GetMapping("/{id}/details")
  public ApiResult<BatchSampleDetailsDTO> getBatchSampleDetailsById(@PathVariable Long id) {
    var sample = batchSampleService
        .getBatchSampleById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch sample not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not the owner of this batch " + "sample"));

    var details = resourceService
        .getResourceById(sample.getResourceId())
        .orElseThrow(() -> ControllerUtils.notFound("resource not found"));
    return BatchSampleDetailsDTO.builder()
        .sample(sample)
        .resource(details)
        .build()
        .toApiResult();
  }

  @GetMapping("/taskId/{id}/details")
  public ApiResult<BatchSampleDetailsDTO> getBatchSampleDetailsByTaskId(@PathVariable Long id) {
    var sample = batchSampleService
        .getBatchSampleById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch sample not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not the owner of this batch " + "sample"));

    var details = resourceService
        .getResourceById(sample.getResourceId())
        .orElseThrow(() -> ControllerUtils.notFound("resource not found"));
    return BatchSampleDetailsDTO.builder()
        .sample(sample)
        .resource(details)
        .build()
        .toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteBatchSampleById(@PathVariable Long id) {
    var bs = batchSampleService
        .getBatchSampleById(id)
        .orThrow(() -> ControllerUtils.notFound("Batch sample not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch sample"));
    resourceService
        .getResourceById(bs.getResourceId())
        .filter(r -> !DEFAULT_LABELING_RESOURCE_NAME.equals(r.getName()))
        .ensurePresent(
            () -> ControllerUtils.forbidden("DEFAULT LABELING RESOURCE are not allowed to delete"));

    batchSampleService.deleteBatchSample(bs);
  }

  @PostMapping("/upload/for/{batchId}")
  public ApiResult<List<BatchSampleDetailsDTO>> uploadBatchSample(
      @PathVariable Long batchId, @RequestParam("files") MultipartFile[] files) {
    if (files.length == 0) {
      throw ControllerUtils.badRequest("No files uploaded");
    }
    if (Arrays.stream(files).anyMatch(f -> f.isEmpty())) {
      throw ControllerUtils.badRequest("Empty file");
    }

    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    // always have a label task list
    var labelingTask = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .first();

    var sampleCount = batchSampleService.getBatchSampleCountByBatchId(batchId);
    if (sampleCount >= Constants.MAX_SAMPLE_COUNT) {
      return List.<BatchSampleDetailsDTO>of().toApiResult();
    }

    //
    var resources = new ArrayList<Resource>();
    Arrays.stream(files).limit(Constants.MAX_SAMPLE_COUNT - sampleCount).forEach(f -> {
      if (f.getContentType() != null
          && batch.getLabelType() == Batch.BatchLabelType.HYBRID
          && f.getContentType().startsWith("text")) {
        resources.add(resourceService.createResource(resourceService.getTextResourceFromFile(
            ControllerUtils.currentUid(), f, Resource.Visibility.TASK)));
      } else if (batch.getLabelType() == Batch.BatchLabelType.USER_DEFINE) {
        resources.addAll(
            resourceService.batchCreateResources(resourceService.getTextResourcesFromFile(
                ControllerUtils.currentUid(),
                f,
                Constants.MAX_SAMPLE_COUNT - sampleCount,
                Resource.Visibility.TASK)));
      } else {
        resources.add(resourceService.createResourceFromFile(
            ControllerUtils.currentUid(), f, Resource.Visibility.TASK));
      }
    });

    var samples = resources
        .map(r -> BatchSample.builder()
            .batchId(batchId)
            .resourceId(r.getId())
            .ownerId(ControllerUtils.currentUid())
            .build())
        .toList();

    CompletableFuture<SimpleResult<List<BatchSample>>> result = ActorUtils.askWithDefault(
        this.clusterConfiguration.getBatchContentActor(),
        new BatchContentActor.OpCreateSamples(batchId, labelingTask.getId(), samples));

    var samplesCreated = result.get();
    batchService.resetBatchStep(batch.getId(), Batch.BatchStatus.DRAFT);

    return samplesCreated.asApiResult().map(created -> Streams.zip(
            resources.stream(),
            created.stream(),
            (r, s) -> BatchSampleDetailsDTO.builder().sample(s).resource(r).build())
        .toList());
  }

  @PostMapping("/upload-txt/for/{batchId}")
  public ApiResult<List<BatchSampleDetailsDTO>> uploadTxtBatchSample(
      @PathVariable Long batchId, @RequestParam("files") MultipartFile[] files) {
    if (files.isEmpty()) {
      throw ControllerUtils.badRequest("No files uploaded");
    }
    if (Arrays.stream(files).anyMatch(f -> f.isEmpty())) {
      throw ControllerUtils.badRequest("Empty file");
    }

    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    // always have a label task list
    var labelingTask = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .first();

    var sampleCount = batchSampleService.getBatchSampleCountByBatchId(batchId);
    if (sampleCount >= Constants.MAX_SAMPLE_COUNT) {
      throw ControllerUtils.forbidden("Too many sampling tasks");
    }

    var res = resourceService.getTextResourcesFromFile(
        ControllerUtils.currentUid(),
        files[0],
        Constants.MAX_SAMPLE_COUNT - sampleCount,
        Resource.Visibility.TASK);
    var resources = resourceService.batchCreateResources(res);

    var samples = resources
        .map(r -> BatchSample.builder()
            .batchId(batchId)
            .resourceId(r.getId())
            .ownerId(ControllerUtils.currentUid())
            .build())
        .toList();

    var samplesCreated = batchSampleService.createBatchSamples(samples, labelingTask.getId());

    return Streams.zip(
            resources.stream(),
            samplesCreated.stream(),
            (r, s) -> BatchSampleDetailsDTO.builder().sample(s).resource(r).build())
        .toList()
        .toApiResult();
  }

  @PostMapping("/submit-txt/for/{batchId}")
  public ApiResult<BatchSampleDetailsDTO> submitTxtBatchSample(
      @PathVariable Long batchId, @RequestBody UpdateResourceTextDataDTO dto) {
    var batch = batchService
        .getBatchById(batchId)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> Batch.DataType.CONTEXT.getValue().equals(b.getDataType())
            || Batch.DataType.NO_NEED_TO_UPLOAD.getValue().equals(b.getDataType()))
        .orThrow(() -> ControllerUtils.notFound("Batch dataType not allow submit text"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    var labelingTask = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .first();

    var sampleCount = batchSampleService.getBatchSampleCountByBatchId(batchId);
    if (sampleCount >= Constants.MAX_SAMPLE_COUNT) {
      throw ControllerUtils.forbidden("Too many sampling tasks");
    }

    var resource = resourceService.createResourceFromText(
        dto.getData(), batch.getOwnerId(), Resource.Visibility.TASK);

    var sample = BatchSample.builder()
        .batchId(batchId)
        .resourceId(resource.getId())
        .ownerId(ControllerUtils.currentUid())
        .build();

    var samplesCreated =
        batchSampleService.createBatchSamples(List.of(sample), labelingTask.getId());

    return ApiResult.success(BatchSampleDetailsDTO.builder()
        .sample(CollectionUtils.isEmpty(samplesCreated) ? sample : samplesCreated.first())
        .resource(resource)
        .build());
  }
}
