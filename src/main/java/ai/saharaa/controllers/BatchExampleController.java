package ai.saharaa.controllers;

import ai.saharaa.daos.BatchExampleDao;
import ai.saharaa.daos.TaskListDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.batch.CreateBatchExampleDTO;
import ai.saharaa.model.BatchExample;
import ai.saharaa.services.BatchService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/batch-examples")
public class BatchExampleController {
  private final BatchExampleDao batchExampleDao;
  private final BatchService batchService;
  private final TaskListDao taskListDao;

  public BatchExampleController(
      BatchExampleDao batchExampleDao, BatchService batchService, TaskListDao taskListDao) {
    this.batchExampleDao = batchExampleDao;
    this.batchService = batchService;
    this.taskListDao = taskListDao;
  }

  @PostMapping
  public ApiResult<BatchExample> createBatchExample(
      @Valid @RequestBody CreateBatchExampleDTO createBatchExampleDTO) {
    taskListDao
        .getTaskListById(createBatchExampleDTO.getTaskListId())
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.badRequest("Task list not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task list"));
    batchService
        .getBatchById(createBatchExampleDTO.getBatchId())
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.badRequest("Batch not found"))
        .filter(v -> v.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this batch"));

    var be = BatchExample.builder()
        .batchId(createBatchExampleDTO.getBatchId())
        .taskListId(createBatchExampleDTO.getTaskListId())
        .ownerId(ControllerUtils.currentUid())
        .exampleType(createBatchExampleDTO.getExampleType())
        .build();

    return batchExampleDao.createBatchExample(be).toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteBatchExampleById(@PathVariable Long id) {
    batchExampleDao
        .getBatchExampleById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch example not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(
            () -> ControllerUtils.forbidden("You are not the owner of this batch example"));
    batchExampleDao.deleteById(id);
  }

  @GetMapping("/{id}")
  public ApiResult<BatchExample> getBatchExampleById(@PathVariable Long id) {
    return batchExampleDao
        .getBatchExampleById(id)
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch example not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden("You are not the owner of this batch example"))
        .toApiResult();
  }
}
