package ai.saharaa.controllers;

import ai.saharaa.config.perms.IsNodeManager;
import ai.saharaa.daos.BatchNDADao;
import ai.saharaa.daos.JobInvitationDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.model.JobInvitation;
import ai.saharaa.services.JobService;
import ai.saharaa.services.NodeService;
import ai.saharaa.utils.ControllerUtils;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/job-invitations")
public class JobInvitationController {
  private final JobService jobService;
  private final JobInvitationDao jobInvitationDao;
  private final NodeService nodeService;
  private final BatchNDADao batchNDADao;

  public JobInvitationController(
      JobService jobService,
      JobInvitationDao jobInvitationDao,
      NodeService nodeService,
      BatchNDADao batchNDADao) {
    this.jobService = jobService;
    this.jobInvitationDao = jobInvitationDao;
    this.nodeService = nodeService;
    this.batchNDADao = batchNDADao;
  }

  @GetMapping("/{id}")
  public ApiResult<JobInvitation> getJobInvitationById(@PathVariable Long id) {
    var invitation = jobInvitationDao
        .getById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("invitation not found"));
    return invitation.toApiResult();
  }

  @PostMapping("/{id}/accept-nda")
  @IsNodeManager
  public ApiResult<Boolean> acceptInvitation(@PathVariable Long id) {
    var invitation = jobInvitationDao
        .getById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("invitation not found"));
    nodeService
        .getNodeById(invitation.getNodeId())
        .filter(n -> !n.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("node not found"))
        .filter(n -> n.getNodeManagerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("no permission"));
    if (invitation.getStatus() != JobInvitation.JobInvitationStatus.NdaSigning) {
      throw ControllerUtils.forbidden("invalid invitation status");
    }
    var job = jobService.getJobById(invitation.getJobId());
    var ndas = batchNDADao.getNDAsByBatch(job.get().getBatchId());
    jobService.acceptJobInvitation(ControllerUtils.currentUid(), invitation, ndas);
    return ApiResult.success(true);
  }
}
