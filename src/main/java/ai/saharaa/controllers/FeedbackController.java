package ai.saharaa.controllers;

import ai.saharaa.daos.FeedbackDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.ErrorMessageDTO;
import ai.saharaa.enums.FileType;
import ai.saharaa.model.Feedback;
import ai.saharaa.model.Feedback.Status;
import ai.saharaa.model.Resource;
import ai.saharaa.services.NotificationService;
import ai.saharaa.services.ResourceService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/feedback")
public class FeedbackController {

  private final Logger log = LoggerFactory.getLogger(FeedbackController.class);
  private final FeedbackDao feedbackDao;
  private final ResourceService resourceService;
  private final NotificationService notificationService;

  public FeedbackController(
      FeedbackDao feedbackDao,
      ResourceService resourceService,
      NotificationService notificationService) {
    this.feedbackDao = feedbackDao;
    this.resourceService = resourceService;
    this.notificationService = notificationService;
  }

  @PostMapping("/submit")
  @ResponseBody
  @Transactional
  public ApiResult<Feedback> submitFeedback(
      @RequestParam("content") @Valid @NotBlank String content,
      @RequestParam(value = "file", required = false) MultipartFile file) {
    log.info("Submitting feedback ${content}");

    var newFeedback = Feedback.builder()
        .ownId(ControllerUtils.currentUid())
        .content(content)
        .status(Status.ACTIVE)
        .build();

    if (file != null) {
      var res = resourceService.createResourceFromFile(
          ControllerUtils.currentUid(), file, Resource.Visibility.ADMIN);
      if (res.getFileType() != FileType.IMAGE) {
        throw ControllerUtils.badRequest("file is not an image");
      }
      newFeedback.setScreenshot("res:${res.getId()}");
    }

    var optionalFeedback = feedbackDao.addNewFeedback(newFeedback);
    if (optionalFeedback.isEmpty()) {
      throw ControllerUtils.badRequest("can't save feedback");
    }
    notificationService.noticeFeedBackSuccess(ControllerUtils.currentUid());

    return optionalFeedback.get().toApiResult();
  }

  @PostMapping("/report/message")
  @ResponseBody
  public ApiResult<Boolean> reportErrorMessage(@Valid @RequestBody ErrorMessageDTO message) {
    log.error("report message form type:${message.getErrorMessageType().toString()}"
        + " message:${message.getMessage()}" + " userId:${message.getUserId()}");

    return ApiResult.success(true);
  }
}
