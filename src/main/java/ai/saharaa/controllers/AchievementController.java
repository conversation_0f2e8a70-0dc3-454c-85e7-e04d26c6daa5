package ai.saharaa.controllers;

import static ai.saharaa.model.achievement.Achievement.Symbol.*;

import ai.saharaa.actors.achievement.Req;
import ai.saharaa.common.AchievementOracleSender;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.config.perms.IsApiKeyClient;
import ai.saharaa.daos.season.SeasonUserPointsDetailDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.Chart2;
import ai.saharaa.dto.achievement.*;
import ai.saharaa.model.*;
import ai.saharaa.model.achievement.*;
import ai.saharaa.model.api.ApiKeys;
import ai.saharaa.services.UserService;
import ai.saharaa.services.achievement.*;
import ai.saharaa.services.season.SeasonService;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

@Slf4j
@RestController
@RequestMapping("/api/achievement")
@Tag(name = "Achievement Service", description = "Achievement service API")
@AllArgsConstructor
public class AchievementController {
  private final AchievementService achievementService;
  private final AchievementSettingService achievementSettingService;
  private final UserService userService;
  private final SaharaLevelService saharaLevelService;
  private final ClusterConfiguration clusterConfiguration;
  private final SeasonUserPointsDetailDao seasonUserPointsDetailDao;
  private final SeasonService seasonService;
  private final AchievementOnContractService achievementOnContractService;
  private final AchievementOracleSender achievementOracleSender;
  private final AchievementOracleLogService achievementOracleLogService;
  private final AchievementProgressChecker achievementProgressChecker;

  @GetMapping("/profile")
  public ApiResult<AchievementProfileDTO> getAchievementProfile() {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService.getAchievementProfile(curId));
  }

  private static final Integer LIMIT_LEVEL = 5;

  private AchievementDTO hideLimitLevel(AchievementDTO dto) {
    var ua = dto.getUserAchievement();
    var userAchievement = UserAchievement.builder()
        .id(ua.getId())
        .userId(ua.getUserId())
        .achievementId(ua.getAchievementId())
        .level(Integer.min(ua.getLevel(), LIMIT_LEVEL))
        .onChainLevel(Integer.min(ua.getOnChainLevel(), LIMIT_LEVEL))
        .requirementBitmap(ua.getRequirementBitmap())
        .onChainRequirementBitmap(ua.getOnChainRequirementBitmap())
        .contractAddress(ua.getContractAddress())
        .passed(ua.getLevel() > LIMIT_LEVEL || ua.getPassed())
        .onChainPassed(ua.getOnChainLevel() > LIMIT_LEVEL || ua.getOnChainPassed())
        .unclaimedExp(ua.getUnclaimedExp())
        .progressMap(ua.getProgressMap())
        .progress(ua.getProgress())
        .passedAt(ua.getPassedAt())
        .deleted(ua.getDeleted())
        .createdAt(ua.getCreatedAt())
        .updatedAt(ua.getUpdatedAt())
        .build();
    var hiddenAchievementLevels = dto.getAchievementLevels()
        .filter(l -> l.getLevel() > LIMIT_LEVEL)
        .map(AchievementLevel::getId)
        .toList();
    return AchievementDTO.builder()
        .achievement(dto.getAchievement())
        .achievementLevels(
            dto.getAchievementLevels().filter(l -> l.getLevel() <= LIMIT_LEVEL).toList())
        .achievementRequirements(dto.getAchievementRequirements()
            .filter(l -> !hiddenAchievementLevels.contains(l.getAchievementLevelId())
                || Objects.isNull(l.getAchievementLevelId()))
            .toList())
        .userAchievement(userAchievement)
        .build();
  }

  private Boolean hiddenAchievementFilter(AchievementDTO dto) {
    return !dto.getAchievement().getLevelType().getValue().endsWith("-hidden")
        || dto.getUserAchievement().getPassed();
  }

  @GetMapping("/times")
  public ApiResult<AchievementTimesDTO> getActiveTimes(
      @RequestParam Achievement.Category category) {
    return ApiResult.success(achievementService.getAchievementTimesByCategory(category));
  }

  @GetMapping("/list")
  public ApiResult<List<AchievementDTO>> getAchievementList() {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService
        .getAchievementDtoList(curId)
        .map(this::hideLimitLevel)
        .toList());
  }

  @GetMapping("/all")
  public ApiResult<List<Achievement>> getAchievementAll() {
    return ApiResult.success(achievementService.getAchievementAll().toList());
  }

  @GetMapping("/in-progress")
  public ApiResult<List<AchievementSeasonGroupDTO>> getAchievementSeasonGroupInProgress() {
    var curId = ControllerUtils.currentUid();

    List<AchievementSeasonGroupDTO> achievementSeasonGroupDTOS =
        achievementService.getAchievementSeasonGroupBaseInfoInProgress();

    var currentSeasonGroupOps = achievementSeasonGroupDTOS
        .filter(group -> Objects.nonNull(group.getSeasonInfo().getSeasonName()))
        .findFirst();

    var noneSeasonGroupOps = achievementSeasonGroupDTOS
        .filter(group -> Objects.isNull(group.getSeasonInfo().getSeasonName()))
        .findFirst();

    // active none season.filter(startAt < now)
    noneSeasonGroupOps.ifPresent(group -> {
      var notOnchainDTOs = group
          .getAchievementDTOS()
          .filter(dto -> {
            var now = Instant.now().atZone(ZoneId.of("UTC")).toLocalDateTime();
            var startAt = dto.getAchievement().getStartAt();
            return Objects.isNull(startAt) || now.isAfter(startAt.toLocalDateTime());
          })
          .toList();
      group.setAchievementDTOS(notOnchainDTOs);
    });

    var dtos = achievementSeasonGroupDTOS
        .map(AchievementSeasonGroupDTO::getAchievementDTOS)
        .flatMap(Collection::stream)
        .toList();

    achievementService.getAchievementDtosOfUser(dtos, curId);

    // active none season.filter(onchain!=true)
    noneSeasonGroupOps.ifPresent(group -> {
      var notOnchainDTOs = group
          .getAchievementDTOS()
          .filter(dto -> Objects.isNull(dto.getUserAchievement())
              || !Boolean.TRUE.equals(dto.getUserAchievement().getOnChainPassed()))
          .toList();
      group.setAchievementDTOS(notOnchainDTOs);
    });

    // Merge
    AchievementSeasonGroupDTO currentSeasonGroup = null;
    if (currentSeasonGroupOps.isPresent()) {
      currentSeasonGroup = currentSeasonGroupOps.get();
      if (noneSeasonGroupOps.isPresent()) {
        var aDtos = new ArrayList<>(currentSeasonGroup.getAchievementDTOS());
        aDtos.addAll(noneSeasonGroupOps.get().getAchievementDTOS());
        currentSeasonGroup.setAchievementDTOS(aDtos);
      }
    } else if (noneSeasonGroupOps.isPresent()) {
      currentSeasonGroup = noneSeasonGroupOps.get();
    }

    if (Objects.nonNull(currentSeasonGroup)) {
      currentSeasonGroup.setAchievementDTOS(currentSeasonGroup
          .getAchievementDTOS()
          .filter(this::hiddenAchievementFilter)
          .map(this::hideLimitLevel)
          .toList());
      return ApiResult.success(List.of(currentSeasonGroup));
    }

    return ApiResult.success(List.of());
  }

  @GetMapping("/closed")
  public ApiResult<List<AchievementSeasonGroupDTO>> getAchievementClosed() {
    var curId = ControllerUtils.currentUid();

    List<AchievementSeasonGroupDTO> achievementSeasonGroupDTOS =
        achievementService.getAchievementSeasonGroupBaseInfoClosed();

    var dtos = achievementSeasonGroupDTOS
        .map(AchievementSeasonGroupDTO::getAchievementDTOS)
        .flatMap(Collection::stream)
        .toList();

    achievementService.getAchievementDtosOfUser(dtos, curId);

    achievementSeasonGroupDTOS.forEach(group -> {
      if (Objects.isNull(group.getSeasonInfo().getSeasonName())) {
        // without season
        group.setAchievementDTOS(group
            .getAchievementDTOS()
            .filter(dto -> Boolean.FALSE.equals(dto.getAchievement().getActive())
                || (Objects.nonNull(dto.getUserAchievement())
                    && Boolean.TRUE.equals(dto.getUserAchievement().getOnChainPassed())))
            .toList());
      }

      group.setAchievementDTOS(group
          .getAchievementDTOS()
          .filter(this::hiddenAchievementFilter)
          .map(this::hideLimitLevel)
          .toList());
    });

    return ApiResult.success(achievementSeasonGroupDTOS);
  }

  @GetMapping("/{id}")
  public ApiResult<AchievementDTO> getAchievementById(@PathVariable Long id) {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(hideLimitLevel(achievementService.getAchievementDtoById(id, curId)));
  }

  @GetMapping("/exp-records/page")
  @CheckPageParam()
  public ApiResult<PageResult<AchievementExpRecordDTO>> getAchievementExpRecords(
      @RequestParam(value = "page", required = false) Integer page,
      @RequestParam(value = "size", required = false) Integer size) {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService.getAchievementExpRecords(curId, page, size));
  }

  private void rejectVerifyLimitLevel(OffChainVerifyDTO dto) {
    if (dto.getLevel() > LIMIT_LEVEL) {
      throw ControllerUtils.badRequest("limit level le ${LIMIT_LEVEL}");
    }
  }

  @PostMapping("/prepare-claim-exp")
  public ApiResult<PrepareClaimExpSuccessDTO> prepareClaimExp(@RequestBody PrepareClaimExpDTO dto) {
    var curId = ControllerUtils.currentUid();
    var user = userService
        .getUserById(curId)
        .orElseThrow(() -> ControllerUtils.notFound("user not found"));

    var achievementDTO = achievementService.getAchievementDtoById(dto.getAchievementId(), curId);

    var requirement = achievementDTO
        .getAchievementRequirements()
        .filter(r -> Objects.isNull(r.getAchievementLevelId())
            && Objects.equals(r.getSort(), dto.getRequirementNo()))
        .findFirst()
        .orElseThrow(
            () -> ControllerUtils.badRequest("RequiremenNo ${dto.getRequirementNo()} not exists"));

    if (!achievementService.requirementExpVerify(
        dto.getAchievementId(), requirement.getId(), requirement.getExp(), user.getId())) {
      throw ControllerUtils.badRequest("not eligible to claim exp");
    }

    var saharaLevel = saharaLevelService.getByUserId(curId);
    var level = Objects.isNull(saharaLevel) ? 0 : saharaLevel.getOnChainLevel() - 1;
    var exp = Objects.isNull(saharaLevel) ? 0L : saharaLevel.getClaimedExp();
    var newExp = exp + requirement.getExp();

    achievementService.prepareClaimExp(
        dto.getAchievementId(), requirement.getId(), user.getId(), level, newExp);
    return ApiResult.success(PrepareClaimExpSuccessDTO.builder()
        .achievementId(dto.getAchievementId())
        .requirementNo(dto.getRequirementNo())
        .level(level)
        .exp(newExp)
        .IncreasedExp(requirement.getExp())
        .build());
  }

  @PostMapping("/offchain-verify")
  @IsApiKeyClient(ApiKeys.Type.ONCHAIN)
  public ApiResult<OffChainVerifyDTO> offVerify(@RequestBody OffChainVerifyDTO dto) {
    var user = userService.findUserByAddress(dto.getWalletAddress());
    if (user.isEmpty()) {
      throw ControllerUtils.notFound("user not found");
    }
    rejectVerifyLimitLevel(dto);

    try {
      achievementService.syncAchievementClaimedStatus(
          dto.getAchievementId(), user.get().getId());
      throw ControllerUtils.badRequest("sync achievement last mint status success");
    } catch (HttpClientErrorException e) {
      log.info("no need to sync achievement mint status {} {}", dto, e.getMessage());
    }

    Boolean verified = achievementService.offChainVerify(
        dto.getAchievementId(), dto.getLevel(), dto.getExp(), user.get());
    if (!verified) {
      throw ControllerUtils.badRequest("not eligible to claim or already claimed");
    }
    return dto.toApiResult();
  }

  @PostMapping("/onchain-success")
  @IsApiKeyClient(ApiKeys.Type.ONCHAIN)
  public ApiResult<OnChainSuccessDTO> onSuccess(@RequestBody OnChainSuccessDTO dto) {
    var userOpt = userService.findUserByAddress(dto.getWalletAddress());
    if (userOpt.isEmpty()) {
      throw ControllerUtils.notFound("user not found");
    }
    var user = userOpt.get();
    try {
      var success = achievementService.onChainSuccess(
          dto.getAchievementId(),
          dto.getLevel(),
          dto.getExp(),
          user.getId(),
          dto.getContractAddress());
      if (!success) {
        log.info("onchain-success not success");
      }
    } catch (Exception e) {
      log.info("onChainSuccess error ${e}");
    }
    return dto.toApiResult();
  }

  @PostMapping("/sync-achievement-claimed")
  public ApiResult<SyncAchievementClaimedDTO> syncAchievementClaimed(
      @RequestBody SyncAchievementClaimedDTO dto) {
    var curId = ControllerUtils.currentUid();
    Boolean success =
        achievementService.syncAchievementClaimedStatus(dto.getAchievementId(), curId);
    if (!success) {
      throw ControllerUtils.badRequest("not eligible to claim");
    }
    return dto.toApiResult();
  }

  @PostMapping("/sync-claim-exp")
  public ApiResult<Boolean> syncClaimExp(@RequestBody SyncHashDTO dto) {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService.syncClaimExp(curId, dto.getHash()));
  }

  @IsAdmin
  @PutMapping("/fix-point-settle-delay")
  public ApiResult<Boolean> fixPointSettleDelay(
      @RequestParam(defaultValue = "2024") Integer year,
      @RequestParam(defaultValue = "12") Integer month,
      @RequestParam(defaultValue = "22") Integer day,
      @RequestParam(defaultValue = "2") Integer delayHour) {
    return ApiResult.success(
        seasonUserPointsDetailDao.fixPointSettleDelay(year, month, day, delayHour));
  }

  @GetMapping("/badges")
  public ApiResult<List<BadgeDTO>> badges() {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService.getBadgeDtoList(curId));
  }

  @GetMapping("/passed")
  public ApiResult<Boolean> passed(@RequestParam Achievement.Symbol symbol) {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService.isPassed(symbol, curId));
  }

  @GetMapping("/onchain-passed")
  public ApiResult<Boolean> onchainPassed(@RequestParam Achievement.Symbol symbol) {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(achievementService.isOnchainPassed(symbol, curId));
  }

  @IsAdmin
  @PostMapping("/grant")
  public ApiResult<List<Boolean>> grant(@RequestBody AchievementGrantDTO dto) {
    var symbolParamsMap = Map.of(
        RISING_STAR,
            Map.of("SAHARA_POINTS", BigDecimal.valueOf(300), "VERIFY_EMAIL", BigDecimal.ONE),
        PIONEER_OF_PROGRESS, Map.of("SAHARA_LABS_QUESTIONNAIRES", BigDecimal.ONE),
        GUARDIAN_OF_GROWTH, Map.of("SAHARA_LABS_TASKS", BigDecimal.ONE),
        DEMI_GOD, Map.of("SAHARA_DEMI_GOD", BigDecimal.ONE),
        SIGIL_OF_INITIATE, Map.of("SAHARA_SIGIL_OF_INITIATE", BigDecimal.ONE));
    if (!symbolParamsMap.containsKey(dto.getAchievementSymbol())
        || CollectionUtils.isEmpty(dto.getWalletAddresses())) {
      return ApiResult.success(Collections.emptyList());
    }
    return ApiResult.success(dto.getWalletAddresses()
        .map(wa -> {
          var userOpt = userService.findUserByAddress(wa);
          if (userOpt.isEmpty()) return false;
          return achievementService.rewardAchievement(
              dto.getAchievementSymbol(),
              userOpt.get().getId(),
              symbolParamsMap.get(dto.getAchievementSymbol()));
        })
        .toList());
  }

  @IsAdmin
  @PostMapping("/grant-progress")
  public ApiResult<List<Boolean>> grantProgress(@RequestBody AchievementGrantProgressDTO dto) {
    var symbolSet = Set.of(
        REFERRAL_INFLUENCER, DATA_CONTRIBUTOR, ACCURACY_EXPERT, TASK_MASTER, DEDICATION_EXPERT);
    if (!symbolSet.contains(dto.getAchievementSymbol())
        || CollectionUtils.isEmpty(dto.getWalletAddresses())) {
      return ApiResult.success(Collections.emptyList());
    }
    return ApiResult.success(dto.getWalletAddresses()
        .map(wa -> {
          var user = userService.findUserByAddress(wa);
          if (user.isEmpty()) return false;
          return achievementService.rewardAchievement(
              dto.getAchievementSymbol(), user.get().getId(), dto.getProgress());
        })
        .toList());
  }

  @IsAdmin
  @PostMapping("/grant-requirement-exp")
  public ApiResult<List<Boolean>> grantRequirementExp(
      @RequestBody AchievementGrantRequirementsDTO dto) {
    var symbolSet = Set.of(
        OLYMPIC_DAILY_CHECK_IN,
        OLYMPIC_DAILY_WORK,
        KNOWLEDGE_EXPERT,
        TITANS_VIGIL,
        TITANS_VIGIL_0128,
        FORGE_OF_PERSEVERANCE,
        FORGE_OF_PERSEVERANCE_0128,
        ORACLE_OF_KNOWLEDGE,
        ORACLE_OF_KNOWLEDGE_0128);
    if (!symbolSet.contains(dto.getAchievementSymbol())
        || CollectionUtils.isEmpty(dto.getWalletAddresses())) {
      return ApiResult.success(Collections.emptyList());
    }
    var progress = dto.getPassedSet()
        .map(passed ->
            Tuples.of("${dto.getAchievementSymbol().getValue()}:${passed}", BigDecimal.ONE))
        .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2, (t, t0) -> t0));
    return ApiResult.success(dto.getWalletAddresses()
        .map(wa -> {
          var user = userService.findUserByAddress(wa);
          if (user.isEmpty()) return false;
          return achievementService.rewardAchievement(
              dto.getAchievementSymbol(), user.get().getId(), progress);
        })
        .toList());
  }

  @IsAdmin
  @PostMapping("/trigger")
  public ApiResult<AchievementDTO> triggerRequirement(@RequestBody AchievementTriggerDTO dto) {
    var achievement = achievementService.getAchievementBySymbol(dto.getAchievementSymbol());
    if (Objects.isNull(achievement)) {
      throw ControllerUtils.badRequest("Achievement ${dto.getAchievementSymbol()} does not exist");
    }
    ActorUtils.askWithDefault(
            clusterConfiguration.getAchievementFlushActor(),
            Req.of(dto.getUserId(), dto.getAchievementSymbol()))
        .get();
    return ApiResult.success(hideLimitLevel(
        achievementService.getAchievementDtoById(achievement.getId(), dto.getUserId())));
  }

  @IsAdmin
  @PostMapping("/force-trigger")
  public ApiResult<Boolean> forceTriggerRequirement(@RequestBody AchievementTriggerDTO dto) {
    var SYMBOL = dto.getAchievementSymbol();
    var userId = dto.getUserId();
    if (SYMBOL == FORGE_OF_PERSEVERANCE) {
      log.info("Achievement Oracle of Knowledge Handling flush {}", userId);

      var achievementDto = achievementService.getAchievementInfoBySymbol(SYMBOL);
      var achievement = achievementDto.getAchievement();
      if (Objects.isNull(achievement)) {
        throw ControllerUtils.badRequest(
            "Achievement Forge of Perseverance achievement null " + SYMBOL.getValue());
      }
      var denominator = achievementDto.getAchievementLevels().firstOrNull().getDenominator();
      var startDate =
          achievement.getStartAt().toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
      var endDate = startDate.plusDays(denominator);
      var datePoints = seasonUserPointsDetailDao.getPointsByUserIdAndDate(
          userId,
          Timestamp.valueOf(startDate.atStartOfDay()),
          Timestamp.valueOf(endDate.atTime(LocalTime.MAX)));

      var MIN_EXP = 20L;
      var reachedSet = datePoints
          .filter(dp -> dp.getY().longValue() >= MIN_EXP)
          .map(Chart2::getX)
          .map(t -> t.toInstant().atZone(ZoneId.of("UTC")).toLocalDate())
          .map(ld -> ChronoUnit.DAYS.between(startDate, ld))
          .toSet();

      var progressDict = new HashMap<String, BigDecimal>();
      for (long i = 0; i < denominator; i++) {
        progressDict.put(
            "FORGE_OF_PERSEVERANCE:${i + 1}",
            reachedSet.contains(i) ? BigDecimal.ONE : BigDecimal.ZERO);
      }

      var success =
          achievementService.rewardAchievement(SYMBOL, userId, progressDict, false, false, true);
      return ApiResult.success(success);
    }
    if (SYMBOL == ORACLE_OF_KNOWLEDGE) {
      log.info("Achievement Oracle of Knowledge Handling flush {}", userId);
      var achievement = achievementService.getAchievementBySymbol(SYMBOL);
      if (Objects.isNull(achievement)) {
        throw ControllerUtils.badRequest(
            "Achievement Oracle of Knowledge does not exist Handling null " + SYMBOL.getValue());
      }
      var startDate = Objects.isNull(achievement.getStartAt())
          ? null
          : Timestamp.valueOf(achievement
              .getStartAt()
              .toInstant()
              .atZone(ZoneId.of("UTC"))
              .toLocalDate()
              .atStartOfDay());
      var endDate = Objects.isNull(achievement.getEndAt())
          ? null
          : Timestamp.valueOf(achievement
              .getEndAt()
              .toInstant()
              .atZone(ZoneId.of("UTC"))
              .toLocalDate()
              .atTime(LocalTime.MAX));
      var knowledgeList = List.of("Prompt Collection", "Model Optimization", "Persona Emulation");

      var progressDict = knowledgeList
          .map(knowledge -> {
            var x =
                seasonService.sumPointsByProjectKnowledge(userId, knowledge, startDate, endDate);
            return Pair.of(
                "ORACLE_OF_KNOWLEDGE:" + knowledge.toUpperCase().replaceAll("[-\\s]", "_"), x);
          })
          .toMap(Pair::getLeft, Pair::getRight);

      var success =
          achievementService.rewardAchievement(SYMBOL, userId, progressDict, false, false, true);
      return ApiResult.success(success);
    }
    throw ControllerUtils.badRequest("not supported achievement");
  }

  @IsAdmin
  @PostMapping
  public ApiResult<Achievement> createAchievement(@RequestBody AchievementCreateUpdateDTO dto) {
    var achievement = Achievement.builder()
        .symbol(dto.getSymbol())
        .name(dto.getName())
        .description(dto.getDescription())
        .category(dto.getCategory())
        .levelType(dto.getLevelType())
        .standard(dto.getStandard())
        .standardUri(dto.getStandardUri())
        .network(dto.getNetwork())
        .feature(dto.getFeature())
        .sort(dto.getSort())
        .active(dto.getActive())
        .startAt(dto.getStartAt())
        .endAt(dto.getEndAt())
        .claimStartAt(dto.getClaimStartAt())
        .claimEndAt(dto.getClaimEndAt())
        .mintStartAt(dto.getMintStartAt())
        .mintEndAt(dto.getMintEndAt())
        .build();

    return ApiResult.success(achievementService.createAchievement(achievement));
  }

  @IsAdmin
  @PutMapping("/{id}")
  public ApiResult<AchievementDTO> updateAchievement(
      @PathVariable Long id, @RequestBody AchievementCreateUpdateDTO dto) {
    var achievement = achievementService
        .getAchievementById(id)
        .orElseThrow(() -> ControllerUtils.notFound("Achievement not found"));
    achievementService.updateAchievement(id, dto, achievement);
    return ApiResult.success(achievementService.getAchievementInfoById(id));
  }

  @IsAdmin
  @DeleteMapping("/{id}")
  public void deleteAchievement(@PathVariable Long id) {
    achievementService
        .getAchievementById(id)
        .ensurePresent(() -> ControllerUtils.notFound("Achievement not found"));
    achievementService.deleteAchievementById(id);
  }

  @IsAdmin
  @PostMapping("/{id}/level")
  public ApiResult<AchievementDTO> createAchievementLevel(
      @PathVariable Long id, @RequestBody AchievementLevelCreateUpdateDTO dto) {
    if (!id.equals(dto.getAchievementId())) {
      throw ControllerUtils.badRequest(
          "Achievement id ${id} not equals to id of dto ${dto.getAchievementId()}");
    }
    var achievementDto = achievementService.getAchievementInfoById(id);
    achievementDto
        .getAchievementLevels()
        .map(AchievementLevel::getLevel)
        .filter(level -> level.equals(dto.getLevel()))
        .findFirst()
        .ifPresent(l -> {
          throw ControllerUtils.badRequest("Achievement id ${id} at level ${l} already exists");
        });

    var achievementLevel = AchievementLevel.builder()
        .achievementId(dto.getAchievementId())
        .level(dto.getLevel())
        .exp(dto.getExp())
        .behaviorCode(dto.getBehaviorCode())
        .requirement(dto.getRequirement())
        .denominator(dto.getDenominator())
        .logo(dto.getLogo())
        .build();

    return ApiResult.success(achievementService.createAchievementLevel(achievementLevel));
  }

  @IsAdmin
  @PutMapping("/{id}/level/{level}")
  public ApiResult<AchievementDTO> updateAchievementLevel(
      @PathVariable Long id,
      @PathVariable Integer level,
      @RequestBody AchievementLevelCreateUpdateDTO dto) {
    if (!id.equals(dto.getAchievementId()) || !level.equals(dto.getLevel())) {
      throw ControllerUtils.badRequest("params not match id: ${id} level: ${level} dto: ${dto}");
    }

    var achievementDto = achievementService.getAchievementInfoById(id);
    var achievementLevel = achievementDto
        .getAchievementLevels()
        .filter(l -> level.equals(l.getLevel()))
        .findFirst();

    var newAchievementLevel = AchievementLevel.builder()
        .achievementId(dto.getAchievementId())
        .level(dto.getLevel())
        .exp(dto.getExp())
        .behaviorCode(dto.getBehaviorCode())
        .requirement(dto.getRequirement())
        .denominator(dto.getDenominator())
        .logo(dto.getLogo())
        .build();

    achievementLevel.ifPresentOrElse(
        al -> achievementService.updateAchievementLevel(al.getId(), newAchievementLevel, al),
        () -> achievementService.createAchievementLevel(newAchievementLevel));
    return ApiResult.success(achievementService.getAchievementInfoById(id));
  }

  @IsAdmin
  @DeleteMapping("/{id}/level/{level}")
  public void deleteAchievementLevel(@PathVariable Long id, @PathVariable Integer level) {
    var achievementDto = achievementService.getAchievementInfoById(id);
    achievementDto
        .getAchievementLevels()
        .filter(l -> level.equals(l.getLevel()))
        .forEach(l -> achievementService.deleteAchievementLevel(l));
  }

  @IsAdmin
  @PostMapping("/{id}/requirement")
  public ApiResult<AchievementDTO> createAchievementRequirement(
      @PathVariable Long id, @RequestBody AchievementRequirementCreateUpdateDTO dto) {
    if (!id.equals(dto.getAchievementId())) {
      throw ControllerUtils.badRequest(
          "Achievement id ${id} not equals to id of dto ${dto.getAchievementId()}");
    }
    var achievementDto = achievementService.getAchievementInfoById(id);

    if (Objects.nonNull(dto.getAchievementLevelId())) {
      achievementDto
          .getAchievementLevels()
          .filter(al ->
              id.equals(al.getAchievementId()) && al.getId().equals(dto.getAchievementLevelId()))
          .findAny()
          .ensurePresent(() -> ControllerUtils.badRequest(
              "Achievement id ${id} not match ${achievementDto.getAchievementLevels()}"));
    }

    achievementDto
        .getAchievementRequirements()
        .filter(r -> r.getBehaviorCode().equalsIgnoreCase(dto.getBehaviorCode()))
        .findAny()
        .ensurePresent(() -> ControllerUtils.badRequest(
            "Achievement id ${id} alreay have BehaviorCode ${dto.getBehaviorCode()}"));

    var achievementRequirement = AchievementRequirement.builder()
        .achievementId(dto.getAchievementId())
        .achievementLevelId(dto.getAchievementLevelId())
        .requirement(dto.getRequirement())
        .denominator(dto.getDenominator())
        .behaviorCode(dto.getBehaviorCode())
        .name(dto.getName())
        .description(dto.getDescription())
        .logo(dto.getLogo())
        .actionText(dto.getActionText())
        .actionPassedText(dto.getActionPassedText())
        .actionUri(dto.getActionUri())
        .sort(dto.getSort())
        .build();

    return ApiResult.success(
        achievementService.createAchievementRequirement(achievementRequirement));
  }

  @IsAdmin
  @PutMapping("/{id}/requirement/{requirementId}")
  public ApiResult<AchievementDTO> updateAchievementRequirement(
      @PathVariable Long id,
      @PathVariable Long requirementId,
      @RequestBody AchievementRequirementCreateUpdateDTO dto) {
    if (!id.equals(dto.getAchievementId())) {
      throw ControllerUtils.badRequest(
          "Achievement id ${id} not equals to id of dto ${dto.getAchievementId()}");
    }
    var achievementDto = achievementService.getAchievementInfoById(id);

    if (Objects.nonNull(dto.getAchievementLevelId())) {
      achievementDto
          .getAchievementLevels()
          .filter(al ->
              id.equals(al.getAchievementId()) && al.getId().equals(dto.getAchievementLevelId()))
          .findAny()
          .ensurePresent(() -> ControllerUtils.badRequest(
              "Achievement id ${id} not match ${achievementDto.getAchievementLevels()}"));
    }

    achievementDto
        .getAchievementRequirements()
        .filter(r -> r.getBehaviorCode().equalsIgnoreCase(dto.getBehaviorCode()))
        .findAny()
        .ensurePresent(() -> ControllerUtils.badRequest(
            "Achievement id ${id} alreay have BehaviorCode ${dto.getBehaviorCode()}"));

    var achievementRequirement = achievementDto
        .getAchievementRequirements()
        .filter(r -> r.getId().equals(requirementId))
        .findAny()
        .orElseThrow(() -> ControllerUtils.notFound(
            "Achievement id ${id} requirement ${requirementId} not found"));

    var newAchievementRequirement = AchievementRequirement.builder()
        .achievementId(dto.getAchievementId())
        .achievementLevelId(dto.getAchievementLevelId())
        .requirement(dto.getRequirement())
        .denominator(dto.getDenominator())
        .behaviorCode(dto.getBehaviorCode())
        .name(dto.getName())
        .description(dto.getDescription())
        .logo(dto.getLogo())
        .actionText(dto.getActionText())
        .actionPassedText(dto.getActionPassedText())
        .actionUri(dto.getActionUri())
        .sort(dto.getSort())
        .build();
    achievementService.updateAchievementRequirement(
        requirementId, newAchievementRequirement, achievementRequirement);
    return ApiResult.success(achievementService.getAchievementInfoById(id));
  }

  @IsAdmin
  @DeleteMapping("/{id}/requirement/{requirementId}")
  public void deleteAchievementRequirement(
      @PathVariable Long id, @PathVariable Long requirementId) {
    var achievementDto = achievementService.getAchievementInfoById(id);
    achievementDto
        .getAchievementRequirements()
        .filter(r -> requirementId.equals(r.getId()))
        .forEach(r -> achievementService.deleteAchievementRequirement(r));
  }

  @IsAdmin
  @GetMapping("/seasons")
  public ApiResult<List<SeasonAchievement>> getAllSeasonAchievements(
      @RequestParam(value = "seasonId", required = false) Long seasonId) {
    var achievementDto = achievementService.getAllSeasonAchievements(seasonId);
    return ApiResult.success(achievementDto);
  }

  @IsAdmin
  @GetMapping("/{id}/seasons")
  public ApiResult<List<SeasonAchievement>> getSeasonAchievement(@PathVariable Long id) {
    var achievementDto = achievementService.getByAchievementId(id);
    return ApiResult.success(achievementDto);
  }

  @IsAdmin
  @PostMapping("/{id}/season/{seasonId}")
  public ApiResult<SeasonAchievement> createSeasonAchievement(
      @PathVariable Long id, @PathVariable Long seasonId) {
    var achievementDto = achievementService.getByAchievementId(id);
    if (!CollectionUtils.isEmpty(achievementDto)
        && achievementDto
            .filter(sa -> sa.getSeasonId().equals(seasonId))
            .findAny()
            .isPresent()) {
      throw ControllerUtils.notFound("Season Achievement already exists");
    }
    var seasonAchievement = achievementService.createSeasonAchievement(id, seasonId);
    return ApiResult.success(seasonAchievement);
  }

  @IsAdmin
  @DeleteMapping("/seasons")
  public void deleteSeasonAchievement(
      @RequestParam(value = "achievementId", required = false) Long achievementId,
      @RequestParam(value = "seasonId", required = false) Long seasonId) {
    if (Objects.isNull(achievementId) && Objects.isNull(seasonId)) {
      throw ControllerUtils.badRequest("achievementId or seasonId are required");
    }
    achievementService.deleteSeasonAchievement(achievementId, seasonId);
  }

  @IsAdmin
  @GetMapping("/partly-check-enabled")
  public ApiResult<Boolean> isPartlyCheckEnabled() {
    return ApiResult.success(achievementSettingService.isPartlyCheckEnabled());
  }

  @IsAdmin
  @GetMapping("/biz-trigger-enabled")
  public ApiResult<Boolean> isBizTriggerEnabled() {
    return ApiResult.success(achievementSettingService.isBizTriggerEnabled());
  }

  @IsAdmin
  @PostMapping("/partly-check-enabled")
  public ApiResult<Boolean> setPartlyCheckEnabled() {
    achievementSettingService.setPartlyCheckEnabled(true);
    return ApiResult.success(true);
  }

  @IsAdmin
  @PostMapping("/biz-trigger-enabled")
  public ApiResult<Boolean> setBizTriggerEnabled() {
    achievementSettingService.setBizTriggerEnabled(true);
    return ApiResult.success(true);
  }

  @IsAdmin
  @PostMapping("/partly-check-disabled")
  public ApiResult<Boolean> setPartlyCheckDisabled() {
    achievementSettingService.setPartlyCheckEnabled(false);
    return ApiResult.success(true);
  }

  @IsAdmin
  @PostMapping("/biz-trigger-disabled")
  public ApiResult<Boolean> setBizTriggerDisabled() {
    achievementSettingService.setBizTriggerEnabled(false);
    return ApiResult.success(true);
  }

  // =====================================================================================
  // ============================ new achievement on contract ============================
  // =====================================================================================

  @GetMapping("/infos")
  public ApiResult<AchievementOnContractInfoDTO> getAchievementOnContractInfos() {
    var infos = achievementOnContractService.getAchievementOnContractInfos();
    return ApiResult.success(infos);
  }

  @GetMapping("/on-contract")
  public ApiResult<List<AchievementOnContract>> getAchievementOnContracts(
      @RequestParam(required = false) Long achievementId,
      @RequestParam(required = false) Long seasonId,
      @RequestParam(required = false) String oracleSymbol,
      @RequestParam(required = false) String contractAddress) {
    var achievements = achievementOnContractService.getAchievementOnContracts(
        achievementId, seasonId, oracleSymbol, contractAddress);
    return ApiResult.success(achievements);
  }

  @GetMapping("/on-contract/{id}")
  public ApiResult<AchievementOnContract> getAchievementOnContract(@PathVariable Long id) {
    var achievement = achievementOnContractService.getAchievementOnContract(id);
    return ApiResult.success(achievement);
  }

  @IsAdmin
  @PostMapping("/on-contract")
  public ApiResult<AchievementOnContract> createAchievementOnContract(
      @RequestBody AchievementOnContractCreateUpdateDTO dto) {
    var achievement = achievementOnContractService.createAchievementOnContract(dto);
    return ApiResult.success(achievement);
  }

  @IsAdmin
  @PostMapping("/on-contract-list")
  public ApiResult<List<AchievementOnContract>> createAchievementOnContractList(
      @RequestBody List<AchievementOnContractCreateUpdateDTO> dtos) {
    var achievements =
        dtos.map(achievementOnContractService::createAchievementOnContract).toList();
    return ApiResult.success(achievements);
  }

  @IsAdmin
  @PutMapping("/on-contract/{id}")
  public ApiResult<AchievementOnContract> updateAchievementOnContract(
      @PathVariable Long id, @RequestBody AchievementOnContractCreateUpdateDTO dto) {
    var achievement = achievementOnContractService.updateAchievementOnContract(id, dto);
    return ApiResult.success(achievement);
  }

  @IsAdmin
  @DeleteMapping("/on-contract/{id}")
  public ApiResult<AchievementOnContract> deleteAchievementOnContract(@PathVariable Long id) {
    var achievement = achievementOnContractService.deleteAchievementOnContract(id);
    return ApiResult.success(achievement);
  }

  @IsAdmin
  @PostMapping("/add-progress")
  public ApiResult<Boolean> addProgress(@RequestBody List<AchievementAddProgressDTO> dtos) {
    var oracleDTOs = dtos.map(dto -> AchievementProgressOracleDTO.of(
            dto.getProgressType(),
            dto.getUser(),
            dto.getAchievementId(),
            dto.getTaskId(),
            dto.getTimestamp(),
            dto.getProgress()))
        .toList();

    try {
      achievementOracleSender.sendMessyMessages(oracleDTOs);
      return ApiResult.success(true);
    } catch (Exception e) {
      log.error("Achievement add progress error ", e);
      return ApiResult.fail(e.getMessage());
    }
  }

  @IsAdmin
  @PostMapping("/check-progress")
  public ApiResult<List<AchievementDiffProgressDTO>> checkProgress(
      @RequestBody AchievementProgressCheckReqDTO dto) {

    if (CollectionUtils.isEmpty(dto.getWalletAddresses())
        || CollectionUtils.isEmpty(dto.getAchievementIds())) {
      return ApiResult.success(Collections.emptyList());
    }
    var achievements = dto.getAchievementIds()
        .map(achievementId ->
            achievementOnContractService.getAchievementOnContracts(achievementId, null, null, null))
        .filter((a) -> !CollectionUtils.isEmpty(a))
        .map(a -> a.first())
        .filter(Objects::nonNull)
        .toList();

    var walletAddresses = dto.getWalletAddresses().map(String::toLowerCase).toList();

    var diffs = achievements
        .map(achievementOnContract ->
            achievementOracleLogService.diffProgress(achievementOnContract, walletAddresses))
        .flatMap(List::stream)
        .toList();
    return ApiResult.success(diffs);
  }

  @IsAdmin
  @PostMapping("/check-all-progress")
  public ApiResult<String> checkAllProgress(@RequestBody AchievementProgressCheckReqDTO dto) {
    var achievementIds = dto.getAchievementIds();
    var achievements = achievementOnContractService.getByAchievementIds(achievementIds);
    log.info("Start Checking all progress for achievement {}", achievementIds.join(","));
    var id = String.valueOf(DateUtils.now().getTime());
    achievementProgressChecker.startCheckAllProgress(achievements, id);
    log.info("Start Checking all progress for achievement {}", id);
    return ApiResult.success(id);
  }

  @IsAdmin
  @GetMapping("/check-all-progress/{id}")
  public ResponseEntity<Object> getAllCheckProgress(@PathVariable String id) {
    achievementProgressChecker.ensureCheckSuccess(id);
    return achievementProgressChecker.downloadCheckResult(id);
  }

  @IsAdmin
  @PostMapping("/repair-all-progress/{id}")
  public ApiResult<String> repairAllProgress(@PathVariable String id) {
    achievementProgressChecker.ensureCheckSuccess(id);
    achievementProgressChecker.startRepairAllProgress(id);
    return ApiResult.success(id);
  }

  @IsAdmin
  @GetMapping("/repair-all-progress/{id}")
  public ApiResult<Boolean> getRepairProgress(@PathVariable String id) {
    return ApiResult.success(achievementProgressChecker.getRepairProgress(id));
  }
}
