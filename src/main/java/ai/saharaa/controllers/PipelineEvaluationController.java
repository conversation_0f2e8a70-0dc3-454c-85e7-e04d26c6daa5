package ai.saharaa.controllers;

import static ai.saharaa.utils.Constants.PIPELINE_EVALUATION_STAGES;

import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.daos.SystemSettingDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.PipelineEvaluationStagesDTO;
import ai.saharaa.utils.DateUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.sql.Timestamp;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/pipeline-evaluation")
@Tag(name = "PipelineEvaluation Service", description = "PipelineEvaluation service API")
@AllArgsConstructor
public class PipelineEvaluationController {
  private final SystemSettingDao systemSettingDao;
  private final ObjectMapper objectMapper;

  private Boolean isTimeAfter(Timestamp time1, Timestamp time2) {
    if (Objects.isNull(time1) || Objects.isNull(time2)) {
      return null;
    }
    return time1.after(time2);
  }

  private void setStartedAndEnded(PipelineEvaluationStagesDTO.Stage stage, Timestamp now) {
    if (Objects.isNull(stage)) {
      return;
    }
    if (Objects.nonNull(stage.getStartTime())) {
      stage.setStarted(isTimeAfter(now, stage.getStartTime()));
    }
    if (Objects.nonNull(stage.getEndTime())) {
      stage.setEnded(isTimeAfter(now, stage.getEndTime()));
    }
  }

  @IsAdmin
  @PostMapping("/stages")
  public ApiResult<Boolean> postStages(@RequestBody PipelineEvaluationStagesDTO dto) {
    if (Objects.nonNull(dto)) {
      try {
        var config = objectMapper.writeValueAsString(dto);
        systemSettingDao.updateConfigByName(PIPELINE_EVALUATION_STAGES, config);
      } catch (JsonProcessingException e) {
        return ApiResult.success(false);
      }
    }
    return ApiResult.success(true);
  }

  @GetMapping("/stages")
  public ApiResult<PipelineEvaluationStagesDTO> getStages() {
    var systemStages = systemSettingDao.getSystemSettingsByName(PIPELINE_EVALUATION_STAGES);
    if (CollectionUtils.isEmpty(systemStages) || Objects.isNull(systemStages.first())) {
      return ApiResult.success(PipelineEvaluationStagesDTO.builder().build());
    }
    var stages = systemStages.first();
    var now = DateUtils.now();

    try {
      var config = objectMapper.readValue(stages.getConfig(), PipelineEvaluationStagesDTO.class);

      setStartedAndEnded(config.getStage1(), now);
      setStartedAndEnded(config.getStage2(), now);
      setStartedAndEnded(config.getStage3(), now);
      return ApiResult.success(config);
    } catch (JsonProcessingException e) {
      return ApiResult.success(PipelineEvaluationStagesDTO.builder().build());
    }
  }
}
