package ai.saharaa.controllers;

import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.SimpleDataDTO;
import ai.saharaa.dto.task.CreateTaskListDTO;
import ai.saharaa.dto.task.TaskDetailsDTO;
import ai.saharaa.dto.task.TaskListDetailsDTO;
import ai.saharaa.model.TaskList;
import ai.saharaa.services.BatchService;
import ai.saharaa.services.TaskListService;
import ai.saharaa.services.TaskService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/task-lists")
public class TaskListController {
  private final TaskListService taskListService;
  private final BatchService batchService;
  private final TaskService taskService;

  public TaskListController(
      TaskListService taskListService, BatchService batchService, TaskService taskService) {
    this.taskListService = taskListService;
    this.batchService = batchService;
    this.taskService = taskService;
  }

  @PostMapping
  public ApiResult<TaskList> createTaskList(
      @Valid @RequestBody CreateTaskListDTO createTaskListDTO) {
    batchService
        .getBatchById(createTaskListDTO.getBatchId())
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Batch not found"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this batch"));
    // TODO: validate list type
    return taskListService
        .createTaskList(createTaskListDTO, ControllerUtils.currentUid())
        .toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<TaskList> getTaskListById(@PathVariable Long id) {
    var tl = taskListService.getTaskListById(id);
    checkIsOwner(tl);
    return tl.toApiResult();
  }

  @GetMapping("/{id}/tasks")
  public ApiResult<List<TaskDetailsDTO>> getTasksByTaskListId(@PathVariable Long id) {
    var tl = taskListService.getTaskListById(id);
    checkIsOwner(tl);
    var batch = batchService.getBatchById(tl.getBatchId()).get();
    return taskService.getTaskDetailsByTaskListId(batch, tl.getId()).toApiResult();
  }

  // tasklist and tasks are modified by only one user at a time
  // we assume there is no multi-user conflict
  @PostMapping("/{id}/task-orders")
  public void updateTasksOrders(
      @PathVariable Long id, @RequestBody SimpleDataDTO<List<Long>> taskIds) {
    if (CollectionUtils.isEmpty(taskIds.getData())) {
      throw ControllerUtils.badRequest("Task ids cannot be empty");
    }
    var tl = taskListService.getTaskListById(id);
    checkIsOwner(tl);
    taskListService.updateTasksOrders(tl, taskIds);
  }

  @DeleteMapping("/{id}")
  public void deleteTaskListById(@PathVariable Long id) {
    var tl = taskListService.getTaskListById(id);
    checkIsOwner(tl);
    if (tl.getListType() == TaskList.TaskListType.LABEL
        || tl.getListType() == TaskList.TaskListType.EXAM) {
      throw ControllerUtils.badRequest("Cannot delete this task list");
    }
    taskListService.deleteTaskListById(id);
  }

  @GetMapping("/{id}/details")
  public ApiResult<TaskListDetailsDTO> getDetails(@PathVariable Long id) {
    var tl = taskListService.getTaskListById(id);
    checkIsOwner(tl);
    var batch = batchService.getBatchById(tl.getBatchId()).get();
    return TaskListDetailsDTO.builder()
        .taskList(tl)
        .tasks(taskService.getTaskDetailsByTaskListId(batch, tl.getId()))
        .build()
        .toApiResult();
  }

  private void checkIsOwner(TaskList taskList) {
    if (!taskList.getOwnerId().equals(ControllerUtils.currentUid())
        && !ControllerUtils.isAdmin()
        && !ControllerUtils.isAccountManager()
        && !ControllerUtils.isNodeManager()) {
      throw ControllerUtils.forbidden("You are not the owner of this task list");
    }
  }
}
