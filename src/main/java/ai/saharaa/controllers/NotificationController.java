package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.*;
import ai.saharaa.model.*;
import ai.saharaa.services.NotificationRecordService;
import ai.saharaa.services.NotificationService;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/notifications")
public class NotificationController {

  private final NotificationService notificationService;
  private final NotificationRecordService notificationRecordService;

  public NotificationController(
      NotificationService notificationService,
      NotificationRecordService notificationRecordService) {
    this.notificationService = notificationService;
    this.notificationRecordService = notificationRecordService;
  }

  @PostMapping("/publish")
  @IsAccountManager
  public ApiResult<Long> publishNotificationConfig(@RequestBody Map<String, Object> dataMap) {
    Long id = notificationService.publishNotificationConfig(
        notificationService.getDataFromDataMap(PublishNotificationConfigDTO.builder(), dataMap),
        dataMap);
    return ApiResult.success(id);
  }

  @DeleteMapping("/deleteNotificationConfig")
  @IsAccountManager
  public ApiResult<Boolean> deleteNotificationConfig(
      @RequestBody @Validated NotificationConfigDeleteDTO ids) {
    notificationService.deleteNotificationConfig(ids.getIds());
    return ApiResult.success(true);
  }

  @GetMapping("/listNotificationConfig")
  @IsAccountManager
  @CheckPageParam()
  public ApiResult<PageResult<NotificationConfig>> listNotificationConfig(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(required = false, defaultValue = "") String name,
      @RequestParam(required = false, defaultValue = "0,1") String type,
      @RequestParam(required = false, defaultValue = "0,1,2,3") String status) {
    return ApiResult.success(
        notificationService.listNotificationConfig(page, limit, name, type, status));
  }

  @GetMapping("/getNotificationConfigById/{id}")
  @IsAccountManager
  public ApiResult<NotificationConfig> getNotificationConfigById(@PathVariable("id") Long id) {
    return ApiResult.success(notificationService.getNotificationConfigById(id));
  }

  @PostMapping("/uploadAddress")
  @IsAccountManager
  public ApiResult<Long> uploadAddress(
      @RequestBody @Validated NotificationConfigUploadAddress data) {
    return ApiResult.success(notificationService.uploadAddress(data));
  }

  @DeleteMapping("/deleteUploadAddress")
  @IsAccountManager
  public ApiResult<Boolean> deleteUploadAddress(
      @RequestBody @Valid NotificationDeleteUploadAddressDTO data) {
    notificationService.deleteUploadAddress(
        data.getAddressIds(), data.getNotificationId(), data.getDeleteAll());
    return ApiResult.success(true);
  }

  @GetMapping("/listUploadAddress")
  @IsAccountManager
  @CheckPageParam()
  public ApiResult<PageResult<NotificationConfigUploadAddressDTO>> listUploadAddress(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam() Long id,
      @RequestParam(required = false, defaultValue = "") String address) {
    return ApiResult.success(notificationService.listUploadAddress(page, limit, id, address));
  }

  @PostMapping("/createConfig")
  @IsAccountManager
  public ApiResult<CreateNotificationConfigResDTO> createNotificationConfig(
      @RequestBody @Valid CreateNotificationConfigDTO data) {
    return ApiResult.success(notificationService.createNotificationConfig(data));
  }

  @PostMapping("/updateConfig")
  @IsAccountManager
  public ApiResult<UpdateNotificationConfigResDTO> updateNotificationConfig(
      @RequestBody Map<String, Object> dataMap) {
    UpdateNotificationConfigDTO data =
        notificationService.getDataFromDataMap(UpdateNotificationConfigDTO.builder(), dataMap);
    UpdateNotificationConfigResDTO updateNotificationConfigResDTO =
        notificationService.updateNotificationConfigNoVerify(data, dataMap);
    return ApiResult.success(updateNotificationConfigResDTO);
  }

  @PostMapping("/uploadCheck")
  @IsAccountManager
  public ApiResult<List<String>> uploadCheck(
      @RequestBody @Valid NotificationConfigUploadDTO uploadData) {
    return ApiResult.success(notificationService.uploadCheck(uploadData.getAddress()));
  }

  @GetMapping()
  @CheckPageParam()
  public ApiResult<PageResult<Notification>> getNotifications(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    IPage<Notification> notifications =
        notificationService.getNotificationsByUserId(page, limit, ControllerUtils.currentUid());
    return notifications.toPageResult().toApiResult();
  }

  @GetMapping("/v2")
  @CheckPageParam()
  public ApiResult<NotificationPageDTO> getNotificationsV2(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    var currUserCreateAt = ControllerUtils.currentUserDetail().getCreatedAt();
    IPage<Notification> notifications = notificationService.getNotificationsByUserIdV2(
        page, limit, ControllerUtils.currentUid(), currUserCreateAt);

    var remainingNotificationCount = notificationService.getNotificationCountByUserId(
        ControllerUtils.currentUid(), currUserCreateAt);

    return NotificationPageDTO.builder()
        .notificationList(notifications.toPageResult())
        .remainingNotificationCount(remainingNotificationCount)
        .build()
        .toApiResult();
  }

  @GetMapping("/types")
  @CheckPageParam()
  public ApiResult<PageResult<Notification>> getNotificationByTypes(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam List<Notification.NotificationType> types) {
    IPage<Notification> notifications = notificationService.getNotificationsByUserIdAndPages(
        page, limit, ControllerUtils.currentUid(), types);
    return notifications.toPageResult().toApiResult();
  }

  @PostMapping("/{id}/read")
  public ApiResult<Notification> markNotificationRead(@PathVariable Long id) {
    return notificationService
        .markNotificationRead(id, ControllerUtils.currentUid())
        .toApiResult();
  }

  /** Deprecated, will be replaced by /notifyWhitelist */
  @PostMapping("/send/whitelist")
  @IsAdmin
  public ApiResult<Boolean> sendMessageToWhitelist(@RequestBody NotificationDTO notification) {
    notificationService.sendMessageToWhitelist(notification);
    return ApiResult.success(true);
  }

  @PostMapping("/notifyWhitelist")
  @IsAdmin
  public ApiResult<Boolean> notifyWhitelist(@RequestBody WhitelistNotificationDTO notification) {
    Boolean result = notificationService.notifyWhitelist(notification);
    return ApiResult.success(result);
  }

  // TODO add ut
  @GetMapping("/getNotificationId")
  public ApiResult<NotificationRecord> changeLastNotificationId() {
    var currentUid = ControllerUtils.currentUid();
    var notificationRecordOpt = notificationRecordService.findOneByUserId(currentUid);
    if (notificationRecordOpt.isEmpty()) {
      notificationRecordService.createEmptyNotificationRecord(currentUid);
      notificationRecordOpt = notificationRecordService.findOneByUserId(currentUid);
    }

    if (notificationRecordOpt.isEmpty()) {
      throw ControllerUtils.badRequest("Notification record can not create");
    }
    var notificationRecord = notificationRecordOpt.get();
    return ApiResult.success(notificationRecord);
  }

  @PostMapping("/lastNotificationId")
  @Transactional
  public ApiResult<Long> changeLastNotificationId(
      @Valid @RequestBody SimpleDataDTO<Long> lastNotification) {

    Long lastNotificationId = lastNotification.getData();

    NotificationRecord notificationRecord = notificationService.changeNotificationRecordCheck();
    if (notificationRecord.getLastNotificationId() == null
        || (lastNotificationId != 0
            && lastNotificationId > notificationRecord.getLastNotificationId())) {
      notificationRecordService.update(
          new UpdateWrapper<NotificationRecord>()
              .lambda()
              .eq(NotificationRecord::getId, notificationRecord.getId())
              .set(NotificationRecord::getLastNotificationId, lastNotificationId));
    }

    return ApiResult.success(notificationRecord.getId());
  }

  @PostMapping("/lastTextNotificationId")
  @Transactional
  public ApiResult<Long> changeLastTextNotificationId(
      @Valid @RequestBody SimpleDataDTO<Long> lastTextNotification) {
    Long lastTextNotificationId = lastTextNotification.getData();

    NotificationRecord notificationRecordOpt = notificationService.changeNotificationRecordCheck();
    if (notificationRecordOpt.getLastTextNotificationId() != null
        && lastTextNotificationId != 0
        && lastTextNotificationId > notificationRecordOpt.getLastNotificationId()) {
      notificationRecordService.update(new UpdateWrapper<NotificationRecord>()
          .lambda()
          .eq(NotificationRecord::getId, notificationRecordOpt.getId())
          .set(NotificationRecord::getLastTextNotificationId, lastTextNotificationId));
    }

    return ApiResult.success(notificationRecordOpt.getId());
  }

  @PostMapping("/readAll")
  @Transactional
  public ApiResult<Long> changeLastMarkReadAllNotificationId() {
    Long currentUid = ControllerUtils.currentUid();
    Timestamp currUserCreateAt = ControllerUtils.currentUserDetail().getCreatedAt();
    IPage<Notification> notifications =
        notificationService.getNotificationsByUserIdV2(1, 1, currentUid, currUserCreateAt);
    Long lastMarkReadNotificationId = 0L;
    if (!notifications.toPageResult().getData().isEmpty()) {
      lastMarkReadNotificationId = notifications.getRecords().last().getId();
    }

    NotificationRecord notificationRecordOpt = notificationService.changeNotificationRecordCheck();
    if (lastMarkReadNotificationId != 0) {
      notificationRecordService.update(new UpdateWrapper<NotificationRecord>()
          .lambda()
          .eq(NotificationRecord::getId, notificationRecordOpt.getId())
          .set(NotificationRecord::getLastNotificationId, lastMarkReadNotificationId)
          .set(NotificationRecord::getLastMarkReadAllNotificationId, lastMarkReadNotificationId)
          .set(NotificationRecord::getLastTextNotificationId, lastMarkReadNotificationId));
    }

    return ApiResult.success(notificationRecordOpt.getId());
  }
}
