package ai.saharaa.controllers;

import ai.saharaa.daos.ExamSessionDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.job.ExamSessionDetailsDTO;
import ai.saharaa.dto.job.SubmitAnswerDTO;
import ai.saharaa.model.Batch;
import ai.saharaa.model.Job;
import ai.saharaa.services.BatchService;
import ai.saharaa.services.JobService;
import ai.saharaa.services.JobSessionService;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import ai.saharaa.utils.ObjectUtils;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Objects;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/exam-sessions")
public class ExamSessionController {
  private ExamSessionDao examSessionDao;
  private final JobSessionService jobSessionService;
  private final JobService jobService;
  private final BatchService batchService;

  public ExamSessionController(
      ExamSessionDao examSessionDao,
      JobSessionService jobSessionService,
      JobService jobService,
      BatchService batchService) {
    this.examSessionDao = examSessionDao;
    this.jobSessionService = jobSessionService;
    this.jobService = jobService;
    this.batchService = batchService;
  }

  @PostMapping("/{id}/submit-answer")
  @Operation(summary = "Submit answer for exam session")
  public ApiResult<ExamSessionDetailsDTO> submitAnswer(
      @PathVariable Long id, @Valid @RequestBody SubmitAnswerDTO answerDTO) {
    // TODO: move to an actor
    var examSession = examSessionDao
        .getExamSessionById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("exam session not found"))
        .filter(v -> v.getUserId().equals(ControllerUtils.currentUid()))
        .orElseThrow(() -> ControllerUtils.forbidden(
            "you are not allowed to submit answer for this exam session"));

    Job job = this.jobService
        .getJobById(examSession.getJobId())
        .orThrow(() -> ControllerUtils.notFound("job not found"))
        .filter(v -> v.getStatus().equals(Job.JobStatus.PRE_TASK))
        .orElseThrow(() -> ControllerUtils.forbidden("wrong job status"));

    this.batchService
        .getBatchById(job.getBatchId())
        .filter(b -> !b.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("batch not found"))
        .filter(b -> b.getStatus().equals(Batch.BatchStatus.PRE_TASK_EXAM))
        .ensurePresent(() -> ControllerUtils.notFound("batch not in PRE_TASK_EXAM status"));

    if (Objects.nonNull(job.getPreTaskDeadline())
        && job.getPreTaskDeadline().before(DateUtils.now())) {
      throw ControllerUtils.forbidden("pre task deadline passed");
    }

    var answerStr = ObjectUtils.toJson(answerDTO.getAnswers());
    jobSessionService.submitExamAnswer(examSession, answerStr);
    examSession = examSessionDao.getExamSessionById(id).get();
    return jobSessionService.getExamSessionDetails(examSession).toApiResult();
  }
}
