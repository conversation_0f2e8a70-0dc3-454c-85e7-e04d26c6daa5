package ai.saharaa.controllers;

import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.SUBMITTED_COUNT;
import static ai.saharaa.utils.Constants.CommonErrorContent.BATCH_NOT_FOUND;

import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.config.perms.IsApiKeyClient;
import ai.saharaa.config.perms.IsLabeler;
import ai.saharaa.config.perms.IsNodeManager;
import ai.saharaa.distribution.CommonCounterManager;
import ai.saharaa.distribution.TaskSubmitter;
import ai.saharaa.distribution.TaskVisitorProvider;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.job.*;
import ai.saharaa.dto.task.AnswerDetailsDTO;
import ai.saharaa.dto.task.MyShellBatchDownloadUrlDTO;
import ai.saharaa.dto.task.MyShellSubmissionFileDTO;
import ai.saharaa.dto.task.PreSignedUrlUploadDTO;
import ai.saharaa.model.*;
import ai.saharaa.model.api.ApiKeys;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.ObjectUtils;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/task-sessions")
public class TaskSessionController {
  private final BatchService batchService;
  private final TaskService taskService;
  private final UserService userService;
  private final TaskSessionService taskSessionService;
  private final JobUserService jobUserService;
  private final SpotSessionService spotSessionService;
  private final ReviewSessionService reviewSessionService;
  private final ResourceService resourceService;
  private final TaskVisitorProvider taskVisitorProvider;
  private final JobService jobService;
  private final CommonCounterManager counterManager;

  public TaskSessionController(
      BatchService batchService,
      TaskService taskService,
      UserService userService,
      TaskSessionService taskSessionService,
      SpotSessionService spotSessionService,
      ReviewSessionService reviewSessionService,
      JobUserService jobUserService,
      HoneyPotService honeyPotService,
      ResourceService resourceService,
      TaskVisitorProvider taskVisitorProvider,
      JobService jobService,
      CommonCounterManager counterManager) {
    this.batchService = batchService;
    this.taskService = taskService;
    this.taskSessionService = taskSessionService;
    this.reviewSessionService = reviewSessionService;
    this.spotSessionService = spotSessionService;
    this.userService = userService;
    this.jobUserService = jobUserService;
    this.resourceService = resourceService;
    this.taskVisitorProvider = taskVisitorProvider;
    this.jobService = jobService;
    this.counterManager = counterManager;
  }

  @GetMapping("/{id}")
  public ApiResult<TaskSession> getTaskSessionById(@PathVariable Long id) {
    var session = taskSessionService
        .getTaskSessionById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("task session not found"))
        .filter(v -> v.getUserId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager())
        .orElseThrow(() -> ControllerUtils.forbidden("no permission"));

    return session.toApiResult();
  }

  @GetMapping("/{id}/details")
  public ApiResult<TaskSessionDetailsDTO> getTaskSessionDetailsById(@PathVariable Long id) {
    var session = taskSessionService
        .getTaskSessionById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("task session not found"))
        .filter(v -> v.getUserId().equals(ControllerUtils.currentUid())
            || ControllerUtils.isAdmin()
            || ControllerUtils.isAccountManager()
            || taskSessionService
                .getRequesterIdByTaskSession(id)
                .filter(r -> r.equals(ControllerUtils.currentUid()))
                .isPresent())
        .orElseThrow(() -> ControllerUtils.forbidden("no permission"));

    var resource = taskService.getResourceByTaskId(session.getTaskId());
    return TaskSessionDetailsDTO.builder()
        .taskSession(session)
        .resource(resource.orElse(null))
        .build()
        .toApiResult();
  }

  @GetMapping("/{id}/get-annotation-summary")
  @IsNodeManager
  public ApiResult<TaskAuditDetailForNodeManagerDTO> getAnnotationDetail(@PathVariable Long id) {
    var taskSession = taskSessionService
        .getTaskSessionById(id)
        .orThrow(() -> ControllerUtils.notFound("task session not found"));
    TaskAuditDetailForNodeManagerDTO res = new TaskAuditDetailForNodeManagerDTO();
    res.setSession(taskSession.get());
    userService.getUserById(taskSession.get().getUserId()).ifPresent(res::setLabeler);
    var reviewSession =
        reviewSessionService.getReviewSessionByTaskSessionId(taskSession.get().getId());
    reviewSession.ifPresent(aSession -> {
      res.setReviewSession(aSession);
      userService.getUserById(aSession.getUserId()).ifPresent(res::setReviewer);
    });
    var auditSession =
        spotSessionService.getSpotSessionByTaskSessionId(taskSession.get().getId());
    auditSession.ifPresent(bSession -> {
      res.setAuditSession(bSession);
      userService.getUserById(bSession.getUserId()).ifPresent(res::setSpotter);
    });
    return res.toApiResult();
  }

  //  @PostMapping("/{id}/pin")
  //  @IsLabeler
  //  public ApiResult<TaskSession> pinSession(@PathVariable Long id) {
  //    var taskSession = taskSessionService
  //        .getTaskSessionById(id)
  //        .filter(v -> !v.getDeleted())
  //        .orThrow(() -> ControllerUtils.notFound("task session not found"))
  //        .filter(v -> TaskSession.TaskSessionStatus.PENDING.equals(v.getStatus()))
  //        .orThrow(() -> ControllerUtils.notFound("task session not pending"))
  //        .filter(v -> v.getUserId().equals(ControllerUtils.currentUid()))
  //        .orElseThrow(
  //            () -> ControllerUtils.forbidden("you are not allowed to pin this task session"));
  //    var job = jobDao
  //        .getJobById(taskSession.getJobId())
  //        .orElseThrow(() -> ControllerUtils.forbidden("invalid data"));
  //    var batchSetting = batchService.getBatchSettingByBatchId(job.getBatchId());
  //    if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.SINGLE)) {
  //      throw ControllerUtils.forbidden("not allowed to pin");
  //    }
  //    var oneDayBeforeDdl = DateUtils.add(job.getReviewDeadline(), Duration.ofDays(-1));
  //    if (DateUtils.now().after(oneDayBeforeDdl)) { // ddl is in 24hrs, can not pin
  //      throw ControllerUtils.forbidden("Close to deadline, you can not pin.");
  //    }
  //    var pinRes = taskSessionService.pinSession(id);
  //    taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(pinRes);
  //    return pinRes.toApiResult();
  //  }

  //  @PostMapping("/{id}/submit-answer")
  //  @IsLabeler
  //  public ApiResult<TaskSession> submitAnswer(
  //      @PathVariable Long id, @Valid @RequestBody SubmitAnswerDTO answer) {
  //
  //    TaskSession taskSession = this.taskDispatcher.submitTasks(
  //        answer,
  //        id,
  //        ControllerUtils.currentUid(),
  //        ControllerUtils.isTesterUser(),
  //        JobUser.JobUserRole.LABELER);
  //
  //    return ApiResult.success(taskSession);
  //  }

  @PostMapping("/submit-answers/pipeline-result")
  @IsApiKeyClient(ApiKeys.Type.PIPELINE)
  public ApiResult<SubmitPipelineResultFeedbackDTO> pipelineEnded(
      @Valid @RequestBody SubmitPipelineResultDTO dto) {
    return ApiResult.success(taskSessionService.pipelineEnded(dto));
  }

  @PostMapping("/submit-answers")
  @IsLabeler
  public ApiResult<Boolean> submitAnswers(@Valid @RequestBody SubmitAnswersDTO answers) {

    if (CollectionUtils.isEmpty(answers.getSubmitAnswers())) {
      throw ControllerUtils.badRequest("No answers submitted");
    }
    if (answers
        .getSubmitAnswers()
        .filter(dp -> dp.getSubmitAnswer()
            .getAnswers()
            .filter(a -> !StringUtils.isBlank(a.getAnswer().replace("\"", "").trim()))
            .findAny()
            .isEmpty())
        .findFirst()
        .isPresent()) {
      throw ControllerUtils.badRequest("invalid submit");
    }

    var userId = ControllerUtils.currentUid();
    var isTester = ControllerUtils.isTesterUser();

    var jobId = answers.getSubmitAnswers().get(0).getTaskSession().getJobId();
    var jobUser = jobUserService
        .getJobUserByJobAndUserId(jobId, userId)
        .orElseThrow(() -> ControllerUtils.notFound("task session not found"));
    if (!jobUser.getActive()) {
      if (!isTester) {
        throw ControllerUtils.badRequest(
            "You've been banned from this task because we've detected malicious activity.");
      } else {
        jobUserService.unbanInJob(userId, jobId);
      }
    }
    Job job =
        jobService.getJobById(jobId).orElseThrow(() -> ControllerUtils.notFound("Task not found."));

    if (job.getStatus().equals(Job.JobStatus.PAUSING)) {
      throw ControllerUtils.badRequest(
          "The task has been paused. We'll notify you when the task resumes.");
    }

    var batch = batchService
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));

    BatchSetting batchSetting = batchService.getBatchSettingByBatchId(batch.getId());

    TaskSubmitter<SubmitAnswersDTO, Boolean> taskSubmitter = this.taskVisitorProvider
        .getTaskVisitor(BatchSetting.DistributeType.SINGLE, JobUser.JobUserRole.LABELER)
        .taskSubmitter();

    Boolean res = taskSubmitter.submitTasks(answers, job, batch, batchSetting, jobUser, isTester);

    return ApiResult.success(res);
  }

  @PostMapping("/{id}/submit-am-review")
  @IsAccountManager
  public ApiResult<Boolean> amApprove(
      @PathVariable Long id, @RequestParam(defaultValue = "true") Boolean approve) {
    var session = taskSessionService
        .getTaskSessionById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("task session not found"));
    taskSessionService.amReviewSession(session, approve, ControllerUtils.currentUid());
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/submit-nm-review")
  @IsNodeManager
  public ApiResult<Boolean> nmApprove(
      @PathVariable Long id, @RequestParam(defaultValue = "true") Boolean approve) {
    var session = taskSessionService
        .getTaskSessionById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("task session not found"));
    taskSessionService.nmReviewSession(session, approve);
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/skip")
  public ApiResult<Boolean> skipTaskSession(@PathVariable Long id) {
    var curId = ControllerUtils.currentUid();

    var session = taskSessionService
        .getTaskSessionById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("wrong task session"));
    var jobUser = jobUserService
        .getJobUserByJobAndUserId(session.getJobId(), curId)
        .orElseThrow(() -> ControllerUtils.notFound("wrong job user"));
    var submittedCount =
        counterManager.getJobUserCount(session.getJobId(), curId, SUBMITTED_COUNT.value);
    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
      if (!session.getJobUserId().equals(jobUser.getId())) {
        throw ControllerUtils.badRequest("invalid data");
      }
      var revisedByMajorityVoteCount =
          taskSessionService.countUserRejectedByMajorityVoteSessionsInJob(id, curId);
      var skipQuota = Math.floor(submittedCount / 50f) * 5 + revisedByMajorityVoteCount + 5;
      if (skipQuota > 0) {
        var skippedCount = taskSessionService.getSkippedCount(session.getJobId(), curId);
        if (skippedCount >= skipQuota) {
          throw ControllerUtils.badRequest("you can not skip more");
        }
      } else {
        throw ControllerUtils.badRequest("you have no skip quota, please do more annotation");
      }
    } else if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
      var myReviewSession =
          reviewSessionService.getMyReviewSessionByTaskSessionId(id, curId).asOpt();
      if (myReviewSession.isEmpty()) {
        throw ControllerUtils.badRequest("invalid data");
      }
      if (!myReviewSession.get().getJobUserId().equals(jobUser.getId())) {
        throw ControllerUtils.badRequest("invalid data");
      }
      var skipQuota = Math.floor(submittedCount / 50f) * 5 + 5;
      if (skipQuota > 0) {
        var skippedCount = taskSessionService.getSkippedCount(session.getJobId(), curId);
        if (skippedCount >= skipQuota) {
          throw ControllerUtils.badRequest("you can not skip more");
        }
      } else {
        throw ControllerUtils.badRequest("you have no skip quota, please do more review");
      }
    }
    //    var skippingPinnedSession = session.getStatus().equals(PINNED);
    //    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER) && !skippingPinnedSession) {
    //      taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(session, jobUser);
    //    }
    //    if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
    //      reviewSessionService.generateNewReviewSessionIfNecessaryCauseOfHoneyPot(session, curId,
    // jobUser);
    //    }
    taskSessionService.skipTaskSession(session, curId);
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/submit-client-review")
  public ApiResult<Boolean> clientReview(
      @PathVariable Long id, @RequestParam(defaultValue = "true") Boolean approve) {
    var session = taskSessionService
        .getTaskSessionById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("task session not found"));
    batchService
        .getBatchByTaskSessionId(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("batch not exists"))
        .filter(b -> b.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("no permission"));
    taskSessionService.clientReviewSession(session, approve, ControllerUtils.currentUid());
    return ApiResult.success(true);
  }

  @PostMapping("/{id}/presigned-url")
  public ApiResult<PreSignedUrlTaskSessionResultDTO> generatePreSignedUrl(
      @PathVariable Long id, @RequestBody PreSignedUrlUploadDTO dto) {
    var result = resourceService.createResourceAndCloudStorageAndPreSignedUrl(
        ControllerUtils.currentUid(), dto, id);
    result.setTaskSessionId(id);
    result.setQuestionId(dto.getQuestionId());
    resourceService.deleteRedundantResourceAsync(
        id, result.getResourceId(), ControllerUtils.currentUid());
    return ApiResult.success(result);
  }

  @GetMapping("/count-by-difficulty")
  @IsApiKeyClient(ApiKeys.Type.DATA_SERVICE)
  @Cacheable(value = "getCountByDifficulty", cacheManager = "apiCacheManager")
  public ApiResult<Long> getCountByDifficulty(
      @RequestParam Batch.CourseDifficulty difficulty,
      @RequestParam(required = false) Long seasonId,
      @RequestParam(required = false) String address) {
    var count = taskSessionService.getCountByDifficulty(difficulty, seasonId, address);
    return ApiResult.success(count);
  }

  @PostMapping("/batch-get-download-url")
  @IsApiKeyClient(ApiKeys.Type.MY_SHELL)
  public ApiResult<List<MyShellSubmissionFileDTO>> batchGetDownloadUrl(
      @RequestBody MyShellBatchDownloadUrlDTO dto) {
    if (CollectionUtils.isEmpty(dto.getDatapoints())) {
      return ApiResult.success(List.of());
    }

    var datapointIdDTOMap =
        dto.getDatapoints().toMap(MyShellSubmissionFileDTO::getDatapointId, Function.identity());

    var taskSessionIds =
        dto.getDatapoints().map(MyShellSubmissionFileDTO::getDatapointId).toList();
    var taskSessions = taskSessionService
        .getTaskSessionByIdList(taskSessionIds)
        .filter(s -> !s.getDeleted())
        .toList();
    var fileDTOs = taskSessions.parallelStream()
        .map(ts -> {
          var answer = ts.getAnswer();
          if (StringUtils.isBlank(answer)) {
            return null;
          }
          var answerDetails = ObjectUtils.fromJsonArray(answer, AnswerDetailsDTO.class);
          if (CollectionUtils.isEmpty(answerDetails)) {
            return null;
          }
          var answerOpt = answerDetails
              .map(AnswerDetailsDTO::getAnswer)
              .filter(Objects::nonNull)
              .findFirst();
          if (answerOpt.isEmpty()) {
            return null;
          }
          var answerStr = answerOpt.get();
          if (answerStr.startsWith("\"") && answerStr.endsWith("\"")) {
            answerStr = answerStr.substring(1, answerStr.length() - 1);
          }
          Long resourceId;
          try {
            resourceId = Long.parseLong(answerStr);
          } catch (NumberFormatException ignore) {
            return null;
          }

          var rOpt = resourceService.getResourceById(resourceId).filter(res -> !res.getDeleted());
          if (rOpt.isEmpty()) {
            return null;
          }
          var r = rOpt.get();

          String preSignedUrl = resourceService.tryToGetPreSignedUrl(r, true);
          var fileDTO = datapointIdDTOMap.get(ts.getId());
          return MyShellSubmissionFileDTO.builder()
              .datapointId(ts.getId())
              .submissionId(Objects.isNull(fileDTO) ? null : fileDTO.getSubmissionId())
              .datafileId(resourceId)
              .datafile(Objects.isNull(preSignedUrl) ? List.of() : List.of(preSignedUrl))
              .build();
        })
        .filter(Objects::nonNull)
        .toList();

    return ApiResult.success(fileDTOs);
  }
}
