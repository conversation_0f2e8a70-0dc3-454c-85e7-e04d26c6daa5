package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.leaderboard.LeaderboardUserDTO;
import ai.saharaa.model.PageResult;
import ai.saharaa.services.LeaderboardService;
import ai.saharaa.services.season.SeasonService;
import ai.saharaa.utils.ControllerUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/leaderboard")
@Tag(name = "Leaderboard Service", description = "Leaderboard Service API")
public class LeaderboardController {
  private final LeaderboardService leaderboardService;
  private final SeasonService seasonService;

  public LeaderboardController(LeaderboardService leaderboardService, SeasonService seasonService) {
    this.leaderboardService = leaderboardService;
    this.seasonService = seasonService;
  }

  @GetMapping("/")
  @CheckPageParam()
  @Cacheable(value = "getLeaderboard", cacheManager = "apiCacheManager")
  public ApiResult<PageResult<LeaderboardUserDTO>> getLeaderboard(
      @RequestParam(required = false) Integer seasonId,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    if (limit > 100) limit = 100;

    if (page * limit > 1000) {
      throw ControllerUtils.badRequest("No more data available");
    }

    var season = seasonService.current();
    if (season.isEmpty()) {
      throw ControllerUtils.notFound("No season found");
    }

    return ApiResult.success(
        leaderboardService
            .getLeaderboard(season.get().getId(), page < 1 ? 1 : page, limit)
            .toPageResult());
  }
}
