package ai.saharaa.controllers;

import static ai.saharaa.model.Batch.TaskType.EXAM;
import static ai.saharaa.utils.Constants.ROLE_EXTERNAL_USER;

import ai.saharaa.config.perms.IsLabeler;
import ai.saharaa.daos.QuestionAnswerDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.SimpleDataDTO;
import ai.saharaa.dto.job.SubmitIndividualExamAnswersItemDTO;
import ai.saharaa.dto.task.TaskDetailsDTO;
import ai.saharaa.model.*;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import java.util.List;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/individuals")
public class IndividualsController {

  private final IndividualsService individualsService;

  private final BatchService batchService;
  private final UserService userService;
  private final QuestionAnswerDao questionAnswerDao;

  public IndividualsController(
      UserService userService,
      IndividualsService individualsService,
      BatchService batchService,
      QuestionAnswerDao questionAnswerDao) {
    this.userService = userService;
    this.batchService = batchService;
    this.individualsService = individualsService;
    this.questionAnswerDao = questionAnswerDao;
  }

  @PostMapping("/join/{batchId}/exam")
  public ApiResult<UserRequirementsStatus> joinIndividualExam(@PathVariable Long batchId) {
    batchService
        .getBatchById(batchId)
        .orThrow(() -> ControllerUtils.notFound("batch not found"))
        .filter(b -> b.getTaskType().equals(EXAM))
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));
    var curId = ControllerUtils.currentUid();
    var banned = individualsService.checkUserBannedInPlatform(curId);
    if (banned) {
      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
    }
    userService
        .getUserById(curId)
        .filter(u -> u.getRole() == ROLE_EXTERNAL_USER)
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));
    var checkExists = individualsService.selectExamRequirementByBatchIdAndUserId(batchId, curId);
    var checkApproveExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.APPROVED))
        .findFirst();
    var checkRejExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.REJECTED))
        .findFirst();
    var checkPendingExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.PENDING))
        .findFirst();
    if (checkRejExists.isPresent()) {
      throw ControllerUtils.badRequest("you can not join this exam");
    }
    if (checkApproveExists.isPresent()) {
      throw ControllerUtils.badRequest("already completed this exam");
    }
    if (checkPendingExists.isPresent()) {
      return checkPendingExists.get().toApiResult();
    }
    return individualsService
        .joinIndividualExam(batchId, curId, BatchAccessRequirement.Type.EXAM)
        .toApiResult();
  }

  @GetMapping("/exam/{batchId}/status")
  public ApiResult<Boolean> getExamStatus(@PathVariable Long batchId) {
    batchService
        .getBatchById(batchId)
        .orThrow(() -> ControllerUtils.notFound("batch not found"))
        .filter(b -> b.getTaskType().equals(EXAM))
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));
    var curId = ControllerUtils.currentUid();
    var banned = individualsService.checkUserBannedInPlatform(curId);
    if (banned) {
      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
    }
    userService
        .getUserById(curId)
        .filter(u -> u.getRole() == ROLE_EXTERNAL_USER)
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));
    var checkExists = individualsService.selectExamRequirementByBatchIdAndUserId(batchId, curId);
    var checkPendingExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.PENDING))
        .findFirst();
    if (checkPendingExists.isPresent()) {
      return ApiResult.success(Boolean.TRUE);
    }
    return ApiResult.success(Boolean.FALSE);
  }

  @GetMapping("/banned")
  @IsLabeler
  public ApiResult<Boolean> checkMyselfBannedPermanent() {
    var curId = ControllerUtils.currentUid();
    return ApiResult.success(individualsService.checkBanUserPermanently(curId));
  }

  @PostMapping("/exam/{batchId}/tasks")
  @IsLabeler
  public ApiResult<List<TaskDetailsDTO>> taskExamTasks(@PathVariable Long batchId) {
    var curId = ControllerUtils.currentUid();
    batchService
        .getBatchById(batchId)
        .orThrow(() -> ControllerUtils.notFound("exam not found"))
        .filter(b -> b.getTaskType().equals(EXAM))
        .ensurePresent(() -> ControllerUtils.badRequest("invalid id"));

    var checkExists = individualsService.selectExamRequirementByBatchIdAndUserId(batchId, curId);
    var checkApproveExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.APPROVED))
        .findFirst();
    var checkRejExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.REJECTED))
        .findFirst();
    var checkPendingExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.PENDING))
        .findFirst();
    if (checkApproveExists.isPresent()) {
      throw ControllerUtils.badRequest("no need to take this exam");
    }
    if (checkRejExists.isPresent()) {
      throw ControllerUtils.badRequest("already failed this exam");
    }
    if (checkPendingExists.isEmpty()) {
      throw ControllerUtils.badRequest("need to take this exam first");
    }
    return individualsService.takeExamBatchTask(batchId, curId).toApiResult();
  }

  @PostMapping("/exam/{batchId}/submit-answers")
  @IsLabeler
  public ApiResult<UserRequirementsStatus> submitExamAnswers(
      @PathVariable Long batchId,
      @RequestBody SimpleDataDTO<List<SubmitIndividualExamAnswersItemDTO>> bodies) {
    var curId = ControllerUtils.currentUid();
    var examBatch = batchService
        .getBatchById(batchId)
        .orThrow(() -> ControllerUtils.notFound("exam not found"))
        .filter(b -> b.getTaskType().equals(EXAM))
        .orElseThrow(() -> ControllerUtils.badRequest("invalid id"));

    var checkExists = individualsService.selectExamRequirementByBatchIdAndUserId(batchId, curId);
    var checkApproveExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.APPROVED))
        .findFirst();
    var checkRejExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.REJECTED))
        .findFirst();
    if (checkApproveExists.isPresent()) {
      return checkApproveExists.get().toApiResult();
    }
    if (checkRejExists.isPresent()) {
      return checkRejExists.get().toApiResult();
    }
    return individualsService
        .submitExamAnswers(batchId, curId, bodies.getData(), examBatch.getExamType())
        .toApiResult();
  }

  @GetMapping("/exam/{batchId}/check-answers/{taskId}")
  public ApiResult<List<QuestionAnswer>> getAnswersByTask(
      @PathVariable Long batchId, @PathVariable Long taskId) {
    var curId = ControllerUtils.currentUid();

    var checkExists = individualsService.selectExamRequirementByBatchIdAndUserId(batchId, curId);
    var checkRejExists = checkExists.stream()
        .filter(r -> r.getStatus().equals(UserRequirementsStatus.UserRequireStatus.REJECTED))
        .findFirst();
    if (checkRejExists.isPresent()) {
      throw ControllerUtils.badRequest("already failed this exam");
    }
    return questionAnswerDao.getExampleAnswerByTaskId(taskId).toApiResult();
  }
}
