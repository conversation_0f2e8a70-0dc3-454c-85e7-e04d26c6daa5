package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.daos.season.SeasonUserDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.certificate.UserVaultCertificatesDTO;
import ai.saharaa.dto.leaderboard.LeaderboardUserDTO;
import ai.saharaa.dto.season.SeasonUserDTO;
import ai.saharaa.dto.season.SeasonUserPointsDTO;
import ai.saharaa.dto.user.UserTotalPointsDTO;
import ai.saharaa.dto.vault.SeasonUserPointsDetailDTO;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.certificate.UserCertificate;
import ai.saharaa.model.season.Season;
import ai.saharaa.model.season.SeasonUser;
import ai.saharaa.model.season.UserPointsSeason;
import ai.saharaa.services.LeaderboardService;
import ai.saharaa.services.VaultService;
import ai.saharaa.services.season.SeasonService;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;

@RestController
@RequestMapping("/api/vault")
@Tag(name = "Vault Service", description = "Vault Service API")
@Slf4j
public class VaultController {
  private final VaultService vaultService;
  private final SeasonService seasonService;
  private final LeaderboardService leaderboardService;
  private final SeasonUserDao seasonUserDao;

  public VaultController(
      VaultService vaultService,
      SeasonService seasonService,
      LeaderboardService leaderboardService,
      SeasonUserDao seasonUserDao) {
    this.vaultService = vaultService;
    this.seasonService = seasonService;
    this.leaderboardService = leaderboardService;
    this.seasonUserDao = seasonUserDao;
  }

  @GetMapping("/certificates")
  public ApiResult<UserVaultCertificatesDTO> getUserAllCertificates() {
    Long uid = ControllerUtils.currentUid();
    return this.vaultService.getUserAllCertificates(uid).toApiResult().map((r) ->
        (UserVaultCertificatesDTO) r);
  }

  @GetMapping("/certificates/{id}/users/")
  public ApiResult<UserCertificate> getUserCertificateByUserIdAndCertificateId(
      @PathVariable Long id) {
    Long uid = ControllerUtils.currentUid();
    return this.vaultService.getUserCertificateByUserIdAndCertificateId(uid, id).toApiResult();
  }

  @GetMapping("/certificates/users/")
  public ApiResult<List<UserCertificate>> getUserCertificateByUserId() {
    Long uid = ControllerUtils.currentUid();
    List<UserCertificate> userCertificates = this.vaultService.getUserCertificateByUserId(uid);
    return ApiResult.success(userCertificates);
  }

  @GetMapping("/seasons")
  public ApiResult<List<UserPointsSeason>> getSeasons() {
    Long uid = ControllerUtils.currentUid();

    List<UserPointsSeason> seasons = this.seasonService.getUserPointsAndExpSeasonList(uid);

    var currentSeason = this.seasonService.current();
    if (currentSeason.isPresent()
        && seasons.indexOfFirst((s) -> s.getId().equals(currentSeason.get().getId())) < 0) {
      seasons.add(
          0,
          UserPointsSeason.builder()
              .id(currentSeason.get().getId())
              .name(currentSeason.get().getName())
              .startedAt(currentSeason.get().getStartedAt())
              .endedAt(currentSeason.get().getEndedAt())
              .userId(uid)
              .userPoints(BigDecimal.ZERO)
              .userExp(0L)
              .ruleVersion(currentSeason.get().getRuleVersion())
              .claimed(false)
              .build());
    }

    // for disable claim now button
    seasons.forEach(season -> {
      if (season.getUserPoints().compareTo(BigDecimal.ONE) < 0) {
        season.setClaimed(true);
      }
    });

    return ApiResult.success(seasons);
  }

  @GetMapping("/isOlympicSeasonStarted")
  public ApiResult<Boolean> isOlympicSeasonStarted() {
    var started = this.seasonService.isOlympicSeasonStarted();
    return ApiResult.success(started);
  }

  @GetMapping("/isLastOlympicSeasonStarted")
  public ApiResult<Boolean> isLastOlympicSeasonStarted() {
    var started = this.seasonService.isLastOlympicSeasonStarted();
    return ApiResult.success(started);
  }

  @GetMapping("/points/allSeasons")
  public ApiResult<UserTotalPointsDTO> getUserAllSeasonsPoints() {
    Long uid = ControllerUtils.currentUid();
    var totalPoints = seasonUserDao.totalPointsAllSeason(uid);
    return ApiResult.success(UserTotalPointsDTO.builder()
        .userId(uid)
        .allSeasonsSaharaPoints(totalPoints.setScale(1, RoundingMode.FLOOR))
        .build());
  }

  @GetMapping("/points")
  public ApiResult<SeasonUserDTO> getUserPoints() {
    var seasonCurr = seasonService.current();
    if (seasonCurr.isEmpty()) {
      ControllerUtils.notFound("no season found");
    }

    Long uid = ControllerUtils.currentUid();
    var season = seasonCurr.get();

    LeaderboardUserDTO u = leaderboardService.getUser(season.getId(), uid).orElse(null);
    SeasonUserPointsDTO user = SeasonUserPointsDTO.builder()
        .rank(u.getRank())
        .rankHist(u.getRankHist())
        .dataPoints(u.getDataPoints())
        .delta(u.getDelta())
        .saharaaPoints(u.getSaharaaPoints())
        .saharaaPointsDelta(u.getSaharaaPointsDelta())
        .build();

    return ApiResult.success(SeasonUserDTO.builder().season(season).user(user).build());
  }

  @PostMapping("/points")
  public ApiResult<SeasonUserDTO> postUserPoints() {
    var seasonCurr = seasonService.current();
    if (seasonCurr.isEmpty()) {
      ControllerUtils.notFound("no season found");
    }

    Long uid = ControllerUtils.currentUid();
    var season = seasonCurr.get();

    LeaderboardUserDTO u = leaderboardService.getUser(season.getId(), uid).orElse(null);
    SeasonUserPointsDTO user = SeasonUserPointsDTO.builder()
        .rank(u.getRank())
        .rankHist(u.getRankHist())
        .dataPoints(u.getDataPoints())
        .delta(u.getDelta())
        .saharaaPoints(u.getSaharaaPoints())
        .saharaaPointsDelta(u.getSaharaaPointsDelta())
        .build();

    return ApiResult.success(SeasonUserDTO.builder().season(season).user(user).build());
  }

  @GetMapping("/points/details")
  @CheckPageParam()
  public ApiResult<PageResult<SeasonUserPointsDetailDTO>> getUserPointsDetails(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {

    var season = seasonService.current();
    if (season.isEmpty()) {
      ControllerUtils.notFound("no season found");
    }
    return getUserPointsDetailsBySeason(season.get().getId(), page, limit);
  }

  @GetMapping("/season/{sid}/points")
  @Cacheable(value = "getUserPoints", condition = "false")
  public ApiResult<SeasonUserDTO> getUserPointsBySeason(@PathVariable Long sid) {
    Long uid = ControllerUtils.currentUid();

    Season season = seasonService
        .getById(sid)
        .orElseThrow(() -> new HttpClientErrorException(HttpStatus.NOT_FOUND, "season not found"));

    LeaderboardUserDTO u = leaderboardService.getUser(season.getId(), uid).orElse(null);
    SeasonUserPointsDTO.SeasonUserPointsDTOBuilder userBuilder = SeasonUserPointsDTO.builder();
    var su = seasonService.getUser(season.getId(), uid);
    if (u != null) {
      var currSeason = seasonService.current();
      if (currSeason.isPresent() && currSeason.get().getId().equals(season.getId())) {
        userBuilder
            .rank(u.getRank())
            .rankHist(u.getRankHist())
            .dataPoints(u.getDataPoints().setScale(1, RoundingMode.FLOOR))
            .delta(u.getDelta())
            .saharaaPoints(su.map(SeasonUser::getTotalPoints)
                .orElse(BigDecimal.ZERO)
                .setScale(1, RoundingMode.FLOOR))
            .exp(u.getExp())
            .levelUpValue(u.getLevelUpValue())
            .saharaaPointsDelta(u.getSaharaaPointsDelta())
            .seasonalRewards(u.getSeasonalRewards());
      } else {
        userBuilder
            .rank(u.getRank())
            .levelUpValue(u.getLevelUpValue())
            .saharaaPoints(su.map(SeasonUser::getTotalPoints)
                .orElse(BigDecimal.ZERO)
                .setScale(1, RoundingMode.FLOOR));
      }
    }
    SeasonUserPointsDTO user = userBuilder.build();
    SeasonUserDTO seasonUserDTO =
        SeasonUserDTO.builder().season(season).user(user).build();
    return ApiResult.success(seasonUserDTO);
  }

  @GetMapping("/season/{sid}/points/details")
  @CheckPageParam()
  public ApiResult<PageResult<SeasonUserPointsDetailDTO>> getUserPointsDetailsBySeason(
      @PathVariable Long sid,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    Long uid = ControllerUtils.currentUid();

    Season season =
        seasonService.getById(sid).orElseThrow(() -> ControllerUtils.notFound("season not found"));

    return seasonService
        .getUserPointsDetailList(season.getId(), uid, Page.of(page, limit))
        .toPageResult()
        .toApiResult()
        .map((r) -> (PageResult<SeasonUserPointsDetailDTO>) r);
  }

  @PostMapping("/season/{sid}/claim")
  public ApiResult<Boolean> claimSeasonUser(@PathVariable Long sid) {
    var uid = ControllerUtils.currentUid();
    var now = Instant.now().atZone(ZoneId.of("UTC")).toLocalDateTime();
    var season = seasonService
        .getById(sid)
        .orThrow(() -> new HttpClientErrorException(HttpStatus.NOT_FOUND, "season not found"))
        .filter(s -> now.isAfter(s.getEndedAt().toLocalDateTime()))
        .orElseThrow(
            () -> new HttpClientErrorException(HttpStatus.BAD_REQUEST, "season is not ended"));

    var seasonUser = seasonUserDao
        .getUser(season.getId(), uid)
        .orElseThrow(
            () -> new HttpClientErrorException(HttpStatus.NOT_FOUND, "season user not found"));
    if (seasonUser.getClaimed()) {
      return ApiResult.success(true);
    }

    seasonUserDao.claim(season.getId(), uid);
    return ApiResult.success(true);
  }
}
