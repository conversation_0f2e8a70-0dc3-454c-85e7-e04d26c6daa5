package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.api.ApiKeysDTO;
import ai.saharaa.model.api.ApiKeys;
import ai.saharaa.services.api.ApiKeysService;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping()
public class ApiKeysController {

  private final ApiKeysService apiKeysService;

  public ApiKeysController(ApiKeysService apiKeysService) {
    this.apiKeysService = apiKeysService;
  }

  @PostMapping("/v1/api-keys")
  @IsAdmin
  public ApiResult<ApiKeys> create(@Valid @RequestBody ApiKeysDTO payload) {
    ApiKeys apiKeys = new ApiKeys();
    BeanUtils.copyProperties(payload, apiKeys);
    ApiKeys res = apiKeysService.create(apiKeys);
    return ApiResult.success(res);
  }

  @PutMapping("/v1/api-keys/{id}")
  @IsAdmin
  public ApiResult<ApiKeys> update(@PathVariable Long id, @RequestBody ApiKeysDTO payload) {
    var apiKeys = apiKeysService.getById(id);
    if (Objects.isNull(apiKeys)) {
      throw ControllerUtils.forbidden("api key ${id} not found");
    }
    if (Objects.nonNull(payload.getAppName())) {
      apiKeys.setAppName(payload.getAppName());
    }
    if (Objects.nonNull(payload.getAppId())) {
      apiKeys.setAppId(payload.getAppId());
    }
    if (Objects.nonNull(payload.getType())) {
      apiKeys.setType(payload.getType());
    }
    if (Objects.nonNull(payload.getExpiresAt())) {
      apiKeys.setExpiresAt(payload.getExpiresAt());
    }
    if (Objects.nonNull(payload.getSignatureRequired())) {
      apiKeys.setSignatureRequired(payload.getSignatureRequired());
    }
    apiKeysService.update(apiKeys);
    return ApiResult.success(apiKeysService.getById(id));
  }

  @DeleteMapping("/v1/api-keys/{id}")
  @IsAdmin
  public void delete(@PathVariable Long id) {
    apiKeysService.deleteById(id);
  }

  @GetMapping("/v1/api-keys/{id}")
  @IsAdmin
  public ApiResult<ApiKeys> get(@PathVariable Long id) {
    return ApiResult.success(apiKeysService.getById(id));
  }

  @GetMapping("/v1/api-keys")
  @CheckPageParam()
  @IsAdmin
  public ApiResult<IPage<ApiKeys>> pages(
      @RequestParam(required = false) String name,
      @RequestParam(required = false) ApiKeys.Type type,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    return ApiResult.success(apiKeysService.pages(name, type, page, size));
  }
}
