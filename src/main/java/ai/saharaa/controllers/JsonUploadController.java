package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.daos.TaskListDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.TaskList;
import ai.saharaa.model.hybridTask.*;
import ai.saharaa.services.JsonUploadService;
import ai.saharaa.services.TaskService;
import ai.saharaa.utils.ControllerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/jsonUpload")
@Slf4j
public class JsonUploadController {

  private final JsonUploadService jsonUploadService;
  private final TaskService taskService;
  private final TaskListDao taskListDao;

  public JsonUploadController(
      JsonUploadService jsonUploadService, TaskService taskService, TaskListDao taskListDao) {
    this.jsonUploadService = jsonUploadService;
    this.taskListDao = taskListDao;
    this.taskService = taskService;
  }

  @PostMapping("/onJsonUploaded")
  public ApiResult<UploadJsonInfoDTO> onJsonUploaded(@RequestBody JsonFileUploadedDTO body) {
    return jsonUploadService.onJsonUploaded(body).toApiResult();
  }

  @DeleteMapping("/delete")
  public ApiResult<Boolean> deleteJsonById(
      @RequestParam(required = false) Long jsonId, @RequestParam(required = false) Long batchId) {
    var jsonObj = jsonUploadService
        .getUploadJsonRecBy2Id(batchId, jsonId)
        .orElseThrow(() -> ControllerUtils.notFound("Invalid data"));
    var taskList = taskListDao
        .listTaskListsByBatchIdAndType(jsonObj.getBatchId(), TaskList.TaskListType.ANNOTATION)
        .first()
        .asOpt()
        .orElseThrow(() -> ControllerUtils.notFound("Invalid data"));
    ;
    taskService.deleteTaskByTaskListId(taskList.getId());
    return jsonUploadService.deleteJsonByBatchId(batchId, jsonId).toApiResult();
  }

  @GetMapping("/getJsonUploaded")
  public ApiResult<UploadJsonInfoDTO> getJsonUploaded(
      @RequestParam(required = false) Long jsonId, @RequestParam(required = false) Long batchId) {
    return jsonUploadService.getJsonUploaded(jsonId, batchId).toApiResult();
  }

  @GetMapping("/{id}/getSyncTaskList")
  @CheckPageParam()
  public ApiResult<PageResult<HybridItemTask>> getSyncTaskList(
      @PathVariable Long id,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(required = false, defaultValue = "-1") Integer status,
      @RequestParam(defaultValue = "10") Integer limit) {
    return jsonUploadService
        .getSyncTaskList(id, page, limit, status)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/onZipUploaded")
  public ApiResult<ZipFile> onZipUploaded(@RequestBody ZipFileUploadedDTO body) {
    return jsonUploadService.onZipUploaded(body).toApiResult();
  }
}
