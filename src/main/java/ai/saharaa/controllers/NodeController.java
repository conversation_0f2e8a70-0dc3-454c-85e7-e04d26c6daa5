package ai.saharaa.controllers;

import static ai.saharaa.config.AuditLogInterceptor.REQUEST_USER_AGENT;

import ai.saharaa.config.AuditLogInterceptor;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.CreateNodeDTO;
import ai.saharaa.dto.invitation.InvitationDetailDTO;
import ai.saharaa.dto.job.NodeDetailsDTO;
import ai.saharaa.dto.node.InvitationUpdateDTO;
import ai.saharaa.dto.node.NodeStatDTO;
import ai.saharaa.dto.user.UserDTO;
import ai.saharaa.dto.user.UserDetailsDTO;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.Node;
import ai.saharaa.model.NodeInvitation;
import ai.saharaa.model.NodeInvitationCode;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.UserSetting;
import ai.saharaa.services.JobService;
import ai.saharaa.services.NodeInvitationService;
import ai.saharaa.services.NodeService;
import ai.saharaa.services.NotificationService;
import ai.saharaa.services.UserSettingService;
import ai.saharaa.services.stat.NodeStatService;
import ai.saharaa.services.user.TokenValidationService;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@RestController
@RequestMapping("/api/nodes")
public class NodeController {
  private final NodeService nodeService;
  private final NodeInvitationService nodeInvitationService;

  private final UserSettingService userSettingService;
  private final NodeStatService nodeStatService;
  private final JobService jobService;
  private final NotificationService notificationService;

  private final TokenValidationService tokenValidationService;

  public NodeController(
      NodeService nodeService,
      NodeInvitationService nodeInvitationService,
      UserSettingService userSettingService,
      NodeStatService nodeStatService,
      JobService jobService,
      NotificationService notificationService,
      TokenValidationService tokenValidationService) {
    this.nodeService = nodeService;
    this.nodeInvitationService = nodeInvitationService;
    this.userSettingService = userSettingService;
    this.nodeStatService = nodeStatService;
    this.jobService = jobService;
    this.notificationService = notificationService;
    this.tokenValidationService = tokenValidationService;
  }

  @PostMapping
  public ApiResult<Node> createNode(@Valid @RequestBody CreateNodeDTO createNodeDTO) {
    if (!ControllerUtils.isAdmin() && !ControllerUtils.isAccountManager()) {
      throw ControllerUtils.forbidden("no permission");
    }
    if (createNodeDTO.getNodeManagerId() != null) {
      tokenValidationService
          .getUserById(createNodeDTO.getNodeManagerId())
          .orThrow(() -> ControllerUtils.badRequest("user not found"))
          .filter(user -> user.getRole().equals(Constants.ROLE_NODE_MANAGER))
          .orThrow(() -> ControllerUtils.badRequest("user is not a node manager"))
          .map(user -> nodeService.getNodeByNodeManager(user.getId()))
          .filter(Optional::isEmpty)
          .ensurePresent(() -> ControllerUtils.badRequest("user already have a node"));
    }
    var node = Node.builder()
        .name(createNodeDTO.getName())
        .region(createNodeDTO.getRegion())
        .nodeManagerId(createNodeDTO.getNodeManagerId())
        .country(createNodeDTO.getCountry())
        .languages(createNodeDTO.getLanguages())
        .build();
    return nodeService.createNode(node).toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<Node> getNode(@PathVariable Long id) {
    return nodeService
        .getNodeById(id)
        .filter(node -> !node.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("node not found"))
        .toApiResult();
  }

  @GetMapping("/list")
  @CheckPageParam()
  public ApiResult<PageResult<Node>> getNode(
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var pageInfo = ControllerUtils.safePageRange(page, size);
    var nodes = nodeService.getNodes(pageInfo.getFirst(), pageInfo.getSecond());
    return nodes.toPageResult().toApiResult();
  }

  @DeleteMapping("/{id}")
  @IsAdmin
  public void deleteNode(@PathVariable Long id) {
    nodeService
        .getNodeById(id)
        .filter(node -> !node.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("node not found"));
    nodeService.deleteNodeById(id);
  }

  @GetMapping("/team/for-node-manager")
  @CheckPageParam()
  public ApiResult<PageResult<UserDetailsDTO>> getNodeMemberForManager(
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false) String userName,
      @RequestParam(required = false) Integer page,
      @RequestParam(required = false) Integer size,
      @RequestParam(required = false) Integer limit) {
    if (nodeId == null) {
      var curId = ControllerUtils.currentUid();
      nodeId = nodeService
          .getNodeByNodeManager(curId)
          .orElseThrow(() -> ControllerUtils.notFound("node not found"))
          .getId();
    }
    return getNodeMemberList(nodeId, userName, page, size, limit);
  }

  @GetMapping("/team/for-node-member")
  @CheckPageParam()
  public ApiResult<PageResult<UserDetailsDTO>> getNodeMemberForMember(
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false) String userName,
      @RequestParam(required = false) Integer page,
      @RequestParam(required = false) Integer size,
      @RequestParam(required = false) Integer limit) {
    if (nodeId == null && ControllerUtils.isRequester()) {
      var curId = ControllerUtils.currentUid();
      nodeId = tokenValidationService
          .getUserById(curId)
          .map(UserDTO::getNodeId)
          .orElseThrow(() -> ControllerUtils.notFound("no node joined"));
      nodeService.getNodeById(nodeId).ensurePresent(() -> ControllerUtils.notFound("wrong node"));
    }
    return getNodeMemberList(nodeId, userName, page, size, limit);
  }

  @CheckPageParam()
  private ApiResult<PageResult<UserDetailsDTO>> getNodeMemberList(
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false) String userName,
      @RequestParam(required = false) Integer page,
      @RequestParam(required = false) Integer size,
      @RequestParam(required = false) Integer limit) {
    IPage<UserDTO> userPageRes =
        tokenValidationService.getNodeMember(nodeId, userName, page, size, limit);

    return PageResult.<UserDetailsDTO>builder()
        .pages(userPageRes.getPages())
        .size(userPageRes.getSize())
        .current(userPageRes.getCurrent())
        .total(userPageRes.getTotal())
        .data(userPageRes
            .getRecords()
            .map(x -> {
              List<UserSetting> userSettings =
                  this.userSettingService.getUserSettingByUserId(x.getId());
              return UserDetailsDTO.builder().userSettings(userSettings).user(x).build();
            })
            .collect(Collectors.toList()))
        .build()
        .toApiResult();
  }

  @PostMapping("/team/invite/token")
  public ApiResult<NodeInvitationCode> getNodeInvitationCode() {
    var curId = ControllerUtils.currentUid();
    var nodeId = nodeService
        .getNodeByNodeManager(curId)
        .orElseThrow(() -> ControllerUtils.notFound("no permission"))
        .getId();
    return ApiResult.success(nodeInvitationService.getNodeInvitationCode(curId, nodeId));
  }

  @GetMapping("/detail/by-invite-code")
  public ApiResult<Node> getNodeByInviteCode(@RequestParam("inviteCode") String id) {
    Long nodeId = nodeInvitationService.getNodeIdByInvitationCode(id);
    return nodeService
        .getNodeById(nodeId)
        .filter(n -> !n.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("node not found"))
        .toApiResult();
  }

  @GetMapping("/node-info/by-invite-code")
  public ApiResult<NodeDetailsDTO> getNodeDetailsByInviteCode(
      @RequestParam("inviteCode") String id) {
    Long nodeId = nodeInvitationService.getNodeIdByInvitationCode(id);
    var node = nodeService
        .getNodeById(nodeId)
        .filter(n -> !n.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("node not found"));

    var details = NodeDetailsDTO.builder()
        .node(node)
        .user(tokenValidationService.getUserById(node.getNodeManagerId()).orElse(null))
        .build();
    return details.toApiResult();
  }

  @GetMapping("/member/invitations")
  @CheckPageParam()
  public ApiResult<PageResult<InvitationDetailDTO>> getNodeInvitations(
      @RequestParam(required = false) Long nodeId,
      @RequestParam(required = false) Integer page,
      @RequestParam(required = false) Integer size,
      @RequestParam(required = false) Integer limit,
      @RequestParam(required = false) String type) {
    if (nodeId == null) {
      var curId = ControllerUtils.currentUid();
      nodeId = nodeService
          .getNodeByNodeManager(curId)
          .orElseThrow(() -> ControllerUtils.notFound("invalid params"))
          .getId();
    }
    IPage<InvitationDetailDTO> res =
        nodeInvitationService.getNodeInvitationByNodeId(nodeId, type, page, size, limit);
    return res.toPageResult().toApiResult();
  }

  @PostMapping("/member/invitation")
  public ApiResult<NodeInvitation> updateNodeInvitation(@RequestBody InvitationUpdateDTO dto) {

    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ua = (AuditLogInterceptor.RequestUserAgent) request.getAttribute(REQUEST_USER_AGENT);

    NodeInvitation res = nodeInvitationService.updateNodeInvitationStatusById(dto, ua);
    if (res.getStatus() == NodeInvitation.NodeInvitationStatus.ACCEPTED) {
      notificationService.noticeUserNodeJoinRequestAccepted(res.getId());
    }
    return res.toApiResult();
  }

  @GetMapping("/profile")
  public ApiResult<Node> getMyNode() {
    var curId = ControllerUtils.currentUid();
    var user = tokenValidationService
        .getUserById(curId)
        .orElseThrow(() -> ControllerUtils.notFound("invalid data"));
    if (user.getRole() == Constants.ROLE_USER) {
      return ApiResult.success(tokenValidationService
          .getUserById(curId)
          .orThrow(() -> ControllerUtils.notFound("invalid data"))
          .filter(u -> u.getNodeId() != null)
          .orThrow(() -> ControllerUtils.notFound("no node joined"))
          .map(u -> nodeService.getNodeById(u.getNodeId()))
          .filter(Optional::isPresent)
          .orElseThrow(() -> ControllerUtils.notFound("no node joined"))
          .get());
    }
    return ApiResult.success(nodeService
        .getNodeByNodeManager(curId)
        .orElseThrow(() -> ControllerUtils.notFound("node not found")));
  }

  @GetMapping("/{id}/users")
  public ApiResult<List<JobUser>> getNodeUsers() {
    return ApiResult.success(List.of());
  }

  @GetMapping("/{id}/stat")
  public ApiResult<NodeStatDTO> getNodeStat(@PathVariable Long id) {
    nodeService
        .getNodeById(id)
        .filter(n -> !n.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("node not found"));
    // .filter(n -> n.getNodeManagerId().equals(ControllerUtils.currentUid()) ||
    // ControllerUtils.isAdmin())
    // .orElseThrow(() -> ControllerUtils.forbidden("node not found"));

    return NodeStatDTO.builder()
        .userCount(tokenValidationService.getUserCountByNodeId(id))
        .submissionCount(100L)
        .accuracy(0.9)
        .averageSpeed(100.0)
        .stat(nodeStatService.getStatByNode(id))
        .build()
        .toApiResult();
  }

  @GetMapping("/for-queue-manager")
  // @IsQueueManager
  @CheckPageParam()
  public ApiResult<PageResult<NodeDetailsDTO>> nodes(
      @RequestParam(required = false) Long batchId,
      @RequestParam(required = false) String filter,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {

    return this.jobService.nodes(batchId, filter, page, size).toApiResult();
  }
}
