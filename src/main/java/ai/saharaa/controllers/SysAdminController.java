package ai.saharaa.controllers;

import static ai.saharaa.utils.DateUtils.fixTimeToLastMidNight;
import static ai.saharaa.utils.DateUtils.fixTimeToYesterdayMidNight;

import ai.saharaa.actors.jobs.IndividualJobUserPointsActor;
import ai.saharaa.actors.jobs.JobStatActor;
import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.config.RateLimiterInterceptor;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.daos.SystemSettingDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.OlympicTimeDTO;
import ai.saharaa.dto.RoleDTO;
import ai.saharaa.dto.job.*;
import ai.saharaa.dto.season.CreateSeasonDTO;
import ai.saharaa.dto.season.UpdateSeasonDTO;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.Role;
import ai.saharaa.model.season.Season;
import ai.saharaa.services.ResourceService;
import ai.saharaa.services.RoleService;
import ai.saharaa.services.achievement.AchievementService;
import ai.saharaa.services.season.SeasonService;
import ai.saharaa.services.stat.JobStatStatusService;
import ai.saharaa.services.stat.UserJobStatService;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import akka.actor.ActorRef;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/system-admins")
public class SysAdminController {
  private final UserJobStatService userJobStatService;
  private final JobStatStatusService jobStatStatusService;
  private final SeasonService seasonService;
  private final RoleService roleService;
  private final String OLYMPIC_SYSTEM_SETTING_NAME = "olympic_season";
  private final SystemSettingDao systemSettingDao;
  private final ClusterConfiguration clusterConfiguration;
  private final ResourceService resourceService;
  private final AchievementService achievementService;
  private final IGlobalCache cache;
  private final RedisTemplate redisTemplate;

  public SysAdminController(
      UserJobStatService userJobStatService,
      JobStatStatusService jobStatStatusService,
      ClusterConfiguration clusterConfiguration,
      ResourceService resourceService,
      SeasonService seasonService,
      RoleService roleService,
      SystemSettingDao systemSettingDao,
      AchievementService achievementService,
      IGlobalCache cache,
      @Qualifier("redisTemplateForRateLimiter") RedisTemplate redisTemplate) {
    this.userJobStatService = userJobStatService;
    this.jobStatStatusService = jobStatStatusService;
    this.seasonService = seasonService;
    this.systemSettingDao = systemSettingDao;
    this.roleService = roleService;
    this.clusterConfiguration = clusterConfiguration;
    this.resourceService = resourceService;
    this.achievementService = achievementService;
    this.cache = cache;
    this.redisTemplate = redisTemplate;
  }

  @GetMapping("/seasons/{seasonId}/points-finalized")
  @IsAdmin
  public ApiResult<Boolean> isSeasonPointsFinalized(@PathVariable Long seasonId) {
    var season = seasonService.closeSeason(seasonId);
    if (season.isEmpty()) {
      ControllerUtils.notFound("season not found");
    }

    return ApiResult.success(seasonService.isLeaderboardRankPointsDistributionCompleted(seasonId));
  }

  @PostMapping("/seasons/{seasonId}/close")
  @IsAdmin
  public void closeSeason(@PathVariable Long seasonId) {
    var season = seasonService.closeSeason(seasonId);
    if (season.isEmpty()) {
      ControllerUtils.notFound("season not found");
    }
    seasonService.noticeUserNeedToClaim();
  }

  @PostMapping("/seasons/{seasonId}/closeOlympic")
  @IsAdmin
  public void closeSeasonOlympic(@PathVariable Long seasonId) {
    var season = seasonService.closeSeasonOlympic(seasonId);
    if (season.isEmpty()) {
      ControllerUtils.notFound("season not found");
    }
    seasonService.noticeUserNeedToClaim();
  }

  @PostMapping("/setOlympicTime")
  @IsAdmin
  public ApiResult<String> setOlympicTime(@RequestBody OlympicTimeDTO olympicTimeDTO) {
    systemSettingDao.updateFlagByNameAndConfig(
        OLYMPIC_SYSTEM_SETTING_NAME,
        "endTime",
        String.valueOf(olympicTimeDTO.getStartAt().toInstant().toEpochMilli()));

    return ApiResult.success("success");
  }

  @GetMapping("/olympic/time")
  public ApiResult<String> getOlympicEndAt() {
    var endTime =
        systemSettingDao.getSystemSettingsByNameAndConfig(OLYMPIC_SYSTEM_SETTING_NAME, "endTime");

    return ApiResult.success(endTime.getFlag());
  }

  @PostMapping("/testNotice/seasonClose")
  @IsAdmin
  public void testNoticeSeasonClose() {
    seasonService.noticeUserNeedToClaim();
  }

  @PostMapping("/seasons")
  @IsAdmin
  public ApiResult<Season> createSeason(@RequestBody CreateSeasonDTO newSeason) {
    var created = seasonService.createSeason(newSeason);
    return ApiResult.success(created);
  }

  @GetMapping("/seasons")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<Season>> getSeasons(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit) {
    var seasons = seasonService.getSeasons(page, limit);
    return ApiResult.success(seasons);
  }

  @PutMapping("/seasons/{seasonId}")
  @IsAdmin
  public ApiResult<Season> updateSeason(
      @PathVariable Long seasonId, @RequestBody UpdateSeasonDTO updateSeason) {
    var updated = seasonService.updateSeason(seasonId, updateSeason);
    if (updated.isEmpty()) {
      ControllerUtils.notFound("season not found");
    }
    return ApiResult.success(updated.get());
  }

  @PostMapping("/seasons/{seasonId}/start")
  @IsAdmin
  public ApiResult<Season> startSeason(@PathVariable Long seasonId) {
    Optional<Season> season = seasonService.startSeason(seasonId);
    if (season.isEmpty()) {
      throw ControllerUtils.notFound("season not found");
    }

    achievementService.clearBaseInfoCache();
    return ApiResult.success(season.get());
  }

  @PostMapping("/reset-job-stat")
  @IsAdmin
  public void resetAllJobStat() {
    userJobStatService.resetAllUserJobStat();
    jobStatStatusService.resetAllJobStat();
    clusterConfiguration
        .getJobStatActor()
        .tell(new JobStatActor.UpdatePendingJobStat(), ActorRef.noSender());
  }

  @PostMapping("/{id}/reset-job-stat")
  @IsAdmin
  @Transactional
  public void resetAllJobStat(@PathVariable Long id) {
    userJobStatService.preCheckJobExist(id);
    userJobStatService.resetOneJobStat(id);
    jobStatStatusService.resetOneJobStat(id);
    clusterConfiguration
        .getJobStatActor()
        .tell(new JobStatActor.UpdatePendingJobStat(), ActorRef.noSender());
  }

  @PostMapping("/role/create")
  @IsAdmin
  public ApiResult<Role> createRole(@RequestBody RoleDTO roleDTO) {
    return roleService.createRole(roleDTO).toApiResult();
  }

  @DeleteMapping("/cache")
  @IsAdmin
  public ApiResult<Boolean> delCache(@RequestParam List<String> keys) {
    if (!CollectionUtils.isEmpty(keys)) {
      cache.del(keys.toArray(String[]::new));
    }
    return ApiResult.success(true);
  }

  @GetMapping("/retryPointDis")
  @IsAdmin
  public ApiResult<Boolean> retryPointDis(@RequestParam Long jobId) {
    this.clusterConfiguration
        .getIndividualJobUserPointsActor()
        .tell(
            new IndividualJobUserPointsActor.SettlePreCalcByJobId(jobId, Set.of()),
            ActorRef.noSender());
    return ApiResult.success(true);
  }

  //  @PostMapping("/checkJobPoints/{id}/miss/check")
  //  @IsAdmin
  //  public ApiResult<List<JobPointsDiffDTO>> getCheckJobMissPoints(
  //      @PathVariable Long id,
  //      @RequestParam String userIds,
  //      @RequestParam String startAt,
  //      @RequestParam String endAt,
  //      @RequestParam String role) {
  //    return jobPointsMakeUpService
  //        .getCheckJobMissPoints(id, userIds, role, startAt, endAt)
  //        .toApiResult();
  //  }
  //  @PostMapping("/checkJobPoints/{id}/miss/update")
  //  @IsAdmin
  //  public ApiResult<IPage<JobUserPointsPreCalc>> checkJobMissPoints(
  //      @PathVariable Long id,
  //      @RequestParam String userIds,
  //      @RequestParam String startAt,
  //      @RequestParam String endAt,
  //      @RequestParam String role,
  //      @RequestParam(required = false, defaultValue = "true") Boolean preview) {
  //    var res = jobPointsMakeUpService.getCheckJobMissPoints(id, userIds, role, startAt, endAt);
  //    return jobPointsMakeUpService.updateCheckJobMissPoints(res, preview).toApiResult();
  //  }
  //  @PostMapping("/checkJobPoints/{id}/revert")
  //  @IsAdmin
  //  public ApiResult<List<JobSeasonJobMakeUpDTO>> revertCheckJobOverPoints(
  //      @PathVariable Long id,
  //      @RequestParam Long seasonId,
  //      @RequestParam String endAt,
  //      @RequestParam String userIds,
  //      @RequestParam(required = false, defaultValue = "true") Boolean preview) {
  //    return jobPointsMakeUpService
  //        .revertCheckJobOverPoints(id, seasonId, userIds, endAt, preview)
  //        .toApiResult();
  //  }
  //  @PostMapping("/checkJobPoints/{id}/divide")
  //  @IsAdmin
  //  public ApiResult<List<JobSeasonJobDivideDTO>> pointsDivide(
  //      @PathVariable Long id,
  //      @RequestParam Long seasonId,
  //      @RequestParam String startAt,
  //      @RequestParam String endAt,
  //      @RequestParam String afterTime,
  //      @RequestParam Long labelPrice,
  //      @RequestParam Long reviewPrice,
  //      @RequestParam String userIds,
  //      @RequestParam(required = false, defaultValue = "true") Boolean preview) {
  //    return jobPointsMakeUpService
  //        .pointsDivide(
  //            id, seasonId, userIds, startAt, endAt, afterTime, labelPrice, reviewPrice, preview)
  //        .toApiResult();
  //  }
  //  @PostMapping("/checkJobPoints/{id}/rollback")
  //  @IsAdmin
  //  public ApiResult<List<UserSeasonPointsRollBackWithTimeRangeDTO>> rollBackPoints(
  //      @PathVariable Long id,
  //      @RequestParam Long seasonId,
  //      @RequestParam String startAt,
  //      @RequestParam String endAt,
  //      @RequestParam String userIds,
  //      @RequestParam Long sessionLimitForL,
  //      @RequestParam Long sessionLimitForR,
  //      @RequestParam Long labelPrice,
  //      @RequestParam Long reviewPrice,
  //      @RequestParam(required = false, defaultValue = "true") Boolean preview) {
  //    return jobPointsMakeUpService
  //        .rollBackPoints(
  //            id,
  //            seasonId,
  //            userIds,
  //            startAt,
  //            endAt,
  //            sessionLimitForL,
  //            sessionLimitForR,
  //            labelPrice,
  //            reviewPrice,
  //            preview)
  //        .toApiResult();
  //  }
  //  @PostMapping("/checkJobPoints/{id}/removeDuplicatedSeasonDetail")
  //  @IsAdmin
  //  public ApiResult<List<JobSeasonJobMakeUp2DTO>> removeDuplicatedSeasonDetail(
  //      @PathVariable Long id,
  //      @RequestParam Long seasonId,
  //      @RequestParam String startAt,
  //      @RequestParam String endAt,
  //      @RequestParam String userIds,
  //      @RequestParam(required = false, defaultValue = "20") String limit,
  //      @RequestParam(required = false, defaultValue = "true") Boolean preview) {
  //    return jobPointsMakeUpService
  //        .removeDuplicatedSeasonDetail(id, seasonId, userIds, startAt, endAt, limit, preview)
  //        .toApiResult();
  //  }

  @GetMapping("/testTime")
  @IsAdmin
  public ApiResult<String> testTime(@RequestParam String time) {
    var standardTime = fixTimeToLastMidNight(Timestamp.valueOf(time).getTime());
    return standardTime.toString().toApiResult();
  }

  @PostMapping("/rateLimit")
  @IsAdmin
  public ApiResult<String> rateLimit(@RequestParam Long rateLimit) {
    if (Objects.isNull(rateLimit) || rateLimit <= 0) {
      return ApiResult.fail("Rate limit must be greater than 0");
    }

    try {
      this.redisTemplate.opsForValue().set(RateLimiterInterceptor.MAX_REQUESTS_KEY, rateLimit);
      return ApiResult.success("success");
    } catch (Exception e) {
      return ApiResult.fail("Failed to set rate limit");
    }
  }

  @PostMapping("/whitelistItems")
  @IsAdmin
  public ApiResult<String> WhitelistItems(@RequestParam String ip) {
    try {
      this.redisTemplate
          .opsForList()
          .rightPush(RateLimiterInterceptor.WHITELIST_LIST_CACHE_KEY, ip);

      String itemKey = RateLimiterInterceptor.WHITELIST_ITEM_CACHE_KEY + ip;
      this.redisTemplate.opsForValue().set(itemKey, ip);
      this.redisTemplate.expire(itemKey, 3, TimeUnit.HOURS);
    } catch (Exception e) {
      return ApiResult.fail("Failed to whitelist IP: " + ip);
    }

    return ApiResult.success("success");
  }

  @PostMapping("/rateLimit/switch")
  @IsAdmin
  public ApiResult<String> rateLimitSwitch(@RequestParam Boolean rateLimitSwitch) {
    try {
      this.redisTemplate
          .opsForValue()
          .set(RateLimiterInterceptor.RATE_LIMIT_SWITCH, rateLimitSwitch);
      return ApiResult.success("success");
    } catch (Exception e) {
      return ApiResult.fail("Failed to set rate limit");
    }
  }

  @PostMapping("/points/scheduleTriggerAgain")
  @IsAdmin
  public ApiResult<String> scheduleTriggerAgain(
      @RequestParam(required = false, defaultValue = "true") Boolean preview) {
    var standardTime = fixTimeToYesterdayMidNight(DateUtils.now().getTime());
    if (preview) {
      return standardTime.toString().toApiResult();
    }
    this.clusterConfiguration
        .getIndividualJobUserPointsActor()
        .tell(new IndividualJobUserPointsActor.SettleAllPreCalc(standardTime), ActorRef.noSender());
    return "triggered".toApiResult();
  }

  @GetMapping("/syncDirToS3")
  @IsAdmin
  public ApiResult<String> syncDirToS3() {
    var adminId = ControllerUtils.currentUid();
    Thread thread = new Thread(() -> resourceService.syncDirToS3(adminId));
    thread.start();
    return ApiResult.success("triggered");
  }
}
