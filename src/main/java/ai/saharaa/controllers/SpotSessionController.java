package ai.saharaa.controllers;

import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.job.SubmitAnswerDTO;
import ai.saharaa.model.SpotSession;
import ai.saharaa.services.SpotSessionService;
import ai.saharaa.services.TaskSessionService;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import ai.saharaa.utils.ObjectUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/spot-sessions")
@Tag(name = "Spot Session Service")
public class SpotSessionController {
  private final SpotSessionService spotSessionService;
  private final TaskSessionService taskSessionService;

  public SpotSessionController(
      SpotSessionService spotSessionService, TaskSessionService taskSessionService) {
    this.spotSessionService = spotSessionService;
    this.taskSessionService = taskSessionService;
  }

  @PostMapping("/{id}/approve")
  public ApiResult<SpotSession> approveSubmission(@PathVariable Long id) {
    var session = spotSessionService
        .getSpotSessionById(id)
        .filter(s -> !s.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Review session not found"))
        .filter(s -> s.getUserId().equals(ControllerUtils.currentUid()))
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not allowed to approve this submission"));
    // TODO: move to an actor?
    var taskSession = taskSessionService
        .getTaskSessionById(session.getTaskSessionId())
        .orElseThrow(() -> ControllerUtils.notFound("Task session not found"));
    var duration = DateUtils.diff(session.getCreatedAt(), DateUtils.now());
    return spotSessionService.approveSubmission(session, taskSession, duration).toApiResult();
  }

  @PostMapping("/{id}/submit-revision")
  public ApiResult<SpotSession> submitRevision(
      @PathVariable Long id, @Valid @RequestBody SubmitAnswerDTO answer) {
    var session = spotSessionService
        .getSpotSessionById(id)
        .filter(s -> !s.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Review session not found"))
        .filter(s -> s.getUserId().equals(ControllerUtils.currentUid()))
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not allowed to approve this submission"));
    // TODO: move to an actor?
    var taskSession = taskSessionService
        .getTaskSessionById(session.getTaskSessionId())
        .orElseThrow(() -> ControllerUtils.notFound("Task session not found"));

    var duration = DateUtils.diff(session.getCreatedAt(), DateUtils.now());
    var answerStr = ObjectUtils.toJson(answer.getAnswers());
    return spotSessionService
        .reviseSubmission(session, taskSession, answerStr, duration)
        .toApiResult();
  }
}
