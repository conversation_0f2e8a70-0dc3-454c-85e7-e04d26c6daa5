package ai.saharaa.controllers;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.config.perms.IsNodeManager;
import ai.saharaa.config.perms.IsQueueManager;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.job.JobInvitationDTO;
import ai.saharaa.dto.job.PreTaskJobDetailsDTO;
import ai.saharaa.dto.pre.ApproveUserDTO;
import ai.saharaa.dto.pre.CreatePreTaskDTO;
import ai.saharaa.dto.pre.NodePreTaskExamDTO;
import ai.saharaa.dto.pre.PassedExamInfoStatDTO;
import ai.saharaa.dto.pre.PassedExamUserDTO;
import ai.saharaa.dto.pre.UpdatePreTaskGradeDTO;
import ai.saharaa.enums.SortType;
import ai.saharaa.model.Batch;
import ai.saharaa.model.Job;
import ai.saharaa.model.JobInvitation;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.Node;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.PreTaskExam;
import ai.saharaa.services.BatchService;
import ai.saharaa.services.JobService;
import ai.saharaa.services.PreTaskService;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/pre-task")
public class PreTaskController {

  private final PreTaskService preTaskService;
  private final JobService jobService;

  private final BatchService batchService;

  public PreTaskController(
      JobService jobService, PreTaskService preTaskService, BatchService batchService) {
    this.preTaskService = preTaskService;
    this.jobService = jobService;
    this.batchService = batchService;
  }

  @PostMapping("/invite-node-manager")
  @IsQueueManager
  @Operation(summary = "Invite a node manager")
  public ApiResult<List<JobInvitationDTO>> createPreTask(
      @Valid @RequestBody CreatePreTaskDTO createPreTaskDTO) {
    return this.preTaskService
        .createPreTask(createPreTaskDTO, ControllerUtils.currentUid())
        .toApiResult();
  }

  @GetMapping("/{inviteId}/for-queue-manager")
  @IsQueueManager
  @Operation(summary = "Query a JobInvitation")
  public ApiResult<List<JobInvitation>> preTaskInvitations(@PathVariable Long inviteId) {
    return this.preTaskService.getPreTaskInvitationsByQueueManager(inviteId).toApiResult();
  }

  @GetMapping("/{batchId}/waiting-passed-user-stat")
  @IsAccountManager
  @Operation(summary = "Number of users waiting to pass the exam")
  public ApiResult<Long> waitingPassedUserStat(@PathVariable Long batchId) {
    return ApiResult.success(
        this.preTaskService.passedUserStat(batchId, PreTaskExam.Status.WAITING));
  }

  @GetMapping("/{batchId}/waiting-passed-users")
  @IsAccountManager
  @Operation(summary = "Information on users who passed the exam")
  public ApiResult<List<PassedExamUserDTO>> waitingPassedUserStat(
      @PathVariable Long batchId,
      @RequestParam(required = false) BigDecimal filter,
      @RequestParam(required = false) Long nodeId) {
    return ApiResult.success(this.preTaskService.passedUser(batchId, filter, nodeId));
  }

  @GetMapping("/{batchId}/nodes")
  @IsAccountManager
  public ApiResult<List<Node>> nodes(
      @PathVariable Long batchId, @RequestParam(required = false) BigDecimal filter) {
    return ApiResult.success(this.preTaskService.getNodes(batchId, filter));
  }

  @GetMapping("/{batchId}/waiting-passed-users-line-chart")
  @IsAccountManager
  @Operation(summary = "Information on users who passed the exam")
  public ApiResult<Map<BigDecimal, Long>> waitingPassedUserStatLineChart(
      @PathVariable Long batchId,
      @RequestParam(required = false) BigDecimal filter,
      @RequestParam(required = false) Long nodeId) {

    return ApiResult.success(this.preTaskService
        .passedUser(batchId, filter, nodeId)
        .collect(groupingBy(x -> x.getPreTaskExam().getExamGrade(), counting())));
  }

  @GetMapping("/{batchId}/passed-user-stat")
  @IsAccountManager
  @Operation(summary = "Number of users who passed the exam")
  public ApiResult<Long> passedUserStat(@PathVariable Long batchId) {
    return ApiResult.success(
        this.preTaskService.passedUserStat(batchId, PreTaskExam.Status.ACCEPTED));
  }

  @PostMapping("/{batchId}/exam")
  @IsAccountManager
  @Operation(summary = "user exam")
  public ApiResult<Boolean> exam(
      @PathVariable Long batchId, @Valid @RequestBody ApproveUserDTO body) {
    this.preTaskService.approveOrRejectUser(batchId, body);
    return ApiResult.success(Boolean.TRUE);
  }

  @GetMapping("/for-node-manager")
  @IsNodeManager
  @CheckPageParam()
  public ApiResult<PageResult<NodePreTaskExamDTO>> getJobByPagination(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(defaultValue = "deadLineDesc") SortType orderType,
      @RequestParam(defaultValue = "status") List<Job.JobStatus> status,
      @RequestParam(required = false) String name) {

    IPage<Job> jobs = jobService.getJobByPaginationByOwner(
        page, limit, orderType, name, status, ControllerUtils.currentUid());

    if (jobs.getRecords().isEmpty()) {
      return PageResult.<NodePreTaskExamDTO>builder()
          .pages(jobs.getCurrent())
          .size(jobs.getSize())
          .total(jobs.getTotal())
          .build()
          .toApiResult();
    }

    Map<Long, List<PreTaskExam>> preTaskExamMaps = this.preTaskService
        .getPreTaskExamsByNodeUserId(ControllerUtils.currentUid())
        .collect(groupingBy(PreTaskExam::getBatchId));

    return PageResult.<NodePreTaskExamDTO>builder()
        .pages(jobs.getCurrent())
        .size(jobs.getSize())
        .total(jobs.getTotal())
        .data(jobs.getRecords()
            .map(j -> {
              Optional<Batch> batch = this.batchService.getBatchById(j.getBatchId());
              if (batch.isEmpty()) {
                throw new RuntimeException("Batch not found");
              }
              return NodePreTaskExamDTO.builder()
                  .preTaskExams(preTaskExamMaps.get(j.getBatchId()))
                  .job(j)
                  .batch(batch.get())
                  .build();
            })
            .collect(toList()))
        .build()
        .toApiResult();
  }

  @GetMapping("/{batchId}/passed-info-stat")
  @Operation(summary = "Information on users who passed the exam")
  public ApiResult<PassedExamInfoStatDTO> passedInfoStat(@PathVariable Long batchId) {
    return ApiResult.success(
        this.preTaskService.passedInfoStat(batchId, ControllerUtils.currentUid()));
  }

  @GetMapping("/{jobId}/exam-users")
  @IsNodeManager
  public ApiResult<List<PassedExamUserDTO>> getExamUsersByJob(
      @PathVariable Long jobId,
      @RequestParam(required = false) List<PreTaskExam.Status> amStatus,
      @RequestParam(required = false) List<PreTaskExam.Status> nmStatus) {
    return ApiResult.success(
        this.preTaskService.examUsers(jobId, ControllerUtils.currentUid(), amStatus, nmStatus));
  }

  @GetMapping("/jobs/{id}/unallocated-role-users")
  @IsNodeManager
  @CheckPageParam()
  public ApiResult<PageResult<PassedExamUserDTO>> getUnallocatedRoleUsers(
      @PathVariable Long id,
      @RequestParam(name = "targetRole", defaultValue = "UnAllocated")
          JobUser.JobUserRole targetRole,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    return this.preTaskService
        .getUnallocatedRoleUsers(id, ControllerUtils.currentUid(), targetRole, page, size)
        .toApiResult();
  }

  @GetMapping("/batches/{batchId}/pre-task-jobs")
  @IsQueueManager
  public ApiResult<List<PreTaskJobDetailsDTO>> preTaskJob(
      @PathVariable Long batchId, @RequestParam(required = false) String filter) {
    return this.preTaskService.preTaskJob(batchId, filter).toApiResult();
  }

  @GetMapping("/batches/{batchId}/can-auto-grade")
  public ApiResult<Boolean> preTaskJob(@PathVariable Long batchId) {
    return this.preTaskService.autoGrade(batchId).toApiResult();
  }

  @PutMapping("/batches/{batchId}/grade")
  public ApiResult<Boolean> grade(
      @PathVariable Long batchId, @Valid @RequestBody UpdatePreTaskGradeDTO body) {
    if (Objects.isNull(body) || body.getSessionDataList().isEmpty()) {
      throw ControllerUtils.badRequest("invalid params.");
    }
    this.preTaskService.updateGrade(batchId, body);
    return ApiResult.success(Boolean.TRUE);
  }
}
