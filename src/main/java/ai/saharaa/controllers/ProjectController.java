package ai.saharaa.controllers;

import static ai.saharaa.config.AuditLogInterceptor.REQUEST_USER_AGENT;
import static ai.saharaa.utils.Constants.ROLE_ACCOUNT_MANAGER;

import ai.saharaa.config.AuditLogInterceptor;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.config.perms.IsAdmin;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.SimpleUserDTO;
import ai.saharaa.dto.batch.BatchDetailsDTO;
import ai.saharaa.dto.client.ClientDTO;
import ai.saharaa.dto.project.*;
import ai.saharaa.enums.ProjectWithAMStatus;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.Project;
import ai.saharaa.model.User;
import ai.saharaa.model.client.Client;
import ai.saharaa.services.BatchService;
import ai.saharaa.services.ProjectService;
import ai.saharaa.services.UserService;
import ai.saharaa.services.user.TokenValidationService;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@RestController
@RequestMapping("/api/projects")
public class ProjectController {
  private final Logger log = org.slf4j.LoggerFactory.getLogger(ProjectController.class);
  private final ProjectService projectService;
  private final BatchService batchService;
  private final UserService userService;
  private final TokenValidationService tokenValidationService;

  public ProjectController(
      ProjectService projectService,
      BatchService batchService,
      UserService userService,
      TokenValidationService tokenValidationService) {
    this.projectService = projectService;
    this.batchService = batchService;
    this.userService = userService;
    this.tokenValidationService = tokenValidationService;
  }

  @RequestMapping("/taskInvitations")
  public ApiResult<String[]> getTaskInvitations() {
    return ApiResult.success(new String[] {});
  }

  @PostMapping
  public ApiResult<Project> createProject(@Valid @RequestBody CreateProjectDTO createProjectDTO) {
    var p = Project.builder()
        .name(createProjectDTO.getName())
        .description(createProjectDTO.getDescription())
        .status(0)
        .topic(createProjectDTO.getTopics().join(","))
        .requesterId(ControllerUtils.currentUid())
        .memo(createProjectDTO.getMemo())
        .build();
    p = projectService.createProject(p);
    // projectService.tryToAdaptAccountManager(p, ControllerUtils.currentUid());
    return p.toApiResult();
  }

  @PostMapping("/v2")
  public ApiResult<ProjectDetailsV2DTO> createProjectV2(
      @Valid @RequestBody CreateProjectV2DTO createProjectDTO) {
    var p = Project.builder()
        .name(createProjectDTO.getName())
        .description(createProjectDTO.getDescription())
        .status(0)
        .projectType(
            Objects.isNull(createProjectDTO.getProjectType())
                ? Project.ProjectType.INDIVIDUAL
                : createProjectDTO.getProjectType())
        .knowledge(createProjectDTO.getKnowledge())
        .memo(createProjectDTO.getMemo())
        .requesterId(ControllerUtils.currentUid())
        .accountManagerId(ControllerUtils.currentUid())
        .build();

    return projectService.createProjectV2(p, createProjectDTO.getClient()).toApiResult();
  }

  @RequestMapping("/pages")
  public ApiResult<PageResult<ProjectDetailsV2DTO>> getAmProject(
      @RequestParam(required = false) String search,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    IPage<ProjectDetailsV2DTO> projectPagesByAmIdAndSearch =
        this.projectService.getProjectPagesByAmIdAndSearch(null, search, page, size);
    return projectPagesByAmIdAndSearch.toPageResult().toApiResult();
  }

  @RequestMapping("/list")
  public ApiResult<PageResult<ProjectDetailsDTO>> getUserProjects(
      @RequestParam(required = false) String searchParam,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    if (searchParam == null) {
      searchParam = "";
    } else {
      searchParam = searchParam.trim();
    }
    var curId = ControllerUtils.currentUid();
    var pageSize = Math.max(5, Math.min(size, 100));
    var curPage = Math.max(1, page);
    var projects = projectService.getUserProjects(curId, searchParam, curPage, pageSize);
    var details = projects.getRecords().stream()
        .map(p -> {
          var batch = batchService.getBatchDetailsByProjectId(p.getId());
          return ProjectDetailsDTO.builder().project(p).batches(batch).build();
        })
        .toList();
    return PageResult.<ProjectDetailsDTO>builder()
        .pages(projects.getPages())
        .current(curPage)
        .size(projects.getSize())
        .total(projects.getTotal())
        .data(details)
        .build()
        .toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<Project> getProjectById(@PathVariable Long id) {
    var p = projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(v ->
            Objects.equals(v.getRequesterId(), ControllerUtils.currentUid()) || passForManagers())
        .orElseThrow(() -> ControllerUtils.forbidden("no permission"));
    return p.toApiResult();
  }

  @GetMapping("/v2/{id}")
  public ApiResult<ProjectDetailsV2DTO> getProjectByIdV2(@PathVariable Long id) {
    ProjectDetailsV2DTO project = projectService.getProjectDetailsById(id);
    if (Objects.isNull(project)) {
      throw ControllerUtils.notFound("Project not found");
    }
    return project.toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteProject(@PathVariable Long id) {
    projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(v ->
            Objects.equals(v.getRequesterId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't delete other user's project"));
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ua = (AuditLogInterceptor.RequestUserAgent) request.getAttribute(REQUEST_USER_AGENT);
    var userDetail = ControllerUtils.currentUserDetail();

    projectService.deleteProject(id, ua, userDetail);
  }

  @DeleteMapping("/v2/{id}")
  public void deleteProjectV2(@PathVariable Long id) {
    projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(v ->
            Objects.equals(v.getRequesterId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't delete other user's project"));
    var launchedBatchCount = batchService.countLaunchedBatchByProjectId(id);
    if (launchedBatchCount > 0) {
      throw ControllerUtils.forbidden("can't delete project that has launched batch");
    }
    projectService.deleteProjectV2(id);
  }

  @PostMapping("/{id}")
  public ApiResult<Project> updateProject(
      @PathVariable Long id, @Valid @RequestBody UpdateProjectDTO updateProjectDTO) {
    projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(v -> v.getRequesterId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("can't update other user's project"));

    if (updateProjectDTO.getName() == null
        && updateProjectDTO.getDescription() == null
        && updateProjectDTO.getTopics() == null) {
      throw ControllerUtils.badRequest("no update");
    }
    projectService.updateProject(
        id,
        updateProjectDTO.getName(),
        updateProjectDTO.getDescription(),
        updateProjectDTO.getTopics(),
        updateProjectDTO.getMemo());
    return projectService.getProjectById(id).get().toApiResult();
  }

  @PostMapping("/v2/{id}")
  public ApiResult<ProjectDetailsV2DTO> updateProjectV2(
      @PathVariable Long id, @Valid @RequestBody CreateProjectV2DTO updateProjectDTO) {
    projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(v ->
            Objects.equals(v.getRequesterId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("can't update other user's project"));

    var p = Project.builder()
        .id(id)
        .name(updateProjectDTO.getName())
        .description(updateProjectDTO.getDescription())
        .status(0)
        .projectType(
            Objects.isNull(updateProjectDTO.getProjectType())
                ? Project.ProjectType.INDIVIDUAL
                : updateProjectDTO.getProjectType())
        .knowledge(updateProjectDTO.getKnowledge())
        .memo(updateProjectDTO.getMemo())
        .build();

    projectService.updateProjectV2(p, updateProjectDTO.getClient());
    return projectService.getProjectDetailsById(id).toApiResult();
  }

  @GetMapping("/{id}/account-manager")
  public ApiResult<Optional<User>> getAccountManagerOfProject(@PathVariable Long id) {
    projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Project not found"))
        .filter(v ->
            Objects.equals(v.getRequesterId(), ControllerUtils.currentUid()) || passForManagers())
        .ensurePresent(() -> ControllerUtils.forbidden("no permission"));

    return userService.getAccountManagerForProject(id).toApiResult();
  }

  @PostMapping("/{id}/account-manager/{adminId}")
  @IsAdmin
  public ApiResult<Project> updateProjectAccountManager(
      @PathVariable Long id, @PathVariable Long adminId) {
    projectService
        .getProjectById(id)
        .filter(v -> !v.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("Project" + " not found"));
    userService
        .getUserById(adminId)
        .filter(v -> v.getRole() == ROLE_ACCOUNT_MANAGER && !v.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("Account manager not found"));

    projectService.updateProjectAccountManager(id, adminId);

    return projectService.getProjectById(id).get().toApiResult();
  }

  @GetMapping("/account-managers")
  public ApiResult<List<SimpleUserDTO>> getAccountMangers() {
    var users = tokenValidationService
        .findUsersByRole(ROLE_ACCOUNT_MANAGER)
        .map(SimpleUserDTO::fromUser)
        .toList();
    return ApiResult.success(users);
  }

  @PutMapping("/{id}/work")
  public void setProjectData() {
    log.info("todo");
  }

  @GetMapping("/{id}/batches")
  public ApiResult<List<BatchDetailsDTO>> getProjectBatches(@PathVariable Long id) {
    var batches = batchService.getBatchDetailsByProjectId(id);
    return ApiResult.success(batches);
  }

  @PutMapping("/{id}/am-claim")
  @IsAccountManager
  public ApiResult<ProjectDetailsDTO> amClaimProject(@PathVariable Long id) {
    return projectService.amClaimProject(id, ControllerUtils.currentUid()).toApiResult();
  }

  @GetMapping("/query-for-am")
  @IsAccountManager
  @CheckPageParam()
  public ApiResult<PageResult<ProjectDetailsDTO>> queryProjectForAm(
      @RequestParam(required = false) Long clientId,
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer limit,
      @RequestParam(defaultValue = "current") ProjectWithAMStatus type,
      @RequestParam(required = false) String searchParam) {
    if (searchParam == null) {
      searchParam = "";
    } else {
      searchParam = searchParam.trim();
    }

    var p = ControllerUtils.safePageRange(page, limit);
    IPage<Project> r;
    if (type == ProjectWithAMStatus.CURRENT) {
      r = projectService.queryProjectsByAmAndClientId(
          ControllerUtils.currentUid(), clientId, searchParam, p.getFirst(), p.getSecond());
    } else if (type == ProjectWithAMStatus.NEW) {
      r = projectService.queryProjectsWithoutAM(searchParam, p.getFirst(), p.getSecond());
    } else {
      throw ControllerUtils.notFound("Task list session not found");
    }

    var details = r.getRecords().stream()
        .map(project -> {
          var batch = batchService.getBatchDetailsByProjectId(project.getId());
          var user = userService.getUserById(project.getRequesterId());
          return ProjectDetailsDTO.builder()
              .project(project)
              .user(type == ProjectWithAMStatus.NEW ? user.get() : null)
              .batches(batch)
              .build();
        })
        .toList();
    return r.toPageResultWithData(details).toApiResult();
  }

  @GetMapping("/query-all")
  @IsAdmin
  @CheckPageParam()
  public ApiResult<PageResult<Project>> queryAllProjects(
      @RequestParam(defaultValue = "1") Integer page,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam Optional<String> query,
      @RequestParam Optional<Long> requesterId,
      @RequestParam Optional<Long> accountManagerId,
      @RequestParam(defaultValue = "true") Boolean deleted,
      @RequestParam(defaultValue = "id") ProjectFields sortBy,
      @RequestParam(defaultValue = "false") Boolean desc) {
    return projectService
        .queryProjects(query, requesterId, accountManagerId, page, size, deleted, sortBy, desc)
        .toPageResult()
        .toApiResult();
  }

  @PostMapping("/{id}/admin-change-am")
  @IsAdmin
  public ApiResult<Project> adminChangeAccountManager(
      @PathVariable Long id, @Valid @RequestBody AdminChangeAmDTO changeAmDTO) {
    var project = projectService
        .getProjectById(id)
        .filter(p -> !p.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("project not found"));
    var newAccountManagerId = changeAmDTO.getAccountManagerId();
    if (Objects.isNull(newAccountManagerId)) {
      throw ControllerUtils.badRequest("invalid accountManagerId");
    }
    var accountManager = userService
        .getUserById(newAccountManagerId)
        .filter(u -> !u.getDeleted())
        .orElseThrow(() -> ControllerUtils.forbidden("account manager not found"));
    if (newAccountManagerId.equals(project.getAccountManagerId())) {
      throw ControllerUtils.badRequest("unchanged account manager");
    }
    if (Objects.isNull(accountManager.getRole())
        || !accountManager.getRole().equals(ROLE_ACCOUNT_MANAGER)) {
      throw ControllerUtils.badRequest("not account manager role");
    }
    return projectService.adminChangeAccountManager(id, newAccountManagerId).toApiResult();
  }

  @GetMapping("/clients/query-by-name")
  public ApiResult<List<Client>> getClientByLikeName(@RequestParam(required = false) String name) {
    List<Client> clients = this.projectService.getClientByLikeName(name);
    return ApiResult.success(clients);
  }

  @GetMapping("/clients/{id}")
  public ApiResult<ClientDTO> getClientById(@PathVariable Long id) {
    ClientDTO client = this.projectService.getClientById(id);
    return ApiResult.success(client);
  }

  @GetMapping("/knowledgeList")
  public ApiResult<List<String>> getAllKnowledgeList() {
    return ApiResult.success(projectService.getAllKnowledgeList());
  }

  @GetMapping("/knowledgeMap")
  @IsAccountManager
  public ApiResult<List<KnowledgeV2ResultDTO>> getAllKnowledgeMap() {
    return ApiResult.success(projectService.getAllKnowledgeMap());
  }

  private boolean passForManagers() {
    return ControllerUtils.isAdmin()
        || ControllerUtils.isAccountManager()
        || ControllerUtils.isNodeManager();
  }
}
