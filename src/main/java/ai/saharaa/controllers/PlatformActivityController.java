package ai.saharaa.controllers;

import ai.saharaa.dto.ApiResult;
import ai.saharaa.model.activity.PlatformActivity;
import ai.saharaa.services.activity.PlatformActivityService;
import ai.saharaa.utils.ControllerUtils;
import java.util.List;
import java.util.Optional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/platform-activities")
public class PlatformActivityController {
  private final PlatformActivityService platformActivityService;

  public PlatformActivityController(PlatformActivityService platformActivityService) {
    this.platformActivityService = platformActivityService;
  }

  @GetMapping("/user/tasks/{id}")
  public ApiResult<Optional<PlatformActivity>> getUserTask(@PathVariable Long id) {
    var uid = ControllerUtils.currentUid();
    var activity = platformActivityService.getUserTask(uid, id);
    return ApiResult.success(activity);
  }

  @GetMapping("/user/certificates/{id}")
  public ApiResult<List<PlatformActivity>> getUseriParentCertificate(@PathVariable Long id) {
    var uid = ControllerUtils.currentUid();
    var activity = platformActivityService.getUserCertificate(uid, id);
    return ApiResult.success(activity);
  }
}
