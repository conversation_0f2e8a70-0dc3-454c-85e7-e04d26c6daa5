package ai.saharaa.controllers;

import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.utils.ControllerUtils;
import akka.actor.ActorSystem;
import akka.cluster.Cluster;
import akka.cluster.MemberStatus;
import java.sql.Connection;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/ping")
public class PingController {
  private final Logger log = LoggerFactory.getLogger(PingController.class);
  private final DataSource dataSource;
  private final ActorSystem actorSystem;
  private final Cluster cluster;
  private final IGlobalCache globalCache;

  public PingController(DataSource dataSource, ActorSystem actorSystem, IGlobalCache globalCache) {
    this.dataSource = dataSource;
    this.actorSystem = actorSystem;
    this.cluster = Cluster.get(this.actorSystem);
    this.globalCache = globalCache;
  }

  @GetMapping("/alive")
  public Boolean isAlive() {
    return true;
  }

  @GetMapping("/ready")
  public Boolean isReady() {
    if (cluster.selfMember().status() != MemberStatus.up()) {
      log.info("cluster is not in up status, it's: {}", cluster.selfMember().status());
      throw ControllerUtils.internal("cluster not ready");
    }
    log.debug("cluster is up");
    Connection conn = null;
    try {
      conn = dataSource.getConnection();
      if (!conn.isValid(1000)) {
        throw ControllerUtils.internal("db connection is not ready");
      }
    } finally {
      if (conn != null) {
        conn.close();
      }
    }

    log.debug("connection is valid");
    globalCache.get("test_key_may_not_exist");
    return true;
  }
}
