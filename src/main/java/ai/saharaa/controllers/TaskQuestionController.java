package ai.saharaa.controllers;

import ai.saharaa.daos.TaskQuestionDao;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.task.CreateTaskQuestionDTO;
import ai.saharaa.model.TaskQuestion;
import ai.saharaa.services.TaskService;
import ai.saharaa.utils.ControllerUtils;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/task-questions")
public class TaskQuestionController {
  private final TaskQuestionDao taskQuestionDao;
  private final TaskService taskService;

  public TaskQuestionController(TaskQuestionDao taskQuestionDao, TaskService taskService) {
    this.taskQuestionDao = taskQuestionDao;
    this.taskService = taskService;
  }

  @PostMapping
  public ApiResult<TaskQuestion> createTaskQuestion(
      @Valid @RequestBody CreateTaskQuestionDTO createTaskQuestionDTO) {
    taskService
        .getTaskById(createTaskQuestionDTO.getTaskId())
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(() -> ControllerUtils.forbidden("You are not the owner of this task"));

    var sort = taskQuestionDao.getMaxSortByTaskId(createTaskQuestionDTO.getTaskId());

    var tq = TaskQuestion.builder()
        .taskId(createTaskQuestionDTO.getTaskId())
        .ownerId(ControllerUtils.currentUid())
        .question(createTaskQuestionDTO.getQuestion())
        .questionType(createTaskQuestionDTO.getQuestionType())
        .details(createTaskQuestionDTO.getDetails())
        .answer(createTaskQuestionDTO.getAnswer())
        .answerRequired(createTaskQuestionDTO.getAnswerRequired())
        .sort(sort)
        .build();

    return taskQuestionDao.createTaskQuestion(tq).toApiResult();
  }

  @GetMapping("/{id}")
  public ApiResult<TaskQuestion> getTaskQuestionById(@PathVariable Long id) {
    var tq = taskQuestionDao
        .getTaskQuestionById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task question not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .orElseThrow(
            () -> ControllerUtils.forbidden("You are not the owner of this task question"));
    return tq.toApiResult();
  }

  @DeleteMapping("/{id}")
  public void deleteTaskQuestionById(@PathVariable Long id) {
    taskQuestionDao
        .getTaskQuestionById(id)
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("Task question not found"))
        .filter(t -> t.getOwnerId().equals(ControllerUtils.currentUid()))
        .ensurePresent(
            () -> ControllerUtils.forbidden("You are not the owner of this task question"));
    taskQuestionDao.deleteTaskQuestionById(id);
  }
}
