package ai.saharaa.controllers;

import static ai.saharaa.config.AuditLogInterceptor.REQUEST_USER_AGENT;
import static java.util.stream.Collectors.toList;

import ai.saharaa.config.AuditLogInterceptor;
import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.batch.*;
import ai.saharaa.enums.CertificateCategory;
import ai.saharaa.enums.CertificateType;
import ai.saharaa.model.*;
import ai.saharaa.model.academy.Academy;
import ai.saharaa.model.academy.AcademyLearningMaterial;
import ai.saharaa.model.academy.AcademyUserExam;
import ai.saharaa.model.academy.AcademyUserLearningMaterial;
import ai.saharaa.model.certificate.Certificate;
import ai.saharaa.model.certificate.UserCertificate;
import ai.saharaa.services.*;
import ai.saharaa.services.academy.AcademyService;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.*;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@RestController
@RequestMapping()
public class AcademyController {

  private final AcademyService academyService;
  private final BatchService batchService;
  private final JobService jobService;
  private final VaultService vaultService;
  private final TaskService taskService;
  private final UserService userService;

  public AcademyController(
      AcademyService academyService,
      BatchService batchService,
      JobService jobService,
      VaultService vaultService,
      TaskService taskService,
      UserService userService) {
    this.academyService = academyService;
    this.batchService = batchService;
    this.jobService = jobService;
    this.vaultService = vaultService;
    this.taskService = taskService;
    this.userService = userService;
  }

  @PostMapping("/v1/academy")
  @IsAccountManager
  public ApiResult<BatchDetailsDTO> create(@Valid @RequestBody CreateBatchDTO payload) {

    if (batchService.hasAcademyBatchByName(payload.getName(), null)) {
      throw ControllerUtils.badRequest("Name already exists: ${payload.getName()}");
    }

    if (vaultService.hasCertificateByName(payload.getCertificateName(), null)) {
      throw ControllerUtils.badRequest(
          "Certificate name already exists: ${payload.getCertificateName()}");
    }

    var deadline = payload.getDeadline();
    if (deadline == null) {
      deadline = DateUtils.TIMESTAMP_NEVER;
    } else {
      if (deadline.before(DateUtils.now())) {
        throw ControllerUtils.badRequest("Deadline must be in the future");
      }
    }

    var mapper = new ObjectMapper();
    Batch batch = Batch.builder()
        .ownerId(ControllerUtils.currentUid())
        .name(payload.getName())
        .projectId(payload.getProjectId())
        .summary(payload.getSummary())
        .summaryForAnnotator(payload.getSummaryForAnnotator())
        .summaryForReviewer(payload.getSummaryForReviewer())
        .description(payload.getDescription())
        .extraInfo(payload.getExtraInfo())
        .deadline(deadline)
        .taskType(payload.getTaskType())
        .userType(payload.getUserType())
        .difficulty(payload.getDifficulty())
        .deadline(payload.getDeadline())
        .requiredRegions(mapper.writeValueAsString(payload.getRequiredRegions()))
        .requiredLanguages(mapper.writeValueAsString(payload.getRequiredLanguages()))
        .gender(payload.getGender())
        .accent(payload.getAccent())
        .ageMin(payload.getAgeMin())
        .ageMax(payload.getAgeMax())
        .specialRequirement(payload.getSpecialRequirement())
        .courseType(payload.getCourseType())
        .academyType(payload.getAcademyType())
        .courseCover(payload.getCourseCover())
        .knowledge(payload.getKnowledge())
        .version(2)
        .build();

    return this.academyService
        .createAcademy(
            batch,
            payload.getCertificateName(),
            payload.getExperiencePoint(),
            payload.getAccessRequirements(),
            payload.getNeedReadMaterial(),
            payload.getNeedExam())
        .toApiResult();
  }

  @PutMapping("/v1/academy/{id}")
  @IsAccountManager
  public ApiResult<BatchDetailsDTO> updateBatchV2(
      @PathVariable Long id, @RequestBody UpdateBatchDTO payload) {

    Batch batchDB = this.batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));

    if (batchService.hasAcademyBatchByName(payload.getName(), id)) {
      throw ControllerUtils.badRequest("Name already exists: ${payload.getName()}");
    }

    if (vaultService.hasCertificateByName(
        payload.getCertificateName(), batchDB.getCertificateId())) {
      throw ControllerUtils.badRequest(
          "Certificate name already exists: ${payload.getCertificateName()}");
    }

    this.academyService.updateAcademy(id, payload, batchDB);
    return this.batchService.getBatchDetails(id).toApiResult();
  }

  @DeleteMapping("/v1/academy/{id}")
  @IsAccountManager
  public void deleteBatch(@PathVariable Long id) {
    this.batchService
        .getBatchById(id)
        .filter(v -> !v.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("Batch not found"));
    HttpServletRequest request =
        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    var ua = (AuditLogInterceptor.RequestUserAgent) request.getAttribute(REQUEST_USER_AGENT);

    var userDetail = ControllerUtils.currentUserDetail();

    this.academyService.deleteAcademy(id, ua, userDetail);
  }

  @GetMapping("/v1/academy/{id}")
  public ApiResult<BatchDetailsDTO> getBatch(@PathVariable Long id) {
    return ApiResult.success(getBatchDetailsDTO(id));
  }

  @GetMapping("/v1/academy")
  @CheckPageParam()
  public ApiResult<PageResult<BatchDetailsDTO>> getAcademyPage(
      @RequestParam CertificateCategory type,
      @RequestParam(required = false) String name,
      @RequestParam(required = false) CertificateType courseType,
      @RequestParam(required = false) Batch.BatchStatus status,
      @RequestParam(required = false) String knowledge,
      @RequestParam(required = false) List<Batch.CourseDifficulty> courseDifficulty,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var batches = this.academyService.getAcademyPages(
        type, name, courseType, status, knowledge, courseDifficulty, page, size);
    var batchDetails = batches
        .getRecords()
        .map((batch) -> {
          Certificate certificate = Objects.isNull(batch.getCertificateId())
              ? null
              : this.vaultService.getCertificateById(batch.getCertificateId());

          Academy academy = Batch.TaskType.ACADEMY.equals(batch.getTaskType())
              ? this.academyService.getAcademyByBatchId(batch.getId())
              : null;

          List<BatchAccessRequirement> batchAccessRequirements =
              batchService.getBatchAccessRequirements(batch.getId());

          UserCertificate userCertificate =
              this.vaultService.getUserCertificateByUserIdAndCertificateId(
                  ControllerUtils.currentUid(), batch.getCertificateId());
          UserRequirementsStatus userRequirementsStatus =
              this.academyService.getUserRequirementsStatus(
                  batch.getId(), ControllerUtils.currentUid());

          List<Batch> preCheck = this.academyService.preCheck(batch.getId());

          Job job = this.academyService.getExamByBatchId(batch.getId());
          if (Objects.isNull(job)) {
            return BatchDetailsDTO.builder()
                .batch(batch)
                .certificate(certificate)
                .academy(academy)
                .accessRequirements(batchAccessRequirements)
                .userCertificate(userCertificate)
                .userRequirementsStatus(userRequirementsStatus)
                .preCheckBatches(preCheck)
                .build();
          }

          JobUser jobUser = this.academyService.getJobUserByJobIdAndUserIdAndRole(
              job.getId(), ControllerUtils.currentUid(), null);

          Long total = this.academyService.countUserRequirementsStatusByUserIdAndBatchId(
              batch.getId(), null);
          Long approved = this.academyService.countUserRequirementsStatusByUserIdAndBatchId(
              batch.getId(), UserRequirementsStatus.UserRequireStatus.APPROVED);
          AcademyExamStatDTO academyExamStatDTO =
              AcademyExamStatDTO.builder().total(total).passed(approved).build();

          var owner = this.userService.getUserById(batch.getOwnerId()).orElse(null);

          BatchDetailsDTO dto = BatchDetailsDTO.builder()
              .batch(batch)
              .certificate(certificate)
              .academy(academy)
              .accessRequirements(batchAccessRequirements)
              .userRequirementsStatus(userRequirementsStatus)
              .jobUser(jobUser)
              .userCertificate(userCertificate)
              .preCheckBatches(preCheck)
              .academyExamStatDTO(academyExamStatDTO)
              .owner(owner)
              .build();

          return dto;
        })
        .toList();

    return PageResult.<BatchDetailsDTO>builder()
        .pages(batches.getPages())
        .size(batches.getSize())
        .current(batches.getCurrent())
        .total(batches.getTotal())
        .data(batchDetails)
        .build()
        .toApiResult();
  }

  @GetMapping("/v1/academy/available")
  @CheckPageParam()
  public ApiResult<PageResult<BatchDetailsDTO>> getAcademyPagesByAvailable(
      @RequestParam CertificateCategory type,
      @RequestParam(required = false) String name,
      @RequestParam CertificateType courseType,
      @RequestParam Boolean available,
      @RequestParam(required = false) String knowledge,
      @RequestParam(required = false) List<Batch.CourseDifficulty> courseDifficulty,
      @RequestParam(defaultValue = "0") Integer page,
      @RequestParam(defaultValue = "10") Integer size) {
    var batches = this.academyService.getAcademyPagesByAvailable(
        ControllerUtils.currentUid(),
        available,
        type,
        courseType,
        knowledge,
        courseDifficulty,
        name,
        page,
        size);
    var batchDetails = batches
        .getData()
        .map((batch) -> {
          Certificate certificate = Objects.isNull(batch.getCertificateId())
              ? null
              : this.vaultService.getCertificateById(batch.getCertificateId());

          Academy academy = Batch.TaskType.ACADEMY.equals(batch.getTaskType())
              ? this.academyService.getAcademyByBatchId(batch.getId())
              : null;

          List<BatchAccessRequirement> batchAccessRequirements =
              batchService.getBatchAccessRequirements(batch.getId());

          UserCertificate userCertificate =
              this.vaultService.getUserCertificateByUserIdAndCertificateId(
                  ControllerUtils.currentUid(), batch.getCertificateId());
          UserRequirementsStatus userRequirementsStatus =
              this.academyService.getUserRequirementsStatus(
                  batch.getId(), ControllerUtils.currentUid());

          List<Batch> preCheck = this.academyService.preCheck(batch.getId());

          Job job = this.academyService.getExamByBatchId(batch.getId());
          if (Objects.isNull(job)) {
            return BatchDetailsDTO.builder()
                .batch(batch)
                .certificate(certificate)
                .academy(academy)
                .accessRequirements(batchAccessRequirements)
                .userCertificate(userCertificate)
                .userRequirementsStatus(userRequirementsStatus)
                .preCheckBatches(preCheck)
                .build();
          }

          JobUser jobUser = this.academyService.getJobUserByJobIdAndUserIdAndRole(
              job.getId(), ControllerUtils.currentUid(), null);

          Long total = this.academyService.countUserRequirementsStatusByUserIdAndBatchId(
              batch.getId(), null);
          Long approved = this.academyService.countUserRequirementsStatusByUserIdAndBatchId(
              batch.getId(), UserRequirementsStatus.UserRequireStatus.APPROVED);
          AcademyExamStatDTO academyExamStatDTO =
              AcademyExamStatDTO.builder().total(total).passed(approved).build();

          BatchDetailsDTO dto = BatchDetailsDTO.builder()
              .batch(batch)
              .certificate(certificate)
              .academy(academy)
              .accessRequirements(batchAccessRequirements)
              .userRequirementsStatus(userRequirementsStatus)
              .jobUser(jobUser)
              .userCertificate(userCertificate)
              .preCheckBatches(preCheck)
              .academyExamStatDTO(academyExamStatDTO)
              .build();

          return dto;
        })
        .toList();

    PageResult<BatchDetailsDTO> pageResult = PageResult.<BatchDetailsDTO>builder()
        .data(batchDetails)
        .pages(batches.getPages())
        .total(batches.getTotal())
        .data(batchDetails)
        .size(batches.getSize())
        .build();

    return ApiResult.success(pageResult);
  }

  @PostMapping("/v1/academy/{id}/join")
  public ApiResult<UserRequirementsStatus> joinAcademy(@PathVariable Long id) {
    return this.academyService.joinAcademy(id, ControllerUtils.currentUid()).toApiResult();
  }

  @GetMapping("/v1/academy/{id}/userRequirementsStatus")
  public ApiResult<UserRequirementsStatus> getUserRequirementsStatus(@PathVariable Long id) {
    return this.academyService
        .getUserRequirementsStatus(id, ControllerUtils.currentUid())
        .toApiResult();
  }

  @PostMapping("/v1/academy/{id}/learningMaterials")
  @IsAccountManager
  public ApiResult<AcademyLearningMaterial> createLearningMaterials(
      @PathVariable Long id, @Valid @RequestBody CreateAcademyLearningMaterialDTO payload) {

    AcademyLearningMaterial academyLearningMaterial = payload.toEntity();
    academyLearningMaterial.setBatchId(id);

    return this.academyService
        .createAcademyLearningMaterial(academyLearningMaterial)
        .toApiResult();
  }

  @PostMapping("/v1/academy/{id}/learningMaterials/claim")
  public ApiResult<UserCertificate> claimLearningMaterials(@PathVariable Long id) {
    return this.academyService
        .claimAcademyLearningMaterial(id, ControllerUtils.currentUid())
        .toApiResult();
  }

  @PutMapping("/v1/academy/learningMaterials/{id}")
  @IsAccountManager
  public ApiResult<AcademyLearningMaterial> updateLearningMaterials(
      @PathVariable Long id, @RequestBody UpdateAcademyLearningMaterialDTO payload) {

    AcademyLearningMaterial academyLearningMaterialById =
        this.academyService.getAcademyLearningMaterialById(id);
    if (academyLearningMaterialById == null) {
      throw ControllerUtils.notFound("AcademyLearningMaterial not found");
    }

    AcademyLearningMaterial academyLearningMaterial = payload.toEntity();
    academyLearningMaterial.setBatchId(academyLearningMaterialById.getBatchId());
    academyLearningMaterial.setId(id);

    return this.academyService
        .updateAcademyLearningMaterial(academyLearningMaterial)
        .toApiResult();
  }

  @GetMapping("/v1/academy/learningMaterials/{id}")
  public ApiResult<AcademyLearningMaterial> getLearningMaterial(@PathVariable Long id) {
    return this.academyService.getAcademyLearningMaterialById(id).toApiResult();
  }

  @DeleteMapping("/v1/academy/learningMaterials/{id}")
  @IsAccountManager
  public void deleteLearningMaterials(@PathVariable Long id) {
    this.academyService.deleteAcademyLearningMaterial(id);
  }

  @GetMapping("/v1/academy/{id}/learningMaterials")
  public ApiResult<List<AcademyLearningMaterial>> getLearningMaterials(@PathVariable Long id) {
    return this.academyService.getAcademyLearningMaterials(id).toApiResult();
  }

  @PostMapping("/v1/academy/learningMaterials/{id}/users")
  public ApiResult<AcademyUserLearningMaterial> createAcademyUserLearningMaterial(
      @PathVariable Long id, @Valid @RequestBody CreateAcademyUserLearningMaterialDTO payload) {

    return this.academyService
        .createAcademyUserLearningMaterial(
            id, ControllerUtils.currentUid(), payload.getReadingTime())
        .toApiResult();
  }

  @GetMapping("/v1/academy/{id}/learningMaterials/users")
  public ApiResult<List<AcademyUserLearningMaterial>> getAcademyUserLearningMaterial(
      @PathVariable Long id) {

    return this.academyService
        .getAcademyUserLearningMaterialByUserIdAndBatchId(ControllerUtils.currentUid(), id)
        .toApiResult();
  }

  @PostMapping("/v1/academy/{id}/exams")
  @IsAccountManager
  public ApiResult<Job> createOrUpdateExams(
      @PathVariable Long id, @Valid @RequestBody CreateAcademyExamDTO payload) {

    var job = this.academyService.getExamByBatchId(id);
    if (Objects.isNull(job)) {
      return this.academyService.createExam(id, payload.getName()).toApiResult();
    } else {
      return this.academyService.updateExam(job.getId(), payload.getName()).toApiResult();
    }
  }

  @GetMapping("/v1/academy/exams/{id}/userLearnings")
  public ApiResult<List<AcademyUserExam>> getAcademyExamByUserIdAndBatchId(@PathVariable Long id) {

    return this.academyService
        .getAcademyExamByUserIdAndJobId(ControllerUtils.currentUid(), id)
        .toApiResult();
  }

  @PostMapping("/v1/academy/exams/{id}/userLearnings")
  public ApiResult<AcademyUserExam> createAcademyUserLearningMaterial(
      @PathVariable Long id, @Valid @RequestBody CreateAcademyUserExamDTO payload) {

    return this.academyService
        .updateAcademyUserExam(ControllerUtils.currentUid(), id, payload.getReadingTime())
        .toApiResult();
  }

  @GetMapping("/v1/academy/exams/{id}/stat")
  public ApiResult<PreTaskExam> getExamStat(@PathVariable Long id) {

    return this.academyService
        .getPreTaskExamByUserIdAndBatchId(ControllerUtils.currentUid(), id)
        .toApiResult();
  }

  @GetMapping("/v1/academy/{id}/exams")
  public ApiResult<Job> getExamByBatchId(@PathVariable Long id) {

    return this.academyService.getExamByBatchId(id).toApiResult();
  }

  @DeleteMapping("/v1/academy/{id}/exams/{jobId}")
  @IsAccountManager
  public void getExamByBatchId(@PathVariable Long id, @PathVariable Long jobId) {

    this.academyService.deleteExamByJobId(jobId);
  }

  @PostMapping("/v1/academy/exams/{id}/join")
  public ApiResult<JobUser> joinExam(@PathVariable Long id) {
    return this.academyService.joinExam(id, ControllerUtils.currentUid()).toApiResult();
  }

  @GetMapping("/v1/academy/exams/{id}/canJoin")
  public ApiResult<JobUser> canJoinExam(@PathVariable Long id) {
    return this.academyService.canJoinExam(id, ControllerUtils.currentUid()).toApiResult();
  }

  @GetMapping("/v1/academy/exams/{id}")
  public ApiResult<JobUser> getExam(@PathVariable Long id) {
    return this.academyService
        .getJobUserByJobIdAndUserIdAndRole(id, ControllerUtils.currentUid(), null)
        .toApiResult();
  }

  @GetMapping("/v1/academy/{id}/checkPreAcademy")
  public ApiResult<List<Batch>> checkPreAcademy(@PathVariable Long id) {
    BatchDetailsDTO batchDetails = this.batchService.getBatchDetails(id);

    List<Long> academyIds = batchDetails
        .getAccessRequirements()
        .filter(v -> Objects.equals(v.getType(), BatchAccessRequirement.Type.ACADEMY))
        .map(BatchAccessRequirement::getRelationId)
        .collect(toList());

    if (academyIds.isEmpty()) {
      return ApiResult.success(Collections.EMPTY_LIST);
    }

    List<Batch> batch = this.batchService.getBatchByIds(academyIds);

    Set<Long> certificateIds = this.academyService
        .getUserCertificateByCertificateIds(
            ControllerUtils.currentUid(),
            batch.stream().map(Batch::getCertificateId).collect(toList()))
        .map(UserCertificate::getCertificateId)
        .toSet();

    List<Batch> preAcademy = batch.stream()
        .filter(x -> !certificateIds.contains(x.getCertificateId()))
        .collect(toList());

    return ApiResult.success(preAcademy);
  }

  @GetMapping("/v1/academy/certificates/{id}/canJoinTask")
  public ApiResult<List<CanJoinTaskDTO>> canJoinTask(@PathVariable Long id) {
    List<Batch> batches = this.academyService.canJoinTask(ControllerUtils.currentUid(), id);
    return ApiResult.success(batches.stream()
        .map(x -> {
          CanJoinTaskDTO dto = getCanJoinTaskDTO(x);
          return dto;
        })
        .collect(toList()));
  }

  @GetMapping("/v1/academy/certificates/{id}/previewCanJoinTaskPreview")
  public ApiResult<List<CanJoinTaskDTO>> previewCanJoinTaskPreview(@PathVariable Long id) {
    List<Batch> batches =
        this.academyService.previewCanJoinTaskPreview(ControllerUtils.currentUid(), id);
    return ApiResult.success(batches.stream()
        .map(x -> {
          CanJoinTaskDTO dto = getCanJoinTaskDTO(x);

          return dto;
        })
        .collect(toList()));
  }

  private CanJoinTaskDTO getCanJoinTaskDTO(Batch x) {
    CanJoinTaskDTO dto = new CanJoinTaskDTO();
    BeanUtils.copyProperties(x, dto);

    List<Job> jobs = this.jobService.getJobsByBatchId(x.getId());
    Optional<Long> leftAnno = jobs.map(
            y -> this.jobService.getLeftAnnoTaskCount2(y.getId(), y.getAssignDataVolume(), 1))
        .reduce(Long::sum);
    if (leftAnno.isPresent()) {
      dto.setDatapoint(leftAnno.get());
    } else {
      Optional<Long> leftReview = jobs.map(
              y -> this.jobService.getLeftHumanReviewTaskCount(y.getId(), null))
          .reduce(Long::sum);
      dto.setDatapoint(leftReview.orElse(0L));
    }

    return dto;
  }

  @GetMapping("/v1/academy/certificates/{id}/canJoinAcademy")
  public ApiResult<List<CanJoinTaskDTO>> canJoinAcademy(@PathVariable Long id) {
    List<Batch> batches = this.academyService.canJoinAcademy(ControllerUtils.currentUid(), id);
    List<CanJoinTaskDTO> res = batches.stream()
        .map(x -> {
          CanJoinTaskDTO dto = new CanJoinTaskDTO();
          BeanUtils.copyProperties(x, dto);

          Optional<Long> leftExam = this.taskService
              .getTaskListByBatchIdAndType(x.getId(), TaskList.TaskListType.EXAM)
              .map(y -> this.taskService.getTaskCountInList(y.getId()))
              .reduce(Long::sum);
          dto.setDatapoint(leftExam.orElse(0L));

          Certificate certificate = this.vaultService.getCertificateById(x.getCertificateId());
          dto.setCertificateName(certificate.getName());
          dto.setCertificateExp(certificate.getExp());

          return dto;
        })
        .collect(toList());

    return ApiResult.success(res);
  }

  @GetMapping("/v1/academy/certificates/{id}/hasLevelUp")
  public ApiResult<List<CanJoinTaskDTO>> hasLevelUp(@PathVariable Long id) {
    Certificate cer = this.vaultService.getCertificateById(id);

    List<Batch> batches = this.academyService.hasLevelUp(
        ControllerUtils.currentUid(), id, cer.getCategory(), cer.getCertificateType());
    List<CanJoinTaskDTO> res = batches.stream()
        .map(x -> {
          CanJoinTaskDTO dto = new CanJoinTaskDTO();
          BeanUtils.copyProperties(x, dto);

          Optional<Long> leftExam = this.taskService
              .getTaskListByBatchIdAndType(x.getId(), TaskList.TaskListType.EXAM)
              .map(y -> this.taskService.getTaskCountInList(y.getId()))
              .reduce(Long::sum);
          dto.setDatapoint(leftExam.orElse(0L));

          Certificate certificate = this.vaultService.getCertificateById(x.getCertificateId());
          dto.setCertificateName(certificate.getName());
          dto.setCertificateExp(certificate.getExp());

          return dto;
        })
        .collect(toList());

    return ApiResult.success(res);
  }

  @GetMapping("/v1/academy/exams/{batchId}/check-answers/{taskId}")
  public ApiResult<List<QuestionAnswer>> getAnswersByTask(
      @PathVariable Long batchId, @PathVariable Long taskId) {
    var curId = ControllerUtils.currentUid();
    return academyService.getAnswersByTask(curId, batchId, taskId).toApiResult();
  }

  private BatchDetailsDTO getBatchDetailsDTO(Long id) {
    BatchDetailsDTO batchDetails = this.batchService.getBatchDetails(id);
    if (Objects.isNull(batchDetails.getBatch())) {
      throw ControllerUtils.notFound("Academy not found");
    }

    UserCertificate userCertificate = this.vaultService.getUserCertificateByUserIdAndCertificateId(
        ControllerUtils.currentUid(), batchDetails.getBatch().getCertificateId());
    UserRequirementsStatus userRequirementsStatus = this.academyService.getUserRequirementsStatus(
        batchDetails.getBatch().getId(), ControllerUtils.currentUid());

    batchDetails.setUserCertificate(userCertificate);
    batchDetails.setUserRequirementsStatus(userRequirementsStatus);

    Job job = this.academyService.getExamByBatchId(batchDetails.getBatch().getId());
    if (Objects.isNull(job)) {
      return batchDetails;
    }

    JobUser jobUser = this.academyService.getJobUserByJobIdAndUserIdAndRole(
        job.getId(), ControllerUtils.currentUid(), null);

    batchDetails.setJobUser(jobUser);
    return batchDetails;
  }
}
