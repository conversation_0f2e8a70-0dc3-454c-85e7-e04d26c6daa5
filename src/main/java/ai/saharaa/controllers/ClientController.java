package ai.saharaa.controllers;

import ai.saharaa.config.perms.CheckPageParam;
import ai.saharaa.config.perms.IsAccountManager;
import ai.saharaa.dto.ApiResult;
import ai.saharaa.dto.client.ClientDTO;
import ai.saharaa.dto.user.UserWalletAddressDTO;
import ai.saharaa.services.ProjectService;
import ai.saharaa.services.client.ClientService;
import ai.saharaa.services.user.TokenValidationService;
import ai.saharaa.utils.ControllerUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/clients")
public class ClientController {
  private final ProjectService projectService;
  private final TokenValidationService tokenValidationService;

  private final ClientService clientService;

  public ClientController(
      ProjectService projectService,
      TokenValidationService tokenValidationService,
      ClientService clientService) {
    this.projectService = projectService;
    this.tokenValidationService = tokenValidationService;
    this.clientService = clientService;
  }

  @GetMapping("/list")
  @IsAccountManager
  public ApiResult<List<UserWalletAddressDTO>> getClients(
      @RequestParam(value = "query", required = false) String query) {
    var curId = ControllerUtils.currentUid();
    var clientIds = projectService.getClientIdsByAmId(curId, query);
    return tokenValidationService.getUsersByIds(clientIds).toApiResult().getData();
  }

  @GetMapping("/pages")
  @CheckPageParam()
  public ApiResult<IPage<ClientDTO>> getClientPages(
      @RequestParam(value = "search", required = false) String search,
      @RequestParam(value = "page", required = false) Integer page,
      @RequestParam(value = "size", required = false) Integer size) {
    return clientService.getClientPage(search, page, size).toApiResult();
  }
}
