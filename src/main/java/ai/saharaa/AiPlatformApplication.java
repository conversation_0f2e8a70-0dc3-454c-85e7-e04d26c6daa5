package ai.saharaa;

import io.opentelemetry.javaagent.OpenTelemetryAgent;
import java.lang.instrument.Instrumentation;
import kamon.Kamon;
import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.EnableLoadTimeWeaving;
import org.springframework.instrument.InstrumentationSavingAgent;

@SpringBootApplication
@MapperScan("ai.saharaa.mappers")
@EnableAspectJAutoProxy
@EnableLoadTimeWeaving(aspectjWeaving = EnableLoadTimeWeaving.AspectJWeaving.ENABLED)
public class AiPlatformApplication {

  public static void loadAgents() {
    // A note on aspectj agent: Some docs recommend also attaching
    // spring-instrument.jar and using @EnableLoadTimeWeaving, however that's not
    // necessary here. spring-instrument.jar, if loaded correctly, is just a wrapper
    // around aspectjweaver.jar that delays attaching aspectjweaver.jar to
    // configuration time (i.e. when the loadTimeWeaver bean is loaded). This is often
    // too late and many classes (Spring or not) are already loaded and can no longer
    // benefit from LTW.
    // https://github.com/spring-projects/spring-framework/issues/29609
    Instrumentation instrumentation = ByteBuddyAgent.install();
    // Get access to an instrumentation instance
    // Activate AspectJ weaver
    Agent.agentmain("", instrumentation);

    // Save instrumentation instance for later use
    InstrumentationSavingAgent.agentmain("", instrumentation);

    String otelEndpoint = System.getenv("OTEL_EXPORTER_OTLP_ENDPOINT");
    if (otelEndpoint != null && !otelEndpoint.isBlank()) {
      OpenTelemetryAgent.premain("", instrumentation);
    }
  }

  public static void main(String[] args) {
    loadAgents();
    Kamon.init();
    SpringApplication.run(AiPlatformApplication.class, args);
  }
}
