package ai.saharaa.services;

import static ai.saharaa.daos.activity.PlatformActivityDao.PLATFORM_ADDRESS;
import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.TASK_ID;
import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.*;
import static ai.saharaa.model.Job.JobStatus.*;
import static ai.saharaa.model.JobUser.JobUserRole.*;
import static ai.saharaa.utils.Constants.Cache.*;
import static ai.saharaa.utils.Constants.STATISTIC_DECIMALS;
import static ai.saharaa.utils.Constants.SpecialTaskAchievementSymbol.CACHE_KEY_DURATION;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.daos.*;
import ai.saharaa.daos.achievement.UserLevelFromSubgraphDao;
import ai.saharaa.daos.activity.PlatformActivityDao;
import ai.saharaa.daos.newTasks.BatchRewardRecordDao;
import ai.saharaa.daos.newTasks.NewTasksDao;
import ai.saharaa.daos.newTasks.RewardTokenInfoDao;
import ai.saharaa.daos.season.SeasonDao;
import ai.saharaa.daos.season.SeasonJobDao;
import ai.saharaa.daos.stat.UserJobStatDao;
import ai.saharaa.distribution.CommonCounterManager;
import ai.saharaa.distribution.Utils.NewDistributionUtils;
import ai.saharaa.dto.batch.BatchDetailsDTO;
import ai.saharaa.dto.batch.RewardTokenDTO;
import ai.saharaa.dto.job.CreateInvitationDTO;
import ai.saharaa.dto.job.InviteJobUserDTO;
import ai.saharaa.dto.job.JobDetailsDTO;
import ai.saharaa.dto.job.JobInvitationDTO;
import ai.saharaa.dto.job.MarketJobDTO;
import ai.saharaa.dto.job.NodeDetailsDTO;
import ai.saharaa.dto.job.ReportTaskInJobDTO;
import ai.saharaa.dto.job.UserJobDTO;
import ai.saharaa.dto.onchain.StakingOnchainDataDTO;
import ai.saharaa.dto.task.CreateJobByPreTaskDTO;
import ai.saharaa.dto.task.CreateJobDTO;
import ai.saharaa.dto.task.CreateJobInfoDTO;
import ai.saharaa.dto.user.UserDTO;
import ai.saharaa.enums.*;
import ai.saharaa.exception.AppException;
import ai.saharaa.mappers.BatchMapper;
import ai.saharaa.mappers.ExamSessionMapper;
import ai.saharaa.mappers.JobInvitationMapper;
import ai.saharaa.mappers.JobMapper;
import ai.saharaa.mappers.JobUserMapper;
import ai.saharaa.mappers.NDASignRecordMapper;
import ai.saharaa.mappers.NodeMapper;
import ai.saharaa.mappers.PreTaskExamMapper;
import ai.saharaa.mappers.ProjectMapper;
import ai.saharaa.mappers.TaskMapper;
import ai.saharaa.mappers.TaskSessionMapper;
import ai.saharaa.mappers.UserMapper;
import ai.saharaa.model.*;
import ai.saharaa.model.TaskSession.TaskSessionStatus;
import ai.saharaa.model.activity.PlatformActivity;
import ai.saharaa.model.newTasks.BatchRewardRecord;
import ai.saharaa.model.newTasks.RewardTokenInfo;
import ai.saharaa.model.newTasks.UserTokenTaskRewards;
import ai.saharaa.model.stat.NodeStat;
import ai.saharaa.model.stat.TaskSessionUserStat;
import ai.saharaa.model.stat.UserJobStat;
import ai.saharaa.services.season.RuleService;
import ai.saharaa.services.user.TokenValidationService;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jcajce.provider.digest.Blake3;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
public class JobService extends ServiceImpl<JobMapper, Job> {
  private final Logger log = LoggerFactory.getLogger(JobService.class);
  private final JobMapper jobMapper;
  private final JobDao jobDao;
  private final TaskMapper taskMapper;
  private final TaskSessionMapper taskSessionMapper;
  private final IGlobalCache cache;
  private final CommonCounterManager counterManager;

  private final ReviewSessionDao reviewSessionDao;
  private final JobTaskReportDao jobTaskReportDao;
  private final SpotSessionDao spotSessionDao;
  private final UserDao userDao;
  private final UserMapper userMapper;
  private final BatchMapper batchMapper;

  private final BatchService batchService;
  private final ProjectMapper projectMapper;
  private final JobInvitationMapper jobInvitationMapper;
  private final NodeMapper nodeMapper;
  private final JobTaskDao jobTaskDao;
  private final JobUserMapper jobUserMapper;
  private final JobUserDao jobUserDao;
  private final BatchRewardRecordDao batchRewardRecordDao;
  private final RewardTokenInfoDao rewardTokenInfoDao;
  private final BatchDao batchDao;
  private final TaskListDao taskListDao;
  private final TaskDao taskDao;
  private final TaskSessionDao taskSessionDao;
  private final SkipTaskSessionDao skipTaskSessionDao;
  private final BatchNDADao batchNDADao;
  private final JobInvitationDao jobInvitationDao;
  private final NDASignRecordMapper ndaSignRecordMapper;
  private final PreTaskExamMapper preTaskExamMapper;
  private final NodeStatDao nodeStatDao;
  private final ExamSessionMapper examSessionMapper;
  private final UserJobStatDao userJobStatDao;

  private final NotificationService notificationService;
  private final PreTaskService preTaskService;
  private final ExamSessionDao examSessionDao;
  private final BatchAccessRequirementDao batchAccessRequirementDao;
  private final IndividualsDao individualsDao;
  private final NdaRecordDao ndaRecordDao;
  private final SeasonJobDao seasonJobDao;
  private final NewTasksDao newTasksDao;
  private final SeasonDao seasonDao;
  private final PlatformActivityDao platformActivityDao;
  private final TaskQuestionDao taskQuestionDao;
  private final SystemSettingDao systemSettingDao;
  private final WorkloadLimitService workloadLimitService;
  private final UserLevelFromSubgraphDao userLevelFromSubgraphDao;
  private final ProjectDao projectDao;
  private final TokenValidationService tokenValidationService;
  private final String onChainStakingUrl;

  public JobService(
      JobMapper jobMapper,
      JobDao jobDao,
      BatchMapper batchMapper,
      TaskMapper taskMapper,
      JobTaskDao jobTaskDao,
      TaskListDao taskListDao,
      JobInvitationMapper jobInvitationMapper,
      NodeMapper nodeMapper,
      JobUserMapper jobUserMapper,
      ProjectMapper projectMapper,
      ReviewSessionDao reviewSessionDao,
      SpotSessionDao spotSessionDao,
      UserMapper userMapper,
      TaskSessionMapper taskSessionMapper,
      JobUserDao jobUserDao,
      NewTasksDao newTasksDao,
      BatchRewardRecordDao batchRewardRecordDao,
      RewardTokenInfoDao rewardTokenInfoDao,
      TaskSessionDao taskSessionDao,
      BatchNDADao batchNDADao,
      JobInvitationDao jobInvitationDao,
      NDASignRecordMapper ndaSignRecordMapper,
      PreTaskExamMapper preTaskExamMapper,
      UserDao userDao,
      PreTaskService preTaskService,
      NodeStatDao nodeStatDao,
      UserJobStatDao userJobStatDao,
      ExamSessionDao examSessionDao,
      ExamSessionMapper examSessionMapper,
      NotificationService notificationService,
      TaskDao taskDao,
      IGlobalCache cache,
      CommonCounterManager counterManager,
      BatchService batchService,
      BatchAccessRequirementDao batchAccessRequirementDao,
      JobTaskReportDao jobTaskReportDao,
      SeasonJobDao seasonJobDao,
      SeasonDao seasonDao,
      BatchDao batchDao,
      NdaRecordDao ndaRecordDao,
      IndividualsDao individualsDao,
      SkipTaskSessionDao skipTaskSessionDao,
      PlatformActivityDao platformActivityDao,
      TaskQuestionDao taskQuestionDao,
      SystemSettingDao systemSettingDao,
      WorkloadLimitService workloadLimitService,
      UserLevelFromSubgraphDao userLevelFromSubgraphDao,
      ProjectDao projectDao,
      @Value("${onchain.staking.url.graphql}") String onChainStakingUrl,
      TokenValidationService tokenValidationService) {
    this.jobMapper = jobMapper;
    this.jobDao = jobDao;
    this.userDao = userDao;
    this.cache = cache;
    this.counterManager = counterManager;
    this.batchMapper = batchMapper;
    this.taskMapper = taskMapper;
    this.spotSessionDao = spotSessionDao;
    this.jobTaskDao = jobTaskDao;
    this.ndaRecordDao = ndaRecordDao;
    this.newTasksDao = newTasksDao;
    this.batchDao = batchDao;
    this.userMapper = userMapper;
    this.taskListDao = taskListDao;
    this.jobInvitationMapper = jobInvitationMapper;
    this.nodeMapper = nodeMapper;
    this.jobUserMapper = jobUserMapper;
    this.projectMapper = projectMapper;
    this.taskSessionMapper = taskSessionMapper;
    this.reviewSessionDao = reviewSessionDao;
    this.jobUserDao = jobUserDao;
    this.rewardTokenInfoDao = rewardTokenInfoDao;
    this.batchRewardRecordDao = batchRewardRecordDao;
    this.taskSessionDao = taskSessionDao;
    this.batchNDADao = batchNDADao;
    this.jobInvitationDao = jobInvitationDao;
    this.ndaSignRecordMapper = ndaSignRecordMapper;
    this.preTaskExamMapper = preTaskExamMapper;
    this.taskDao = taskDao;
    this.nodeStatDao = nodeStatDao;
    this.userJobStatDao = userJobStatDao;
    this.examSessionMapper = examSessionMapper;
    this.notificationService = notificationService;
    this.examSessionDao = examSessionDao;
    this.jobTaskReportDao = jobTaskReportDao;
    this.preTaskService = preTaskService;
    this.batchService = batchService;
    this.batchAccessRequirementDao = batchAccessRequirementDao;
    this.seasonJobDao = seasonJobDao;
    this.seasonDao = seasonDao;
    this.individualsDao = individualsDao;
    this.skipTaskSessionDao = skipTaskSessionDao;
    this.platformActivityDao = platformActivityDao;
    this.taskQuestionDao = taskQuestionDao;
    this.systemSettingDao = systemSettingDao;
    this.workloadLimitService = workloadLimitService;
    this.userLevelFromSubgraphDao = userLevelFromSubgraphDao;
    this.projectDao = projectDao;
    this.onChainStakingUrl = onChainStakingUrl;
    this.tokenValidationService = tokenValidationService;
  }

  public Optional<Job> getJobById(Long id) {
    return jobDao.getJobById(id);
  }

  @Transactional
  public Job reassignJob(Long jobId, CreateJobDTO data, Long currentUserId) {
    var job = this.jobMapper.selectById(jobId);
    if (Objects.isNull(job)) {
      throw ControllerUtils.notFound("job not found");
    }

    if (job.getStatus() != Job.JobStatus.DRAFT) {
      throw ControllerUtils.badRequest("job already assigned");
    }

    job.setStatus(Job.JobStatus.DELEGATING_JOB);
    job.setNodeId(data.getNodeId());
    job.setTimeSpentPerTask(data.getTimeSpentPerTask());
    job.setReviewDeadline(data.getReviewDeadline());
    job.setAuditDeadline(data.getAuditDeadline());
    job.setInviteAcceptanceDeadline(data.getInviteAcceptanceDeadline());
    this.jobMapper.updateById(job);

    this.createJobUserByCreateJobOrAssignJob(data.getNodeId(), job);
    this.createJobInvitation(
        CreateInvitationDTO.builder()
            .jobId(job.getId())
            .nodeId(data.getNodeId())
            .invitationType(JobInvitation.InvitationType.JOB)
            .build(),
        currentUserId);

    return jobMapper.selectById(job.getId());
  }

  private void createJobUserByCreateJobOrAssignJob(Long nodeId, Job job) {
    BatchDetailsDTO batchDetails = batchService.getBatchDetails(job.getBatchId());
    List<Long> examRelationIds = batchDetails
        .getAccessRequirements()
        .filter(x -> Objects.equals(x.getType(), BatchAccessRequirement.Type.EXAM))
        .map(BatchAccessRequirement::getRelationId)
        .collect(toList());

    if (Objects.isNull(batchDetails.getBatch().getVersion())
        || batchDetails.getBatch().getVersion() == 1
        || examRelationIds.size() > 0) {

      examRelationIds.add(job.getBatchId());
      List<PreTaskExam> preTaskExams =
          this.preTaskExamMapper.selectList(new QueryWrapper<>(PreTaskExam.class)
              .lambda()
              .in(PreTaskExam::getBatchId, examRelationIds)
              .eq(PreTaskExam::getDeleted, false)
              .eq(PreTaskExam::getNodeId, nodeId)
              .eq(PreTaskExam::getAmPassed, PreTaskExam.Status.ACCEPTED));

      if (preTaskExams.isEmpty()) {
        throw ControllerUtils.badRequest("no pre task exam passed");
      }

      if (Objects.isNull(batchDetails.getBatch().getVersion())
          || batchDetails.getBatch().getVersion() == 1
          || examRelationIds.size() > 0) {
        // Create JobUser who passed the exam
        var usreIdList = preTaskExams.stream().map(PreTaskExam::getUserId).toList();
        for (Long id : usreIdList) {
          JobUser userInvitor = JobUser.builder()
              .userId(id)
              .taskListSessionId(job.getId())
              .role(UNALLOCATED)
              .build();
          jobUserMapper.insert(userInvitor);
        }
      }
    }
  }

  @Transactional
  public void acceptJobInvitation(Long uid, JobInvitation invitation, List<BatchNDA> ndas) {
    jobInvitationDao.updateJobInvitationStatus(
        invitation, JobInvitation.JobInvitationStatus.PENDING);
    ndas.forEach(nda -> {
      ndaSignRecordMapper.insert(NDASignRecord.builder()
          .ndaId(nda.getId())
          .userId(uid)
          .jobId(invitation.getJobId())
          .jobInvitationId(invitation.getId())
          .batchId(nda.getBatchId())
          .signType(NDASignRecord.NDASignType.JobInvitation)
          .build());
    });
  }

  @Transactional
  public Boolean dealWithJobInvitation(
      Long curId, Long inviteId, JobInvitation.JobInvitationStatus type) {
    JobInvitation targetRes = jobInvitationMapper.selectById(inviteId);
    if (targetRes.getDeleted()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    if (!Objects.equals(targetRes.getStatus(), JobInvitation.JobInvitationStatus.PENDING)
        && !Objects.equals(targetRes.getStatus(), JobInvitation.JobInvitationStatus.NdaSigning)) {
      throw ControllerUtils.forbidden("invalid data");
    }
    Job targetJob = jobMapper.selectById(targetRes.getJobId());
    Node myNode =
        nodeMapper.selectOne(new QueryWrapper<Node>().lambda().eq(Node::getNodeManagerId, curId));
    if (!targetJob.getNodeId().equals(myNode.getId())) {
      throw ControllerUtils.forbidden("no permission");
    }

    if (type == JobInvitation.JobInvitationStatus.ACCEPTED) {
      targetRes.setStatus(JobInvitation.JobInvitationStatus.ACCEPTED);
      jobInvitationMapper.updateById(targetRes);

      if (targetRes.getInvitationType() == JobInvitation.InvitationType.PRE_TASK) {
        targetJob.setStatus(Job.JobStatus.PRE_TASK);
      } else {
        targetJob.setStatus(WORKING);
      }

      jobMapper.updateById(targetJob);

      List<JobUser> jobUsers = this.jobUserMapper.selectList(new QueryWrapper<JobUser>()
          .lambda()
          .eq(JobUser::getTaskListSessionId, targetJob.getId())
          .eq(JobUser::getDeleted, false)
          .eq(JobUser::getUserId, curId)
          .last("limit 1"));
      if (jobUsers.isEmpty()) {
        var ju = JobUser.builder()
            .userId(myNode.getNodeManagerId())
            .role(JobUser.JobUserRole.ADMIN)
            .taskListSessionId(targetJob.getId())
            .active(true)
            .build();
        jobUserMapper.insert(ju);
      }

      notificationService.noticeQmDelegationAccepted(myNode.getNodeManagerId(), inviteId);
    } else if (type == JobInvitation.JobInvitationStatus.REJECTED) {
      targetRes.setStatus(JobInvitation.JobInvitationStatus.REJECTED);
      jobInvitationMapper.updateById(targetRes);

      lambdaUpdate()
          .eq(Job::getId, targetJob.getId())
          .set(Job::getStatus, Job.JobStatus.DROPPED)
          .set(Job::getNodeId, null)
          .set(Job::getUpdatedAt, DateUtils.now())
          .update();

      notificationService.noticeQmDelegationRejected(myNode.getNodeManagerId(), inviteId);
    }
    return true;
  }

  public JobInvitationDTO createJobInvitation(CreateInvitationDTO data, Long currentUserId) {
    var job = jobMapper.selectById(data.getJobId());

    var status = JobInvitation.JobInvitationStatus.PENDING;
    if (data.getInvitationType() == JobInvitation.InvitationType.PRE_TASK) {
      var hasNda = batchNDADao.getNdaCountByBatchId(job.getBatchId()) > 0;
      status = hasNda
          ? JobInvitation.JobInvitationStatus.NdaSigning
          : JobInvitation.JobInvitationStatus.PENDING;
    }

    JobInvitation invitation = JobInvitation.builder()
        .status(status)
        .jobId(data.getJobId())
        .invitationType(data.getInvitationType())
        .nodeId(data.getNodeId())
        .createdBy(currentUserId)
        .build();

    jobInvitationMapper.insert(invitation);
    JobInvitationDTO res = JobInvitationDTO.builder().invitation(invitation).build();

    res.setJob(jobMapper.selectById(data.getJobId()));
    res.setNode(nodeMapper.selectById(data.getNodeId()));

    notificationService.noticeNMNewDelegation(
        currentUserId, data.getNodeId(), data.getJobId(), invitation.getId());
    return res;
  }

  public IPage<JobInvitationDTO> getJobInvitations(
      Long nodeId, Integer page, Integer limit, String type, Long curId) {
    if (nodeId == null) {
      Node myNode =
          nodeMapper.selectOne(new QueryWrapper<Node>().lambda().eq(Node::getNodeManagerId, curId));
      if (myNode == null) throw ControllerUtils.badRequest("invalid data");
      nodeId = myNode.getId();
    }
    var q = new QueryWrapper<JobInvitation>()
        .lambda()
        .eq(JobInvitation::getNodeId, nodeId)
        .eq(JobInvitation::getDeleted, false)
        .eq(
            !(type == null || type.isEmpty() || type.equalsIgnoreCase("pending")),
            JobInvitation::getStatus,
            type)
        .in(
            type != null && type.equalsIgnoreCase("pending"),
            JobInvitation::getStatus,
            List.of("pending", "ndaSigning"));

    return jobInvitationMapper.selectPage(Page.of(page, limit), q).convert(jobInvitation -> {
      JobInvitationDTO res =
          JobInvitationDTO.builder().invitation(jobInvitation).build();
      Node node = nodeMapper.selectById(jobInvitation.getNodeId());
      if (node != null) res.setNode(node);
      Job targetJob = jobMapper.selectById(jobInvitation.getJobId());
      if (targetJob != null) res.setJob(targetJob);
      Batch batch = batchMapper.selectById(targetJob.getBatchId());
      if (batch != null) res.setBatch(batch);
      return res;
    });
  }

  private List<Job> getSpecialTaskListByStatus(
      String cacheKey, String achieveSymbol, List<Job.JobStatus> status) {
    var specialTaskList = new ArrayList<Job>();
    if (cache.hasKey(cacheKey) && !cache.get(cacheKey).toString().isEmpty()) {
      var jobIds = Arrays.stream(cache.get(cacheKey).toString().split(","))
          .map(Long::valueOf)
          .toList();
      specialTaskList.addAll(this.jobMapper.selectList(new QueryWrapper<Job>()
          .lambda()
          .in(Job::getId, jobIds)
          .in(!CollectionUtils.isEmpty(status), Job::getStatus, status)
          .eq(Job::getDeleted, false)));
    } else {
      specialTaskList.addAll(this.jobMapper.selectJoinList(
          Job.class,
          JoinWrappers.lambda(Job.class)
              .rightJoin(Batch.class, Batch::getId, Job::getBatchId)
              .selectAll(Job.class)
              .in(!CollectionUtils.isEmpty(status), Job::getStatus, status)
              .eq(Batch::getRequiredAchievementSymbol, achieveSymbol)
              .eq(Job::getDeleted, false)));
      var cachingContent =
          String.join(",", specialTaskList.map(j -> j.getId().toString()).toList());
      cache.set(cacheKey, cachingContent, CACHE_KEY_DURATION);
    }
    return specialTaskList;
  }

  private List<Job> getSpecialTaskListLikeByStatus(
      String cacheKey, String achieveSymbol, List<Job.JobStatus> status) {
    var specialTaskList = new ArrayList<Job>();
    if (cache.hasKey(cacheKey) && !cache.get(cacheKey).toString().isEmpty()) {
      var jobIds = Arrays.stream(cache.get(cacheKey).toString().split(","))
          .map(Long::valueOf)
          .toList();
      specialTaskList.addAll(this.jobMapper.selectList(new QueryWrapper<Job>()
          .lambda()
          .in(Job::getId, jobIds)
          .in(!CollectionUtils.isEmpty(status), Job::getStatus, status)
          .eq(Job::getDeleted, false)));
    } else {
      specialTaskList.addAll(this.jobMapper.selectJoinList(
          Job.class,
          JoinWrappers.lambda(Job.class)
              .rightJoin(Batch.class, Batch::getId, Job::getBatchId)
              .selectAll(Job.class)
              .in(!CollectionUtils.isEmpty(status), Job::getStatus, status)
              .like(Batch::getRequiredAchievementSymbol, achieveSymbol + "%")
              .eq(Job::getDeleted, false)));
      var cachingContent =
          String.join(",", specialTaskList.map(j -> j.getId().toString()).toList());
      cache.set(cacheKey, cachingContent, CACHE_KEY_DURATION);
    }
    return specialTaskList;
  }

  private Map<Long, Batch> getBatchMapByIds(List<Long> idList) {
    return batchMapper.selectByIds(idList).stream()
        .filter(b -> !b.getDeleted())
        .toList()
        .toMap(Batch::getId, Function.identity());
  }

  public List<MarketJobDTO> getSpecialTaskList(Long curId, String achieveSymbol, String ip) {
    var cacheKey =
        String.format(Constants.SpecialTaskAchievementSymbol.CACHE_KEY_FORMATTER, achieveSymbol);
    var specialTaskList = getSpecialTaskListByStatus(cacheKey, achieveSymbol, List.of(WORKING));
    if (specialTaskList.isEmpty()) {
      return new ArrayList<>();
    }
    var specialTaskIds = specialTaskList.map(Job::getId).toList();
    var joinedPartIds = jobUserMapper
        .selectList(new QueryWrapper<JobUser>()
            .lambda()
            .select(JobUser::getTaskListSessionId)
            .in(JobUser::getTaskListSessionId, specialTaskIds)
            .ne(JobUser::getRole, NOT_PASS_EXAM)
            .eq(JobUser::getUserId, curId)
            .eq(JobUser::getDeleted, false))
        .map(JobUser::getTaskListSessionId)
        .toList();
    if (!joinedPartIds.isEmpty()) {
      specialTaskList = specialTaskList.stream()
          .filter(job -> !joinedPartIds.contains(job.getId()))
          .toList();
    }
    if (specialTaskList.isEmpty()) {
      return new ArrayList<>();
    }
    var jobBatchIds = specialTaskList.map(Job::getBatchId).toList();
    var batchesMap = getBatchMapByIds(jobBatchIds);
    var rewardRecords =
        batchRewardRecordDao.listByBatchIds(jobBatchIds).groupingBy(BatchRewardRecord::getBatchId);
    var userTempBan = individualsDao.getUserBansByUserId(curId);
    Long banExpireDuration = null;
    if (userTempBan.isPresent()) {
      var userBanStartTime = userTempBan.get().getCreatedAt();
      banExpireDuration =
          DateUtils.diff(DateUtils.add(userBanStartTime, Duration.ofDays(7)), DateUtils.now());
    }
    Long finalBanExpireDuration = banExpireDuration;
    return specialTaskList
        .map(j -> {
          var batch = batchesMap.getOrDefault(j.getBatchId(), null);
          var batchSetting = batchDao.getBatchSettingById(batch.getId());
          var project = projectMapper.selectById(batch.getProjectId());
          var requirements =
              batchAccessRequirementDao.getBatchAccessRequirementsByBatchId(j.getBatchId());
          var jobUser = jobUserDao.getJobUserByJobAndUserId(j.getId(), curId);
          var ndas = ndaRecordDao.getMySignedNdaInBatch(j.getBatchId(), curId);
          var currentTaskRewards =
              rewardRecords.getOrDefault(j.getBatchId(), List.<BatchRewardRecord>of());
          var tokensMap = rewardTokenInfoDao
              .findByIds(currentTaskRewards.mapToList(BatchRewardRecord::getTokenInfoId))
              .toMap(RewardTokenInfo::getId, Function.identity());
          var rewards = currentTaskRewards.stream()
              .map(r -> RewardTokenDTO.builder()
                  .recordId(r.getId())
                  .tokenInfoId(r.getTokenInfoId())
                  .amount(r.getAmount())
                  .rewardTokenType(r.getRewardTokenType())
                  .decimals(tokensMap.get(r.getTokenInfoId()).getTokenDecimal())
                  .tokenName(tokensMap.get(r.getTokenInfoId()).getTokenName())
                  .tokenIconResourceId(tokensMap.get(r.getTokenInfoId()).getResourceId())
                  .tgeAlready(tokensMap.get(r.getTokenInfoId()).getTgeAlready())
                  .build())
              .toList();
          var result = MarketJobDTO.builder()
              .job(j)
              .batch(batch)
              .requirements(requirements)
              .project(project)
              .hasAvailableTaskLeft(true)
              .ndaSignRecordList(ndas)
              .jobUser(jobUser)
              .rewards(rewards)
              .stakingRequest(batchSetting.getStakingRequest())
              .requiredUserLevel(batchSetting.getRequiredUserLevel())
              .annotationMethod(batchSetting.getAnnotationMethod())
              .totalBudgetPoints(batchSetting.getTotalBudgetPoints())
              .taskPaymentType(batchSetting.getTaskPaymentType())
              .userLevel(getUserLevel(curId).intValue())
              .build();
          var newJobUser = fetchUserRequirementStatus(curId, requirements, result, j.getId());
          if (newJobUser.asOpt().isPresent()) {
            result.setJobUser(newJobUser);
          }
          var leftTask = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
              ? getLeftAnnoTaskCountForSingle(j.getId(), curId, batchSetting)
              : getLeftAnnoTaskCount2(
                  j.getId(),
                  j.getAssignDataVolume(),
                  batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
          result.setJobLeftTaskCount(Math.max(leftTask, 0));
          if (finalBanExpireDuration != null) {
            result.setTempBanExpire(finalBanExpireDuration);
          }
          result.setWorkloadType(workloadLimitService.getIpAndUserIdLimitWorkloadType(curId, ip));
          var completedCount = counterManager.getJobCount(j.getId(), COMPLETE_COUNT.value);
          result.setCompleteCount(completedCount);
          result.setTotalTaskCount((long) j.getAssignDataVolume()
              * batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
          return result;
        })
        .toList();
  }

  public IPage<MarketJobDTO> getMarketByPaginationForIndividuals(
      Long curId,
      Integer page,
      Integer limit,
      String jobName,
      MarketSortType sortType,
      List<Batch.BatchLabelType> labelType,
      List<Batch.CourseDifficulty> difficulties,
      List<String> knowledgeList,
      String ip,
      BatchSetting.AnnotationMethod annotationMethod,
      UserLevelFilterType userLevelFilter,
      Boolean onlyStakeReq) {
    var userLevel = getUserLevel(curId);
    // Step 1: Build the query wrapper
    MPJLambdaWrapper<Job> queryWrapper = buildMarketQueryWrapper(
        curId,
        jobName,
        sortType,
        labelType,
        difficulties,
        knowledgeList,
        annotationMethod,
        userLevelFilter,
        userLevel,
        onlyStakeReq);

    IPage<Job> res = this.jobMapper.selectJoinPage(Page.of(page, limit), Job.class, queryWrapper);

    if (res.getRecords().isEmpty()) {
      return res.convert(r -> MarketJobDTO.builder().job(r).build());
    }

    // Step 2: Pre-fetch data for DTO conversion
    List<Job> jobs = res.getRecords();
    List<Long> jobBatchIds = jobs.stream().map(Job::getBatchId).distinct().toList();
    List<Long> jobIds = jobs.stream().map(Job::getId).distinct().toList();

    Map<Long, Batch> batchesMap =
        getBatchMapByIds(jobBatchIds); // Assuming this fetches efficiently
    Map<Long, BatchSetting> batchSettingsMap = batchDao.getBatchSettingsByBatchIds(jobBatchIds);
    var projectIds =
        batchesMap.values().stream().map(Batch::getProjectId).distinct().toList();
    Map<Long, Project> projectsMap = projectDao.getProjectsByIds(projectIds);
    Map<Long, List<BatchAccessRequirement>> requirementsMap =
        batchAccessRequirementDao.getRequirementsForBatchIds(jobBatchIds);

    var rewardRecords = batchRewardRecordDao.listByBatchIds(jobBatchIds);
    Map<Long, List<RewardTokenDTO>> recordsMap = new HashMap<>();
    if (!rewardRecords.isEmpty()) {
      var rewardRecordMap = rewardRecords.groupingBy(BatchRewardRecord::getBatchId);
      rewardRecordMap.forEach((key, value) -> {
        var tokensMap = rewardTokenInfoDao
            .findByIds(value.mapToList(BatchRewardRecord::getTokenInfoId))
            .toMap(RewardTokenInfo::getId, Function.identity());
        recordsMap.set(
            key,
            value.stream()
                .map(r -> RewardTokenDTO.builder()
                    .recordId(r.getId())
                    .tokenInfoId(r.getTokenInfoId())
                    .amount(r.getAmount())
                    .rewardTokenType(r.getRewardTokenType())
                    .decimals(tokensMap.get(r.getTokenInfoId()).getTokenDecimal())
                    .tokenName(tokensMap.get(r.getTokenInfoId()).getTokenName())
                    .tokenIconResourceId(tokensMap.get(r.getTokenInfoId()).getResourceId())
                    .tgeAlready(tokensMap.get(r.getTokenInfoId()).getTgeAlready())
                    .build())
                .toList());
      });
    }
    Map<Long, JobUser> jobUsersMap;
    Map<Long, List<NDASignRecord>> ndasMap;
    Long finalBanExpireDuration = null;
    if (curId != null) {
      jobUsersMap = jobUserDao.getJobUsersByJobIdsAndUserId(jobIds, curId);
      ndasMap = ndaRecordDao.getNdasForBatchesAndUser(jobBatchIds, curId);
      finalBanExpireDuration = getUserBanExpireDuration(curId);
    } else {
      jobUsersMap = Collections.emptyMap();
      ndasMap = Collections.emptyMap();
    }
    Long finalBanDuration = finalBanExpireDuration;
    return res.convert(job -> mapJobToMarketJobDTO(
        job,
        curId,
        ip,
        userLevel,
        batchesMap,
        batchSettingsMap,
        projectsMap,
        requirementsMap,
        jobUsersMap,
        ndasMap,
        recordsMap,
        finalBanDuration));
  }

  private MPJLambdaWrapper<Job> buildMarketQueryWrapper(
      Long curId,
      String jobName,
      MarketSortType sortType,
      List<Batch.BatchLabelType> labelType,
      List<Batch.CourseDifficulty> difficulties,
      List<String> knowledgeList,
      BatchSetting.AnnotationMethod annotationMethod,
      UserLevelFilterType userLevelFilter,
      Long userLevel,
      Boolean onlyStakeReq) {
    // Define aliases used in joins and .apply()
    String jobAlias = "j";
    String jobUserAlias = "ju";
    String batchAlias = "b";
    String batchSettingAlias = "bs";
    String projectAlias = "p";

    MPJLambdaWrapper<Job> wrapper = JoinWrappers.lambda(jobAlias, Job.class)
        .selectAll(Job.class)
        .leftJoin(
            JobUser.class, jobUserAlias, on -> on.eq(Job::getId, JobUser::getTaskListSessionId)
                .eq(JobUser::getDeleted, false)
                .eq(JobUser::getUserId, curId))
        .leftJoin(Batch.class, batchAlias, Batch::getId, Job::getBatchId)
        .leftJoin(BatchSetting.class, batchSettingAlias, BatchSetting::getBatchId, Batch::getId)
        .leftJoin(Project.class, projectAlias, Project::getId, Batch::getProjectId);

    // Filters
    wrapper
        .apply(
            !(jobName == null || jobName.isEmpty()),
            jobAlias + ".name ilike {0}",
            "%".concat(jobName == null ? "" : jobName).concat("%"))
        .eq(Job::getJobType, Job.JobType.INDIVIDUAL)
        .and(i -> i.isNull(JobUser::getId).or().eq(JobUser::getRole, NOT_PASS_EXAM))
        .eq(Job::getDeleted, false)
        .eq(Batch::getTaskType, Batch.TaskType.TASK)
        .eq(Batch::getRequiredAchievementSymbol, "")
        .eq(Job::getStatus, WORKING)
        .in(!CollectionUtils.isEmpty(labelType), Batch::getLabelType, labelType)
        .apply(
            !CollectionUtils.isEmpty(knowledgeList),
            "string_to_array(" + projectAlias + ".knowledge, ',') && string_to_array({0}, ',')",
            String.join(",", knowledgeList))
        .in(!CollectionUtils.isEmpty(difficulties), Batch::getDifficulty, difficulties);

    if (Boolean.TRUE.equals(onlyStakeReq)) {
      wrapper.gt(BatchSetting::getStakingRequest, 0);
    }
    if (annotationMethod != null && !annotationMethod.getValue().isEmpty()) {
      wrapper.eq(BatchSetting::getAnnotationMethod, annotationMethod);
    }
    if (userLevelFilter != null && userLevelFilter.getValue().equals("eligible")) {
      // Ensure bs.required_user_level is comparable with userLevel (e.g. both int or long)
      wrapper.le(BatchSetting::getRequiredUserLevel, userLevel);
    }

    StringBuilder orderByFullClause = new StringBuilder("ORDER BY ");

    orderByFullClause.append(String.format(
        "CASE WHEN (%s.required_user_level <= %s AND %s.id IS NULL) THEN 0 ELSE 1 END ASC, ",
        batchSettingAlias, userLevel, jobUserAlias));

    orderByFullClause.append(String.format("%s.required_user_level DESC, ", batchSettingAlias));
    orderByFullClause.append(
        String.format("CASE WHEN %s.id IS NULL THEN 0 ELSE 1 END ASC", jobUserAlias));

    String defaultSortColumn = jobAlias + ".created_at DESC";

    // Special sorting logic for staking tasks
    if (Boolean.TRUE.equals(onlyStakeReq)) {
      // For staking tasks, sort by staking amount descending (higher stake first)
      orderByFullClause.append(", ");
      orderByFullClause.append(String.format("%s.staking_request DESC", batchSettingAlias));

      // Add secondary sort by creation time as fallback
      orderByFullClause.append(", ");
      orderByFullClause.append(defaultSortColumn);
    } else {
      // Original sorting logic for non-staking tasks
      if (sortType != null) {
        orderByFullClause.append(", ");
        switch (sortType) {
          case Oldest:
            orderByFullClause.append(jobAlias).append(".created_at ASC");
            break;
          case Earliest: // This means newest
            orderByFullClause.append(jobAlias).append(".created_at DESC");
            break;
          case MaxEarning:
            orderByFullClause.append(batchAlias).append(".est_earnings DESC, ");
            orderByFullClause.append(jobAlias).append(".created_at DESC");
            break;
          case MinEarning:
            orderByFullClause.append(batchAlias).append(".est_earnings ASC, ");
            orderByFullClause.append(jobAlias).append(".created_at DESC");
            break;
          case EndingSoon:
            orderByFullClause.append(jobAlias).append(".review_deadline ASC");
            break;
          default: // Should not happen if MarketSortType is exhaustive, but good for safety
            orderByFullClause.append(defaultSortColumn);
            break;
        }
      } else {
        // Default sort if sortType is null
        orderByFullClause.append(", ");
        orderByFullClause.append(defaultSortColumn);
      }
    }

    wrapper.last(orderByFullClause.toString());
    return wrapper;
  }

  private MarketJobDTO mapJobToMarketJobDTO(
      Job job,
      Long curId,
      String ip,
      Long currentUserLevel,
      Map<Long, Batch> batchesMap,
      Map<Long, BatchSetting> batchSettingsMap,
      Map<Long, Project> projectsMap,
      Map<Long, List<BatchAccessRequirement>> requirementsMap,
      Map<Long, JobUser> jobUsersMap,
      Map<Long, List<NDASignRecord>> ndasMap,
      Map<Long, List<RewardTokenDTO>> rewardsMap,
      Long banExpireDuration) {
    var batch = batchesMap.get(job.getBatchId());
    if (batch == null) {
      return null;
    }
    var batchSetting = batchSettingsMap.get(job.getBatchId());
    var project = projectsMap.get(batch.getProjectId());
    List<BatchAccessRequirement> requirements =
        requirementsMap.getOrDefault(job.getBatchId(), List.of());
    var jobUser = jobUsersMap.get(job.getId());
    List<NDASignRecord> ndas = ndasMap.getOrDefault(job.getBatchId(), List.of());
    List<RewardTokenDTO> rewards = rewardsMap.getOrDefault(job.getBatchId(), List.of());

    var result = MarketJobDTO.builder()
        .job(job)
        .batch(batch)
        .requirements(requirements)
        .project(project)
        .hasAvailableTaskLeft(true)
        .ndaSignRecordList(ndas)
        .jobUser(jobUser)
        .userLevel(Math.toIntExact(currentUserLevel))
        .annotationMethod(batchSetting.getAnnotationMethod())
        .requiredUserLevel(batchSetting.getRequiredUserLevel())
        .totalBudgetPoints(batchSetting.getTotalBudgetPoints())
        .taskPaymentType(batchSetting.getTaskPaymentType())
        .rewards(rewards)
        .build();
    long totalTaskCount;
    if (batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE) {
      totalTaskCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    } else {
      totalTaskCount = (long) job.getAssignDataVolume()
          * batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    }
    result.setTotalTaskCount(totalTaskCount);
    if (curId != null) {
      var completedCount = counterManager.getJobCount(job.getId(), COMPLETE_COUNT.value);
      result.setCompleteCount(completedCount);
      result.setUserLevel(Math.toIntExact(currentUserLevel));

      if (banExpireDuration != null) {
        result.setTempBanExpire(banExpireDuration);
      }

      var updatedJobUser = fetchUserRequirementStatus(curId, requirements, result, job.getId());
      if (updatedJobUser.asOpt().isPresent()) {
        result.setJobUser(updatedJobUser);
      }

      result.setWorkloadType(workloadLimitService.getIpAndUserIdLimitWorkloadType(curId, ip));

      // As per requirement, hide completeCount and jobLeftTaskCount for non-logged-in users.
      // It will remain null if this block is skipped.
      long leftTask;
      if (batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE) {
        leftTask = getLeftAnnoTaskCountForSingleIgnoreWorkload(job.getId());
      } else {
        leftTask = getPublicLeftAnnoTaskCountForRaw(
            job.getId(), batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
      }
      result.setJobLeftTaskCount(Math.max(leftTask, 0));
      if (!batch.getReviewerRequired()) {
        var pendingPipelineCount =
            counterManager.getJobCount(job.getId(), PENDING_PIPELINE_COUNT.value);
        if (pendingPipelineCount > totalTaskCount) pendingPipelineCount = totalTaskCount;
        result.setSubmittedCount(pendingPipelineCount + completedCount);
      }
    }
    result.setStakingRequest(batchSetting.getStakingRequest());
    return result;
  }

  private Long getUserBanExpireDuration(Long curId) {
    var userTempBan = individualsDao.getUserBansByUserId(curId);
    if (userTempBan.isPresent()) {
      var userBanStartTime = userTempBan.get().getCreatedAt();
      // Ensure DateUtils.add and DateUtils.diff handle timezones consistently if your Dates do
      return DateUtils.diff(DateUtils.add(userBanStartTime, Duration.ofDays(7)), DateUtils.now());
    }
    return null;
  }

  private JobUser fetchUserRequirementStatus(
      Long curId, List<BatchAccessRequirement> requirements, MarketJobDTO result, Long jobId) {
    if (!requirements.isEmpty()) {
      var examRequirementIds =
          requirements.stream().map(BatchAccessRequirement::getRelationId).toList();
      if (!examRequirementIds.isEmpty()) {
        var userReqStatus = individualsDao.getUserExamRecordByBatchIds(examRequirementIds, curId);
        var filteredByExamReqIdMap = new HashMap<Long, UserRequirementsStatus>();
        userReqStatus.forEach(req -> {
          if (filteredByExamReqIdMap.containsKey(req.getRequirementTargetId())) {
            if (!filteredByExamReqIdMap
                .get(req.getRequirementTargetId())
                .getStatus()
                .equals(UserRequirementsStatus.UserRequireStatus.APPROVED)) {
              if (!req.getStatus().equals(UserRequirementsStatus.UserRequireStatus.PENDING)) {
                filteredByExamReqIdMap.put(req.getRequirementTargetId(), req);
              }
            }
          } else {
            filteredByExamReqIdMap.put(req.getRequirementTargetId(), req);
          }
        });
        result.setUserRequirementStatus(filteredByExamReqIdMap.values().toList());
        var failedRecs = filteredByExamReqIdMap
            .values()
            .toList()
            .filter(u -> u.getStatus() == UserRequirementsStatus.UserRequireStatus.REJECTED)
            .toList();
        if (failedRecs.size() >= examRequirementIds.size()) {
          var jobUserExist = jobUserDao.getJobUserByJobAndUserId(jobId, curId);
          if (jobUserExist.asOpt().isEmpty()) {
            JobUser rec = JobUser.builder()
                .userId(curId)
                .active(false)
                .taskListSessionId(jobId)
                .role(JobUser.JobUserRole.NOT_PASS_EXAM)
                .build();
            return jobUserDao.createJobUser(rec);
          }
        }
      }
    }
    return null;
  }

  public List<UserJobDTO> getJoinedSpecialTaskList(
      String achieveSymbol, Long curId, String userIp) {
    var joinedCacheKey = String.format(
        Constants.SpecialTaskAchievementSymbol.CACHE_KEY_FORMATTER_FOR_MEMBER, achieveSymbol);
    var specialTaskList = getSpecialTaskListByStatus(
        joinedCacheKey, achieveSymbol, Arrays.asList(WORKING, COMMITTED));
    if (specialTaskList.isEmpty()) {
      return new ArrayList<>();
    }
    var finalJobIds = specialTaskList.map(Job::getId).toSet();
    var jobUsers = this.jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .in(JobUser::getTaskListSessionId, finalJobIds)
        .eq(JobUser::getUserId, curId)
        .in(JobUser::getRole, Arrays.asList(LABELER, REVIEWER))
        .eq(JobUser::getDeleted, false)
        .eq(JobUser::getDisabled, false)
        .eq(JobUser::getActive, true));
    var userLevel = getUserLevel(curId);
    return jobUsers
        .map(ju -> {
          var dto = mapJobUserToJobUserDto(ju, true);
          dto.setUserLevel(userLevel.intValue());
          return dto;
        })
        .toList();
  }

  private UserJobDTO mapJobUserToJobUserDto(JobUser jobUser, boolean needProjectData) {
    var jobCell = jobMapper.selectById(jobUser.getTaskListSessionId());
    var batchCell = batchMapper.selectById(jobCell.getBatchId());
    var batchSetting = batchDao.getBatchSettingById(batchCell.getId());
    var result = UserJobDTO.builder()
        .user(jobUser)
        .job(jobCell)
        .batch(batchCell)
        .workloadType(WorkloadType.NO_WORKLOAD)
        .taskPaymentType(batchSetting.getTaskPaymentType())
        .requiredUserLevel(batchSetting.getRequiredUserLevel())
        .annotationMethod(batchSetting.getAnnotationMethod())
        .build();
    if (jobUser.getRole().equals(REVIEWER)) {
      var hasPendingTask =
          taskSessionDao.hasPendingReviewForUser(jobUser.getTaskListSessionId(), jobUser.getId());
      result.setHasPendingTask(hasPendingTask);
      if (WORKING.equals(jobCell.getStatus())) {
        result.setLeftTaskCount(
            getLeftHumanReviewTaskCount(jobUser.getTaskListSessionId(), jobUser.getUserId()));
      }
    }
    if (jobUser.getRole().equals(LABELER)) {
      var hasPendingTask =
          jobTaskDao.hasPendingJobForJobUser(jobUser.getTaskListSessionId(), jobUser.getId());
      result.setHasPendingTask(hasPendingTask);
      if (WORKING.equals(jobCell.getStatus())) {
        var jobLeftCount = getLeftAnnoTaskCount2(
            jobUser.getTaskListSessionId(),
            jobCell.getAssignDataVolume(),
            batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
        var jobUserPendingCount =
            getMyPendingTaskCount(jobUser.getTaskListSessionId(), jobUser.getUserId());
        var pinnedCount = taskSessionDao.countUserPinnedSessionInJob(
            jobUser.getTaskListSessionId(), jobUser.getUserId());
        result.setLeftTaskCount(jobLeftCount + jobUserPendingCount + pinnedCount);
      }
    }
    if (Job.JobStatus.PRE_TASK.equals(jobCell.getStatus())) {
      assignPreTaskData(result, batchCell, jobCell, jobUser);
    }
    if (Arrays.asList(WORKING, AUDITING, AM_AUDIT, COMMITTED, SETTLING, DROPPED, REJECTED, FINISHED)
        .contains(jobCell.getStatus())) {
      assignDataOfOtherStatus(result, jobCell, jobUser);
    }
    if (Arrays.asList(POST_EXAM, WAITING_AM_PASSED).contains(jobUser.getRole())) {
      assignDataAboutExam(result, jobCell, jobUser);
    }
    if (needProjectData) {
      projectMapper.selectById(batchCell.getProjectId()).asOpt().ifPresent(result::setProject);
    }
    return result;
  }

  private void assignDataAboutExam(UserJobDTO result, Job jobCell, JobUser jobUser) {
    var canAutoGrade = preTaskService.autoGrade(jobCell.getBatchId());
    if (Boolean.TRUE.equals(canAutoGrade)) {
      result.setCanAutoGrade(true);
      var userExamSessionsCount = examSessionDao.countExamSessionsByJobUserId(jobUser.getId());
      var userExamSessionsAutoGradeCount =
          examSessionDao.countAutoGradeExamSessionsByJobUserId(jobUser.getId());
      result.setUserExamCount(userExamSessionsCount.intValue());
      result.setUserPassExamCount(userExamSessionsAutoGradeCount.intValue());
    } else {
      result.setCanAutoGrade(false);
    }
  }

  private void assignDataOfOtherStatus(UserJobDTO result, Job jobCell, JobUser jobUser) {
    if (jobUser.getRole().equals(LABELER)) {
      var sessionCount = taskSessionDao.countUserSessionInJob(jobCell.getId(), jobUser.getUserId());
      var spentTime = taskSessionDao.getUserWholeDuringInJob(jobCell.getId(), jobUser.getUserId());
      result.setJobAnnotatorData(JobAnnotatorStatisticDTO.builder()
          .completeCount(sessionCount.intValue())
          .spentTime(spentTime)
          .build());
    } else if (jobUser.getRole().equals(REVIEWER)) {
      var sessionCount =
          reviewSessionDao.countUserSessionInJob(jobCell.getId(), jobUser.getUserId());
      var spentTime =
          reviewSessionDao.getUserWholeDuringInJob(jobCell.getId(), jobUser.getUserId());
      result.setJobAnnotatorData(JobAnnotatorStatisticDTO.builder()
          .completeCount(sessionCount.intValue())
          .spentTime(spentTime)
          .build());
    }
  }

  private void assignPreTaskData(UserJobDTO result, Batch batchCell, Job jobCell, JobUser jobUser) {
    var taskList = taskListDao.listTaskListsByBatchIdAndType(
        batchCell.getId(), TaskList.TaskListType.ANNOTATION);
    if (!taskList.isEmpty() && taskList.first() != null) {
      var dataCount = taskDao.getTaskCountInList(taskList.first().getId());
      result.setMaxEndpointCount(dataCount);
    }
    var sessionCount = taskSessionDao.countUserSessionInJob(jobCell.getId(), jobUser.getUserId());
    var spentTime = taskSessionDao.getUserWholeDuringInJob(jobCell.getId(), jobUser.getUserId());
    result.setJobAnnotatorData(JobAnnotatorStatisticDTO.builder()
        .completeCount(sessionCount.intValue())
        .spentTime(spentTime)
        .build());
  }

  public IPage<UserJobDTO> getJobByPagination(
      Long curId,
      Integer page,
      Integer size,
      JobUser.JobUserType status,
      List<JobUser.JobUserRole> roles,
      String jobName,
      SortType sortType,
      List<Batch.BatchLabelType> labelType,
      Job.JobType jobType,
      List<Batch.CourseDifficulty> difficulties,
      List<String> knowledgeList,
      String userIp,
      BatchSetting.AnnotationMethod annotationMethod,
      UserLevelFilterType userLevelFilter) {
    var userLevel = getUserLevel(curId);
    IPage<JobUser> jobUserIPage = this.jobUserMapper.selectJoinPage(
        Page.of(page, size),
        JobUser.class,
        JoinWrappers.lambda("ju", JobUser.class)
            .selectAll(JobUser.class)
            .leftJoin(Job.class, "j", Job::getId, JobUser::getTaskListSessionId)
            .leftJoin(Batch.class, "b", Batch::getId, Job::getBatchId)
            .leftJoin(BatchSetting.class, "bs", BatchSetting::getBatchId, Batch::getId)
            .leftJoin(Project.class, "p", Project::getId, Batch::getProjectId)
            .eq(Objects.nonNull(curId), JobUser::getUserId, curId)
            .eq(JobUser::getDeleted, false)
            .eq(Job::getDeleted, false)
            .eq(JobUser::getDisabled, false)
            .eq(Job::getJobType, jobType)
            .in(!CollectionUtils.isEmpty(roles), JobUser::getRole, roles)
            .in(!CollectionUtils.isEmpty(labelType), Batch::getLabelType, labelType)
            .apply(
                !CollectionUtils.isEmpty(knowledgeList),
                "string_to_array(p.knowledge, ',') && string_to_array({0}, ',')",
                String.join(",", knowledgeList))
            .in(!CollectionUtils.isEmpty(difficulties), Batch::getDifficulty, difficulties)
            .apply(
                !(jobName == null || jobName.isEmpty()),
                "j.name ilike {0}",
                "%".concat(jobName == null ? "" : jobName).concat("%"))
            .and(JobUser.JobUserType.COMPLETE == status, jw -> jw.in(
                    Job::getStatus,
                    List.of(Job.JobStatus.DROPPED, Job.JobStatus.SETTLING, Job.JobStatus.FINISHED))
                .ne(JobUser::getRole, NOT_PASS_EXAM)
                .or()
                .in(Job::getStatus, WORKING, COMMITTED)
                .eq(JobUser::getActive, false)
                .ne(JobUser::getRole, NOT_PASS_EXAM))
            .and(JobUser.JobUserType.COMMITTED == status, jw -> jw.in(
                    Job::getStatus, List.of(Job.JobStatus.COMMITTED, AM_AUDIT))
                .ne(JobUser::getRole, NOT_PASS_EXAM))
            .and(JobUser.JobUserType.ACTIVE == status, jw -> jw.notIn(
                    JobUser.JobUserType.ACTIVE == status,
                    Job::getStatus,
                    List.of(Job.JobStatus.DROPPED, Job.JobStatus.SETTLING, Job.JobStatus.FINISHED))
                .in(JobUser::getActive, true)
                .ne(JobUser::getRole, NOT_PASS_EXAM))
            // .and(!CollectionUtils.isEmpty(roles), jw -> jw.leftJoin(Batch.class,
            // Batch::getId, Job::getBatchId).eq(Batch::getLabelType, labelType))
            .eq(JobUser.JobUserType.ACTIVE == status, Batch::getRequiredAchievementSymbol, "")
            .eq(
                annotationMethod != null && !annotationMethod.getValue().isEmpty(),
                "bs.annotation_method",
                annotationMethod)
            .le(
                userLevelFilter != null && userLevelFilter.getValue().equals("eligible"),
                "bs.required_user_level",
                userLevel)
            .orderBy(JobUser.JobUserType.COMPLETE == status, false, JobUser::getActive)
            .orderBy(
                (sortType == null || SortType.CreatedAtDesc == sortType),
                false,
                JobUser::getCreatedAt)
            .orderBy(SortType.CreatedAtAsc == sortType, true, JobUser::getCreatedAt));

    // @formatter:on
    return jobUserIPage.convert(ju -> mapJobUserToJobUserDto(ju, false)).convert(dto -> {
      dto.setUserLevel(userLevel.intValue());
      return dto;
    });
  }

  public Long getLeftAnnoTaskCount(Long jobId, Integer assignDataVolume, Integer repeatCount) {
    if (repeatCount == null) repeatCount = 1;
    var cacheKey = String.format(JOB_LEFT_TASK_COUNT, "ANNO", jobId);
    if (cache.get(cacheKey) != null) {
      return Long.parseLong(cache.get(cacheKey).toString());
    }
    var pCount = taskSessionDao.countTasksByJobId(jobId);
    var res = (assignDataVolume.longValue() * repeatCount) - pCount;
    cache.set(cacheKey, res, JOB_LEFT_TASK_COUNT_EXPIRE_MINUTE);
    return res;
  }

  public Long getLeftAnnoTaskCount3(
      Integer assignDataVolume, Integer repeatCount, Long jobCompleteCount, Long myPendingCount) {
    if (repeatCount == null) repeatCount = 1;
    return (assignDataVolume.longValue() * repeatCount) - jobCompleteCount - myPendingCount;
  }

  public Long getLeftAnnoTaskCount2(Long jobId, Integer assignDataVolume, Integer repeatCount) {
    if (repeatCount == null) repeatCount = 1;
    var pCount = counterManager.getJobCount(
        jobId, ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value);
    return (assignDataVolume.longValue() * repeatCount) - pCount;
  }

  public Long getLeftAnnoTaskCountForRaw(
      Long jobId, Long userId, BatchSetting batchSetting, List<Long> skippedTaskIds) {
    //    var key = String.format("job_%d:leftCountForUser:%d", jobId, userId);
    //    if (cache.hasKey(key)) return Long.parseLong(cache.get(key).toString());
    var myDidTaskIds = taskSessionDao.getMyEffectTaskSessionTaskIds(jobId, userId);
    var myExcludeIds =
        Stream.concat(myDidTaskIds.stream(), skippedTaskIds.stream()).toSet();
    var maxRepeatCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    var publicLeftFromJob = new ArrayList<>(getPublicLeftAnnoTaskForRaw(jobId, maxRepeatCount));
    if (!myExcludeIds.isEmpty()) {
      publicLeftFromJob.removeAll(myExcludeIds);
    }
    var myLeftInJobCount = (long) publicLeftFromJob.size();
    var pCount = counterManager.getJobUserCount(jobId, userId, SUBMITTED_COUNT.value);

    var userMaxWorkload = batchSetting.getWorkloadMaxPerJobUser();
    if (null == userMaxWorkload || userMaxWorkload <= 0) {
      //      cache.set(key, myLeftInJobCount, 30);
      return myLeftInJobCount;
    }
    var workloadLeft = userMaxWorkload - pCount - skippedTaskIds.size();
    var res = Math.min(workloadLeft, myLeftInJobCount);
    //    cache.set(key, res, 60);
    return res;
  }

  public Long getPublicLeftAnnoTaskCountForRaw(Long jobId, Integer maxRepeatCount) {
    return (long) getPublicLeftAnnoTaskForRaw(jobId, maxRepeatCount).size();
  }

  public List<Long> getPublicLeftAnnoTaskForRaw(Long jobId, Integer maxRepeatCount) {
    var key = String.format("job_%d:publicLeftTaskCount", jobId);
    if (cache.hasKey(key))
      return Arrays.stream(cache.get(key).toString().split(","))
          .map(Long::parseLong)
          .toList();
    var disCountMap = counterManager.hgetAllJobCount(
        jobId, ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.TASK_COUNTS.value);
    if (disCountMap.isEmpty()) return Collections.EMPTY_LIST;
    var res = disCountMap
        .entrySet()
        .filter(en -> Long.parseLong(en.getValue().toString()) < maxRepeatCount)
        .map(en -> Long.parseLong(en.getKey().toString()))
        .toList();
    if (res.isEmpty()) return Collections.EMPTY_LIST;
    cache.set(key, String.join(",", res.mapToList(Object::toString)), 15);
    return res;
  }

  public Long getLeftAnnoTaskCountForSingle(Long jobId, Long userId, BatchSetting batchSetting) {
    var userMaxWorkload = batchSetting.getWorkloadMaxPerJobUser();
    Long singleTaskId = counterManager.getJobCount(jobId, TASK_ID.value);
    var taskSessionsLeftCountObj =
        counterManager.getByKey(NewDistributionUtils.getSingleQueueKey(jobId, singleTaskId));
    var taskSessionsLeftCount = Objects.nonNull(taskSessionsLeftCountObj)
        ? Long.valueOf(taskSessionsLeftCountObj.toString())
        : 0L;
    //    var taskSessionsLeftCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint()
    //        - counterManager.getJobCount(
    //            jobId,
    //
    // ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value);
    if (null == userMaxWorkload || userMaxWorkload <= 0) {
      return taskSessionsLeftCount;
    }
    var pCount = counterManager.getJobUserCount(jobId, userId, COMPLETE_COUNT.value);
    return Math.min(taskSessionsLeftCount, userMaxWorkload - pCount);
  }

  public Long getLeftAnnoTaskCountForSingleIgnoreWorkload(Long jobId) {
    Long singleTaskId = counterManager.getJobCount(jobId, TASK_ID.value);
    var taskSessionsLeftCountObj =
        counterManager.getByKey(NewDistributionUtils.getSingleQueueKey(jobId, singleTaskId));
    return Objects.nonNull(taskSessionsLeftCountObj)
        ? Long.valueOf(taskSessionsLeftCountObj.toString())
        : 0L;
  }

  public Long getLeftReviewTaskCountForSingle(
      Long jobId, Long doneAnnotation, BatchSetting batchSetting, Long myReviewedCount) {
    var allAnnoTimes = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    return allAnnoTimes - doneAnnotation - myReviewedCount;
  }

  public Long getMyPendingTaskCount(Long jobId, Long userId) {
    return taskSessionDao.countMyPendingTasksByJobId(jobId, userId);
  }

  public List<TaskSession> getReviseSessions(Long jobId, Long curId) {
    return taskSessionDao.getReviseSessionsByJobIdAndUserId(jobId, curId);
  }

  public Long getLeftHumanReviewTaskCount(Long jobId, Long curId) {
    if (curId == null) {
      var cacheKey = String.format(JOB_LEFT_TASK_COUNT, "REVIEW", jobId);
      if (cache.get(cacheKey) != null) {
        return Long.parseLong(cache.get(cacheKey).toString());
      }
      var reviewingSessionCount = taskSessionDao.countHumanReviewSessionsByJobId(jobId);
      cache.set(cacheKey, reviewingSessionCount, JOB_LEFT_TASK_COUNT_EXPIRE_MINUTE);
      return reviewingSessionCount;
    }
    var reviewingSessionCount =
        taskSessionDao.countWaitingHumanReviewSessionsWithoutSkipping(jobId, curId);
    var waitingCount = reviewSessionDao.countUserSessionInJobWithStatus(
        jobId, curId, ReviewSession.ReviewSessionStatus.FINISH);
    return reviewingSessionCount - waitingCount;
  }

  public IPage<UserJobDTO> getAllJobByPagination(Integer page, Integer size) {

    Page<Job> jobPage = this.jobMapper.selectPage(
        Page.of(page, size),
        new QueryWrapper<Job>()
            .lambda()
            .eq(Job::getDeleted, false)
            .notIn(
                Job::getStatus,
                List.of(Job.JobStatus.DROPPED, Job.JobStatus.SETTLING, Job.JobStatus.FINISHED))
            .orderBy(true, false, Job::getCreatedAt));

    // @formatter:on
    return jobPage.convert(job -> {
      var batchCell = batchMapper.selectById(job.getBatchId());
      return UserJobDTO.builder().user(null).job(job).batch(batchCell).build();
    });
  }

  public IPage<Job> getJobByPaginationByOwner(
      Integer page,
      Integer size,
      SortType sortType,
      String name,
      List<Job.JobStatus> status,
      Long curId) {
    var myNode = nodeMapper.selectOne(new QueryWrapper<Node>()
        .lambda()
        .eq(Node::getNodeManagerId, curId)
        .eq(Node::getDeleted, false));
    if (myNode == null) {
      return Page.of(0L, 0L);
    }
    return jobMapper.selectPage(
        Page.of(page, size),
        new QueryWrapper<Job>()
            .lambda()
            .eq(Job::getNodeId, myNode.getId())
            .eq(Job::getDeleted, false)
            .in(Job::getStatus, status)
            // .ne(Job::getStatus, Constants.JobStatus.DRAFT)
            // .ne(Job::getStatus, Constants.JobStatus.DELEGATING)
            .like(!(name == null || name.isEmpty()), Job::getName, "%" + name + "%")
            .orderBy(SortType.DeadLineDesc == sortType, false, Job::getInviteAcceptanceDeadline)
            .orderBy(SortType.AccuracyAsc == sortType, true, Job::getInviteAcceptanceDeadline));
  }

  public void deleteJobById(Long id) {
    jobDao.deleteJobById(id);
  }

  public JobUser inviteJobUser(Long jobId, InviteJobUserDTO data, Long currentUserid) {
    // @formatter:off
    var jobUser = JobUser.builder()
        .role(data.getRole())
        .userId(data.getUserId())
        .taskListSessionId(jobId)
        .build();
    // @formatter:on
    jobUserMapper.insert(jobUser);
    notificationService.noticeUserNewJob(currentUserid, data.getUserId(), jobId);

    return jobUser;
  }

  public Job validateJobPermission(Long id, Long currentUid, boolean isAM) {
    var job = getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    // TODO: validate account manager id
    if (isAM) {
      return job;
    }
    if (job.getNodeId() != null) {
      var node = nodeMapper.selectById(job.getNodeId());
      if (Objects.nonNull(node) && Objects.equals(node.getNodeManagerId(), currentUid)) {
        return job;
      }
    }
    jobUserDao
        .getJobUserOptByJobAndUserId(id, currentUid)
        .ensurePresent(() -> ControllerUtils.forbidden("no perm"));
    return job;
  }

  public JobStatisticDTO getJobStatistic(Long id, Long uid) {
    var job = getJobById(id).orElseThrow(() -> ControllerUtils.notFound("job not found"));

    JobStatisticDTO res = new JobStatisticDTO();
    res.setJob(job);
    res.setBatch(batchMapper.selectById(job.getBatchId()));
    var allJobUsers = jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .eq(JobUser::getTaskListSessionId, id)
        .eq(JobUser::getDeleted, false)
        .eq(JobUser::getDisabled, false));
    var taskList = taskListDao.getTaskListById(job.getTaskListId());
    if (taskList.isEmpty() || TaskList.TaskListType.ANNOTATION != taskList.get().getListType()) {
      return res;
    }
    var taskCount = jobTaskDao.countJobTaskByJobIdList(List.of(id)).intValue();
    res.setTaskCount(taskCount);
    var allTaskSession = taskSessionMapper.selectList(new QueryWrapper<TaskSession>()
        .lambda()
        .eq(TaskSession::getJobId, id)
        .eq(TaskSession::getDeleted, false)
        .ne(TaskSession::getStatus, TaskSessionStatus.REJECTED));
    var postExamCount =
        allJobUsers.filter(ju -> ju.getRole().equals(POST_EXAM)).toList().size();
    res.setPostExamCount(postExamCount);
    var pastExamCount =
        allJobUsers.filter(ju -> ju.getRole().equals(UNALLOCATED)).toList().size();
    res.setPastExamCount(pastExamCount);

    var labelerCount =
        allJobUsers.filter(ju -> ju.getRole().equals(LABELER)).toList().size();
    var submittedAnnotations = allTaskSession.stream()
        .filter(s -> s.getStatus() != TaskSessionStatus.PENDING
            && s.getStatus() != TaskSessionStatus.REJECTED)
        .map(TaskSession::getTaskId)
        .collect(Collectors.toSet())
        .size();
    res.setWaitingForAnnotations(taskCount - submittedAnnotations);
    res.setSubmittedAnnotations(submittedAnnotations);
    res.setLabelers(labelerCount);

    var labelerFeedbacks = allTaskSession.stream()
        .filter(ts -> StringUtils.isNotBlank(ts.getFeedback()))
        .map(TaskSession::getTaskId)
        .collect(Collectors.toSet())
        .size();
    res.setLabelerFeedbacks(labelerFeedbacks);

    var reviewerCount =
        allJobUsers.filter(ju -> ju.getRole().equals(REVIEWER)).toList().size();
    res.setReviewers(reviewerCount);
    var spotterCount =
        allJobUsers.filter(ju -> ju.getRole().equals(SPOTTER)).toList().size();
    res.setSpotters(spotterCount);

    var taskSessionStat = taskSessionMapper.getTaskSessionStat(id);
    taskSessionStat = taskSessionStat == null ? TaskSessionUserStat.empty() : taskSessionStat;
    res.setSubmittedReviews(taskSessionStat.getApproved() + taskSessionStat.getRevised());
    res.setWaitingForReviews(taskSessionStat.getSessionCount() - res.getSubmittedReviews());

    res.setAudited(spotSessionDao.countAuditedSessionByJobId(id).intValue());
    res.setAuditedRevised(reviewSessionDao.getRevisedSessionCount(id).intValue());
    if (job.getStatus() == WORKING) {
      res.setUnaudited(res.getSubmittedReviews().intValue());
    } else {
      res.setUnaudited((int) Math.max(0L, res.getSubmittedReviews() - res.getAudited()));
    }
    res.setSentBackByReviewers(
        taskSessionDao.countRejectedTaskSessionsByJobId(id).intValue());
    res.setSentBackBySpotters(
        reviewSessionDao.countRejectReviewSessionsByJobId(id).intValue());
    return res;
  }

  public Optional<TaskList> getTaskListById(Long id) {
    return taskListDao.getTaskListById(id);
  }

  public List<Job> getAmAuditJobsByBatchId(Long batchId) {
    return jobMapper.selectJoinList(
        Job.class,
        JoinWrappers.lambda(Job.class)
            .selectAll(Job.class)
            .leftJoin(Batch.class, Batch::getId, Job::getBatchId)
            // .leftJoin(Project.class, Project::getId, Batch::getProjectId)
            // .eq(Project::getAccountManagerId, uid)
            .eq(Job::getBatchId, batchId)
            .eq(Job::getDeleted, false)
            .in(Job::getStatus, Arrays.asList(Job.JobStatus.AM_AUDIT, Job.JobStatus.COMMITTED))
            .orderByAsc(Job::getId));
  }

  public List<Job> listJobsByBatchId(Long batchId) {
    return jobDao.listJobsByBatchId(batchId);
  }

  public List<JobDetailsDTO> getJobsByBatchIdAndStatus(
      Long batchId, String filter, List<Job.JobStatus> status) {

    Long nodeId = StringUtils.isNumeric(filter) ? Long.valueOf(filter) : null;
    String name = StringUtils.isNumeric(filter) ? null : filter;

    return jobMapper
        .selectJoinList(
            Job.class,
            JoinWrappers.lambda(Job.class)
                .selectAll(Job.class)
                .leftJoin(Batch.class, Batch::getId, Job::getBatchId)
                .leftJoin(Node.class, Node::getId, Job::getNodeId)
                .eq(Job::getDeleted, false)
                .eq(Batch::getDeleted, false)
                .eq(!Objects.isNull(nodeId), Node::getId, nodeId)
                .like(StringUtils.isNotBlank(name), Node::getName, name)
                .eq(Job::getBatchId, batchId)
                .in(Job::getStatus, status)
                .orderByAsc(Job::getId))
        .map(x -> {
          Node node = this.nodeMapper.selectById(x.getNodeId());
          JobStatisticDTO jobStatistic = this.getJobStatistic(x.getId(), x.getNodeId());

          User user = null;
          if (!Objects.isNull(node)) {
            user = this.userMapper.selectById(node.getNodeManagerId());
          }

          return JobDetailsDTO.builder()
              .node(node)
              .job(x)
              .user(user)
              .stat(jobStatistic)
              .build();
        })
        .toList();
  }

  public Long getTimeRangeJobCount(Timestamp startTime) {
    return jobMapper.selectCount(new QueryWrapper<Job>().lambda().gt(Job::getCreatedAt, startTime));
  }

  public void nmReview(Job job, Boolean approve) {
    var status = approve ? TaskSessionAmReviewStatus.APPROVED : TaskSessionAmReviewStatus.REJECTED;
    lambdaUpdate()
        .eq(Job::getId, job.getId())
        .set(Job::getNmReviewStatus, status)
        .set(Job::getUpdatedAt, DateUtils.now())
        .update();
  }

  @Transactional
  public Job submitJob(Long id) {
    var targetJob = jobMapper.selectOne(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getId, id)
        .eq(Job::getDeleted, false)
        .last(" limit 1"));

    var unreviewedTaskCount = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
        .lambda()
        .eq(TaskSession::getJobId, targetJob.getId())
        .eq(TaskSession::getDeleted, false)
        .in(
            TaskSession::getStatus,
            TaskSessionStatus.PendingMachineReview,
            TaskSessionStatus.PendingReview));
    if (unreviewedTaskCount > 0) {
      throw ControllerUtils.badRequest("All task should be reviewed.");
    }

    var batch = batchMapper.selectById(targetJob.getBatchId());
    var reviewedCount = reviewSessionDao.countReviewSessionsByJobId(id).intValue();
    var audited = spotSessionDao.countSpotSessionByJobId(id).intValue();

    if (Objects.nonNull(batch.getSpotAudit()) && batch.getSpotAudit() > 0) {
      if (reviewedCount == 0) {
        log.info(
            "akka submitJob error, reviewedCount is zero, batch id: {}, job id: {}",
            batch.getId(),
            id);
        throw new AppException("No reviewed task session");
      }

      double spotAudit = (double) (audited * STATISTIC_DECIMALS) / reviewedCount;
      if (spotAudit < batch.getSpotAudit()) {
        throw new AppException("The spot audit does not meet the requirements");
      }
    }

    if (Objects.nonNull(batch.getRequiredAccuracy()) && batch.getRequiredAccuracy() > 0) {
      var approved = taskSessionDao.countApprovedTaskSessionsByJobId(id);
      double accuracy = (double) (approved * STATISTIC_DECIMALS) / audited;
      if (accuracy < batch.getRequiredAccuracy()) {
        throw ControllerUtils.badRequest("The accuracy does not meet the requirements");
      }
    }

    targetJob.setSubmittedAt(DateUtils.now());
    targetJob.setStatus(Job.JobStatus.AM_AUDIT);
    jobMapper.updateById(targetJob);
    var jobsInSameBatch = jobMapper.selectList(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getBatchId, targetJob.getBatchId())
        .eq(Job::getDeleted, false)
        .ne(Job::getStatus, Job.JobStatus.DROPPED));
    var jobListSubmittedByNM = jobsInSameBatch.stream()
        .filter(j -> j.getStatus().equals(Job.JobStatus.AM_AUDIT))
        .toList();
    if (jobListSubmittedByNM.size() == jobsInSameBatch.size()) {
      batchMapper.update(
          null,
          new UpdateWrapper<Batch>()
              .lambda()
              .eq(Batch::getId, targetJob.getBatchId())
              .set(Batch::getStatus, Batch.BatchStatus.AM_REVIEW));
    }
    return jobMapper.selectById(id);
  }

  public void checkAllJobStatus(Long batchId) {

    TaskList taskList = taskListDao
        .listTaskListsByBatchIdAndType(batchId, TaskList.TaskListType.ANNOTATION)
        .first();
    Long relativeTaskCount = taskMapper.selectCount(
        new QueryWrapper<Task>().lambda().eq(Task::getTaskListId, taskList.getId()));

    var jobs = jobMapper.selectList(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getBatchId, batchId)
        .eq(Job::getDeleted, false)
        .notIn(
            Job::getStatus,
            List.of(
                Job.JobStatus.NDA_SIGNING,
                Job.JobStatus.DELEGATING,
                Job.JobStatus.REJECTING,
                Job.JobStatus.REJECTED,
                Job.JobStatus.DROPPED,
                Job.JobStatus.PRE_TASK)));

    var doneJobCount = jobs.stream()
        .filter(job -> Objects.equals(job.getNmReviewStatus(), TaskSessionAmReviewStatus.APPROVED))
        .toList()
        .size();
    var jobTaskCountSum = jobs.stream().map(Job::getAssignDataVolume).reduce(0, Integer::sum);

    // all jobs are submitted by nm and these jobs contains all task of this batch.
    if (doneJobCount >= jobs.size() && jobTaskCountSum >= relativeTaskCount) {
      batchMapper.update(
          null,
          new UpdateWrapper<Batch>()
              .lambda()
              .eq(Batch::getId, batchId)
              .set(Batch::getStatus, "am_review"));
    }
  }

  // public void checkAllJobUserStatus(Long jobId) {
  // jobUserMapper.update(null, new UpdateWrapper<JobUser>().lambda()
  // .eq(JobUser::getTaskListSessionId, jobId)
  // .eq(JobUser::getDeleted, false)
  // .eq(JobUser::getDisabled, false)
  // );
  // }

  public IPage<TaskAuditForNodeManagerDTO> getAnnotationsSummary(
      Long jobId, Long currentUid, Page pager, AuditSubmissionType source) {
    var job = jobMapper.selectById(jobId).asOpt();
    if (job.isEmpty()) {
      throw ControllerUtils.badRequest("no perm");
    }
    var myNode = nodeMapper
        .selectOne(new QueryWrapper<Node>()
            .lambda()
            .eq(Node::getNodeManagerId, currentUid)
            .eq(Node::getDeleted, false))
        .asOpt();
    if (myNode.isEmpty()) {
      throw ControllerUtils.badRequest("no perm");
    }
    if (!Objects.equals(myNode.get().getId(), job.get().getNodeId())) {
      throw ControllerUtils.badRequest("no perm");
    }
    switch (source) {
      case ALL_SUBMISSION:
        IPage<TaskSession> res = taskSessionMapper.selectPage(
            pager,
            new QueryWrapper<TaskSession>()
                .lambda()
                .eq(TaskSession::getJobId, jobId)
                .eq(TaskSession::getDeleted, false)
                .in(
                    TaskSession::getStatus,
                    List.of(TaskSessionStatus.Finish, TaskSessionStatus.PendingSpot)));
        return res.convert(r -> {
          TaskAuditForNodeManagerDTO dto = new TaskAuditForNodeManagerDTO();
          dto.setSession(r);
          var reviewSession = reviewSessionDao.getReviewSessionByTaskSessionId(r.getId());
          reviewSession.ifPresent(dto::setReviewSesion);
          var spotOption = spotSessionDao.getSpotSessionByTaskSessionId(r.getId());
          spotOption.ifPresent(spotSession -> {
            dto.setAuditSession(spotSession);
            dto.setSpotter(userMapper.selectById(spotSession.getUserId()));
          });
          return dto;
        });
      case AUDIT_APPROVED:
        IPage<TaskSession> res2 = taskSessionMapper.selectJoinPage(
            pager,
            TaskSession.class,
            JoinWrappers.lambda(TaskSession.class)
                .selectAll(TaskSession.class)
                .innerJoin(SpotSession.class, SpotSession::getTaskSessionId, TaskSession::getId)
                .eq(TaskSession::getJobId, jobId)
                .eq(TaskSession::getDeleted, false)
                .eq(TaskSession::getStatus, TaskSessionStatus.Finish)
                .eq(SpotSession::getStatus, SpotSession.SpotSessionStatus.FINISH)
                .eq(SpotSession::getRevised, false));
        return res2.convert(r -> {
          TaskAuditForNodeManagerDTO dto = new TaskAuditForNodeManagerDTO();
          dto.setSession(r);
          var reviewSession = reviewSessionDao.getReviewSessionByTaskSessionId(r.getId());
          reviewSession.ifPresent(dto::setReviewSesion);
          spotSessionDao.getSpotSessionByTaskSessionId(r.getId()).ifPresent(s -> {
            dto.setAuditSession(s);
            dto.setSpotter(userMapper.selectById(s.getUserId()));
          });
          return dto;
        });
      case AUDIT_REVISED:
        IPage<TaskSession> res3 = taskSessionMapper.selectJoinPage(
            pager,
            TaskSession.class,
            JoinWrappers.lambda(TaskSession.class)
                .selectAll(TaskSession.class)
                .innerJoin(SpotSession.class, SpotSession::getTaskSessionId, TaskSession::getId)
                .leftJoin(ReviewSession.class, ReviewSession::getTaskSessionId, TaskSession::getId)
                .eq(TaskSession::getJobId, jobId)
                .eq(TaskSession::getDeleted, false)
                .eq(TaskSession::getStatus, TaskSessionStatus.Finish)
                .eq(SpotSession::getStatus, SpotSession.SpotSessionStatus.FINISH)
                .eq(ReviewSession::getRevised, true)
                .eq(ReviewSession::getDeleted, false));
        return res3.convert(r -> {
          TaskAuditForNodeManagerDTO dto = new TaskAuditForNodeManagerDTO();
          dto.setSession(r);
          var reviewSession = reviewSessionDao.getReviewSessionByTaskSessionId(r.getId());
          reviewSession.ifPresent(dto::setReviewSesion);
          spotSessionDao.getSpotSessionByTaskSessionId(r.getId()).ifPresent(s -> {
            dto.setAuditSession(s);
            dto.setSpotter(userMapper.selectById(s.getUserId()));
          });
          return dto;
        });
      case NOT_AUDITED:
        IPage<TaskSession> res4 =
            taskSessionDao.getNotAuditingTaskSessions(jobId, pager.getCurrent(), pager.getSize());
        return res4.convert(r -> {
          TaskAuditForNodeManagerDTO dto = new TaskAuditForNodeManagerDTO();
          dto.setSession(r);
          var reviewSession = reviewSessionDao.getReviewSessionByTaskSessionId(r.getId());
          reviewSession.ifPresent(dto::setReviewSesion);
          var spotOption = spotSessionDao.getSpotSessionByTaskSessionId(r.getId());
          spotOption.ifPresent(spotSession -> {
            dto.setAuditSession(spotSession);
            dto.setSpotter(userMapper.selectById(spotSession.getUserId()));
          });
          return dto;
        });
      case SENDBACK_BY_REVIEWER:
        IPage<TaskSession> res5 = taskSessionDao.listRejectedTaskSessionsByJobId(
            jobId, pager.getCurrent(), pager.getSize());
        return res5.convert(r -> {
          TaskAuditForNodeManagerDTO dto = new TaskAuditForNodeManagerDTO();
          Long rejectedTimes = taskSessionDao.countRejectedByReviewerSessionByTaskId(r.getTaskId());
          dto.setSession(r);
          dto.setSendBackTimes(rejectedTimes);
          return dto;
        });
      case SENDBACK_BY_SPOTTER:
        IPage<TaskSession> res6 = taskSessionDao.getRejectedBySpotterTaskSessions(
            jobId, pager.getCurrent(), pager.getSize());
        return res6.convert(r -> {
          TaskAuditForNodeManagerDTO dto = new TaskAuditForNodeManagerDTO();
          dto.setSession(r);
          return dto;
        });
      default:
        throw ControllerUtils.badRequest("invalid params");
    }
  }

  // 标记job为将要过期
  @Transactional
  public void preExpireJobInvitation(Job job, JobInvitation ji) {
    this.jobInvitationDao.updateJobInvitationStatus(ji, JobInvitation.JobInvitationStatus.EXPIRED);
    this.jobUserDao.deleteJobUserByJobId(job.getId());
    // this.jobTaskDao.deleteJobTasksByJobId(job.getId());

    // 注意， 我们在这里同时也更新了jobTask的状态
    // 如果后续需要更新batch的状态， 则需要调整这里， 使用batchJobManager来做二次cleanup
    this.jobDao.postExpireJobInvitation(job, ji);
  }

  public List<Job> getCommittedJobIdsByBatchId(Long batchId) {
    return jobMapper.selectList(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getBatchId, batchId)
        .eq(Job::getStatus, Job.JobStatus.COMMITTED)
        .eq(Job::getDeleted, false)
        .orderByAsc(Job::getId));
  }

  public void postExpireJobInvitation(Job job, JobInvitation ji) {
    jobDao.postExpireJobInvitation(job, ji);
  }

  public List<Job> getWorkingExpiredJobs(Integer limit) {
    var now = DateUtils.now();
    return lambdaQuery()
        .eq(Job::getDeleted, false)
        .in(Job::getStatus, List.of(WORKING, PAUSING, COMMITTED))
        .isNotNull(Job::getReviewDeadline)
        .lt(Job::getReviewDeadline, now)
        .last(" limit ${limit}")
        .list();
  }

  public List<Job> getAuditingExpiredJobs(Integer limit) {
    var now = DateUtils.now();
    return lambdaQuery()
        .eq(Job::getDeleted, false)
        .in(Job::getStatus, List.of(Job.JobStatus.AUDITING, WORKING))
        .isNotNull(Job::getNodeId)
        .lt(Job::getAuditDeadline, now)
        .last(" limit ${limit}")
        .list();
  }

  @Transactional
  public Job createJobForIndividuals(Long batchId) {
    var batch = batchMapper.selectById(batchId);
    var existJobs = jobDao.listJobsByBatchId(batchId);
    // each batch have only 1 job in individual type.
    if (!existJobs.isEmpty()) throw ControllerUtils.badRequest("job already created");
    TaskList taskList = taskListDao
        .listTaskListsByBatchIdAndType(batchId, TaskList.TaskListType.ANNOTATION)
        .first();
    var taskCount = taskDao.countTasksByTaskListId(taskList.getId());
    var job = this.createJobObjForIndividuals(batch, taskList, taskCount);

    var optSeason = seasonDao.current();
    if (optSeason.isPresent()) {
      var season = optSeason.get();
      seasonJobDao.createJob(season.getId(), job.getId(), RuleService.Version.MARCH);
    }
    var blake3 = new Blake3.Blake3_256();
    var knowledge =
        Optional.ofNullable(batch.getCertificateId()).map((c) -> List.of(c)).orElse(List.of());
    var task = PlatformActivity.Task.builder()
        .platformAddress(PLATFORM_ADDRESS)
        .id(job.getId())
        .nameHash(Hex.toHexString(blake3.digest(job.getName().getBytes(UTF_8))))
        .startedAt(job.getAcceptedAt().getTime())
        .endedAt(job.getRejectedAt().getTime())
        .dataType(
            Objects.isNull(batch.getLabelType())
                ? PlatformActivity.DataType.Other
                : PlatformActivity.DataType.FromBatchLabelType(batch.getLabelType()))
        .knowledgeRequirements(knowledge)
        .difficult(batch.getDifficulty().toInt())
        .annoPrice(batch.getAnnotatingPrice())
        .reviewPrice(batch.getReviewingPrice())
        .totalDataPoint(job.getAssignDataVolume())
        .accuracyLowerBound(batch.getRequiredAccuracy())
        .quantityOfDataPtToGainBonus(batch.getBonusMinimumSubmissions())
        .accuracyBonus(batch.getBonusPercentage())
        .build();
    var activity = PlatformActivity.builder()
        .activityType(PlatformActivity.ActivityType.Task)
        .seasonId(optSeason.map((s) -> s.getId()).orElse(null))
        .jobId(job.getId())
        .detail(task)
        .build();
    platformActivityDao.create(activity);

    return job;
  }

  private JobUser.JobUserRole getWhichRoleShouldAssign(Long jobId, Long curId) {
    var pendingHumanReviewTaskSessionCount =
        taskSessionDao.countTasksByJobIdAndStatus(jobId, TaskSessionStatus.PendingReview);
    var job = jobDao.getJobById(jobId);
    var batch = batchDao.getBatchById(job.get().getBatchId());
    var reviewRepeatCount = batch.get().getReviewingTimesReviewPerDatapoint() != null
        ? batch.get().getReviewingTimesReviewPerDatapoint()
        : 3;
    var annotateReward = batch.get().getAnnotatingPrice();
    var reviewReward = batch.get().getReviewingPrice();
    var labelerUserCount = jobUserDao.countLabelerUsersByJobId(jobId);
    var reviewUserCount = jobUserDao.countReviewerUsersByJobId(jobId);
    var batchSizeForReview =
        Optional.ofNullable(batch.get().getReviewingRequiredDatapoint()).orElse(1);
    boolean isLackOfReviewer = ((1f * labelerUserCount) / reviewUserCount)
        >= (annotateReward.floatValue() / (reviewReward.floatValue() * reviewRepeatCount));

    var cutoffTime = batchDao.getBatchSettingById(batch.get().getId()).getCutoffTime();
    var userEligibleForReview = isUserEligibleForReview(batch.get().getId(), curId);
    if (cutoffTime != null && DateUtils.now().after(cutoffTime)) {
      if (userEligibleForReview) {
        return REVIEWER;
      } else {
        return BLOCKED_BY_CUTOFF;
      }
    }
    // Role allocation/assignment rules are as follows:
    if (isLabelAndReviewThresholdSame(batch.get().getId())) {
      if (pendingHumanReviewTaskSessionCount < batchSizeForReview) {
        return LABELER;
      } else if (isLackOfReviewer) {
        return REVIEWER;
      }
    } else {
      var setting =
          systemSettingDao.getSystemSettingByName(Constants.ALLOCATE_REVIEWER_BATCH_THRESHOLD_KEY);
      long allocateReviewerThreshold = setting == null
          ? Constants.ALLOCATE_REVIEWER_BATCH_THRESHOLD_DEFAULT
          : Long.parseLong(setting.getConfig());
      if (pendingHumanReviewTaskSessionCount < (allocateReviewerThreshold * batchSizeForReview)) {
        return LABELER;
      } else if (userEligibleForReview && isLackOfReviewer) {
        return REVIEWER;
      }
    }
    return LABELER;
  }

  private boolean isLabelAndReviewThresholdSame(Long batchId) {
    var examBatchIds =
        batchAccessRequirementDao.getExamBatchAccessRequirementsByBatchId(batchId).stream()
            .map(BatchAccessRequirement::getRelationId)
            .toList();
    if (examBatchIds.isEmpty()) {
      return true;
    }

    // return true only if all exam batches have the same numerator for labeling and reviewing.
    return examBatchIds.stream().noneMatch(examBatchId -> {
      var b = batchDao.getBatchById(examBatchId).orElse(null);
      if (b == null) {
        return true;
      }
      var examNumerator = b.getExamNumerator() == null ? 0 : b.getExamNumerator();
      var examNumeratorForReviewing =
          b.getExamNumeratorForReviewing() == null ? 0 : b.getExamNumeratorForReviewing();
      return examNumeratorForReviewing != examNumerator;
    });
  }

  private boolean isUserEligibleForReview(Long batchId, Long curId) {
    var examBatchIds =
        batchAccessRequirementDao.getExamBatchAccessRequirementsByBatchId(batchId).stream()
            .map(BatchAccessRequirement::getRelationId)
            .toList();
    if (examBatchIds.isEmpty()) {
      return true;
    }
    var approvedUserReqStatuses = individualsDao
        .getUserExamRecordByBatchIds(examBatchIds, curId)
        .filter(u -> u.getStatus() == UserRequirementsStatus.UserRequireStatus.APPROVED)
        .toList();
    return approvedUserReqStatuses.stream().anyMatch(u -> {
      var b = batchDao.getBatchById(u.getRequirementTargetId()).orElse(null);
      if (b == null) {
        return true;
      }
      var examNumerator = b.getExamNumerator() == null ? 0 : b.getExamNumerator();
      var examNumeratorForReviewing =
          b.getExamNumeratorForReviewing() == null ? 0 : b.getExamNumeratorForReviewing();
      if (examNumeratorForReviewing <= examNumerator) {
        return true;
      }
      var examType = b.getExamType();
      // Regular exam size is the number of data points; QA exam size is the number of
      // questions.
      switch (examType) {
        case REGULAR -> {
          var examTaskLists = taskListDao.listTaskListsByBatchIdAndType(
              u.getRequirementTargetId(), TaskList.TaskListType.EXAM);
          if (examTaskLists.isEmpty()) {
            return true;
          }
          var examTaskSize =
              taskDao.getExamTaskByTaskListId(examTaskLists.get(0).getId()).size();
          var requiredScore = 10000.0 * examNumeratorForReviewing / examTaskSize;
          return Double.parseDouble(u.getContent()) >= requiredScore;
        }
        case QA -> {
          var qaTaskLists = taskListDao.listTaskListsByBatchIdAndType(
              u.getRequirementTargetId(), TaskList.TaskListType.LABEL);
          if (qaTaskLists.isEmpty()) {
            return true;
          }
          var taskId =
              taskDao.getExamTaskByTaskListId(qaTaskLists.get(0).getId()).get(0).getId();
          var qaExamTaskSize = taskQuestionDao.getQuestionsByTaskId(taskId).size();
          var requiredQaScore = 10000.0 * examNumeratorForReviewing / qaExamTaskSize;
          return Double.parseDouble(u.getContent()) >= requiredQaScore;
        }
        default -> {
          return true;
        }
      }
    });
  }

  public JobUser joinIndividualJob(Job job, Long curId) {
    var batch = batchDao.getBatchById(job.getBatchId());
    if (batch.get().getTaskType().equals(Batch.TaskType.TASK)) {
      var access =
          batchAccessRequirementDao.checkUserAccessToTaskId(curId, batch.get().getId());
      if (!access) {
        throw ControllerUtils.badRequest("please pass the exam first.");
      }
    } else if (batch.get().getTaskType().equals(Batch.TaskType.EXAM)) {
      var currentExamRequireStatus =
          batchAccessRequirementDao.getUserRequirement(batch.get().getId(), curId);
      if (currentExamRequireStatus
          .getStatus()
          .equals(UserRequirementsStatus.UserRequireStatus.APPROVED)) {
        throw ControllerUtils.badRequest("you passed the exam already");
      }
    }
    var bs = batchDao.getBatchSettingById(batch.get().getId());
    var requiredUserLevel = bs.getRequiredUserLevel();
    if (requiredUserLevel != null && getUserLevel(curId) < requiredUserLevel) {
      throw ControllerUtils.badRequest("please reach level " + requiredUserLevel + " to unlock");
    }
    var existsRec = jobUserDao.getJobUserByJobAndUserId(job.getId(), curId);
    if (null != existsRec) return existsRec;
    var role = LABELER;
    if (batch.get().getReviewerRequired().equals(true)
        && batch.get().getTaskType().equals(Batch.TaskType.TASK)) {
      role = getWhichRoleShouldAssign(job.getId(), curId);
    }
    JobUser jobUser = JobUser.builder()
        .userId(curId)
        .taskListSessionId(job.getId())
        .role(role)
        .active(true)
        .build();
    jobUserMapper.insert(jobUser);
    if (BLOCKED_BY_CUTOFF.equals(role)) {
      throw ControllerUtils.badRequest(
          "Sorry, there are no remaining roles available for assignment.");
    }
    return jobUser;
  }

  @Transactional
  public void createJobByPreTask(CreateJobByPreTaskDTO payload, Long currentUserid) {
    Batch batch = batchMapper
        .selectById(payload.getBatchId())
        .asOpt()
        .filter(t -> !t.getDeleted())
        .orThrow(() -> ControllerUtils.badRequest("Batch not found"))
        .filter(t -> t.getStatus() != Batch.BatchStatus.PRE_TASK_EXAM_PASSED
            || t.getStatus() != Batch.BatchStatus.ASSIGN_JOBS)
        .orElseThrow(() -> ControllerUtils.badRequest("Wrong batch status"));

    Project project = projectMapper.selectById(batch.getProjectId());

    TaskList taskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .first();

    Long relativeTaskCount = taskMapper.selectCount(
        new QueryWrapper<Task>().lambda().eq(Task::getTaskListId, taskList.getId()));

    var existJobs = jobMapper.selectList(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getBatchId, payload.getBatchId())
        .eq(Job::getDeleted, false)
        .notIn(
            Job::getStatus,
            List.of(Job.JobStatus.REJECTED, Job.JobStatus.REJECTING, Job.JobStatus.DROPPED)));

    Long existTaskCount =
        jobTaskDao.countJobTaskByJobIdList(existJobs.map(Job::getId).toList());

    if (payload
            .getAssignDataVolumes()
            .map(CreateJobInfoDTO::getAssignDataVolume)
            .reduce(0, Integer::sum)
        > (relativeTaskCount - existTaskCount)) {
      throw ControllerUtils.badRequest("Not enough task to create job");
    }

    for (int i = 1; i <= payload.getAssignDataVolumes().size(); i++) {
      CreateJobInfoDTO createJobInfoPayload = payload.getAssignDataVolumes().get(i - 1);

      Job job = this.jobMapper.selectOne(new QueryWrapper<Job>()
          .lambda()
          .eq(Job::getBatchId, batch.getId())
          .eq(Job::getNodeId, createJobInfoPayload.getNodeId())
          .eq(Job::getDeleted, false)
          .eq(Job::getStatus, Job.JobStatus.PRE_TASK));

      // 如果没有 pre task 的 job 则创建一个 job 分配给 node
      if (Objects.isNull(job)) {
        job = this.createJob(payload, batch, createJobInfoPayload, taskList, project);
        this.createJobUserByCreateJobOrAssignJob(createJobInfoPayload.getNodeId(), job);
      }

      job.setTaskListId(taskList.getId());
      // job.setName(jobName);
      job.setAssignDataVolume(createJobInfoPayload.getAssignDataVolume());
      job.setTimeSpentPerTask(payload.getTimeSpentPerTask());
      job.setReviewDeadline(payload.getReviewDeadline());
      job.setAuditDeadline(payload.getAuditDeadline());
      job.setInviteAcceptanceDeadline(payload.getInviteAcceptanceDeadline());
      job.setStatus(Job.JobStatus.DELEGATING_JOB);
      job.setSubmittedAt(DateUtils.now());
      job.setAcceptedAt(DateUtils.TIMESTAMP_NEVER);
      job.setRejectedAt(DateUtils.TIMESTAMP_NEVER);

      this.jobMapper.updateById(job);
      this.assignTask(taskList, job);
      this.createJobInvitation(
          CreateInvitationDTO.builder()
              .jobId(job.getId())
              .invitationType(JobInvitation.InvitationType.JOB)
              .nodeId(job.getNodeId())
              .build(),
          currentUserid);

      batch.setStatus(Batch.BatchStatus.ASSIGN_JOBS);
      this.batchMapper.updateById(batch);
    }
  }

  public Job create(Job job) {
    return this.jobDao.createJob(job);
  }

  private void assignTask(TaskList taskList, Job job) {

    Integer taskCount = job.getAssignDataVolume();
    Integer batchLimit = 200;
    Integer limit = batchLimit;

    while (true) {
      if (taskCount <= 0) {
        break;
      }

      if (taskCount < batchLimit) {
        limit = taskCount;
      }

      var existJobs = jobMapper.selectList(new QueryWrapper<Job>()
          .lambda()
          .eq(Job::getBatchId, job.getBatchId())
          .eq(Job::getDeleted, false)
          .notIn(
              Job::getStatus,
              List.of(Job.JobStatus.REJECTED, Job.JobStatus.REJECTING, Job.JobStatus.DROPPED)));

      List<Task> tasks = this.taskMapper.selectList(new QueryWrapper<Task>()
          .lambda()
          .eq(Task::getTaskListId, taskList.getId())
          .eq(Task::getDeleted, false)
          .notInSql(
              Task::getId,
              "SELECT task_id FROM job_task WHERE deleted = false AND job_id IN ("
                  .concat(existJobs
                      .map(Job::getId)
                      .map(String::valueOf)
                      .collect(Collectors.joining(",")))
                  .concat(")"))
          .last("limit " + limit));

      List<JobTask> jobTasks = new ArrayList<>();
      for (Task task : tasks) {
        JobTask jobTask =
            JobTask.builder().jobId(job.getId()).taskId(task.getId()).build();
        jobTasks.add(jobTask);
      }
      jobTaskDao.saveBatch(jobTasks);
      taskCount -= limit;
    }
  }

  private Job createJob(
      CreateJobByPreTaskDTO payload,
      Batch batch,
      CreateJobInfoDTO createJobInfoPayload,
      TaskList taskList,
      Project project) {

    Long existJobsCount = jobMapper.selectCount(new QueryWrapper<Job>()
            .lambda()
            .eq(Job::getBatchId, payload.getBatchId())
            .eq(Job::getDeleted, false))
        + 1;

    String jobName =
        String.format("%s#%s#%s", project.getName(), batch.getName(), "phase" + existJobsCount);

    Job job = Job.builder()
        .batchId(batch.getId())
        .taskListId(taskList.getId())
        .ownerId(batch.getOwnerId())
        .nodeId(createJobInfoPayload.getNodeId())
        .name(jobName)
        .assignDataVolume(createJobInfoPayload.getAssignDataVolume())
        .timeSpentPerTask(payload.getTimeSpentPerTask())
        .reviewDeadline(payload.getReviewDeadline())
        .auditDeadline(payload.getAuditDeadline())
        .inviteAcceptanceDeadline(payload.getInviteAcceptanceDeadline())
        .status(Job.JobStatus.DELEGATING_JOB)
        .submittedAt(DateUtils.now())
        .acceptedAt(DateUtils.TIMESTAMP_NEVER)
        .rejectedAt(DateUtils.TIMESTAMP_NEVER)
        .build();
    jobDao.createJob(job);

    return job;
  }

  private Job createJobObjForIndividuals(Batch batch, TaskList taskList, Long taskCount) {
    return jobDao.createJobObjForIndividuals(batch, taskList, taskCount);
  }

  public List<NodeDetailsDTO> waitingForAllocation(
      Long batchId, Long filterNodeId, String filterName) {

    Set<Long> nodeIds = this.preTaskExamMapper
        .selectObjs(new QueryWrapper<>(PreTaskExam.class)
            .lambda()
            .select(PreTaskExam::getNodeId)
            .eq(PreTaskExam::getBatchId, batchId)
            .eq(PreTaskExam::getDeleted, false)
            .eq(PreTaskExam::getAmPassed, PreTaskExam.Status.ACCEPTED))
        .stream()
        .mapToLong(e -> (Long) e)
        .boxed()
        .collect(toSet());

    if (CollectionUtils.isEmpty(nodeIds)) {
      return Collections.EMPTY_LIST;
    }

    return this.nodeMapper
        .selectList(new QueryWrapper<>(Node.class)
            .lambda()
            .eq(Node::getDeleted, false)
            .eq(filterNodeId != null, Node::getId, filterNodeId)
            .eq(StringUtils.isNotBlank(filterName), Node::getName, filterName)
            .in(Node::getId, nodeIds))
        .map(x -> {
          Long activeUserCount =
              this.preTaskExamMapper.selectCount(new QueryWrapper<>(PreTaskExam.class)
                  .lambda()
                  .select(PreTaskExam::getUserId)
                  .eq(PreTaskExam::getBatchId, batchId)
                  .eq(PreTaskExam::getDeleted, false)
                  .eq(PreTaskExam::getAmPassed, PreTaskExam.Status.ACCEPTED)
                  .eq(PreTaskExam::getNodeId, x.getId()));

          Job job = this.jobMapper.selectOne(new QueryWrapper<>(Job.class)
              .lambda()
              .eq(Job::getDeleted, false)
              .eq(Job::getBatchId, batchId)
              .eq(Job::getNodeId, x.getId())
              .eq(Job::getStatus, Job.JobStatus.PRE_TASK));

          UserDTO user =
              tokenValidationService.getUserById(x.getNodeManagerId()).get();

          NodeStat nodeStat = this.nodeStatDao.getStatByNode(x.getId());
          Set<Batch.BatchLabelType> dataTypes = this.nodeStatDao.getDataTypesByNodeId(x.getId());

          UserJobStat userStat = this.userJobStatDao.getUserStatByNode(x.getId());

          return NodeDetailsDTO.builder()
              .node(x)
              .activeUserCount(activeUserCount.intValue())
              .job(job)
              .user(user)
              .topics(Arrays.stream(nodeStat.getTopics().split(",")).toList())
              .nodeAccuracy(
                  nodeStat.getJobCount() == 0L
                      ? BigDecimal.ZERO.toString()
                      : BigDecimal.valueOf(nodeStat.getApprovedJobCount())
                          .divide(
                              BigDecimal.valueOf(nodeStat.getJobCount()), 2, RoundingMode.HALF_UP)
                          .toString())
              .dataTypes(dataTypes)
              .timeSpentPerTask(userStat.getTimeUsedAnswer())
              .build();
        })
        .collect(toList());
  }

  public List<NodeDetailsDTO> waitingForAllocationV2(
      Long batchId, Long filterNodeId, String filterName) {

    BatchDetailsDTO batchDetails = this.batchService.getBatchDetails(batchId);
    List<Long> examIds = batchDetails
        .getAccessRequirements()
        .filter(x -> Objects.equals(BatchAccessRequirement.Type.EXAM, x.getType()))
        .map(BatchAccessRequirement::getBatchId)
        .toList();

    // TODO: 2024/2/28 page
    Set<Long> nodeIds = new HashSet<>();
    if (CollectionUtils.isEmpty(examIds)) {
      nodeIds = this.nodeMapper
          .selectList(new QueryWrapper<>(Node.class)
              .lambda()
              .eq(Node::getDeleted, false)
              .eq(filterNodeId != null, Node::getId, filterNodeId)
              .like(StringUtils.isNotBlank(filterName), Node::getName, filterName))
          .stream()
          .map(Node::getId)
          .collect(toSet());

    } else {
      List<PreTaskExam> preTaskExams =
          this.preTaskExamMapper.selectList(new QueryWrapper<>(PreTaskExam.class)
              .lambda()
              .select(PreTaskExam::getNodeId)
              .eq(PreTaskExam::getBatchId, batchId)
              .eq(PreTaskExam::getDeleted, false)
              .eq(PreTaskExam::getAmPassed, PreTaskExam.Status.ACCEPTED));

      Map<Long, List<PreTaskExam>> groupbyPreTaskExams = preTaskExams.stream()
          .collect(groupingBy(PreTaskExam::getNodeId, mapping(Function.identity(), toList())));

      for (Map.Entry<Long, List<PreTaskExam>> e : groupbyPreTaskExams.entrySet()) {
        if (e.getValue().size() >= examIds.size()) {
          nodeIds.add(e.getKey());
        }
      }
    }

    if (CollectionUtils.isEmpty(nodeIds)) {
      return Collections.EMPTY_LIST;
    }

    return this.nodeMapper
        .selectList(new QueryWrapper<>(Node.class)
            .lambda()
            .eq(Node::getDeleted, false)
            .eq(filterNodeId != null, Node::getId, filterNodeId)
            .eq(StringUtils.isNotBlank(filterName), Node::getName, filterName)
            .in(Node::getId, nodeIds))
        .map(x -> {
          Long activeUserCount =
              this.preTaskExamMapper.selectCount(new QueryWrapper<>(PreTaskExam.class)
                  .lambda()
                  .select(PreTaskExam::getUserId)
                  .eq(PreTaskExam::getBatchId, batchId)
                  .eq(PreTaskExam::getDeleted, false)
                  .eq(PreTaskExam::getAmPassed, PreTaskExam.Status.ACCEPTED)
                  .eq(PreTaskExam::getNodeId, x.getId()));

          Job job = this.jobMapper.selectOne(new QueryWrapper<>(Job.class)
              .lambda()
              .eq(Job::getDeleted, false)
              .eq(Job::getBatchId, batchId)
              .eq(Job::getNodeId, x.getId())
              .eq(Job::getStatus, Job.JobStatus.PRE_TASK));

          UserDTO user =
              tokenValidationService.getUserById(x.getNodeManagerId()).get();

          NodeStat nodeStat = this.nodeStatDao.getStatByNode(x.getId());
          Set<Batch.BatchLabelType> dataTypes = this.nodeStatDao.getDataTypesByNodeId(x.getId());

          UserJobStat userStat = this.userJobStatDao.getUserStatByNode(x.getId());

          return NodeDetailsDTO.builder()
              .node(x)
              .activeUserCount(activeUserCount.intValue())
              .job(job)
              .user(user)
              .topics(Arrays.stream(nodeStat.getTopics().split(",")).toList())
              .nodeAccuracy(
                  nodeStat.getJobCount() == 0L
                      ? BigDecimal.ZERO.toString()
                      : BigDecimal.valueOf(nodeStat.getApprovedJobCount())
                          .divide(
                              BigDecimal.valueOf(nodeStat.getJobCount()), 2, RoundingMode.HALF_UP)
                          .toString())
              .dataTypes(dataTypes)
              .timeSpentPerTask(userStat.getTimeUsedAnswer())
              .build();
        })
        .collect(toList());
  }

  public PageResult<NodeDetailsDTO> nodes(Long batchId, String filter, Integer page, Integer size) {

    Long nodeId = StringUtils.isNumeric(filter) ? Long.valueOf(filter) : null;
    String name = StringUtils.isNumeric(filter) ? null : filter;

    List<JobInvitation> jobInvitations = this.jobInvitationMapper.selectJoinList(
        JobInvitation.class,
        JoinWrappers.lambda(JobInvitation.class)
            .selectAll(JobInvitation.class)
            .leftJoin(Job.class, Job::getId, JobInvitation::getJobId)
            .leftJoin(Node.class, Node::getId, JobInvitation::getNodeId)
            .eq(JobInvitation::getDeleted, false)
            .eq(Job::getDeleted, false)
            .eq(Node::getDeleted, false)
            .eq(Job::getBatchId, batchId)
            .in(
                JobInvitation::getStatus,
                Arrays.asList(
                    JobInvitation.JobInvitationStatus.PENDING,
                    JobInvitation.JobInvitationStatus.NdaSigning,
                    JobInvitation.JobInvitationStatus.ACCEPTED)));

    Page<Node> nodePage = this.nodeMapper.selectPage(
        Page.of(page, size),
        new QueryWrapper<>(Node.class)
            .lambda()
            .eq(Node::getDeleted, false)
            .eq(!Objects.isNull(nodeId), Node::getId, nodeId)
            .like(StringUtils.isNotBlank(name), Node::getName, name)
            .notIn(
                !jobInvitations.isEmpty(),
                Node::getId,
                jobInvitations.stream().map(JobInvitation::getNodeId).collect(toList())));

    List<NodeDetailsDTO> data = nodePage
        .getRecords()
        .map(x -> {
          NodeStat nodeStat = this.nodeStatDao.getStatByNode(x.getId());
          Set<Batch.BatchLabelType> dataTypes = this.nodeStatDao.getDataTypesByNodeId(x.getId());

          Long userCount = this.userMapper.selectCount(new QueryWrapper<>(User.class)
              .lambda()
              .eq(User::getNodeId, x.getId())
              .eq(User::getDeleted, false));

          UserJobStat userStat = this.userJobStatDao.getUserStatByNode(x.getId());
          return NodeDetailsDTO.builder()
              .node(x)
              .activeUserCount(userCount.intValue())
              .user(tokenValidationService.getUserById(x.getNodeManagerId()).get())
              .topics(Arrays.stream(nodeStat.getTopics().split(",")).toList())
              .nodeAccuracy(
                  nodeStat.getJobCount() == 0L
                      ? BigDecimal.ZERO.toString()
                      : BigDecimal.valueOf(nodeStat.getApprovedJobCount())
                          .divide(
                              BigDecimal.valueOf(nodeStat.getJobCount()), 2, RoundingMode.HALF_UP)
                          .toString())
              .dataTypes(dataTypes)
              .timeSpentPerTask(userStat.getTimeUsedAnswer())
              .build();
        })
        .collect(toList());

    return PageResult.<NodeDetailsDTO>builder()
        .pages(nodePage.getCurrent())
        .size(nodePage.getSize())
        .total(nodePage.getTotal())
        .data(data)
        .build();
  }

  public List<ExamSession> examSessions(Long batchId, Long userId) {
    return this.examSessionMapper.selectList(new QueryWrapper<>(ExamSession.class)
        .lambda()
        .eq(ExamSession::getDeleted, false)
        .eq(ExamSession::getUserId, userId)
        .eq(ExamSession::getBatchId, batchId));
  }

  public void changeDeadline(Long jobId, Timestamp auditDeadline, Timestamp reviewDeadline) {
    Job job = this.jobDao
        .getJobById(jobId)
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job not found"))
        .filter(j -> !Objects.equals(j.getStatus(), WORKING)
            || !Objects.equals(j.getStatus(), Job.JobStatus.AUDITING))
        .orElseThrow(() -> ControllerUtils.notFound("wrong job status"));

    if (Objects.nonNull(auditDeadline)) {
      job.setAuditDeadline(auditDeadline);
    }

    if (Objects.nonNull(reviewDeadline)) {
      job.setReviewDeadline(reviewDeadline);
    }

    this.jobDao.updateJobById(job);
  }

  public JobAnnotatorStatisticDTO getJobStatisticForAnnotator(Long jobId, Long currentUid) {
    var job = getJobById(jobId).orElseThrow(() -> ControllerUtils.notFound("job not found"));
    var jobUser = jobUserDao.getJobUserByJobAndUserId(jobId, currentUid);
    var userJobStat = userJobStatDao.getUserStatByJobAndUserId(currentUid, jobId);
    if (userJobStat != null)
      return JobAnnotatorStatisticDTO.builder().userJobStat(userJobStat).build();
    if (job.getStatus().equals(Job.JobStatus.PRE_TASK)) {
      taskDao.countTasksByTaskListId(job.getTaskListId());
      var sessionCount = taskSessionDao.countUserSessionInJob(jobId, currentUid);
      var spentTime = taskSessionDao.getUserWholeDuringInJob(jobId, currentUid);
      return JobAnnotatorStatisticDTO.builder()
          .completeCount(sessionCount.intValue())
          .spentTime(spentTime)
          .build();
    } else if (Arrays.asList(
            WORKING, AUDITING, AM_AUDIT, COMMITTED, SETTLING, DROPPED, REJECTED, FINISHED)
        .contains(job.getStatus())) {
      // } else if (job.getStatus().equals(Job.JobStatus.WORKING)) {
      if (jobUser.getRole().equals(LABELER)) {
        taskDao.countTasksByTaskListId(job.getTaskListId());
        var sessionCount = taskSessionDao.countUserSessionInJob(jobId, currentUid);
        var spentTime = taskSessionDao.getUserWholeDuringInJob(jobId, currentUid);
        return JobAnnotatorStatisticDTO.builder()
            .completeCount(sessionCount.intValue())
            .spentTime(spentTime)
            .build();
      } else if (jobUser.getRole().equals(REVIEWER)) {
        taskDao.countTasksByTaskListId(job.getTaskListId());
        var sessionCount = reviewSessionDao.countUserSessionInJob(jobId, currentUid);
        var spentTime = reviewSessionDao.getUserWholeDuringInJob(jobId, currentUid);
        return JobAnnotatorStatisticDTO.builder()
            .completeCount(sessionCount.intValue())
            .spentTime(spentTime)
            .build();
      }
    }
    return null;
  }

  public Boolean reportTask(Long jobId, Long curId, ReportTaskInJobDTO body) {
    jobTaskReportDao.createJobTaskReportByData(jobId, curId, body);
    return true;
  }

  public IPage<JobTaskReportItemDTO> getReportTaskList(
      Integer page, Integer limit, Long jobId, Integer status) {
    var res = jobTaskReportDao.getReportTaskList(page, limit, jobId, status);
    return res.convert(r -> {
      var item = JobTaskReportItemDTO.builder().report(r).build();
      taskDao.getTaskById(r.getTaskId()).ifPresent(item::setTask);
      userDao.getUserById(r.getUserId()).ifPresent(item::setReporter);
      return item;
    });
  }

  public MarketJobDTO getJobDetailForFinished(Long id, Long userId, Job job, JobUser ju) {
    var cacheKey = String.format("MarketTaskBasicInfoEnded:%d:%d", job.getId(), userId);
    ObjectMapper objectMapper = new ObjectMapper();
    if (cache.hasKey(cacheKey)) {
      var query = objectMapper.readValue(cache.get(cacheKey).toString(), MarketJobDTO.class);
      return query;
    }
    var res = setResBasicInfo(job);
    var bs = res.getBatchSetting();
    Calendar calendar = Calendar.getInstance();
    calendar.set(2025, Calendar.JULY, 15); // this date is in the gap
    Date date = calendar.getTime();
    var tokenRewards = getRewards(job.getBatchId());
    if (res.getJob().getCreatedAt().after(date)) {
      res.setRewards(tokenRewards);
      var tokenEarnings = newTasksDao.selectTaskRewardByUserAndJobId(userId, job.getId());
      if (!tokenEarnings.isEmpty()) {
        res.setTokenEarning(tokenEarnings);
        res.setTokenEarnedAlready(true);
      } else {
        res.setTokenEarnedAlready(false);
        var approvedCount =
            counterManager.getJobUserCount(job.getId(), userId, USER_RIGHT_COUNT.value);
        res.setTokenEarning(estEarning(
            approvedCount, tokenRewards, res.getBatch(), res.getBatchSetting(), job, ju));
      }
    } else {
      var sessionReward = individualsDao.sumJobUserPointsRecord(id, userId);
      var bonusPointsRecord = individualsDao.getJobUserBonusPointsRecord(id, userId);
      Long bonusPoints =
          bonusPointsRecord.isEmpty() ? 0 : bonusPointsRecord.get().getSessionCount();
      res.setBonusEarning(bonusPoints);
      res.setEarning(
          sessionReward == null
              ? BigDecimal.ZERO
              : sessionReward.add(BigDecimal.valueOf(bonusPoints)).setScale(1, RoundingMode.FLOOR));
    }

    res.setTaskPaymentType(bs.getTaskPaymentType());
    res.setTotalBudgetPoints(bs.getTotalBudgetPoints());
    res.setRequiredUserLevel(bs.getRequiredUserLevel());
    res.setUserLevel(getUserLevel(userId).intValue());
    res.setAnnotationMethod(bs.getAnnotationMethod());
    res.setTotalTaskCount(
        (long) job.getAssignDataVolume() * bs.getAnnotatingTimesAnnotationPerDatapoint());
    if (ju.getRole().equals(LABELER)) {
      if (!ju.getActive()
          && bs.getTaskPaymentType().equals(BatchSetting.TaskPaymentType.REWARD_POOL)) {
        res.setApprovedCount(0L);
        res.setSubmittedCount(0L);
        res.setTokenEarning(estEarning(0L, tokenRewards, res.getBatch(), bs, job, ju));
        res.setTokenEarnedAlready(false);
      } else {
        var approvedCount =
            counterManager.getJobUserCount(job.getId(), userId, USER_RIGHT_COUNT.value);
        var submittedCount =
            counterManager.getJobUserCount(job.getId(), userId, SUBMITTED_COUNT.value);
        res.setApprovedCount(approvedCount);
        res.setSubmittedCount(submittedCount);
      }
    } else if (ju.getRole().equals(REVIEWER)) {
      if (!ju.getActive()
          && bs.getTaskPaymentType().equals(BatchSetting.TaskPaymentType.REWARD_POOL)) {
        res.setApprovedCount(0L);
        res.setSubmittedCount(0L);
        res.setTokenEarning(estEarning(0L, tokenRewards, res.getBatch(), bs, job, ju));
        res.setTokenEarnedAlready(false);
      } else {
        var approvedCount =
            counterManager.getJobUserCount(job.getId(), userId, USER_RIGHT_COUNT.value);
        var submittedCount =
            counterManager.getJobUserCount(job.getId(), userId, SUBMITTED_COUNT.value);
        res.setApprovedCount(approvedCount);
        res.setSubmittedCount(submittedCount);
      }
    }
    res.setRequirements(new ArrayList<>()); // hide infos
    res.setBatchSetting(null); // hide infos
    var resultString = objectMapper.writeValueAsString(res);
    cache.set(cacheKey, resultString, CACHE_KEY_DURATION);
    return res;
  }

  public MarketJobDTO getJobDetailForPublish(Long id, Long userId, Job job, String ip) {
    var res = setResBasicInfo(job);
    var batchSetting = res.getBatchSetting();
    var requirements =
        batchAccessRequirementDao.getBatchAccessRequirementsByBatchId(job.getBatchId());
    var leftTask = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
        ? getLeftAnnoTaskCountForSingle(id, userId, batchSetting)
        : getPublicLeftAnnoTaskCountForRaw(
            id, batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
    res.setJobLeftTaskCount(Math.max(leftTask, 0));
    res.setRequirements(requirements);
    res.setRewards(getRewards(job.getBatchId()));
    fetchUserRequirementStatus(userId, requirements, res, job.getId());
    res.setBatchSetting(null); // hide infos
    res.setWorkloadType(workloadLimitService.getIpAndUserIdLimitWorkloadType(userId, ip));
    res.setTotalBudgetPoints(batchSetting.getTotalBudgetPoints());
    res.setTotalTaskCount(
        (long) job.getAssignDataVolume() * batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
    res.setTaskPaymentType(batchSetting.getTaskPaymentType());
    res.setStakingRequest(batchSetting.getStakingRequest());
    res.setRequiredUserLevel(batchSetting.getRequiredUserLevel());
    res.setUserLevel(getUserLevel(userId).intValue());
    res.setAnnotationMethod(batchSetting.getAnnotationMethod());
    var completedCount = counterManager.getJobCount(job.getId(), COMPLETE_COUNT.value);
    res.setCompleteCount(completedCount);
    return res;
  }

  public MarketJobDTO setResBasicInfo(Job job) {
    ObjectMapper objectMapper = new ObjectMapper();
    var cacheKey = String.format("MarketTaskBasicInfo:%d", job.getId());
    if (cache.hasKey(cacheKey)) {
      var query = objectMapper.readValue(cache.get(cacheKey).toString(), MarketJobDTO.class);
      return query;
    }
    var res = MarketJobDTO.builder().build();
    var b = batchMapper.selectById(job.getBatchId());
    var p = projectMapper.selectById(b.getProjectId());
    var batchSetting = batchDao.getBatchSettingById(job.getBatchId());
    res.setJob(job);
    res.setBatch(b);
    res.setBatchSetting(batchSetting);
    res.setProject(p);

    var resultString = objectMapper.writeValueAsString(res);
    cache.set(cacheKey, resultString, CACHE_KEY_DURATION);
    return res;
  }

  private List<RewardTokenDTO> getRewards(Long batchId) {
    var rewardRecords = batchRewardRecordDao.listByBatchId(batchId);
    List<RewardTokenDTO> rewards = new ArrayList<>();
    if (!rewardRecords.isEmpty()) {
      var tokensMap = rewardTokenInfoDao
          .findByIds(rewardRecords.mapToList(BatchRewardRecord::getTokenInfoId))
          .toMap(RewardTokenInfo::getId, Function.identity());
      rewards.addAll(rewardRecords.stream()
          .map(r -> RewardTokenDTO.builder()
              .recordId(r.getId())
              .tokenInfoId(r.getTokenInfoId())
              .amount(r.getAmount())
              .rewardTokenType(r.getRewardTokenType())
              .tokenName(tokensMap.get(r.getTokenInfoId()).getTokenName())
              .tokenIconResourceId(tokensMap.get(r.getTokenInfoId()).getResourceId())
              .tokenIconUrl(tokensMap.get(r.getTokenInfoId()).getIconUrl())
              .decimals(tokensMap.get(r.getTokenInfoId()).getTokenDecimal())
              .tgeAlready(tokensMap.get(r.getTokenInfoId()).getTgeAlready())
              .build())
          .toList());
      return rewards;
    }
    return List.of();
  }

  public MarketJobDTO getJobForIndividuals(Long id, Long userId, Job j, JobUser ju, String userIp) {
    var res = setResBasicInfo(j);
    var b = res.getBatch();
    var batchSetting = res.getBatchSetting();
    var requirements =
        batchAccessRequirementDao.getBatchAccessRequirementsByBatchId(j.getBatchId());
    var ndas = ndaRecordDao.getMySignedNdaInBatch(j.getBatchId(), userId);
    res.setRequirements(requirements);
    fetchUserRequirementStatus(userId, requirements, res, j.getId());
    res.setJobUser(ju);
    res.setNdaSignRecordList(ndas);
    var tokenRewards = getRewards(j.getBatchId());
    res.setRewards(tokenRewards);
    var tokenEarnings = newTasksDao.selectTaskRewardByUserAndJobId(userId, j.getId());
    if (!tokenEarnings.isEmpty()) {
      res.setTokenEarning(tokenEarnings);
      res.setTokenEarnedAlready(true);
    }

    var userTempBan = individualsDao.getUserBansByUserId(userId);
    if (userTempBan.isPresent()) {
      var userBanStartTime = userTempBan.get().getCreatedAt();
      var banExpireDuration =
          DateUtils.diff(DateUtils.add(userBanStartTime, Duration.ofDays(7)), DateUtils.now());
      res.setTempBanExpire(banExpireDuration);
    }
    res.setWorkloadType(workloadLimitService.getIpAndUserIdLimitWorkloadType(userId, userIp));
    res.setTotalBudgetPoints(batchSetting.getTotalBudgetPoints());
    res.setTaskPaymentType(batchSetting.getTaskPaymentType());
    res.setRequiredUserLevel(batchSetting.getRequiredUserLevel());
    res.setUserLevel(getUserLevel(userId).intValue());
    res.setAnnotationMethod(batchSetting.getAnnotationMethod());
    var completedCount = counterManager.getJobCount(j.getId(), COMPLETE_COUNT.value);
    res.setCompleteCount(completedCount);
    res.setStakingRequest(batchSetting.getStakingRequest());
    if (ju.getRole().equals(LABELER)) {
      if (!ju.getActive()
          && batchSetting.getTaskPaymentType().equals(BatchSetting.TaskPaymentType.REWARD_POOL)) {
        res.setTokenEarning(estEarning(0L, tokenRewards, b, batchSetting, j, ju));
        res.setTokenEarnedAlready(false);
        res.setApprovedCount(0L);
        res.setWaitingReviewCount(0L);
        res.setRevisedCount(0L);
        res.setSubmittedCount(0L);
        res.setReviseCountForLabeler(0L);
      } else {
        var approvedCount =
            counterManager.getJobUserCount(j.getId(), userId, USER_RIGHT_COUNT.value);
        if (!Boolean.TRUE.equals(res.getTokenEarnedAlready())) {
          res.setTokenEarning(estEarning(approvedCount, tokenRewards, b, batchSetting, j, ju));
          res.setTokenEarnedAlready(false);
        }
        var submittedCount =
            counterManager.getJobUserCount(j.getId(), userId, SUBMITTED_COUNT.value);

        var havePendingCount = taskSessionDao.countMyPendingTasksByJobId(id, userId);
        var skippedRecs = skipTaskSessionDao.getUserSkippedTaskSessionIdListInJob(id, userId);
        var skippedCount = skippedRecs.size();
        var jobLeftTaskCount =
            batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
                ? getLeftAnnoTaskCountForSingle(id, userId, batchSetting)
                : getLeftAnnoTaskCountForRaw(
                    id, userId, batchSetting, skippedRecs.mapToList(SkipTaskSession::getTaskId));
        res.setApprovedCount(approvedCount);

        var userTaskSessionStatusList = taskSessionDao.getUserTaskSessionsStatusList(ju.getId());
        AtomicInteger waitingReviewCount = new AtomicInteger();
        AtomicInteger rejectedByMajorityVoteCount = new AtomicInteger();
        AtomicInteger rejectedByMajorityVoteAndWaitingRevisionCount = new AtomicInteger();
        userTaskSessionStatusList.stream().forEach(statusCell -> {
          if (Boolean.FALSE.equals(statusCell.getDeleted())) {
            if (TaskSessionStatus.PendingReview.equals(statusCell.getStatus())) {
              waitingReviewCount.addAndGet(1);
            }
          }
          if (TaskSessionStatus.REJECTED.equals(statusCell.getStatus())
              && TaskSession.RejectedBy.MAJORITY_VOTE.equals(statusCell.getRejectedBy())
              && TaskSession.TaskSessionReviewStatus.REVISED.equals(
                  statusCell.getReviewerStatus())) {
            if (Boolean.FALSE.equals(statusCell.getDeleted())) {
              rejectedByMajorityVoteAndWaitingRevisionCount.addAndGet(1);
            }
            rejectedByMajorityVoteCount.addAndGet(1);
          }
        });
        res.setWaitingReviewCount(waitingReviewCount.longValue());
        res.setRevisedCount(rejectedByMajorityVoteCount.longValue());
        res.setSubmittedCount(submittedCount);
        var revisedSessions = getReviseSessions(ju.getTaskListSessionId(), ju.getUserId());
        if (userTempBan.isEmpty()) {
          if (havePendingCount <= 0) {
            res.setReviseCountForLabeler((long) revisedSessions.size());
          } else {
            res.setPendingCountForLabeler(havePendingCount);
          }
          res.setJobLeftTaskCount(Math.max(
              (jobLeftTaskCount
                  + rejectedByMajorityVoteAndWaitingRevisionCount.get()
                  + havePendingCount),
              0));

          var skipQuota =
              Math.floor(submittedCount / 50f) * 5 + rejectedByMajorityVoteCount.get() + 5;
          res.setLeftSkipQuota(Double.valueOf(skipQuota).intValue() - skippedCount);
        }
        res.setWorkloadType(workloadLimitService.getJobUserWorkloadType(ju, LABELER, userIp));
        if (res.getReviseCountForLabeler() != null
            && res.getReviseCountForLabeler() > 0L
            && res.getWorkloadType().equals(WorkloadType.LABELER_TASK_SYBIL)) {
          workloadLimitService.deleteRevisedSessions(revisedSessions);
          res.setReviseCountForLabeler(0L);
        }
      }
      var totalTaskCount =
          j.getAssignDataVolume() * batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
      res.setTotalTaskCount((long) totalTaskCount);
    } else if (ju.getRole().equals(REVIEWER)) {
      if (!ju.getActive()
          && batchSetting.getTaskPaymentType().equals(BatchSetting.TaskPaymentType.REWARD_POOL)) {
        res.setTokenEarning(estEarning(0L, tokenRewards, b, batchSetting, j, ju));
        res.setTokenEarnedAlready(false);
        res.setApprovedCount(0L);
        res.setWaitingReviewCount(0L);
        res.setRevisedCount(0L);
        res.setSubmittedCount(0L);
        res.setPendingCountForReviewer(0L);
      } else {
        var approvedCount =
            counterManager.getJobUserCount(j.getId(), userId, USER_RIGHT_COUNT.value);
        var userCompleteCount =
            counterManager.getJobUserCount(j.getId(), userId, COMPLETE_COUNT.value);
        var submittedCount =
            counterManager.getJobUserCount(j.getId(), userId, SUBMITTED_COUNT.value);
        if (!Boolean.TRUE.equals(res.getTokenEarnedAlready())) {
          res.setTokenEarning(estEarning(approvedCount, tokenRewards, b, batchSetting, j, ju));
          res.setTokenEarnedAlready(false);
        }
        var myPendingCount = reviewSessionDao.countUserSessionInJobWithStatus(
            ju.getTaskListSessionId(), ju.getUserId(), ReviewSession.ReviewSessionStatus.PENDING);
        var waitingCount = ju.getActive() ? (submittedCount - userCompleteCount) : 0L;

        var leftCount = taskSessionDao.countReviewJobsForUser(
            ju.getTaskListSessionId(), ju.getUserId(), b.getReviewingTimesReviewPerDatapoint());
        res.setJobLeftTaskCount(Math.max(0, (leftCount + myPendingCount)));
        res.setApprovedCount(approvedCount);
        var wrongCount = userCompleteCount - approvedCount;
        res.setRevisedCount(wrongCount);
        if (userTempBan.isEmpty()) {
          var leftTask = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
              ? getLeftReviewTaskCountForSingle(
                  id, completedCount, batchSetting, submittedCount - userCompleteCount)
              : getLeftAnnoTaskCount3(
                  j.getAssignDataVolume(),
                  batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
                  completedCount,
                  submittedCount - userCompleteCount);
          res.setJobLeftAnnotateCountForReviewer(leftTask);
          res.setWaitingReviewCount(waitingCount);
          res.setSubmittedCount(submittedCount);
          var skipQuota = Math.floor(submittedCount / 50f) * 5 + 5;
          var skippedCount = skipTaskSessionDao.getSkippedCount(id, userId);
          res.setLeftSkipQuota(Double.valueOf(skipQuota).intValue() - skippedCount.intValue());
          var havePendingReviewCount = reviewSessionDao.countMyPendingTasksByJobId(id, userId);
          res.setPendingCountForReviewer(havePendingReviewCount);
        }
        res.setWorkloadType(workloadLimitService.getJobUserWorkloadType(ju, REVIEWER, userIp));
      }
      var totalTaskCount =
          j.getAssignDataVolume() * batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
      res.setTotalTaskCount((long) totalTaskCount);
    }
    res.setBatchSetting(null);
    return res;
  }

  private List<UserTokenTaskRewards> estEarning(
      Long approvedCount,
      List<RewardTokenDTO> tokenRewards,
      Batch b,
      BatchSetting batchSetting,
      Job j,
      JobUser ju) {
    if (tokenRewards.isEmpty()) {
      return List.of();
    }
    var role = ju.getRole();
    var res = new ArrayList<UserTokenTaskRewards>();
    if (approvedCount <= 0L) {
      tokenRewards.forEach(tokenReward -> {
        res.add(UserTokenTaskRewards.builder()
            .userId(ju.getUserId())
            .jobId(j.getId())
            .rewardTokenType(tokenReward.getRewardTokenType())
            .thirdPartyTokenId(tokenReward.getTokenInfoId())
            .amount(BigDecimal.ZERO)
            .build());
      });
      return res;
    }
    tokenRewards.forEach(tokenReward -> {
      var taskTokenPriceCacheKey =
          String.format(TASK_TOKEN_PRICE, role.getValue(), tokenReward.getRecordId());
      var lPrice = cache.hasKey(taskTokenPriceCacheKey)
          ? BigDecimal.valueOf(
              Double.parseDouble(cache.get(taskTokenPriceCacheKey).toString()))
          : calcPrice(j, b, batchSetting, role);
      cache.set(taskTokenPriceCacheKey, lPrice.doubleValue(), 24L * 60 * 60);
      res.add(UserTokenTaskRewards.builder()
          .userId(ju.getUserId())
          .jobId(j.getId())
          .rewardTokenType(tokenReward.getRewardTokenType())
          .thirdPartyTokenId(tokenReward.getTokenInfoId())
          .amount(BigDecimal.valueOf(approvedCount)
              .multiply(lPrice)
              .multiply(tokenReward.getAmount())
              .setScale(0, RoundingMode.FLOOR))
          .build());
    });
    return res;
  }

  private BigDecimal calcPrice(
      Job j, Batch b, BatchSetting batchSetting, JobUser.JobUserRole role) {
    var totalTaskCount =
        j.getAssignDataVolume() * batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    if (!b.getReviewerRequired()) {
      return BigDecimal.ONE.divide(
          BigDecimal.valueOf(totalTaskCount), new MathContext(5, RoundingMode.FLOOR));
    }
    var labelerPercentage = b.getAnnotatingPrice()
        .divide(
            b.getReviewingPrice()
                .multiply(BigDecimal.valueOf(b.getReviewingTimesReviewPerDatapoint()))
                .add(b.getAnnotatingPrice()),
            8,
            RoundingMode.FLOOR);
    if (role == LABELER) {
      return BigDecimal.ONE
          .multiply(labelerPercentage)
          .divide(BigDecimal.valueOf(totalTaskCount), 5, RoundingMode.FLOOR);
    }
    return BigDecimal.ONE
        .multiply(BigDecimal.ONE.subtract(labelerPercentage))
        .divide(BigDecimal.valueOf(totalTaskCount), 5, RoundingMode.FLOOR);
  }

  public void assignJobToExternalBatchJob(Batch batch) {
    if (batch.getUserType().equals(Batch.UserType.EXTERNAL_LABELER)) {
      checkBatchHasCertificatesOrExam(batch);
      var jobExist = jobDao.listJobsByBatchId(batch.getId());
      TaskList taskList = taskListDao
          .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
          .first();
      if (jobExist.isEmpty()) {
        var taskCount = taskDao.countTasksByTaskListId(taskList.getId());
        var job = jobDao.createJobObjForIndividuals(batch, taskList, taskCount);

        var optSeason = seasonDao.current();
        if (optSeason.isPresent()) {
          var season = optSeason.get();
          seasonJobDao.createJob(season.getId(), job.getId(), RuleService.Version.MARCH);
        }
        var blake3 = new Blake3.Blake3_256();
        var knowledge =
            Optional.ofNullable(batch.getCertificateId()).map((c) -> List.of(c)).orElse(List.of());
        var task = PlatformActivity.Task.builder()
            .platformAddress(PLATFORM_ADDRESS)
            .id(job.getId())
            .nameHash(Hex.toHexString(blake3.digest(job.getName().getBytes(UTF_8))))
            .startedAt(job.getAcceptedAt().getTime())
            .endedAt(job.getRejectedAt().getTime())
            .dataType(
                Objects.isNull(batch.getLabelType())
                    ? PlatformActivity.DataType.Other
                    : PlatformActivity.DataType.FromBatchLabelType(batch.getLabelType()))
            .knowledgeRequirements(knowledge)
            .difficult(batch.getDifficulty().toInt())
            .annoPrice(batch.getAnnotatingPrice())
            .reviewPrice(batch.getReviewingPrice())
            .totalDataPoint(job.getAssignDataVolume())
            .accuracyLowerBound(batch.getRequiredAccuracy())
            .quantityOfDataPtToGainBonus(batch.getBonusMinimumSubmissions())
            .accuracyBonus(batch.getBonusPercentage())
            .build();
        var activity = PlatformActivity.builder()
            .activityType(PlatformActivity.ActivityType.Task)
            .seasonId(optSeason.map((s) -> s.getId()).orElse(null))
            .jobId(job.getId())
            .detail(task)
            .build();
        platformActivityDao.create(activity);
      }
      var jobExistAgain = jobDao.listJobsByBatchId(batch.getId());
      var jobTaskCount =
          jobTaskDao.countJobTaskByJobIdList(Arrays.asList(jobExistAgain.first().getId()));
      if (jobTaskCount <= 0) {
        this.assignTask(taskList, jobExistAgain.first());
      }
    }
  }

  private void checkBatchHasCertificatesOrExam(Batch batch) {
    var reqs = batchAccessRequirementDao.getExamBatchAccessRequirementsByBatchId(batch.getId());
    var hasExam = reqs.stream()
        .filter(rq -> rq.getType().equals(BatchAccessRequirement.Type.EXAM))
        .findFirst();
    var hasAcademy = reqs.stream()
        .filter(rq -> rq.getType().equals(BatchAccessRequirement.Type.ACADEMY))
        .findFirst();
    if (hasExam.isPresent() || hasAcademy.isPresent()) {
      hasExam.ifPresent((nil) -> batch.setHaveExam(true));
      hasAcademy.ifPresent((nil) -> batch.setHaveCertificate(true));
      batchDao.updateById(batch);
    }
  }

  public List<Job> selectWorkingIndividualJobs(Integer limit) {
    return jobMapper.selectList(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getJobType, Job.JobType.INDIVIDUAL)
        .in(Job::getStatus, Arrays.asList(WORKING, COMMITTED)) // FINISHED status will count by
        // submit-revise
        .eq(Job::getDeleted, false)
        .last(limit != null, " last ${limit}"));
  }

  public Job getJobByTaskListId(Long id) {
    return jobMapper.selectOne(
        new QueryWrapper<Job>().lambda().eq(Job::getTaskListId, id).eq(Job::getDeleted, false));
  }

  public Optional<Job> updateJobNameById(Long jobId, String name) {
    lambdaUpdate()
        .eq(Job::getId, jobId)
        .set(Job::getName, name)
        .set(Job::getUpdatedAt, DateUtils.now())
        .update();
    return jobDao.getJobById(jobId);
  }

  public List<Job> getIndividualJobByStatus(List<Job.JobStatus> statuses) {
    return jobMapper.selectList(new QueryWrapper<Job>()
        .lambda()
        .eq(Job::getJobType, Job.JobType.INDIVIDUAL)
        .in(Job::getStatus, statuses)
        .eq(Job::getDeleted, false));
  }

  public List<Job> reQueue() {
    return jobMapper.selectJoinList(
        Job.class,
        JoinWrappers.lambda(Job.class)
            .selectAll(Job.class)
            .leftJoin(Batch.class, Batch::getId, Job::getBatchId)
            .eq(Job::getDeleted, false)
            .eq(Batch::getDeleted, false)
            .in(Job::getStatus, List.of(Job.JobStatus.WORKING, Job.JobStatus.COMMITTED))
            .eq(Batch::getTaskType, Batch.TaskType.TASK));
  }

  public List<Job> getJobsByBatchId(Long batchId) {
    return jobMapper.selectList(
        new QueryWrapper<Job>().lambda().eq(Job::getBatchId, batchId).eq(Job::getDeleted, false));
  }

  public Set<Long> getJoinedSpecialTaskSet(String achieveSymbol) {
    var joinedCacheKey = String.format(
        Constants.SpecialTaskAchievementSymbol.CACHE_KEY_FORMATTER_FOR_MEMBER, achieveSymbol);
    var specialTaskList = getSpecialTaskListByStatus(
        joinedCacheKey, achieveSymbol, Arrays.asList(WORKING, COMMITTED));
    if (specialTaskList.isEmpty()) {
      return new HashSet<>();
    }
    return specialTaskList.map(Job::getId).toSet();
  }

  public Set<Long> getAllSpecialTaskLikeSet(String achieveSymbol) {
    var joinedCacheKey = String.format(
        Constants.SpecialTaskAchievementSymbol.CACHE_KEY_FORMATTER_FOR_ORACLE, achieveSymbol);
    var specialTaskList = getSpecialTaskListLikeByStatus(joinedCacheKey, achieveSymbol, null);
    if (specialTaskList.isEmpty()) {
      return new HashSet<>();
    }
    return specialTaskList.map(Job::getId).toSet();
  }

  public void pauseTask(Long id) {
    jobDao.setStatus(id, PAUSING);
    var job = jobDao.getJobById(id);
    job.ifPresent(value -> doNotifyTaskPauseOrResume(id, value.getName(), true));
  }

  private void doNotifyTaskPauseOrResume(Long id, String jobName, Boolean isPauseNotResume) {
    Long cursorId = 0L;
    String title =
        isPauseNotResume ? "Your Task Has Been Paused" : "Your Task Is Now Available Again";
    String message = isPauseNotResume
        ? "Your current task %s has been temporarily paused as part of ongoing platform operations. You won’t be able to continue working on it at the moment. We’ll notify you once it’s available again. Thanks for your understanding."
        : "Your task %s is now available again. You can continue working on it at your convenience. Thank you for being part of Sahara!";

    while (Objects.nonNull(cursorId)) {
      var jobUsersUserIdList = jobUserDao.getActiveJobUsersByBatch(id, cursorId);
      if (!jobUsersUserIdList.isEmpty()) {
        cursorId = jobUsersUserIdList.last().getId();
        var notifyList = jobUsersUserIdList
            .map(ju -> Notification.builder()
                .toUserId(ju.getUserId())
                .title(title)
                .message(String.format(message, jobName))
                .meme("task pause or resumed")
                .notificationType(Notification.NotificationType.USER_JOB_PAUSED_OR_RESUMED)
                .param(Notification.UserJobPausedOrResumed.builder()
                    .jobId(id)
                    .jobName(jobName)
                    .isPaused(isPauseNotResume)
                    .build())
                .build())
            .toList();
        notificationService.createNotifications(notifyList);
      } else {
        cursorId = null;
      }
    }
  }

  public void resumeBatch(Long id) {
    jobDao.setStatus(id, WORKING);
    var job = jobDao.getJobById(id);
    job.ifPresent(value -> doNotifyTaskPauseOrResume(id, value.getName(), false));
  }

  @Transactional
  public void terminalBatch(Long id) {
    var now = DateUtils.now();
    var job = jobDao.getJobById(id).orElseThrow(() -> ControllerUtils.forbidden("invalid data."));
    job.setReviewDeadline(now);
    job.setAuditDeadline(now);
    job.setPreTaskDeadline(now);
    var batch = batchDao
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.forbidden("invalid data."));
    batch.setDeadline(now);
    jobDao.updateJobById(job);
    batchDao.updateById(batch);
  }

  private Long getUserLevel(Long userId) {
    var level = userLevelFromSubgraphDao.getUserLevelRecordFromSubgraph(userId);
    return level.isPresent() ? level.get().getLevel() : 0L;
  }

  public Integer queryUserStaking(String userAddr) throws IOException {
    OkHttpClient client = new OkHttpClient();
    String query =
        """
          query GetUserStaking {
              userStakedAmountChangeds(where: { user: "%s" }, orderBy: blockTimestamp, orderDirection: desc, first: 1) {
                user totalStaked blockTimestamp
              }
            }
      """
            .formatted(userAddr);
    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("query", query);
    String jsonBody = new ObjectMapper().writeValueAsString(requestBody);
    Request request = new Request.Builder()
        .url(onChainStakingUrl)
        .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
        .addHeader("accept", "application/graphql-response+json, application/json, multipart/mixed")
        .addHeader("cache-control", "no-cache")
        .addHeader("content-type", "application/json")
        .build();
    Response response = client.newCall(request).execute();
    if (!response.isSuccessful()) {
      return 0;
    }
    var objectMapper = new ObjectMapper();
    var qs = objectMapper.readValue(response.body().string(), StakingOnchainDataDTO.class);
    var record = qs.getData().getUserStakedAmountChangeds().stream().findFirst();
    return record
        .map(stakingOnchainItemContentDTO -> BigInteger.valueOf(
                Long.parseLong(stakingOnchainItemContentDTO.getTotalStaked()))
            .divide(BigInteger.TEN.pow(18))
            .intValue())
        .orElse(0);
  }

  public Integer getStakingAmount(Long curId) {
    var userStakingKey = String.format("staking_user:%d", curId);
    if (cache.hasKey(userStakingKey)) {
      return Integer.parseInt(cache.get(userStakingKey).toString());
    }
    var u =
        userDao.getUserById(curId).orElseThrow(() -> ControllerUtils.forbidden("invalid data."));
    var addr = u.getWalletAddress().toLowerCase();
    var res = queryUserStaking(addr);
    cache.set(userStakingKey, userStakingKey, 3 * 60L);
    return res;
  }
}
