package ai.saharaa.services.newTasks;

import static ai.saharaa.model.newTasks.BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD;
import static ai.saharaa.model.newTasks.BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD_READY;
import static ai.saharaa.model.newTasks.UserTokenTaskRewards.UserRewardStatus.PENDING_CLAIM;

import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.common.contracts.DSPReward;
import ai.saharaa.config.BinanceApiConfig;
import ai.saharaa.daos.JobDao;
import ai.saharaa.daos.ReviewSessionDao;
import ai.saharaa.daos.TaskSessionDao;
import ai.saharaa.daos.UserDao;
import ai.saharaa.daos.newTasks.BatchRewardRecordDao;
import ai.saharaa.daos.newTasks.RewardTokenInfoDao;
import ai.saharaa.daos.newTasks.UserTokenTaskRewardsDao;
import ai.saharaa.dto.reward.*;
import ai.saharaa.enums.RewardTokenType;
import ai.saharaa.enums.SortType;
import ai.saharaa.model.Job;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.newTasks.*;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URI;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
@Slf4j
public class NewRewardService {

  private final RewardTokenInfoDao rewardTokenInfoDao;
  private final TaskSessionDao taskSessionDao;
  private final UserTokenTaskRewardsDao userTokenTaskRewardsDao;
  private final ObjectMapper objectMapper;
  private final ReviewSessionDao reviewSessionDao;
  private final BinanceApiConfig binanceApiConfig;
  private final RestTemplate restTemplate;
  private final BatchRewardRecordDao batchRewardRecordDao;
  private final JobDao jobDao;
  private final DSPReward dspRewardContract;
  private final IGlobalCache cache;
  private final UserDao userDao;
  private final String rewardGraphqlUrl;

  public NewRewardService(
      IGlobalCache cache,
      RewardTokenInfoDao rewardTokenInfoDao,
      TaskSessionDao taskSessionDao,
      UserTokenTaskRewardsDao userTokenTaskRewardsDao,
      ObjectMapper objectMapper,
      ReviewSessionDao reviewSessionDao,
      BinanceApiConfig binanceApiConfig,
      BatchRewardRecordDao batchRewardRecordDao,
      JobDao jobDao,
      @Value("${ai.saharaa.web3.dsp_reward_graphql}") String rewardGraphqlUrl,
      DSPReward dspRewardContract,
      UserDao userDao) {
    this.rewardTokenInfoDao = rewardTokenInfoDao;
    this.taskSessionDao = taskSessionDao;
    this.userTokenTaskRewardsDao = userTokenTaskRewardsDao;
    this.objectMapper = objectMapper;
    this.reviewSessionDao = reviewSessionDao;
    this.binanceApiConfig = binanceApiConfig;
    this.restTemplate = new RestTemplateBuilder()
        .connectTimeout(Duration.ofSeconds(5))
        .readTimeout(Duration.ofSeconds(10))
        .build();
    this.batchRewardRecordDao = batchRewardRecordDao;
    this.jobDao = jobDao;
    this.cache = cache;
    this.dspRewardContract = dspRewardContract;
    this.userDao = userDao;
    this.rewardGraphqlUrl = rewardGraphqlUrl;
  }

  private static final String TOKEN_NAME_SAHARA = "SAHARA";
  private static final String TOKEN_NAME_USD1 = "USD1";

  // Cache for exchange rates - expires after 1 minute
  private final Cache<String, Map<String, BigDecimal>> exchangeRateCache = Caffeine.newBuilder()
      .expireAfterWrite(Duration.ofMinutes(1))
      .maximumSize(10)
      .build();

  // Fallback cache for exchange rates - expires after 1 hour, used when API fails
  private final Cache<String, Map<String, BigDecimal>> fallbackExchangeRateCache =
      Caffeine.newBuilder()
          .expireAfterWrite(Duration.ofHours(1))
          .maximumSize(10)
          .build();

  public MyEarningsDTO getMyEarnings(Long userId) {
    // Load user data and token info
    var mainTokenType = Set.of(RewardTokenType.USD1, RewardTokenType.SAHARA);
    var userTokenTaskRewards = userTokenTaskRewardsDao.getUserTokenTaskRewards(userId);
    if (userTokenTaskRewards.isEmpty()) {
      return MyEarningsDTO.builder()
          .estTotalEarningsSAH("0")
          .estTotalEarningsUSD1("0")
          .estTotalEarningsUSD("0")
          .approvedDatapoints(0L)
          .tokenEarnings(List.of())
          .build();
    }
    var recJobIds = userTokenTaskRewards.map(UserTokenTaskRewards::getJobId).toSet();
    var jobsMapByBatchId =
        jobDao.getJobByIds(recJobIds).toMap(Job::getBatchId, Function.identity());
    var batchRrMap = batchRewardRecordDao
        .listByJobIdsAndType(recJobIds, mainTokenType)
        .toMap(
            r -> String.format(
                "%d-%d",
                jobsMapByBatchId.get(r.getBatchId()).getId(),
                r.getRewardTokenType().getValue()),
            Function.identity());
    var tokenInfoMap = rewardTokenInfoDao
        .findByTokenTypes(List.of(RewardTokenType.USD1, RewardTokenType.SAHARA))
        .toMap(RewardTokenInfo::getRewardTokenType, Function.identity());

    // Get exchange rates for all tokens
    Map<String, BigDecimal> exchangeRates = getExchangeRatesForAllTokens(tokenInfoMap);

    // Process token earnings - filter to only include USD1 and SAHARA
    List<MyEarningsDTO.TokenEarning> tokenEarnings = new ArrayList<>();
    BigDecimal totalUsdValue = processTokenEarnings(
        userTokenTaskRewards, tokenInfoMap, exchangeRates, tokenEarnings, batchRrMap);

    // Calculate total earnings in different currencies
    TotalEarnings totalEarnings = calculateTotalEarnings(totalUsdValue, exchangeRates);

    // Get additional user data
    Long approvedDatapoints = getUserApprovedDatapoints(userId);
    //    String nextClaimDate = getNextClaimDate();

    // Check if any exchange rates are missing - if so, return "-" for all estimates
    boolean hasAllExchangeRates =
        exchangeRates.containsKey(TOKEN_NAME_SAHARA) && exchangeRates.containsKey(TOKEN_NAME_USD1);

    return MyEarningsDTO.builder()
        .estTotalEarningsSAH(
            hasAllExchangeRates
                ? totalEarnings.sahAmount().setScale(0, RoundingMode.FLOOR).toString()
                : "-")
        .estTotalEarningsUSD1(
            hasAllExchangeRates
                ? totalEarnings.usd1Amount().setScale(0, RoundingMode.FLOOR).toString()
                : "-")
        .estTotalEarningsUSD(
            hasAllExchangeRates
                ? totalEarnings.usdAmount().setScale(0, RoundingMode.FLOOR).toString()
                : "-")
        .approvedDatapoints(approvedDatapoints)
        .tokenEarnings(tokenEarnings)
        .build();
  }

  /** Get exchange rates for all tokens including defaults */
  private Map<String, BigDecimal> getExchangeRatesForAllTokens(
      Map<RewardTokenType, RewardTokenInfo> tokenInfoMap) {
    // Collect all unique token names
    Set<String> allTokenNames = new HashSet<>();
    allTokenNames.add(TOKEN_NAME_SAHARA);
    allTokenNames.add(TOKEN_NAME_USD1);

    tokenInfoMap.values().forEach(tokenInfo -> {
      if (tokenInfo != null && tokenInfo.getTokenName() != null) {
        allTokenNames.add(tokenInfo.getTokenName());
      }
    });

    // Get exchange rates from Binance
    Set<String> binanceSymbols =
        allTokenNames.stream().map(this::mapTokenNameToBinanceSymbol).collect(Collectors.toSet());

    Map<String, BigDecimal> binanceRates = getAllTokenExchangeRates(binanceSymbols);

    // Map token names to exchange rates
    Map<String, BigDecimal> exchangeRates = new HashMap<>();
    for (String tokenName : allTokenNames) {
      String binanceSymbol = mapTokenNameToBinanceSymbol(tokenName);
      BigDecimal rate = binanceRates.get(binanceSymbol);
      if (rate == null) {
        rate = getDefaultExchangeRate(tokenName);
      }
      // Only put in map if rate is not null (excludes unknown tokens)
      if (rate != null) {
        exchangeRates.put(tokenName, rate);
      }
    }

    return exchangeRates;
  }

  /** Process all token earnings and calculate total USD value */
  private BigDecimal processTokenEarnings(
      List<UserTokenTaskRewards> userTokenTaskRewards,
      Map<RewardTokenType, RewardTokenInfo> tokenInfoMap,
      Map<String, BigDecimal> exchangeRates,
      List<MyEarningsDTO.TokenEarning> tokenEarnings,
      Map<String, BatchRewardRecord> batchRrMap) {

    BigDecimal totalUsdValue = BigDecimal.ZERO;
    var userTokenRewardMap = userTokenTaskRewards.stream()
        .collect(Collectors.groupingBy(UserTokenTaskRewards::getRewardTokenType));
    for (var entry : userTokenRewardMap.entrySet()) {
      RewardTokenType tokenType = entry.getKey();
      List<UserTokenTaskRewards> rewards = entry.getValue();

      MyEarningsDTO.TokenEarning tokenEarning =
          processTokenEarning(tokenType, rewards, tokenInfoMap, batchRrMap);

      tokenEarnings.add(tokenEarning);

      // Add to total USD value only if exchange rate is available
      BigDecimal tokenAmount = tokenEarning.getHistoricalTotal();
      String tokenName = tokenEarning.getTokenName();
      BigDecimal exchangeRate = exchangeRates.get(tokenName);
      if (exchangeRate != null) {
        totalUsdValue = totalUsdValue.add(tokenAmount.multiply(exchangeRate));
      }
      // If exchangeRate is null, skip this token in USD calculation
    }

    return totalUsdValue;
  }

  /** Process a single token earning */
  private MyEarningsDTO.TokenEarning processTokenEarning(
      RewardTokenType tokenType,
      List<UserTokenTaskRewards> rewards,
      Map<RewardTokenType, RewardTokenInfo> tokenInfoMap,
      Map<String, BatchRewardRecord> batchRrMap) {

    // Get token info
    RewardTokenInfo tokenInfo = tokenInfoMap.get(tokenType);
    if (tokenInfo == null) {
      throw ControllerUtils.notFound("Token info not found for id " + tokenType);
    }

    // Calculate amounts
    BigDecimal totalAmount = rewards.stream()
        .map(UserTokenTaskRewards::getAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    //    RewardTokenType tokenType = rewards.get(0).getRewardTokenType();
    String tokenName = tokenInfo.getTokenName();

    BigDecimal availableAmount = rewards
        .filter(r -> UserTokenTaskRewards.UserRewardStatus.DEFAULT.equals(r.getStatus())
            && batchRrMap
                .get(String.format("%d-%d", r.getJobId(), r.getRewardTokenType().getValue()))
                .getPoolStatus()
                .equals(LAUNCHED_WITH_CD_READY))
        .map(UserTokenTaskRewards::getAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal freezingTotalAmount = rewards
        .filter(r -> UserTokenTaskRewards.UserRewardStatus.DEFAULT.equals(r.getStatus())
            && !batchRrMap
                .get(String.format("%d-%d", r.getJobId(), r.getRewardTokenType().getValue()))
                .getPoolStatus()
                .equals(LAUNCHED_WITH_CD_READY))
        .map(UserTokenTaskRewards::getAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    // Apply threshold rule
    BigDecimal minWithdrawThreshold = getMinimumWithdrawThreshold(tokenType);
    BigDecimal availableToWithdraw =
        availableAmount.compareTo(minWithdrawThreshold) >= 0 ? availableAmount : BigDecimal.ZERO;

    String actionState = availableToWithdraw.compareTo(BigDecimal.ZERO) > 0
        ? "WITHDRAW_ENABLED"
        : "WITHDRAW_DISABLED";

    return MyEarningsDTO.TokenEarning.builder()
        .tokenName(tokenName)
        .resourceId(tokenInfo.getResourceId())
        .rewardTokenType(tokenType)
        .historicalTotal(totalAmount.setScale(0, RoundingMode.FLOOR))
        .availableToWithdraw(availableToWithdraw.setScale(0, RoundingMode.FLOOR))
        .freezingTotal(freezingTotalAmount.setScale(0, RoundingMode.FLOOR))
        .action(actionState)
        .build();
  }

  /** Calculate total earnings in different currencies */
  private TotalEarnings calculateTotalEarnings(
      BigDecimal totalUsdValue, Map<String, BigDecimal> exchangeRates) {

    BigDecimal sahExchangeRate = exchangeRates.get(TOKEN_NAME_SAHARA);
    BigDecimal usd1ExchangeRate = exchangeRates.get(TOKEN_NAME_USD1);

    // Handle null exchange rates to prevent NPE
    BigDecimal sahAmount = sahExchangeRate != null && sahExchangeRate.compareTo(BigDecimal.ZERO) > 0
        ? totalUsdValue.divide(sahExchangeRate, 6, RoundingMode.HALF_UP)
        : BigDecimal.ZERO;

    BigDecimal usd1Amount =
        usd1ExchangeRate != null && usd1ExchangeRate.compareTo(BigDecimal.ZERO) > 0
            ? totalUsdValue.divide(usd1ExchangeRate, 6, RoundingMode.HALF_UP)
            : BigDecimal.ZERO;

    return new TotalEarnings(sahAmount, usd1Amount, totalUsdValue);
  }

  /** Get user's approved datapoints count */
  private Long getUserApprovedDatapoints(Long userId) {
    var jobIds = userTokenTaskRewardsDao.getJobIdsByUserId(userId);
    return taskSessionDao.countUserFinishedSessionsByUserIdAndJobIds(userId, jobIds)
        + reviewSessionDao.countUserFinishedSessionsByUserIdAndJobIds(userId, jobIds);
  }

  /** Get next claim date (next Monday) */
  //  private String getNextClaimDate() {
  //    LocalDate nextMonday =
  //        LocalDate.now(ZoneOffset.UTC).with(TemporalAdjusters.next(DayOfWeek.MONDAY));
  //    return nextMonday.format(DateTimeFormatter.ISO_LOCAL_DATE);
  //  }

  public IPage<EarningBreakdownDTO> getEarningBreakdown(
      Long userId,
      int page,
      int size,
      Integer coinType,
      String taskName,
      String role,
      SortType sortType,
      Boolean claimed) {
    var coinTypeEnum = RewardTokenType.fromValue(coinType);
    var tokenInfoMap =
        rewardTokenInfoDao.findAllActive().toMap(RewardTokenInfo::getId, Function.identity());
    IPage<EarningBreakdownDTO> userTaskRewardsPage =
        userTokenTaskRewardsDao.getUserTokenTaskRewards(
            userId, page, size, coinTypeEnum, taskName, role, sortType, claimed);
    if (userTaskRewardsPage.getRecords().isEmpty()) {
      return userTaskRewardsPage;
    }
    var jobIds =
        userTaskRewardsPage.getRecords().map(EarningBreakdownDTO::getJobId).toSet();
    var jobMap = jobDao.getJobByIds(jobIds).toMap(Job::getId, Function.identity());
    var tokenTypes = userTaskRewardsPage
        .getRecords()
        .map(EarningBreakdownDTO::getRewardTokenType)
        .toSet();
    var batchRewardRecordsMap = batchRewardRecordDao
        .listByJobIdsAndType(jobIds, tokenTypes)
        .toMap(
            e -> String.format("%d_%d", e.getBatchId(), e.getRewardTokenType().value),
            Function.identity());
    updateBatchRewardRecordsStatus(
        batchRewardRecordsMap
            .values()
            .filter(r -> r.getPoolStatus().equals(LAUNCHED_WITH_CD))
            .toList(),
        jobMap.values());
    return userTaskRewardsPage.convert(reward -> {
      var tokenInfo = tokenInfoMap.getOrDefault(
          reward.getThirdPartyTokenId(), RewardTokenInfo.builder().build());

      reward.setIsPartnerTokenTGE(tokenInfo.getTgeAlready());
      reward.setTokenName(tokenInfo.getTokenName());
      reward.setBatchRewardRecord(batchRewardRecordsMap.get(String.format(
          "%d_%d",
          jobMap
              .getOrDefault(reward.getJobId(), Job.builder().batchId(0L).build())
              .getBatchId(),
          reward.getRewardTokenType().value)));
      reward.setRole(
          JobUser.JobUserRole.LABELER.getValue().equals(reward.getRole())
              ? "Annotator"
              : reward.getRole());
      if (claimed) {
        reward.setCreatedAt(reward.getUpdatedAt());
      }
      reward.setUpdatedAt(null);
      return reward;
    });
  }

  private final AtomicBoolean isRunning = new AtomicBoolean(false);

  @Async
  public void updateBatchRewardRecordsStatus(
      List<BatchRewardRecord> batchRewardRecordStream, Collection<Job> values) {
    if (isRunning.compareAndSet(false, true)) {
      try {
        var bjMap = values.toMap(Job::getBatchId, Job::getId);
        for (BatchRewardRecord batchRewardRecord : batchRewardRecordStream) {
          var cacheKey =
              String.format("BatchRewardRecord_POOLSTATUS:%d", batchRewardRecord.getBatchId());
          if (cache.hasKey(cacheKey)) {
            continue;
          }
          try {
            var res = dspRewardContract
                .getCooldownStatus(BigInteger.valueOf(bjMap.get(batchRewardRecord.getBatchId())))
                .send();
            var res2 = dspRewardContract
                .getTaskInfo(BigInteger.valueOf(bjMap.get(batchRewardRecord.getBatchId())))
                .send();
            if (Boolean.TRUE.equals(res.component1())
                && Boolean.TRUE.equals(
                    res2.component1())) { // timing is done and task is approved by admin
              batchRewardRecord.setPoolStatus(LAUNCHED_WITH_CD_READY);
              batchRewardRecordDao.update(batchRewardRecord);
              cache.set(cacheKey, "0", Duration.ofHours(1L).getSeconds());
            } else if (res.component4().intValue() > 0) {
              cache.set(
                  cacheKey,
                  res.component4().toString(),
                  Math.min(res.component4().longValue() + 60L, 30L * 60));
            } else {
              cache.set(cacheKey, res.component4().toString(), 5L * 60);
            }
          } catch (Exception e) {
            cache.set(cacheKey, "0", Duration.ofHours(1L).getSeconds());
            log.error("Failed to update batch reward record pool status", e);
          }
        }
      } finally {
        isRunning.set(false);
      }
    }
  }

  /** Fetch exchange rates for multiple tokens using Binance API batch endpoint with caching */
  private Map<String, BigDecimal> getAllTokenExchangeRates(Set<String> binanceSymbols) {
    if (binanceSymbols.isEmpty()) {
      return new HashMap<>();
    }

    // Create cache key and check cache first
    String cacheKey = binanceSymbols.stream().sorted().collect(Collectors.joining(","));
    Map<String, BigDecimal> cachedRates = exchangeRateCache.getIfPresent(cacheKey);
    if (cachedRates != null) {
      return cachedRates;
    }

    // Try to fetch from Binance API
    Map<String, BigDecimal> exchangeRates = fetchFromBinanceAPI(binanceSymbols);

    // If API call was successful, cache the results in both caches
    if (exchangeRates != null) {
      exchangeRateCache.put(cacheKey, exchangeRates);
      fallbackExchangeRateCache.put(cacheKey, exchangeRates);
      log.debug("Successfully fetched and cached exchange rates from Binance API");
      return exchangeRates;
    }

    // API failed, try fallback cache first
    Map<String, BigDecimal> fallbackRates = fallbackExchangeRateCache.getIfPresent(cacheKey);
    if (fallbackRates != null) {
      log.warn("Binance API failed, using fallback cache for exchange rates");
      return fallbackRates;
    }

    // Both API and cache failed, return default rates without caching
    log.warn("Both Binance API and fallback cache failed, using default exchange rates");
    return getDefaultRatesForSymbols(binanceSymbols);
  }

  /** Fetch exchange rates from Binance API, returns null if failed */
  private Map<String, BigDecimal> fetchFromBinanceAPI(Set<String> binanceSymbols) {
    try {
      // Filter out invalid symbols first
      Set<String> validSymbols = binanceSymbols.stream()
          .filter(symbol -> symbol.matches("^[A-Z0-9]{1,20}$"))
          .collect(Collectors.toSet());

      if (validSymbols.isEmpty()) {
        log.warn("No valid symbols to request from Binance");
        return new HashMap<>();
      }

      // Build the JSON array string as before
      String symbolsJson = objectMapper.writeValueAsString(validSymbols);

      // Use the dynamic API URL from configuration
      URI uri = UriComponentsBuilder.fromUriString(binanceApiConfig.getPriceApiUrl())
          .queryParam("symbols", symbolsJson)
          .build()
          .encode()
          .toUri();

      // Use the configured RestTemplate with hostname forwarding or proxy support
      Map<String, String>[] response = restTemplate.getForObject(uri, Map[].class);
      if (response == null) {
        log.warn("Binance API returned null response");
        return null;
      }
      return processBinanceResponse(response, binanceSymbols);
    } catch (org.springframework.web.client.ResourceAccessException e) {
      log.error("Network error accessing Binance API (timeout/connection): {}", e.getMessage());
      return null;
    } catch (org.springframework.web.client.HttpClientErrorException e) {
      log.error("HTTP client error from Binance API: {} - {}", e.getStatusCode(), e.getMessage());
      return null;
    } catch (org.springframework.web.client.HttpServerErrorException e) {
      log.error("HTTP server error from Binance API: {} - {}", e.getStatusCode(), e.getMessage());
      return null;
    } catch (Exception e) {
      log.error("Unexpected error fetching exchange rates from Binance: {}", e.getMessage(), e);
      return null;
    }
  }

  /** Process Binance API response and handle missing symbols */
  private Map<String, BigDecimal> processBinanceResponse(
      Map<String, String>[] response, Set<String> binanceSymbols) {
    Map<String, BigDecimal> exchangeRates = new HashMap<>();

    // Process successful responses
    for (Map<String, String> priceData : response) {
      String symbol = priceData.get("symbol");
      String price = priceData.get("price");
      if (symbol != null && price != null) {
        exchangeRates.put(symbol, new BigDecimal(price));
      }
    }

    // Handle missing symbols with default rates (only for stable coins and SAHARA)
    for (String symbol : binanceSymbols) {
      if (!exchangeRates.containsKey(symbol)) {
        BigDecimal defaultRate = getDefaultExchangeRate(symbol);
        if (defaultRate != null) {
          log.warn(
              "Symbol {} not found in Binance response, using default rate: {}",
              symbol,
              defaultRate);
          exchangeRates.put(symbol, defaultRate);
        } else {
          log.warn("Symbol {} not found in Binance response and no default rate available", symbol);
          // Don't add to exchangeRates map - this will result in null when retrieved
        }
      }
    }

    return exchangeRates;
  }

  /** Get default exchange rates for all requested symbols */
  private Map<String, BigDecimal> getDefaultRatesForSymbols(Set<String> binanceSymbols) {
    Map<String, BigDecimal> defaultRates = new HashMap<>();
    for (String symbol : binanceSymbols) {
      BigDecimal rate = getDefaultExchangeRate(symbol);
      // Only add to map if it's not null (stable coins get 1.0, others get null)
      if (rate != null) {
        defaultRates.put(symbol, rate);
      }
    }
    return defaultRates;
  }

  /** Map token names to Binance API symbols */
  private String mapTokenNameToBinanceSymbol(String tokenName) {
    return switch (tokenName.toUpperCase()) {
      case "SAH", "SAHARA" -> "SAHARAUSDT";
      case "USD1" -> "USD1USDT";
      default -> tokenName.toUpperCase() + "USDT";
    };
  }

  /** Get default exchange rates as fallback - returns null for non-stable coins */
  private BigDecimal getDefaultExchangeRate(String tokenName) {
    return switch (tokenName.toUpperCase()) {
      case "USD1", "USDC", "USDT" -> BigDecimal.ONE;
      default -> null; // SAHARA and other tokens return null
    };
  }

  private BigDecimal getMinimumWithdrawThreshold(RewardTokenType tokenType) {
    return switch (tokenType) {
      case SAHARA -> new BigDecimal("100"); // Minimum 100 SAH to withdraw
      case USD1 -> new BigDecimal("100"); // Minimum 100 USD1 to withdraw
      case THIRD_PARTY_TOKEN -> new BigDecimal("100"); // Default minimum 100 for third party tokens
    };
  }

  public List<UserTokenTaskRewards> requestClaim(Long userId, Integer tokenType) {
    return userTokenTaskRewardsDao.selectReadyClaimRecords(userId, tokenType);
  }

  public RewardTokenInfo getTokenInfo(Integer tokenType) {
    return rewardTokenInfoDao
        .findByTokenType(RewardTokenType.fromValue(tokenType))
        .firstOrNull();
  }

  public Boolean requestClaimCheckAfter(Long userId) {
    var user =
        userDao.getUserById(userId).orElseThrow(() -> ControllerUtils.notFound("wrong data"));
    var userAddr = user.getWalletAddress().toLowerCase();
    var datas = userTokenTaskRewardsDao
        .selectClaimingAndClaimableDatas(userId)
        .map(r -> r.getJobId().toString())
        .toSet();
    if (datas.isEmpty()) {
      return false;
    }
    var res = queryUserClaims(userAddr, String.join(",", datas));
    var res2 = res.filter(r -> new BigInteger(r.getReward()).compareTo(BigInteger.ZERO) > 0)
        .toList();
    if (res2.isEmpty()) {
      return userTokenTaskRewardsDao.checkExpiredClaimPendingData(userId);
    }
    userTokenTaskRewardsDao.updateUserClaiming(userId, res2);
    return userTokenTaskRewardsDao.checkExpiredClaimPendingData(userId);
  }

  private List<UserClaimRewardsDTO> queryUserClaims(String userAddr, String taskIds)
      throws IOException {
    OkHttpClient client = new OkHttpClient();
    String query =
        """
        query MyQuery {
          claimeds(where: {taskId_in: [%s], user: "%s"}) {
            reward
            rewardToken
            taskId
          }
        }
          """
            .formatted(taskIds, userAddr);

    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("query", query);
    requestBody.put("operationName", "queryUserClaims");
    requestBody.put("extensions", new HashMap<>());
    String jsonBody = new ObjectMapper().writeValueAsString(requestBody);
    Request request = new Request.Builder()
        .url(rewardGraphqlUrl)
        .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
        .addHeader("accept", "application/graphql-response+json, application/json, multipart/mixed")
        .addHeader("cache-control", "no-cache")
        .addHeader("content-type", "application/json")
        .build();
    Response response = client.newCall(request).execute();
    if (!response.isSuccessful()) {
      return List.of();
    }
    var objectMapper = new ObjectMapper();
    var qs = objectMapper.readValue(response.body().string(), UserClaimRewardsTopDTO.class);
    return qs.getData().getClaimeds();
  }

  public void requestClaimJustDid(Long userId, String claimedIds) {
    var now = DateUtils.now();
    var justClaimedJobIds =
        claimedIds.split(",").stream().map(Long::parseLong).distinct().toList();
    var userRewardsRecList =
        userTokenTaskRewardsDao.getMainRecListByUserId(userId, justClaimedJobIds);
    userRewardsRecList.forEach(reward -> {
      reward.setStatus(PENDING_CLAIM);
      reward.setUpdatedAt(now);
    });
    userTokenTaskRewardsDao.updateByArrDataWithId(userRewardsRecList);
  }

  /** Record class for total earnings calculation */
  private record TotalEarnings(BigDecimal sahAmount, BigDecimal usd1Amount, BigDecimal usdAmount) {}
}
