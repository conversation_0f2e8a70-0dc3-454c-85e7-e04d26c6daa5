commit d74761c7e2ecebf751d0f114407b29f94e107f00
Author: 1cixinzhe <<EMAIL>>
Date:   Thu May 8 10:08:06 2025 +0800

    feat: web3j oracle addUserTimeRangeTask&addUserBarTask (#2188)

diff --git a/helm/templates/configmap.yaml b/helm/templates/configmap.yaml
index 45633f29..86b94fe7 100644
--- a/helm/templates/configmap.yaml
+++ b/helm/templates/configmap.yaml
@@ -36,6 +36,7 @@ data:
   AI_WEB3_RPC_URL: "{{.Values.platform.settings.AI_WEB3_RPC_URL}}"
   AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS}}"
   AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS}}"
+  AI_WEB3_ACHIEVEMENT_ORACLE_PK: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_ORACLE_PK}}"
   GOOGLE_RECAPTCHA_SECRET_V3: "{{.Values.platform.settings.GOOGLE_RECAPTCHA_SECRET_V3}}"
   GOOGLE_RECAPTCHA_SECRET_V2: "{{.Values.platform.settings.GOOGLE_RECAPTCHA_SECRET_V2}}"
   AWS_ACCESS_KEY_ID: "{{.Values.platform.settings.AWS_ACCESS_KEY_ID}}"
diff --git a/helm/values.yaml b/helm/values.yaml
index 72e91dde..16839231 100644
--- a/helm/values.yaml
+++ b/helm/values.yaml
@@ -22,6 +22,7 @@ platform:
     AI_WEB3_RPC_URL: ""
     AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: ""
     AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS: ""
+    AI_WEB3_ACHIEVEMENT_ORACLE_PK: ""
     GOOGLE_RECAPTCHA_SECRET_V3: ""
     GOOGLE_RECAPTCHA_SECRET_V2: ""
     AWS_ACCESS_KEY_ID: ""
diff --git a/src/main/java/ai/saharaa/actors/achievement/AchievementProgressBatchTxSender.java b/src/main/java/ai/saharaa/actors/achievement/AchievementProgressBatchTxSender.java
new file mode 100644
index 00000000..e0964368
--- /dev/null
+++ b/src/main/java/ai/saharaa/actors/achievement/AchievementProgressBatchTxSender.java
@@ -0,0 +1,210 @@
+package ai.saharaa.actors.achievement;
+
+import ai.saharaa.daos.achievement.AchievementOracleBatchDao;
+import ai.saharaa.dto.UUIDMessageDTO;
+import ai.saharaa.dto.achievement.AchievementProgressOracleDTO;
+import ai.saharaa.enums.AchievementOracleTxStatus;
+import ai.saharaa.enums.AchievementProgressType;
+import ai.saharaa.model.achievement.AchievementOracleBatch;
+import akka.actor.AbstractActorWithTimers;
+import com.fasterxml.jackson.core.JsonProcessingException;
+import com.fasterxml.jackson.core.type.TypeReference;
+import com.fasterxml.jackson.databind.ObjectMapper;
+import java.math.BigInteger;
+import java.time.Duration;
+import java.util.Collections;
+import java.util.List;
+import java.util.Objects;
+import java.util.stream.Collectors;
+import lombok.AllArgsConstructor;
+import lombok.Getter;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.beans.factory.annotation.Value;
+import org.springframework.beans.factory.config.ConfigurableBeanFactory;
+import org.springframework.context.annotation.Scope;
+import org.springframework.stereotype.Component;
+import org.springframework.util.CollectionUtils;
+import org.web3j.abi.FunctionEncoder;
+import org.web3j.abi.datatypes.*;
+import org.web3j.abi.datatypes.generated.Uint256;
+import org.web3j.crypto.Credentials;
+import org.web3j.crypto.RawTransaction;
+import org.web3j.crypto.TransactionEncoder;
+import org.web3j.protocol.Web3j;
+import org.web3j.protocol.core.DefaultBlockParameterName;
+import org.web3j.protocol.core.methods.request.Transaction;
+import org.web3j.utils.Numeric;
+
+@Slf4j
+@Component
+@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
+public class AchievementProgressBatchTxSender extends AbstractActorWithTimers {
+
+  private final ObjectMapper objectMapper;
+  private final Web3j web3j;
+  private final Long web3ChainId;
+  private final Credentials achievementOracleCredentials;
+  private final AchievementOracleBatchDao achievementOracleBatchDao;
+  private final String achievementOnContractAddress;
+
+  public AchievementProgressBatchTxSender(
+      ObjectMapper objectMapper,
+      Web3j web3j,
+      Long web3ChainId,
+      Credentials achievementOracleCredentials,
+      AchievementOracleBatchDao achievementOracleBatchDao,
+      @Value("${ai.saharaa.web3.achievement_on_contract_address}")
+          String achievementOnContractAddress) {
+    this.objectMapper = objectMapper;
+    this.web3j = web3j;
+    this.web3ChainId = web3ChainId;
+    this.achievementOracleCredentials = achievementOracleCredentials;
+    this.achievementOracleBatchDao = achievementOracleBatchDao;
+    this.achievementOnContractAddress = achievementOnContractAddress;
+
+    getTimers()
+        .startTimerWithFixedDelay(
+            "ACHIEVEMENT_PROGRESS_TX_SEND", new SendTrigger(), Duration.ofSeconds(20));
+  }
+
+  @Override
+  public Receive createReceive() {
+    return receiveBuilder().match(SendTrigger.class, this::handleSend).build();
+  }
+
+  private void handleSend(SendTrigger trigger) {
+    var oracleBatch = achievementOracleBatchDao.pickOneToSend();
+    log.info("start achievement progress batch send tx {}", oracleBatch);
+    if (Objects.isNull(oracleBatch)) {
+      log.info("achievement progress batch send empty");
+      return;
+    }
+
+    UUIDMessageDTO<List<AchievementProgressOracleDTO>> body;
+    try {
+      body = objectMapper.readValue(oracleBatch.getBody(), new TypeReference<>() {});
+    } catch (JsonProcessingException e) {
+      log.error("achievement progress batch parse body error {}", oracleBatch, e);
+      achievementOracleBatchDao.updateStatus(
+          oracleBatch.getUuid(), AchievementOracleTxStatus.FAILED);
+      return;
+    }
+    if (CollectionUtils.isEmpty(body.getMessage())) {
+      log.info("achievement progress batch send empty");
+      achievementOracleBatchDao.updateStatus(
+          oracleBatch.getUuid(), AchievementOracleTxStatus.CONFIRMED);
+      return;
+    }
+
+    try {
+      var txHash = sendTx(oracleBatch, body);
+      log.info("send-progress-tx-success: {}; {}", oracleBatch, txHash);
+    } catch (Exception e) {
+      achievementOracleBatchDao.updateStatus(
+          oracleBatch.getUuid(), AchievementOracleTxStatus.FAILED);
+      return;
+    }
+    achievementOracleBatchDao.updateStatus(
+        oracleBatch.getUuid(), AchievementOracleTxStatus.CONFIRMED);
+
+    log.info("achievement progress kafka send finished {}", oracleBatch);
+  }
+
+  private String sendTx(
+      AchievementOracleBatch oracleBatch, UUIDMessageDTO<List<AchievementProgressOracleDTO>> body) {
+
+    try {
+      var nonce = web3j
+          .ethGetTransactionCount(
+              achievementOracleCredentials.getAddress(), DefaultBlockParameterName.PENDING)
+          .send()
+          .getTransactionCount();
+
+      Function function;
+      if (AchievementProgressType.TIME_RANGE == oracleBatch.getProgressType()) {
+        function = new Function(
+            "addUserTimeRangeTask",
+            Collections.singletonList(new DynamicArray<>(
+                DynamicStruct.class,
+                body.getMessage().stream()
+                    .map((m) -> new DynamicStruct(
+                        new Address(m.getUser()),
+                        new Uint256(BigInteger.valueOf(m.getAchievementId())),
+                        new Utf8String(m.getTaskId()),
+                        new Uint256(BigInteger.valueOf(m.getTimestamp())),
+                        new DynamicStruct(
+                            new Uint256(BigInteger.valueOf(m.get_p().getStartTime())),
+                            new Uint256(BigInteger.valueOf(m.get_p().getEndTime())),
+                            new Uint256(BigInteger.valueOf(m.get_p().getProgress())))))
+                    .collect(Collectors.toList()))),
+            Collections.emptyList());
+      } else if (AchievementProgressType.BAR == oracleBatch.getProgressType()) {
+        function = new Function(
+            "addUserBarTask",
+            Collections.singletonList(new DynamicArray<>(
+                DynamicStruct.class,
+                body.getMessage().stream()
+                    .map((m) -> new DynamicStruct(
+                        new Address(m.getUser()),
+                        new Uint256(BigInteger.valueOf(m.getAchievementId())),
+                        new Utf8String(m.getTaskId()),
+                        new Uint256(BigInteger.valueOf(m.getProgress())),
+                        new Uint256(BigInteger.valueOf(m.getTimestamp()))))
+                    .collect(Collectors.toList()))),
+            Collections.emptyList());
+      } else {
+        throw new RuntimeException("Unknown progress type");
+      }
+
+      var encodedFunction = FunctionEncoder.encode(function);
+
+      var estimateGas = web3j
+          .ethEstimateGas(Transaction.createEthCallTransaction(
+              achievementOracleCredentials.getAddress(),
+              achievementOnContractAddress,
+              encodedFunction))
+          .send();
+      if (estimateGas.hasError()) {
+        log.error(
+            "estimate-gas-progress-tx-error: {}; {}; nonce: {}",
+            oracleBatch,
+            estimateGas.getError().getMessage(),
+            nonce);
+        throw new RuntimeException(
+            "estimate-gas-progress-tx-error: " + estimateGas.getError().getMessage());
+      }
+      var baseGasLimit = estimateGas.getAmountUsed();
+      var adjustedGasLimit = baseGasLimit.multiply(BigInteger.valueOf(14)).divide(BigInteger.TEN);
+
+      var gasPrice = web3j.ethGasPrice().send().getGasPrice();
+      var adjustedGasPrice = gasPrice.multiply(BigInteger.valueOf(20)).divide(BigInteger.TEN);
+
+      var rawTransaction = RawTransaction.createTransaction(
+          nonce, adjustedGasPrice, adjustedGasLimit, achievementOnContractAddress, encodedFunction);
+
+      var signedMessage =
+          TransactionEncoder.signMessage(rawTransaction, web3ChainId, achievementOracleCredentials);
+      var hexValue = Numeric.toHexString(signedMessage);
+
+      var response = web3j.ethSendRawTransaction(hexValue).send();
+      if (response.hasError()) {
+        log.error(
+            "send-progress-tx-error: {}; {}; nonce: {}, gasLimit: {}, gasPrice: {}",
+            oracleBatch,
+            response.getError().getMessage(),
+            nonce,
+            adjustedGasLimit,
+            adjustedGasPrice);
+        throw new RuntimeException("send tx error: " + response.getError().getMessage());
+      }
+      return response.getTransactionHash();
+    } catch (Exception e) {
+      log.error("achievement progress batch send tx error {}", oracleBatch, e);
+      throw new RuntimeException(e);
+    }
+  }
+
+  @Getter
+  @AllArgsConstructor
+  public static class SendTrigger {}
+}
diff --git a/src/main/java/ai/saharaa/common/AchievementOracleSender.java b/src/main/java/ai/saharaa/common/AchievementOracleSender.java
index 3d665eb9..2acc0a78 100644
--- a/src/main/java/ai/saharaa/common/AchievementOracleSender.java
+++ b/src/main/java/ai/saharaa/common/AchievementOracleSender.java
@@ -6,7 +6,7 @@ import ai.saharaa.daos.UserDao;
 import ai.saharaa.daos.achievement.AchievementOnContractDao;
 import ai.saharaa.daos.achievement.AchievementOracleBatchDao;
 import ai.saharaa.daos.achievement.AchievementOracleLogDao;
-import ai.saharaa.dto.KafkaMessageDTO;
+import ai.saharaa.dto.UUIDMessageDTO;
 import ai.saharaa.dto.achievement.AchievementProgressOracleDTO;
 import ai.saharaa.dto.achievement.AchievementProgressReq;
 import ai.saharaa.enums.AchievementOracleTxStatus;
@@ -21,12 +21,10 @@ import java.time.ZoneOffset;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
-import java.util.concurrent.TimeUnit;
 import java.util.stream.Collectors;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.beans.factory.annotation.Value;
-import org.springframework.kafka.core.KafkaTemplate;
 import org.springframework.stereotype.Component;
 import org.springframework.util.CollectionUtils;
 import reactor.util.function.Tuples;
@@ -34,17 +32,14 @@ import reactor.util.function.Tuples;
 @Slf4j
 @Component
 public class AchievementOracleSender {
-  private final KafkaTemplate<String, String> kafkaTemplate;
   private final ObjectMapper objectMapper;
   private final UserDao userDao;
   private final AchievementOnContractDao achievementOnContractDao;
   private final AchievementOracleLogDao achievementOracleLogDao;
   private final AchievementOracleBatchDao achievementOracleBatchDao;
-  private static final String ACHIEVEMENT_PROGRESS_ORACLE_TOPIC = "achievement-progress-onchain";
   private final Integer achievementProgressOnchainBatchSize;
 
   public AchievementOracleSender(
-      KafkaTemplate<String, String> kafkaTemplate,
       ObjectMapper objectMapper,
       UserDao userDao,
       AchievementOnContractDao achievementOnContractDao,
@@ -52,7 +47,6 @@ public class AchievementOracleSender {
       AchievementOracleBatchDao achievementOracleBatchDao,
       @Value("${ai.saharaa.achievement_progress_onchain_batch_size}")
           Integer achievementProgressOnchainBatchSize) {
-    this.kafkaTemplate = kafkaTemplate;
     this.objectMapper = objectMapper;
     this.userDao = userDao;
     this.achievementOnContractDao = achievementOnContractDao;
@@ -179,11 +173,11 @@ public class AchievementOracleSender {
     var batchOracles = batchSplitList(achievementProgressOnchainBatchSize, messages);
 
     for (var oracles : batchOracles) {
-      sendMessage(KafkaMessageDTO.of(oracles));
+      sendMessage(UUIDMessageDTO.of(oracles));
     }
   }
 
-  private void sendMessage(KafkaMessageDTO<List<AchievementProgressOracleDTO>> message) {
+  private void sendMessage(UUIDMessageDTO<List<AchievementProgressOracleDTO>> message) {
     var uuid = message.getUuid().toString();
     try {
       String payload = objectMapper.writeValueAsString(message);
@@ -204,18 +198,13 @@ public class AchievementOracleSender {
               .filter(Objects::nonNull)
               .toList(),
           uuid);
-      var future =
-          kafkaTemplate.send(ACHIEVEMENT_PROGRESS_ORACLE_TOPIC, payload).get(5, TimeUnit.SECONDS);
-      log.info(
-          "AchievementOracleSender send message success {} to {}",
-          message,
-          future.getProducerRecord());
-      achievementOracleBatchDao.updateStatus(
-          message.getUuid().toString(), AchievementOracleTxStatus.CONFIRMED);
+      //      var future = kafkaTemplate.send(ACHIEVEMENT_PROGRESS_ORACLE_TOPIC, payload)
+      //      .get(5, TimeUnit.SECONDS);
+      log.info("AchievementOracleSender save message success {}", message);
+      //      achievementOracleBatchDao.updateStatus(
+      //          message.getUuid().toString(), AchievementOracleTxStatus.CONFIRMED);
     } catch (Exception e) {
-      achievementOracleBatchDao.updateStatus(
-          message.getUuid().toString(), AchievementOracleTxStatus.FAILED);
-      log.error("Failed to send message to Kafka: ", e);
+      log.error("Failed to save message to batch: {}", message, e);
     }
   }
 }
diff --git a/src/main/java/ai/saharaa/config/ContractConfig.java b/src/main/java/ai/saharaa/config/ContractConfig.java
index 50aad985..1c4f4994 100644
--- a/src/main/java/ai/saharaa/config/ContractConfig.java
+++ b/src/main/java/ai/saharaa/config/ContractConfig.java
@@ -17,18 +17,21 @@ public class ContractConfig {
 
   private final String rpcUrl;
   private final String credentialsPrivateKey;
+  private final String achievementOraclePrivateKey;
   private final String achievementManagerContractAddress;
   private final String achievementOnContractAddress;
 
   public ContractConfig(
       @Value("${ai.saharaa.web3.rpc_url}") String rpcUrl,
       @Value("${ai.saharaa.web3.credentials_pk}") String credentialsPrivateKey,
+      @Value("${ai.saharaa.web3.achievement_oracle_pk}") String achievementOraclePrivateKey,
       @Value("${ai.saharaa.web3.achievement_manager_contract_address}")
           String achievementManagerContractAddress,
       @Value("${ai.saharaa.web3.achievement_on_contract_address}")
           String achievementOnContractAddress) {
     this.rpcUrl = rpcUrl;
     this.credentialsPrivateKey = credentialsPrivateKey;
+    this.achievementOraclePrivateKey = achievementOraclePrivateKey;
     this.achievementManagerContractAddress = achievementManagerContractAddress;
     this.achievementOnContractAddress = achievementOnContractAddress;
   }
@@ -38,6 +41,16 @@ public class ContractConfig {
     return Web3j.build(new HttpService(rpcUrl));
   }
 
+  @Bean
+  public Long web3ChainId(Web3j web3j) throws Exception {
+    return web3j.ethChainId().send().getChainId().longValue();
+  }
+
+  @Bean
+  public Credentials achievementOracleCredentials() throws Exception {
+    return Credentials.create(achievementOraclePrivateKey);
+  }
+
   @Bean
   public LevelAchievementManager levelAchievementManager(Web3j web3j) throws Exception {
     var credentials = Credentials.create(credentialsPrivateKey);
@@ -49,11 +62,11 @@ public class ContractConfig {
   }
 
   @Bean
-  public AchievementOnContractManager achievementOnContractManager(Web3j web3j) throws Exception {
-    var credentials = Credentials.create(credentialsPrivateKey);
+  public AchievementOnContractManager achievementOnContractManager(
+      Web3j web3j, Credentials achievementOracleCredentials) throws Exception {
     var gasProvider = new DefaultGasProvider();
     var achievementOnContractManager = AchievementOnContractManager.load(
-        achievementOnContractAddress, web3j, credentials, gasProvider);
+        achievementOnContractAddress, web3j, achievementOracleCredentials, gasProvider);
     log.info("achievementOnContractManager created: {}", achievementOnContractManager);
     return achievementOnContractManager;
   }
diff --git a/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java b/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java
index 06b4b32f..22a6355d 100644
--- a/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java
+++ b/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java
@@ -90,6 +90,7 @@ public class ClusterConfiguration implements SmartInitializingSingleton {
   private ActorRef userAchievementProgressActor;
   private ActorRef userViewOlympusStatusActor;
   private ActorRef userDailyCheckInRecordActor;
+  private ActorRef achievementProgressBatchTxSender;
 
   public ClusterConfiguration(ActorSystem system, ApplicationContext context) {
     this.system = system;
@@ -121,246 +122,209 @@ public class ClusterConfiguration implements SmartInitializingSingleton {
   }
 
   private void initializeUserActors(ClusterShardingSettings settings) {
-    this.userTopicsActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/topics",
-                Props.create(UserTopicsActor.class, () -> context.getBean(UserTopicsActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userLoginHistoryActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/firstLogin",
-                Props.create(
-                    UserLoginHistoryActor.class,
-                    () -> context.getBean(UserLoginHistoryActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userLastNotificationActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/noticeLast",
-                Props.create(
-                    UserLastNotificationActor.class,
-                    () -> context.getBean(UserLastNotificationActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userViewOlympusStatusActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/viewOlympus",
-                Props.create(
-                    UserViewOlympusStatusActor.class,
-                    () -> context.getBean(UserViewOlympusStatusActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userRegionsActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/regions",
-                Props.create(UserRegionsActor.class, () -> context.getBean(UserRegionsActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userTutorialActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/tutorials",
-                Props.create(
-                    UserTutorialActor.class, () -> context.getBean(UserTutorialActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userLanguagesActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/languages",
-                Props.create(
-                    UserLanguagesActor.class, () -> context.getBean(UserLanguagesActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userInterestsActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/interests",
-                Props.create(
-                    UserInterestsActor.class, () -> context.getBean(UserInterestsActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userAccentsActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/accents",
-                Props.create(UserAccentsActor.class, () -> context.getBean(UserAccentsActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userSkillsActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/skills",
-                Props.create(UserSkillsActor.class, () -> context.getBean(UserSkillsActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userAvailabilityActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/availability",
-                Props.create(
-                    UserAvailabilityActor.class,
-                    () -> context.getBean(UserAvailabilityActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userTaskTypesActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/task-types",
-                Props.create(
-                    UserTaskTypesActor.class, () -> context.getBean(UserTaskTypesActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userCloudUploadS3Actor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/cloud-s3",
-                Props.create(
-                    UserCloudUploadS3Actor.class,
-                    () -> context.getBean(UserCloudUploadS3Actor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userSessionActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/user-session/record",
-                Props.create(UserSessionActor.class, () -> context.getBean(UserSessionActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.userSaharaLevelActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/sahara-level",
-                Props.create(
-                    UserSaharaLevelActor.class, () -> context.getBean(UserSaharaLevelActor.class)),
-                settings,
-                new ClusterMessageExtractor());
+    this.userTopicsActor = ClusterSharding.get(system)
+        .start(
+            "/user/topics",
+            Props.create(UserTopicsActor.class, () -> context.getBean(UserTopicsActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userLoginHistoryActor = ClusterSharding.get(system)
+        .start(
+            "/user/firstLogin",
+            Props.create(
+                UserLoginHistoryActor.class, () -> context.getBean(UserLoginHistoryActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userLastNotificationActor = ClusterSharding.get(system)
+        .start(
+            "/user/noticeLast",
+            Props.create(
+                UserLastNotificationActor.class,
+                () -> context.getBean(UserLastNotificationActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userViewOlympusStatusActor = ClusterSharding.get(system)
+        .start(
+            "/user/viewOlympus",
+            Props.create(
+                UserViewOlympusStatusActor.class,
+                () -> context.getBean(UserViewOlympusStatusActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userRegionsActor = ClusterSharding.get(system)
+        .start(
+            "/user/regions",
+            Props.create(UserRegionsActor.class, () -> context.getBean(UserRegionsActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userTutorialActor = ClusterSharding.get(system)
+        .start(
+            "/user/tutorials",
+            Props.create(UserTutorialActor.class, () -> context.getBean(UserTutorialActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userLanguagesActor = ClusterSharding.get(system)
+        .start(
+            "/user/languages",
+            Props.create(UserLanguagesActor.class, () -> context.getBean(UserLanguagesActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userInterestsActor = ClusterSharding.get(system)
+        .start(
+            "/user/interests",
+            Props.create(UserInterestsActor.class, () -> context.getBean(UserInterestsActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userAccentsActor = ClusterSharding.get(system)
+        .start(
+            "/user/accents",
+            Props.create(UserAccentsActor.class, () -> context.getBean(UserAccentsActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userSkillsActor = ClusterSharding.get(system)
+        .start(
+            "/user/skills",
+            Props.create(UserSkillsActor.class, () -> context.getBean(UserSkillsActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userAvailabilityActor = ClusterSharding.get(system)
+        .start(
+            "/user/availability",
+            Props.create(
+                UserAvailabilityActor.class, () -> context.getBean(UserAvailabilityActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userTaskTypesActor = ClusterSharding.get(system)
+        .start(
+            "/user/task-types",
+            Props.create(UserTaskTypesActor.class, () -> context.getBean(UserTaskTypesActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userCloudUploadS3Actor = ClusterSharding.get(system)
+        .start(
+            "/user/cloud-s3",
+            Props.create(
+                UserCloudUploadS3Actor.class, () -> context.getBean(UserCloudUploadS3Actor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userSessionActor = ClusterSharding.get(system)
+        .start(
+            "/user/user-session/record",
+            Props.create(UserSessionActor.class, () -> context.getBean(UserSessionActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.userSaharaLevelActor = ClusterSharding.get(system)
+        .start(
+            "/user/sahara-level",
+            Props.create(
+                UserSaharaLevelActor.class, () -> context.getBean(UserSaharaLevelActor.class)),
+            settings,
+            new ClusterMessageExtractor());
   }
 
   private void initializeBatchAndJobActors(ClusterShardingSettings settings) {
-    this.batchSampleActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/batch-samples",
-                Props.create(BatchSampleActor.class, () -> context.getBean(BatchSampleActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.batchContentActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/batch-contents",
-                Props.create(
-                    BatchContentActor.class, () -> context.getBean(BatchContentActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.batchJobManagerActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/batch-job-manager",
-                Props.create(
-                    BatchJobManagerActor.class, () -> context.getBean(BatchJobManagerActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.taskActor =
-        ClusterSharding.get(system)
-            .start(
-                "/user/tasks",
-                Props.create(TaskActor.class, () -> context.getBean(TaskActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.jobSessionActor =
-        ClusterSharding.get(system)
-            .start(
-                "/jobs/job-session",
-                Props.create(JobSessionActor.class, () -> context.getBean(JobSessionActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.individualJobSessionActor =
-        ClusterSharding.get(system)
-            .start(
-                "/jobs/job-session-for-annotator",
-                Props.create(
-                    IndividualJobSessionActor.class,
-                    () -> context.getBean(IndividualJobSessionActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-    this.individualJobSessionForReviewerActor =
-        ClusterSharding.get(system)
-            .start(
-                "/jobs/job-session-for-review",
-                Props.create(
-                    IndividualJobSessionForReviewerActor.class,
-                    () -> context.getBean(IndividualJobSessionForReviewerActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-    this.individualJobOtherProcessActor =
-        ClusterSharding.get(system)
-            .start(
-                "/jobs/job-other-sessions",
-                Props.create(
-                    IndividualJobOtherProcessActor.class,
-                    () -> context.getBean(IndividualJobOtherProcessActor.class)),
-                settings,
-                new ClusterMessageExtractor());
+    this.batchSampleActor = ClusterSharding.get(system)
+        .start(
+            "/user/batch-samples",
+            Props.create(BatchSampleActor.class, () -> context.getBean(BatchSampleActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.batchContentActor = ClusterSharding.get(system)
+        .start(
+            "/user/batch-contents",
+            Props.create(BatchContentActor.class, () -> context.getBean(BatchContentActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.batchJobManagerActor = ClusterSharding.get(system)
+        .start(
+            "/user/batch-job-manager",
+            Props.create(
+                BatchJobManagerActor.class, () -> context.getBean(BatchJobManagerActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.taskActor = ClusterSharding.get(system)
+        .start(
+            "/user/tasks",
+            Props.create(TaskActor.class, () -> context.getBean(TaskActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.jobSessionActor = ClusterSharding.get(system)
+        .start(
+            "/jobs/job-session",
+            Props.create(JobSessionActor.class, () -> context.getBean(JobSessionActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.individualJobSessionActor = ClusterSharding.get(system)
+        .start(
+            "/jobs/job-session-for-annotator",
+            Props.create(
+                IndividualJobSessionActor.class,
+                () -> context.getBean(IndividualJobSessionActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+    this.individualJobSessionForReviewerActor = ClusterSharding.get(system)
+        .start(
+            "/jobs/job-session-for-review",
+            Props.create(
+                IndividualJobSessionForReviewerActor.class,
+                () -> context.getBean(IndividualJobSessionForReviewerActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+    this.individualJobOtherProcessActor = ClusterSharding.get(system)
+        .start(
+            "/jobs/job-other-sessions",
+            Props.create(
+                IndividualJobOtherProcessActor.class,
+                () -> context.getBean(IndividualJobOtherProcessActor.class)),
+            settings,
+            new ClusterMessageExtractor());
   }
 
   private void initializeOtherClusterActors(ClusterShardingSettings settings) {
-    this.pingActor =
-        ClusterSharding.get(system)
-            .start("Ping", Props.create(PingActor.class), settings, new ClusterMessageExtractor());
-
-    this.httpClientActor =
-        ClusterSharding.get(system)
-            .start(
-                "/akka-http/client-request",
-                Props.create(HttpClientActor.class, () -> context.getBean(HttpClientActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.notificationUserActor =
-        ClusterSharding.get(system)
-            .start(
-                "/notification/notification_user",
-                Props.create(
-                    NotificationUserActor.class,
-                    () -> context.getBean(NotificationUserActor.class)),
-                settings,
-                new ClusterMessageExtractor());
-
-    this.achievementFlushActor =
-        ClusterSharding.get(system)
-            .start(
-                "/achievement/flush_actor",
-                Props.create(
-                    AchievementFlushActor.class,
-                    () -> context.getBean(AchievementFlushActor.class)),
-                settings,
-                new ClusterMessageExtractor());
+    this.pingActor = ClusterSharding.get(system)
+        .start("Ping", Props.create(PingActor.class), settings, new ClusterMessageExtractor());
+
+    this.httpClientActor = ClusterSharding.get(system)
+        .start(
+            "/akka-http/client-request",
+            Props.create(HttpClientActor.class, () -> context.getBean(HttpClientActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.notificationUserActor = ClusterSharding.get(system)
+        .start(
+            "/notification/notification_user",
+            Props.create(
+                NotificationUserActor.class, () -> context.getBean(NotificationUserActor.class)),
+            settings,
+            new ClusterMessageExtractor());
+
+    this.achievementFlushActor = ClusterSharding.get(system)
+        .start(
+            "/achievement/flush_actor",
+            Props.create(
+                AchievementFlushActor.class, () -> context.getBean(AchievementFlushActor.class)),
+            settings,
+            new ClusterMessageExtractor());
   }
 
   private void initializeSingletonActors(ClusterSingletonManagerSettings singletonSettings) {
@@ -552,82 +516,78 @@ public class ClusterConfiguration implements SmartInitializingSingleton {
             PoisonPill.getInstance(),
             singletonSettings),
         "user-daily-checkin");
+
+    system.actorOf(
+        ClusterSingletonManager.props(
+            Props.create(
+                    AchievementProgressBatchTxSender.class,
+                    () -> context.getBean(AchievementProgressBatchTxSender.class))
+                .withDispatcher("akka.actor.blocking-io-dispatcher"),
+            PoisonPill.getInstance(),
+            singletonSettings),
+        "achievement-progress-batch-tx-sender");
   }
 
   private void initializeProxyActors() {
     ClusterSingletonProxySettings proxySettings =
         ClusterSingletonProxySettings.create(system).withRole("ai-db");
-    this.notificationConfigScannerActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/notification-config-scanner", proxySettings),
-            "notificationConfigScannerProxy");
-    this.individualJobUserPointsActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/job-user-points-distribution-new", proxySettings),
-            "jobUserPointsDistributionNewProxy");
-    this.individualJobPipelineNotifyActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/job-pipeline-notify", proxySettings),
-            "jobUserPipelineNotifyProxy");
-    this.notificationConfigExecutorActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/notification-config-executor", proxySettings),
-            "notificationConfigExecutorProxy");
-    this.userRegisterActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/user-register", proxySettings), "userRegisterProxy");
-    this.jobStateSchedulerActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/job-state-scheduler", proxySettings),
-            "jobStateSchedulerProxy");
-
-    this.jobStatActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/job-stat", proxySettings), "jobStatActorProxy");
-
-    this.projectActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/project-actor", proxySettings), "projectActorProxy");
-
-    this.resourceSyncActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/resource-sync", proxySettings),
-            "resourceSyncActorProxy");
-
-    this.batchExportActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/batch-export", proxySettings),
-            "batchExportActorProxy");
-
-    this.settleSeasonJobPointsActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/settle-season-job-points", proxySettings),
-            "settleSeasonJobPointsActorProxy");
-
-    this.seasonExpActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/season-exp", proxySettings), "seasonExpActorProxy");
-
-    this.sendEmailActor =
-        system.actorOf(
-            ClusterSingletonProxy.props(
-                "/user/send-email",
-                ClusterSingletonProxySettings.create(system).withRole("ai-api")),
-            "sendEmailActorProxy");
-
-    this.userAchievementRecordActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/achievement-record", proxySettings),
-            "userAchievementRecordProxy");
-
-    this.userAchievementProgressActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/achievement-progress", proxySettings),
-            "userAchievementProgressProxy");
-
-    this.userDailyCheckInRecordActor =
-        system.actorOf(
-            ClusterSingletonProxy.props("/user/user-daily-checkin", proxySettings),
-            "userDailyCheckinProxy");
+    this.notificationConfigScannerActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/notification-config-scanner", proxySettings),
+        "notificationConfigScannerProxy");
+    this.individualJobUserPointsActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/job-user-points-distribution-new", proxySettings),
+        "jobUserPointsDistributionNewProxy");
+    this.individualJobPipelineNotifyActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/job-pipeline-notify", proxySettings),
+        "jobUserPipelineNotifyProxy");
+    this.notificationConfigExecutorActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/notification-config-executor", proxySettings),
+        "notificationConfigExecutorProxy");
+    this.userRegisterActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/user-register", proxySettings), "userRegisterProxy");
+    this.jobStateSchedulerActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/job-state-scheduler", proxySettings),
+        "jobStateSchedulerProxy");
+
+    this.jobStatActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/job-stat", proxySettings), "jobStatActorProxy");
+
+    this.projectActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/project-actor", proxySettings), "projectActorProxy");
+
+    this.resourceSyncActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/resource-sync", proxySettings),
+        "resourceSyncActorProxy");
+
+    this.batchExportActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/batch-export", proxySettings), "batchExportActorProxy");
+
+    this.settleSeasonJobPointsActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/settle-season-job-points", proxySettings),
+        "settleSeasonJobPointsActorProxy");
+
+    this.seasonExpActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/season-exp", proxySettings), "seasonExpActorProxy");
+
+    this.sendEmailActor = system.actorOf(
+        ClusterSingletonProxy.props(
+            "/user/send-email", ClusterSingletonProxySettings.create(system).withRole("ai-api")),
+        "sendEmailActorProxy");
+
+    this.userAchievementRecordActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/achievement-record", proxySettings),
+        "userAchievementRecordProxy");
+
+    this.userAchievementProgressActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/achievement-progress", proxySettings),
+        "userAchievementProgressProxy");
+
+    this.userDailyCheckInRecordActor = system.actorOf(
+        ClusterSingletonProxy.props("/user/user-daily-checkin", proxySettings),
+        "userDailyCheckinProxy");
+
+    this.achievementProgressBatchTxSender = system.actorOf(
+        ClusterSingletonProxy.props("/user/achievement-progress-batch-tx-sender", proxySettings),
+        "achievementProgressBatchTxProxy");
   }
 }
diff --git a/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java b/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java
index d243702b..bcff5ea4 100644
--- a/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java
+++ b/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java
@@ -165,6 +165,10 @@ public class AchievementOnContractDao
                 Objects.nonNull(achievement.getOracleSymbol()),
                 AchievementOnContract::getOracleSymbol,
                 achievement.getOracleSymbol())
+            .set(
+                Objects.nonNull(achievement.getProgressType()),
+                AchievementOnContract::getProgressType,
+                achievement.getProgressType())
             .set(
                 Objects.nonNull(achievement.getContractAddress()),
                 AchievementOnContract::getContractAddress,
diff --git a/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java b/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java
index 1ceb9ae4..32e928ed 100644
--- a/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java
+++ b/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java
@@ -3,9 +3,11 @@ package ai.saharaa.daos.achievement;
 import ai.saharaa.enums.AchievementOracleTxStatus;
 import ai.saharaa.mappers.achievement.AchievementOracleBatchMapper;
 import ai.saharaa.model.achievement.AchievementOracleBatch;
+import ai.saharaa.utils.DateUtils;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
+import java.util.List;
 import lombok.AllArgsConstructor;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.stereotype.Component;
@@ -40,7 +42,19 @@ public class AchievementOracleBatchDao
         new UpdateWrapper<AchievementOracleBatch>()
             .lambda()
             .set(AchievementOracleBatch::getStatus, status)
+            .set(AchievementOracleBatch::getUpdatedAt, DateUtils.now())
             .eq(AchievementOracleBatch::getUuid, uuid)
             .eq(AchievementOracleBatch::getDeleted, false));
   }
+
+  public AchievementOracleBatch pickOneToSend() {
+    return achievementOracleBatchMapper.selectOne(new QueryWrapper<AchievementOracleBatch>()
+        .lambda()
+        .in(
+            AchievementOracleBatch::getStatus,
+            List.of(AchievementOracleTxStatus.PENDING, AchievementOracleTxStatus.FAILED))
+        .eq(AchievementOracleBatch::getDeleted, false)
+        .orderByAsc(AchievementOracleBatch::getUpdatedAt)
+        .last("LIMIT 1"));
+  }
 }
diff --git a/src/main/java/ai/saharaa/dto/KafkaMessageDTO.java b/src/main/java/ai/saharaa/dto/KafkaMessageDTO.java
deleted file mode 100644
index 1546eb52..00000000
--- a/src/main/java/ai/saharaa/dto/KafkaMessageDTO.java
+++ /dev/null
@@ -1,16 +0,0 @@
-package ai.saharaa.dto;
-
-import java.util.UUID;
-import lombok.Builder;
-import lombok.Data;
-
-@Data
-@Builder
-public class KafkaMessageDTO<T> {
-  private UUID uuid;
-  private T message;
-
-  public static <T> KafkaMessageDTO<T> of(T message) {
-    return KafkaMessageDTO.<T>builder().uuid(UUID.randomUUID()).message(message).build();
-  }
-}
diff --git a/src/main/java/ai/saharaa/dto/UUIDMessageDTO.java b/src/main/java/ai/saharaa/dto/UUIDMessageDTO.java
new file mode 100644
index 00000000..dab39f76
--- /dev/null
+++ b/src/main/java/ai/saharaa/dto/UUIDMessageDTO.java
@@ -0,0 +1,16 @@
+package ai.saharaa.dto;
+
+import java.util.UUID;
+import lombok.Builder;
+import lombok.Data;
+
+@Data
+@Builder
+public class UUIDMessageDTO<T> {
+  private UUID uuid;
+  private T message;
+
+  public static <T> UUIDMessageDTO<T> of(T message) {
+    return UUIDMessageDTO.<T>builder().uuid(UUID.randomUUID()).message(message).build();
+  }
+}
diff --git a/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java b/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java
index 12dcef0b..4a6ddaee 100644
--- a/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java
+++ b/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java
@@ -1,5 +1,6 @@
 package ai.saharaa.dto.achievement;
 
+import ai.saharaa.enums.AchievementProgressType;
 import lombok.AllArgsConstructor;
 import lombok.Builder;
 import lombok.Data;
@@ -14,6 +15,7 @@ public class AchievementOnContractCreateUpdateDTO {
   private Long seasonId;
   private String oracleSymbol;
   private String contractAddress;
+  private AchievementProgressType progressType;
   private Integer sort;
   private Boolean active;
 }
diff --git a/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java b/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java
index 18cef433..b2509336 100644
--- a/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java
+++ b/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java
@@ -124,6 +124,7 @@ public class AchievementOnContractService {
         .seasonId(dto.getSeasonId())
         .oracleSymbol(dto.getOracleSymbol())
         .contractAddress(dto.getContractAddress())
+        .progressType(dto.getProgressType())
         .sort(dto.getSort())
         .active(dto.getActive())
         .build());
@@ -156,6 +157,7 @@ public class AchievementOnContractService {
         .seasonId(dto.getSeasonId())
         .oracleSymbol(dto.getOracleSymbol())
         .contractAddress(dto.getContractAddress())
+        .progressType(dto.getProgressType())
         .sort(dto.getSort())
         .active(dto.getActive())
         .build());
diff --git a/src/main/resources/application.properties b/src/main/resources/application.properties
index bc2ef6af..69273967 100644
--- a/src/main/resources/application.properties
+++ b/src/main/resources/application.properties
@@ -97,6 +97,7 @@ ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
 ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
 ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
 ai.saharaa.web3.credentials_pk=${AI_WEB3_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
+ai.saharaa.web3.achievement_oracle_pk=${AI_WEB3_ACHIEVEMENT_ORACLE_PK:0x4329a9e86a94ec6971f7d3b7f9848c3065bc2d727edcc174309306abbf839339}
 ai.saharaa.web3.achievement_manager_contract_address=${AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS:0x265EED7bF4387a5d1816850f7174cC6469ed9018}
 ai.saharaa.web3.achievement_on_contract_address=${AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS:0xD9348667cef8c0bcaE5186db9478FC6e55Fa8946}
 ai.saharaa.achievement_progress_onchain_batch_size=${ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE:10}
diff --git a/src/test/resources/application.properties b/src/test/resources/application.properties
index b533a3ec..04b9db74 100644
--- a/src/test/resources/application.properties
+++ b/src/test/resources/application.properties
@@ -67,6 +67,7 @@ ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
 ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
 ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
 ai.saharaa.web3.credentials_pk=${AI_WEB3_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
+ai.saharaa.web3.achievement_oracle_pk=${AI_WEB3_ACHIEVEMENT_ORACLE_PK:0x4329a9e86a94ec6971f7d3b7f9848c3065bc2d727edcc174309306abbf839339}
 ai.saharaa.web3.achievement_manager_contract_address=${AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS:0x265EED7bF4387a5d1816850f7174cC6469ed9018}
 ai.saharaa.web3.achievement_on_contract_address=${AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS:0xD9348667cef8c0bcaE5186db9478FC6e55Fa8946}
 ai.saharaa.achievement_progress_onchain_batch_size=${ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE:10}
