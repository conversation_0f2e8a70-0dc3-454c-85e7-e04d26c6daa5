diff --git a/src/main/java/ai/saharaa/actors/jobs/IndividualJobUserPointsActor.java b/src/main/java/ai/saharaa/actors/jobs/IndividualJobUserPointsActor.java
index 279a22a3..c6ea14d0 100644
--- a/src/main/java/ai/saharaa/actors/jobs/IndividualJobUserPointsActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/IndividualJobUserPointsActor.java
@@ -53,7 +53,8 @@ public class IndividualJobUserPointsActor extends ActorBase {
     var job = jobService.getJobById(settlePreCalcByJobId.getJobId()).get();
     var batchSetting = batchService.getBatchSettingByBatchId(job.getBatchId());
     if (BatchSetting.TaskPaymentType.TRADITIONAL.equals(batchSetting.getTaskPaymentType())) {
-      jobUserService.doSettleAllPreCalcRecs(null, settlePreCalcByJobId.getJobId());
+
+//      jobUserService.doSettleAllPreCalcRecs(null, settlePreCalcByJobId.getJobId());
     } else if (BatchSetting.TaskPaymentType.REWARD_POOL.equals(batchSetting.getTaskPaymentType())) {
       jobUserService.disPointsInRewardPoolMode(settlePreCalcByJobId.getJobId());
     }
diff --git a/src/main/java/ai/saharaa/daos/newTasks/NewTasksDao.java b/src/main/java/ai/saharaa/daos/newTasks/NewTasksDao.java
new file mode 100644
index 00000000..cfe0a510
--- /dev/null
+++ b/src/main/java/ai/saharaa/daos/newTasks/NewTasksDao.java
@@ -0,0 +1,151 @@
+package ai.saharaa.daos.newTasks;
+
+import ai.saharaa.mappers.newTasks.ThirdPartyRewardTokenMapper;
+import ai.saharaa.mappers.newTasks.UserTokenRewardClaimsMapper;
+import ai.saharaa.mappers.newTasks.UserTokenRewardsMapper;
+import ai.saharaa.mappers.newTasks.UserTokenTaskRewardsMapper;
+import ai.saharaa.model.BatchSetting;
+import ai.saharaa.model.JobUser;
+import ai.saharaa.model.newTasks.ThirdPartyRewardToken;
+import ai.saharaa.model.newTasks.UserTokenRewardClaims;
+import ai.saharaa.model.newTasks.UserTokenRewards;
+import ai.saharaa.model.newTasks.UserTokenTaskRewards;
+import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
+import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.stereotype.Component;
+import org.springframework.stereotype.Service;
+
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+import java.util.List;
+
+@Component
+@Slf4j
+public class NewTasksDao {
+  private final ThirdPartyRewardTokenMapper thirdPartyRewardTokenMapper;
+  private final UserTokenRewardsMapper userTokenRewardsMapper;
+  private final UserTokenTaskRewardsMapper userTokenTaskRewardsMapper;
+  private final UserTokenRewardClaimsMapper userTokenRewardClaimsMapper;
+
+  public NewTasksDao(
+    ThirdPartyRewardTokenMapper thirdPartyRewardTokenMapper,
+    UserTokenRewardsMapper userTokenRewardsMapper,
+    UserTokenTaskRewardsMapper userTokenTaskRewardsMapper,
+    UserTokenRewardClaimsMapper userTokenRewardClaimsMapper
+  ) {
+    this.thirdPartyRewardTokenMapper = thirdPartyRewardTokenMapper;
+    this.userTokenRewardsMapper = userTokenRewardsMapper;
+    this.userTokenTaskRewardsMapper = userTokenTaskRewardsMapper;
+    this.userTokenRewardClaimsMapper = userTokenRewardClaimsMapper;
+  }
+
+  public void createThirdPartyRewardTokenRecord(BigDecimal tokenAmount, String tokenName, Long batchId, Long iconResId, Boolean tgeAlready) {
+    var inserting = ThirdPartyRewardToken.builder()
+      .amount(tokenAmount)
+      .batchId(batchId)
+      .tokenName(tokenName)
+      .resourceId(iconResId)
+      .tgeAlready(tgeAlready)
+      .build();
+    thirdPartyRewardTokenMapper.insert(inserting);
+  }
+  public List<ThirdPartyRewardToken> listThirdPartyRewardTokenRecordsByBatchId(Long batchId) {
+    return thirdPartyRewardTokenMapper.selectList(new QueryWrapper<ThirdPartyRewardToken>().lambda()
+      .eq(ThirdPartyRewardToken::getBatchId, batchId)
+      .eq(ThirdPartyRewardToken::getDeleted, false)
+    );
+  }
+  public void updateThirdPartyRewardTokenRecord(ThirdPartyRewardToken updatingData) {
+    thirdPartyRewardTokenMapper.updateById(updatingData);
+  }
+  public void deleteThirdPartyRewardTokenRecordById(Long id) {
+    thirdPartyRewardTokenMapper.update(null, new UpdateWrapper<ThirdPartyRewardToken>().lambda()
+      .eq(ThirdPartyRewardToken::getId, id)
+      .set(ThirdPartyRewardToken::getDeleted, true)
+    );
+  }
+
+  public void createUserTokenRewardsRecord(BatchSetting.TokenType tokenType, BigDecimal tokenAmount, Long userId) {
+    var data = UserTokenRewards.builder()
+      .userId(userId)
+      .amount(tokenAmount)
+      .rewardTokenType(tokenType)
+      .thirdPartyTokenId(0L)
+      .build();
+    userTokenRewardsMapper.insert(data);
+  }
+  public void create3rdPartyUserTokenRewardsRecord(Long thirdTokenId, BigDecimal tokenAmount, Long userId) {
+    var data = UserTokenRewards.builder()
+      .userId(userId)
+      .amount(tokenAmount)
+      .rewardTokenType(BatchSetting.TokenType.THIRD_PARTY_TOKEN)
+      .thirdPartyTokenId(thirdTokenId)
+      .build();
+    userTokenRewardsMapper.insert(data);
+  }
+  public void updateUserTokenRewardsRecord(Long id, BigDecimal tokenAmount) {
+    userTokenRewardsMapper.update(null, new UpdateWrapper<UserTokenRewards>().lambda()
+      .eq(UserTokenRewards::getId, id)
+      .eq(UserTokenRewards::getDeleted, false)
+      .set(UserTokenRewards::getAmount, tokenAmount)
+    );
+  }
+
+
+  public void createUserTokenTaskRewardsRecord(BatchSetting.TokenType tokenType, JobUser jobUser, BigDecimal tokenAmount) {
+    var data = UserTokenTaskRewards.builder()
+      .userId(jobUser.getUserId())
+      .jobId(jobUser.getTaskListSessionId())
+      .amount(tokenAmount)
+      .thirdPartyTokenId(0L)
+      .rewardTokenType(tokenType)
+      .build();
+    userTokenTaskRewardsMapper.insert(data);
+  }
+  public void create3rdPartyUserTokenTaskRewardsRecord(Long tokenId, JobUser jobUser, BigDecimal tokenAmount) {
+    var data = UserTokenTaskRewards.builder()
+      .userId(jobUser.getUserId())
+      .jobId(jobUser.getTaskListSessionId())
+      .amount(tokenAmount)
+      .thirdPartyTokenId(tokenId)
+      .rewardTokenType(BatchSetting.TokenType.THIRD_PARTY_TOKEN)
+      .build();
+    userTokenTaskRewardsMapper.insert(data);
+  }
+
+  public void createUserTokenRewardClaimsRecord(BatchSetting.TokenType tokenType, BigDecimal tokenAmount, JobUser jobUser) {
+    var data = UserTokenRewardClaims.builder()
+      .userId(jobUser.getUserId())
+      .amount(tokenAmount)
+      .rewardTokenType(tokenType)
+      .thirdPartyTokenId(0L)
+      .status(UserTokenRewardClaims.TokenClaimStatus.PENDING)
+      .build();
+    userTokenRewardClaimsMapper.insert(data);
+  }
+  public void create3rdPartyUserTokenRewardClaimsRecord(Long tokenId, BigDecimal tokenAmount, JobUser jobUser) {
+    var data = UserTokenRewardClaims.builder()
+      .userId(jobUser.getUserId())
+      .amount(tokenAmount)
+      .rewardTokenType(BatchSetting.TokenType.THIRD_PARTY_TOKEN)
+      .thirdPartyTokenId(tokenId)
+      .status(UserTokenRewardClaims.TokenClaimStatus.PENDING)
+      .build();
+    userTokenRewardClaimsMapper.insert(data);
+  }
+  public void listUserTokenRewardClaimsRecordsByTimeRank(Timestamp start, Timestamp end, UserTokenRewardClaims.TokenClaimStatus status) {
+    userTokenRewardClaimsMapper.selectList(new QueryWrapper<UserTokenRewardClaims>().lambda()
+      .ge(UserTokenRewardClaims::getCreatedAt, start)
+      .le(UserTokenRewardClaims::getCreatedAt, end)
+      .eq(UserTokenRewardClaims::getStatus, status)
+      .eq(UserTokenRewardClaims::getDeleted, false)
+    );
+  }
+  public void updateUserTokenRewardClaimsRecord(Long id, UserTokenRewardClaims.TokenClaimStatus status) {
+    userTokenRewardClaimsMapper.update(null, new UpdateWrapper<UserTokenRewardClaims>().lambda()
+      .eq(UserTokenRewardClaims::getId, id)
+      .set(UserTokenRewardClaims::getStatus, status)
+    );
+  }
+}
diff --git a/src/main/java/ai/saharaa/mappers/newTasks/ThirdPartyRewardTokenMapper.java b/src/main/java/ai/saharaa/mappers/newTasks/ThirdPartyRewardTokenMapper.java
new file mode 100644
index 00000000..74dcf87f
--- /dev/null
+++ b/src/main/java/ai/saharaa/mappers/newTasks/ThirdPartyRewardTokenMapper.java
@@ -0,0 +1,6 @@
+package ai.saharaa.mappers.newTasks;
+
+import ai.saharaa.model.newTasks.ThirdPartyRewardToken;
+import com.github.yulichang.base.MPJBaseMapper;
+
+public interface ThirdPartyRewardTokenMapper extends MPJBaseMapper<ThirdPartyRewardToken> {}
diff --git a/src/main/java/ai/saharaa/mappers/newTasks/UserTokenRewardClaimsMapper.java b/src/main/java/ai/saharaa/mappers/newTasks/UserTokenRewardClaimsMapper.java
new file mode 100644
index 00000000..f5e44911
--- /dev/null
+++ b/src/main/java/ai/saharaa/mappers/newTasks/UserTokenRewardClaimsMapper.java
@@ -0,0 +1,6 @@
+package ai.saharaa.mappers.newTasks;
+
+import ai.saharaa.model.newTasks.UserTokenRewardClaims;
+import com.github.yulichang.base.MPJBaseMapper;
+
+public interface UserTokenRewardClaimsMapper extends MPJBaseMapper<UserTokenRewardClaims> {}
diff --git a/src/main/java/ai/saharaa/mappers/newTasks/UserTokenRewardsMapper.java b/src/main/java/ai/saharaa/mappers/newTasks/UserTokenRewardsMapper.java
new file mode 100644
index 00000000..4ea79eee
--- /dev/null
+++ b/src/main/java/ai/saharaa/mappers/newTasks/UserTokenRewardsMapper.java
@@ -0,0 +1,6 @@
+package ai.saharaa.mappers.newTasks;
+
+import ai.saharaa.model.newTasks.UserTokenRewards;
+import com.github.yulichang.base.MPJBaseMapper;
+
+public interface UserTokenRewardsMapper extends MPJBaseMapper<UserTokenRewards> {}
diff --git a/src/main/java/ai/saharaa/mappers/newTasks/UserTokenTaskRewardsMapper.java b/src/main/java/ai/saharaa/mappers/newTasks/UserTokenTaskRewardsMapper.java
new file mode 100644
index 00000000..1fcbc4c4
--- /dev/null
+++ b/src/main/java/ai/saharaa/mappers/newTasks/UserTokenTaskRewardsMapper.java
@@ -0,0 +1,6 @@
+package ai.saharaa.mappers.newTasks;
+
+import ai.saharaa.model.newTasks.UserTokenTaskRewards;
+import com.github.yulichang.base.MPJBaseMapper;
+
+public interface UserTokenTaskRewardsMapper extends MPJBaseMapper<UserTokenTaskRewards> {}
diff --git a/src/main/java/ai/saharaa/model/BatchSetting.java b/src/main/java/ai/saharaa/model/BatchSetting.java
index 728a8a63..e4b2c0a1 100644
--- a/src/main/java/ai/saharaa/model/BatchSetting.java
+++ b/src/main/java/ai/saharaa/model/BatchSetting.java
@@ -11,6 +11,7 @@ import com.baomidou.mybatisplus.annotation.TableName;
 import com.fasterxml.jackson.annotation.JsonValue;
 import java.io.Serial;
 import java.io.Serializable;
+import java.math.BigDecimal;
 import java.sql.Timestamp;
 import java.util.List;
 import lombok.AllArgsConstructor;
@@ -71,6 +72,7 @@ public class BatchSetting implements Serializable {
 
   private TaskPaymentType taskPaymentType;
   private Long totalBudgetPoints;
+  private BigDecimal totalBudgetTokens;
 
   private AnnotationMethod annotationMethod;
   private Integer requiredUserLevel;
@@ -86,6 +88,7 @@ public class BatchSetting implements Serializable {
   private DistributeType distributeType;
 
   private Timestamp cutoffTime;
+  private TokenType rewardTokenType;
 
   @Serial
   private static final long serialVersionUID = 1L;
@@ -165,6 +168,25 @@ public class BatchSetting implements Serializable {
     }
   }
 
+  public enum TokenType implements BaseIntegerEnum {
+    SAHARA(0),
+    USD1(1),
+    THIRD_PARTY_TOKEN(9);
+
+    @EnumValue
+    @JsonValue
+    public final Integer value;
+
+    TokenType(Integer value) {
+      this.value = value;
+    }
+
+    @Override
+    public Integer getValue() {
+      return value;
+    }
+  }
+
   public enum TaskPaymentType implements BaseEnum {
     TRADITIONAL("traditional"),
     REWARD_POOL("reward_pool");
diff --git a/src/main/java/ai/saharaa/model/newTasks/ThirdPartyRewardToken.java b/src/main/java/ai/saharaa/model/newTasks/ThirdPartyRewardToken.java
new file mode 100644
index 00000000..e82c8d39
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTasks/ThirdPartyRewardToken.java
@@ -0,0 +1,41 @@
+package ai.saharaa.model.newTasks;
+
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "third_party_reward_token", autoResultMap = true)
+public class ThirdPartyRewardToken implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long resourceId;
+  private Long batchId;
+  private BigDecimal amount;
+  private String tokenName;
+
+  private Boolean tgeAlready;
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+//  id                BIGSERIAL       PRIMARY KEY,
+//  resource_id       BIGINT          NOT NULL,
+//  batch_id          BIGINT          NOT NULL,
+//  amount            numeric(24, 6)  NOT NULL,
+//  token_name        varchar(255)    NOT NULL,
+//  deleted           BOOLEAN         NOT NULL DEFAULT false,
+//  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+//  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+}
diff --git a/src/main/java/ai/saharaa/model/newTasks/UserTokenRewardClaims.java b/src/main/java/ai/saharaa/model/newTasks/UserTokenRewardClaims.java
new file mode 100644
index 00000000..413bc0bf
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTasks/UserTokenRewardClaims.java
@@ -0,0 +1,55 @@
+package ai.saharaa.model.newTasks;
+
+import ai.saharaa.enums.BaseIntegerEnum;
+import ai.saharaa.model.BatchSetting;
+import com.baomidou.mybatisplus.annotation.EnumValue;
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import com.fasterxml.jackson.annotation.JsonValue;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "user_token_reward_claims", autoResultMap = true)
+public class UserTokenRewardClaims implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long userId;
+  private BigDecimal amount;
+  private BatchSetting.TokenType rewardTokenType;
+  private TokenClaimStatus status;
+  private Long thirdPartyTokenId;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+  public enum TokenClaimStatus implements BaseIntegerEnum {
+    PENDING(0),
+    DISTRIBUTED(1),
+    FAILED(2);
+
+    @EnumValue
+    @JsonValue
+    public final Integer value;
+
+    TokenClaimStatus(Integer value) {
+      this.value = value;
+    }
+
+    @Override
+    public Integer getValue() {
+      return value;
+    }
+  }
+}
diff --git a/src/main/java/ai/saharaa/model/newTasks/UserTokenRewards.java b/src/main/java/ai/saharaa/model/newTasks/UserTokenRewards.java
new file mode 100644
index 00000000..543c44c6
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTasks/UserTokenRewards.java
@@ -0,0 +1,32 @@
+package ai.saharaa.model.newTasks;
+
+import ai.saharaa.model.BatchSetting;
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "user_token_rewards", autoResultMap = true)
+public class UserTokenRewards implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long userId;
+  private BigDecimal amount;
+  private BatchSetting.TokenType rewardTokenType;
+  private Long thirdPartyTokenId;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+}
diff --git a/src/main/java/ai/saharaa/model/newTasks/UserTokenTaskRewards.java b/src/main/java/ai/saharaa/model/newTasks/UserTokenTaskRewards.java
new file mode 100644
index 00000000..e8d7df98
--- /dev/null
+++ b/src/main/java/ai/saharaa/model/newTasks/UserTokenTaskRewards.java
@@ -0,0 +1,41 @@
+package ai.saharaa.model.newTasks;
+
+import ai.saharaa.model.BatchSetting;
+import com.baomidou.mybatisplus.annotation.*;
+import lombok.AllArgsConstructor;
+import lombok.Builder;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+import java.io.Serializable;
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+
+@Data
+@Builder
+@NoArgsConstructor
+@AllArgsConstructor
+@TableName(value = "user_token_task_rewards", autoResultMap = true)
+public class UserTokenTaskRewards implements Serializable {
+  @TableId(type = IdType.AUTO)
+  private Long id;
+  private Long userId;
+  private Long jobId;
+  private BigDecimal amount;
+  private BatchSetting.TokenType rewardTokenType;
+  private Long thirdPartyTokenId;
+
+  private Boolean deleted;
+  private Timestamp createdAt;
+  private Timestamp updatedAt;
+
+//  id                    BIGSERIAL       PRIMARY KEY,
+//  user_id               BIGINT          NOT NULL,
+//  job_id                BIGINT          NOT NULL,
+//  amount                numeric(20, 6)  NOT NULL,
+//  reward_token_type     smallint        NOT NULL DEFAULT 0,
+//  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+//  deleted               BOOLEAN         NOT NULL DEFAULT false,
+//  created_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+//  updated_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+}
diff --git a/src/main/java/ai/saharaa/services/JobUserPointPreCalcService.java b/src/main/java/ai/saharaa/services/JobUserPointPreCalcService.java
index 88728b63..bea023f8 100644
--- a/src/main/java/ai/saharaa/services/JobUserPointPreCalcService.java
+++ b/src/main/java/ai/saharaa/services/JobUserPointPreCalcService.java
@@ -10,10 +10,13 @@ import ai.saharaa.dto.task.JobUserPointsGroupedForBonusDTO;
 import ai.saharaa.mappers.JobUserPointsMapper;
 import ai.saharaa.mappers.season.JobUserPointsPreCalcMapper;
 import ai.saharaa.model.Batch;
+import ai.saharaa.model.BatchSetting;
 import ai.saharaa.model.JobUser;
 import ai.saharaa.model.JobUserPoints;
+import ai.saharaa.model.newTasks.ThirdPartyRewardToken;
 import ai.saharaa.model.season.JobUserPointsPreCalc;
 import ai.saharaa.model.season.SeasonUserPointsDetail;
+import ai.saharaa.services.newTasks.NewTasksService;
 import ai.saharaa.services.season.rule.SeasonRuleMarch;
 import ai.saharaa.utils.DateUtils;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
@@ -49,6 +52,7 @@ public class JobUserPointPreCalcService
   private final ReviewSessionDao reviewSessionDao;
   private final SeasonDao seasonDao;
   private final AchievementRouter achievementRouter;
+  private final NewTasksService newTasksService;
 
   public JobUserPointPreCalcService(
       JobUserPointsMapper jobUserPointsMapper,
@@ -62,6 +66,7 @@ public class JobUserPointPreCalcService
       JobUserDao jobUserDao,
       TaskSessionDao taskSessionDao,
       ReviewSessionDao reviewSessionDao,
+      NewTasksService newTasksService,
       JobUserPointsPreCalcMapper jobUserPointsPreCalcMapper) {
     this.jobUserPointsPreCalcMapper = jobUserPointsPreCalcMapper;
     this.notificationService = notificationService;
@@ -74,6 +79,7 @@ public class JobUserPointPreCalcService
     this.individualsDao = individualsDao;
     this.meterRegistry = meterRegistry;
     this.jobUserDao = jobUserDao;
+    this.newTasksService = newTasksService;
     this.seasonDao = seasonDao;
   }
 
@@ -343,37 +349,25 @@ public class JobUserPointPreCalcService
   }
 
   @Transactional
-  public void doDisPointInNewMode(JobUser waitingJobUser, Batch batch, BigDecimal sessionPrice) {
+  public void doDisPointInNewMode(JobUser waitingJobUser, Batch batch, BigDecimal sessionPrice, BatchSetting batchSetting, List<ThirdPartyRewardToken> task3rdRewardToken) {
     var finishedSessionCount = waitingJobUser.getRole().equals(JobUser.JobUserRole.LABELER)
         ? taskSessionDao.getFinishedSessionCount(waitingJobUser.getId())
         : reviewSessionDao.getFinishedSessionCount(waitingJobUser.getId());
     var targetPoints = sessionPrice
         .multiply(BigDecimal.valueOf(finishedSessionCount))
-        .setScale(1, RoundingMode.FLOOR);
+        .setScale(4, RoundingMode.FLOOR);
 
-    var jobUserPointsObj = JobUserPoints.builder()
-        .jobId(waitingJobUser.getTaskListSessionId())
-        .userId(waitingJobUser.getUserId())
-        .sessionCount(finishedSessionCount)
-        .price(sessionPrice)
-        .endCountAt(DateUtils.now())
-        .rewardType(
-            waitingJobUser.getRole().equals(JobUser.JobUserRole.LABELER)
-                ? JobUserPoints.JobUserPointsRewardType.ANNOTATE
-                : JobUserPoints.JobUserPointsRewardType.REVIEW)
-        .build();
-    jobUserPointsMapper.insert(jobUserPointsObj);
+    newTasksService.createUserTokenTaskRewardsRecord(batchSetting.getRewardTokenType(), waitingJobUser, targetPoints);
+    for (ThirdPartyRewardToken thirdPartyRewardToken : task3rdRewardToken) {
+      var targetPointsOf3rd = targetPoints.multiply(batchSetting.getTotalBudgetTokens().divide(thirdPartyRewardToken.getAmount(), 6, RoundingMode.FLOOR))
+        .setScale(4, RoundingMode.FLOOR);
+      newTasksService.create3rdPartyUserTokenTaskRewardsRecord(thirdPartyRewardToken.getId(), waitingJobUser, targetPointsOf3rd);
+    }
     meterRegistry.counter(USER_DATA_POINTS_COUNTER).increment(targetPoints.floatValue());
 
-    var stat = JobUserStat2.builder()
-        .jup(jobUserPointsObj)
-        .ju(waitingJobUser)
-        .basePoints(targetPoints)
-        .build();
-    appendPointsDetail2(batch, stat);
     if (targetPoints.compareTo(BigDecimal.ONE) > 0) {
-      notificationService.noticeUserGetSaharaPoint(
-          jobUserPointsObj.getUserId(), targetPoints, jobUserPointsObj.getJobId());
+//      notificationService.noticeUserGetSaharaPoint( // new notify
+//          jobUserPointsObj.getUserId(), targetPoints, jobUserPointsObj.getJobId());
     }
     waitingJobUser.setPointsDistributed(true);
     jobUserDao.updateJobUserById(waitingJobUser);
diff --git a/src/main/java/ai/saharaa/services/JobUserService.java b/src/main/java/ai/saharaa/services/JobUserService.java
index 2fb78385..7f3fb97c 100644
--- a/src/main/java/ai/saharaa/services/JobUserService.java
+++ b/src/main/java/ai/saharaa/services/JobUserService.java
@@ -8,6 +8,7 @@ import static ai.saharaa.utils.Constants.ROLE_USER;
 
 import ai.saharaa.config.ChainConfig;
 import ai.saharaa.daos.*;
+import ai.saharaa.daos.newTasks.NewTasksDao;
 import ai.saharaa.distribution.CommonCounterManager;
 import ai.saharaa.distribution.Contants.Constants;
 import ai.saharaa.dto.job.JobFields;
@@ -60,6 +61,7 @@ public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
   private final IndividualsDao individualsDao;
   private final JobDao jobDao;
   private final BatchDao batchDao;
+  private final NewTasksDao newTasksDao;
   private final TaskSessionDao taskSessionDao;
   private final ReviewSessionDao reviewSessionDao;
   private final JobUserPointPreCalcService jobUserPointPreCalcService;
@@ -76,6 +78,7 @@ public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
       JobUserMapper jobUserMapper,
       UserMapper userMapper,
       JobUserDao jobUserDao,
+      NewTasksDao newTasksDao,
       ChainConfig chainConfig,
       NotificationService notificationService,
       IndividualsDao individualsDao,
@@ -96,6 +99,7 @@ public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
     this.examSessionMapper = examSessionMapper;
     this.jobUserDao = jobUserDao;
     this.chainConfig = chainConfig;
+    this.newTasksDao = newTasksDao;
     this.notificationService = notificationService;
     this.individualsDao = individualsDao;
     this.jobDao = jobDao;
@@ -585,6 +589,7 @@ public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
     var batch = batchDao
         .getBatchById(job.getBatchId())
         .orElseThrow(() -> ControllerUtils.notFound("invalid data"));
+    var task3rdRewardToken = newTasksDao.listThirdPartyRewardTokenRecordsByBatchId(batch.getId());
     var batchSetting = batchDao.getBatchSettingById(batch.getId());
     var annotatingCompleteCount = taskSessionDao.countPendingSpotTaskSessionsByJobId(jobId);
     var maxCompleteCount =
@@ -595,15 +600,15 @@ public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
     var eachReviewingPoints = BigDecimal.ZERO;
     if (!batch.getReviewerRequired()) {
       eachAnnotatingPoints = distributingTotalPoints.divide(
-          BigDecimal.valueOf(annotatingCompleteCount), 1, RoundingMode.FLOOR);
+          BigDecimal.valueOf(annotatingCompleteCount), 5, RoundingMode.FLOOR);
     } else {
       var reviewingCompleteCount = reviewSessionDao.countCompleteSessionCount(jobId);
       var ratioSum = reviewingCompleteCount * batch.getReviewingPrice().floatValue()
           + annotatingCompleteCount * batch.getAnnotatingPrice().floatValue();
       var priceCell =
-          distributingTotalPoints.divide(BigDecimal.valueOf(ratioSum), 2, RoundingMode.FLOOR);
-      eachAnnotatingPoints = batch.getAnnotatingPrice().multiply(priceCell);
-      eachReviewingPoints = batch.getReviewingPrice().multiply(priceCell);
+          distributingTotalPoints.divide(BigDecimal.valueOf(ratioSum), 5, RoundingMode.FLOOR);
+      eachAnnotatingPoints = batch.getAnnotatingPrice().multiply(priceCell).setScale(5, RoundingMode.FLOOR);
+      eachReviewingPoints = batch.getReviewingPrice().multiply(priceCell).setScale(5, RoundingMode.FLOOR);
     }
     do {
       var waitingPointsDisJobUsers = jobUserDao.getWaitingPointsUsers(jobId, 300);
@@ -617,7 +622,7 @@ public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
           if (waitingJobUser.getRole().equals(LABELER)) sessionPrice = finalEachAnnotatingPoints;
           if (waitingJobUser.getRole().equals(REVIEWER)) sessionPrice = finalEachReviewingPoints;
           if (sessionPrice.floatValue() > 0.001f) {
-            jobUserPointPreCalcService.doDisPointInNewMode(waitingJobUser, batch, sessionPrice);
+            jobUserPointPreCalcService.doDisPointInNewMode(waitingJobUser, batch, sessionPrice, batchSetting, task3rdRewardToken);
           }
         });
       }
diff --git a/src/main/java/ai/saharaa/services/newTasks/NewTasksService.java b/src/main/java/ai/saharaa/services/newTasks/NewTasksService.java
new file mode 100644
index 00000000..3dd79d0b
--- /dev/null
+++ b/src/main/java/ai/saharaa/services/newTasks/NewTasksService.java
@@ -0,0 +1,151 @@
+package ai.saharaa.services.newTasks;
+
+import ai.saharaa.config.AuditLogInterceptor.RequestUserAgent;
+import ai.saharaa.mappers.AuditLogMapper;
+import ai.saharaa.mappers.newTasks.ThirdPartyRewardTokenMapper;
+import ai.saharaa.mappers.newTasks.UserTokenRewardClaimsMapper;
+import ai.saharaa.mappers.newTasks.UserTokenRewardsMapper;
+import ai.saharaa.mappers.newTasks.UserTokenTaskRewardsMapper;
+import ai.saharaa.model.*;
+import ai.saharaa.model.newTasks.ThirdPartyRewardToken;
+import ai.saharaa.model.newTasks.UserTokenRewardClaims;
+import ai.saharaa.model.newTasks.UserTokenRewards;
+import ai.saharaa.model.newTasks.UserTokenTaskRewards;
+import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
+import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.stereotype.Service;
+
+import java.math.BigDecimal;
+import java.sql.Timestamp;
+import java.util.Optional;
+
+@Service
+@Slf4j
+public class NewTasksService {
+  private final ThirdPartyRewardTokenMapper thirdPartyRewardTokenMapper;
+  private final UserTokenRewardsMapper userTokenRewardsMapper;
+  private final UserTokenTaskRewardsMapper userTokenTaskRewardsMapper;
+  private final UserTokenRewardClaimsMapper userTokenRewardClaimsMapper;
+
+  public NewTasksService(
+    ThirdPartyRewardTokenMapper thirdPartyRewardTokenMapper,
+    UserTokenRewardsMapper userTokenRewardsMapper,
+    UserTokenTaskRewardsMapper userTokenTaskRewardsMapper,
+    UserTokenRewardClaimsMapper userTokenRewardClaimsMapper
+  ) {
+    this.thirdPartyRewardTokenMapper = thirdPartyRewardTokenMapper;
+    this.userTokenRewardsMapper = userTokenRewardsMapper;
+    this.userTokenTaskRewardsMapper = userTokenTaskRewardsMapper;
+    this.userTokenRewardClaimsMapper = userTokenRewardClaimsMapper;
+  }
+
+  public void createThirdPartyRewardTokenRecord(BigDecimal tokenAmount, String tokenName, Long batchId, Long iconResId, Boolean tgeAlready) {
+    var inserting = ThirdPartyRewardToken.builder()
+      .amount(tokenAmount)
+      .batchId(batchId)
+      .tokenName(tokenName)
+      .resourceId(iconResId)
+      .tgeAlready(tgeAlready)
+      .build();
+    thirdPartyRewardTokenMapper.insert(inserting);
+  }
+  public void listThirdPartyRewardTokenRecordsByBatchId(Long batchId) {
+    thirdPartyRewardTokenMapper.selectList(new QueryWrapper<ThirdPartyRewardToken>().lambda()
+      .eq(ThirdPartyRewardToken::getBatchId, batchId)
+      .eq(ThirdPartyRewardToken::getDeleted, false)
+    );
+  }
+  public void updateThirdPartyRewardTokenRecord(ThirdPartyRewardToken updatingData) {
+    thirdPartyRewardTokenMapper.updateById(updatingData);
+  }
+  public void deleteThirdPartyRewardTokenRecordById(Long id) {
+    thirdPartyRewardTokenMapper.update(null, new UpdateWrapper<ThirdPartyRewardToken>().lambda()
+      .eq(ThirdPartyRewardToken::getId, id)
+      .set(ThirdPartyRewardToken::getDeleted, true)
+    );
+  }
+
+  public void createUserTokenRewardsRecord(BatchSetting.TokenType tokenType, BigDecimal tokenAmount, Long userId) {
+    var data = UserTokenRewards.builder()
+      .userId(userId)
+      .amount(tokenAmount)
+      .rewardTokenType(tokenType)
+      .thirdPartyTokenId(0L)
+      .build();
+    userTokenRewardsMapper.insert(data);
+  }
+  public void create3rdPartyUserTokenRewardsRecord(Long thirdTokenId, BigDecimal tokenAmount, Long userId) {
+    var data = UserTokenRewards.builder()
+      .userId(userId)
+      .amount(tokenAmount)
+      .rewardTokenType(BatchSetting.TokenType.THIRD_PARTY_TOKEN)
+      .thirdPartyTokenId(thirdTokenId)
+      .build();
+    userTokenRewardsMapper.insert(data);
+  }
+  public void updateUserTokenRewardsRecord(Long id, BigDecimal tokenAmount) {
+    userTokenRewardsMapper.update(null, new UpdateWrapper<UserTokenRewards>().lambda()
+      .eq(UserTokenRewards::getId, id)
+      .eq(UserTokenRewards::getDeleted, false)
+      .set(UserTokenRewards::getAmount, tokenAmount)
+    );
+  }
+
+
+  public void createUserTokenTaskRewardsRecord(BatchSetting.TokenType tokenType, JobUser jobUser, BigDecimal tokenAmount) {
+    var data = UserTokenTaskRewards.builder()
+      .userId(jobUser.getUserId())
+      .jobId(jobUser.getTaskListSessionId())
+      .amount(tokenAmount)
+      .thirdPartyTokenId(0L)
+      .rewardTokenType(tokenType)
+      .build();
+    userTokenTaskRewardsMapper.insert(data);
+  }
+  public void create3rdPartyUserTokenTaskRewardsRecord(Long tokenId, JobUser jobUser, BigDecimal tokenAmount) {
+    var data = UserTokenTaskRewards.builder()
+      .userId(jobUser.getUserId())
+      .jobId(jobUser.getTaskListSessionId())
+      .amount(tokenAmount)
+      .thirdPartyTokenId(tokenId)
+      .rewardTokenType(BatchSetting.TokenType.THIRD_PARTY_TOKEN)
+      .build();
+    userTokenTaskRewardsMapper.insert(data);
+  }
+
+  public void createUserTokenRewardClaimsRecord(BatchSetting.TokenType tokenType, BigDecimal tokenAmount, JobUser jobUser) {
+    var data = UserTokenRewardClaims.builder()
+      .userId(jobUser.getUserId())
+      .amount(tokenAmount)
+      .rewardTokenType(tokenType)
+      .thirdPartyTokenId(0L)
+      .status(UserTokenRewardClaims.TokenClaimStatus.PENDING)
+      .build();
+    userTokenRewardClaimsMapper.insert(data);
+  }
+  public void create3rdPartyUserTokenRewardClaimsRecord(Long tokenId, BigDecimal tokenAmount, JobUser jobUser) {
+    var data = UserTokenRewardClaims.builder()
+      .userId(jobUser.getUserId())
+      .amount(tokenAmount)
+      .rewardTokenType(BatchSetting.TokenType.THIRD_PARTY_TOKEN)
+      .thirdPartyTokenId(tokenId)
+      .status(UserTokenRewardClaims.TokenClaimStatus.PENDING)
+      .build();
+    userTokenRewardClaimsMapper.insert(data);
+  }
+  public void listUserTokenRewardClaimsRecordsByTimeRank(Timestamp start, Timestamp end, UserTokenRewardClaims.TokenClaimStatus status) {
+    userTokenRewardClaimsMapper.selectList(new QueryWrapper<UserTokenRewardClaims>().lambda()
+      .ge(UserTokenRewardClaims::getCreatedAt, start)
+      .le(UserTokenRewardClaims::getCreatedAt, end)
+      .eq(UserTokenRewardClaims::getStatus, status)
+      .eq(UserTokenRewardClaims::getDeleted, false)
+    );
+  }
+  public void updateUserTokenRewardClaimsRecord(Long id, UserTokenRewardClaims.TokenClaimStatus status) {
+    userTokenRewardClaimsMapper.update(null, new UpdateWrapper<UserTokenRewardClaims>().lambda()
+      .eq(UserTokenRewardClaims::getId, id)
+      .set(UserTokenRewardClaims::getStatus, status)
+    );
+  }
+}
diff --git a/src/main/resources/db/migration/V49_22__publicTaskUpdate.sql b/src/main/resources/db/migration/V49_22__publicTaskUpdate.sql
new file mode 100644
index 00000000..a65dde6e
--- /dev/null
+++ b/src/main/resources/db/migration/V49_22__publicTaskUpdate.sql
@@ -0,0 +1,60 @@
+ALTER TABLE batch_setting ADD COLUMN IF NOT EXISTS reward_token_type smallint not null default 0;
+ALTER TABLE batch_setting ADD COLUMN IF NOT EXISTS total_budget_tokens numeric(24, 6) NOT NULL default 0.000000,
+
+CREATE TABLE IF NOT EXISTS third_party_reward_token
+(
+  id                BIGSERIAL       PRIMARY KEY,
+  resource_id       BIGINT          NOT NULL,
+  batch_id          BIGINT          NOT NULL,
+  amount            numeric(24, 6)  NOT NULL,
+  token_name        varchar(255)    NOT NULL,
+  tge_already       BOOLEAN         NOT NULL DEFAULT false,
+  deleted           BOOLEAN         NOT NULL DEFAULT false,
+  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+CREATE INDEX IF NOT EXISTS third_party_reward_token_batch_idx ON third_party_reward_token (batch_id);
+
+CREATE TABLE IF NOT EXISTS user_token_task_rewards
+(
+  id                    BIGSERIAL       PRIMARY KEY,
+  user_id               BIGINT          NOT NULL,
+  job_id                BIGINT          NOT NULL,
+  amount                numeric(20, 6)  NOT NULL,
+  reward_token_type     smallint        NOT NULL DEFAULT 0,
+  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+  deleted               BOOLEAN         NOT NULL DEFAULT false,
+  created_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at            TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+
+CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_idx ON user_token_task_rewards (user_id);
+CREATE INDEX IF NOT EXISTS user_token_task_rewards_uid_status_idx ON user_token_task_rewards (user_id, status);
+
+CREATE TABLE IF NOT EXISTS user_token_rewards
+(
+  id                BIGSERIAL       PRIMARY KEY,
+  user_id           BIGINT          NOT NULL,
+  amount            numeric(20, 6)  NOT NULL,
+  reward_token_type smallint        NOT NULL DEFAULT 0,
+  third_party_token_id  BIGINT          NOT NULL DEFAULT 0,
+  deleted           BOOLEAN         NOT NULL DEFAULT false,
+  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+CREATE INDEX IF NOT EXISTS user_token_rewards_uid_idx ON user_token_rewards (user_id);
+
+
+CREATE TABLE IF NOT EXISTS user_token_reward_claims
+(
+  id                BIGSERIAL       PRIMARY KEY,
+  user_id           BIGINT          NOT NULL,
+  amount            numeric(20, 6)  NOT NULL,
+  reward_token_type smallint        NOT NULL DEFAULT 0,
+  status            smallint        NOT NULL DEFAULT 0,
+  tx_hash           varchar(255)    NOT NULL DEFAULT '',
+  deleted           BOOLEAN         NOT NULL DEFAULT false,
+  created_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
+  updated_at        TIMESTAMPTZ     NOT NULL DEFAULT NOW()
+);
+CREATE INDEX IF NOT EXISTS user_token_reward_claims_uid_idx ON user_token_reward_claims (user_id);
