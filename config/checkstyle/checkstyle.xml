<?xml version="1.0"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>

    <property name="severity" value="error"/>

    <property name="fileExtensions" value="java, properties, xml"/>

    <module name="TreeWalker">
        <module name="MethodLength">
            <!-- Consider further cutting down these limits -->
            <property name="max" value="200"/>
            <property name="countEmpty" value="false"/>
        </module>
        <module name="CyclomaticComplexity">
            <property name="max" value="36"/>
            <property name="switchBlockAsSingleDecisionPoint" value="true"/>
        </module>
    </module>
</module>