-- Step 1: Create composite index to speed up checking visibility process
CREATE INDEX idx_hybrid_task_resource_task_id_resource_id
  ON public.hybrid_task_resource (task_id, resource_id);


-- Step 2: Add visibility column to resource table
ALTER TABLE resource ADD COLUMN visibility varchar(64) DEFAULT 'public' NOT NULL;


-- Step 3: Update existing resource visibilities
UPDATE resource
SET visibility = 'task'
WHERE id IN (
  SELECT resource_id FROM task
  UNION
  SELECT resource_id FROM hybrid_task_resource
);
