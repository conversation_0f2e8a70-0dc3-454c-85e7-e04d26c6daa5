CREATE TABLE IF NOT EXISTS white_list_drop_points_record (
   id bigserial NOT NULL,
   unregistered_wallet_address jsonb DEFAULT NULL,
   dropped_wallet_address jsonb DEFAULT NULL,
   points int8 DEFAULT 0 NOT NULL,
   "type" varchar(25550) DEFAULT ''::character varying NULL,
   created_at timestamptz DEFAULT now() NOT NULL,
   updated_at timestamptz DEFAULT now() NOT NULL,
   CONSTRAINT white_list_drop_points_record_pkey PRIMARY KEY (id)
);

