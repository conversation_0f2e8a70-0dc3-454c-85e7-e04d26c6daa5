
-- create settings table for user, supports key/value pairs
create table user_setting (
                             id bigserial primary key,
                             user_id bigint not null references users(id),
                             name varchar(255) not null,
                             content varchar(255) not null,
                             deleted boolean default false,
                             created_at timestamp default now(),
                             updated_at timestamp default now()
);

-- add name as index
create index user_settings_name_idx on user_setting(name);