-- Create table season_achievement_on_contract
CREATE TABLE IF NOT EXISTS achievement_on_contract
(
  id               BIGSERIAL PRIMARY KEY,
  achievement_id   BIGINT      NOT NULL, -- new achievement on contract achievementId
  season_id        BIGINT      NULL,
  oracle_symbol    VARCHAR(64) NOT NULL DEFAULT '',
  contract_address VARCHAR(64) NULL,
  active           BOOLEAN     NOT NULL DEFAULT true,
  deleted          BOOLEAN     NOT NULL DEFAULT false,
  created_at       TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at       TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS achievement_on_contract_achievement_id_idx ON achievement_on_contract USING btree (achievement_id);
CREATE INDEX IF NOT EXISTS achievement_on_contract_season_id_idx ON achievement_on_contract USING btree (season_id);
CREATE INDEX IF NOT EXISTS achievement_on_contract_oracle_symbol_idx ON achievement_on_contract USING btree (oracle_symbol);

-- drop fk season_user_exp_detail(achievement_id)
ALTER TABLE season_user_exp_detail
  DROP CONSTRAINT IF EXISTS fk_season_user_exp_detail_achievement_id;
