
create table node_invitation_code (
  id bigserial primary key,
  node_id bigint not null references node(id),
  manager_id bigint not null references users(id),
  code varchar(255) not null,
  expired_time timestamptz not null,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

create index node_invitation_code_code_idx on node_invitation_code(code);
