CREATE TABLE IF NOT EXISTS abnormal_rec
(
    id                          bigserial PRIMARY KEY,
    user_id                     bigint NOT NULL,
    job_id                      bigint NOT NULL,
    count                       int NOT NULL default 0,
    detail                      VARCHAR(255) NOT NULL DEFAULT '',

    deleted                     boolean NOT NULL default false,
    created_at                  timestamptz not null default now(),
    updated_at                  timestamptz not null default now()
);

CREATE INDEX IF NOT EXISTS abnormal_rec_user_idx ON abnormal_rec (user_id) where deleted = false;
CREATE INDEX IF NOT EXISTS abnormal_rec_user_job_idx ON abnormal_rec (user_id, job_id) where deleted = false;
