alter table batch
alter column difficulty type varchar(128) using difficulty::varchar(128);

alter table batch
  alter column difficulty set default 'beginner';

alter table batch
  add annotating_price integer;

alter table batch
  rename column submit_required to annotating_submit_required;

alter table batch
  add reviewing_price integer;
alter table batch
  add reviewing_required_datapoint integer;
alter table batch
  add reviewing_times_review_per_datapoint integer;

alter table batch
  add bonus_required_accuracy integer;

alter table batch
  add bonus_percentage integer;

alter table batch
  add bonus_minimum_submissions integer;

alter table batch
  rename column passing_threshold to exam_numerator;

alter table batch
  add exam_nenominator integer;

alter table batch
drop column final_qa_percentage;




