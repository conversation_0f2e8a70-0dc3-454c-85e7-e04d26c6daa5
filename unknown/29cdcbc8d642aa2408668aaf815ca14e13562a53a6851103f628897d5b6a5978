-- ALTER TABLE season_user_points_detail ADD COLUMN IF NOT EXISTS points_backup bigint NOT NULL DEFAULT 0;
-- UPDATE season_user_points_detail set points_backup = points;
--
-- ALTER TABLE job_user_points ADD COLUMN IF NOT EXISTS price_backup bigint NOT NULL DEFAULT 0;
-- UPDATE job_user_points set price_backup = price;
--
ALTER TABLE season_user ADD COLUMN IF NOT EXISTS total_points_backup bigint NOT NULL DEFAULT 0;
UPDATE season_user set total_points_backup = total_points;
--
-- ALTER TABLE season_user ADD COLUMN IF NOT EXISTS season_points_backup bigint NOT NULL DEFAULT 0;
-- UPDATE season_user set season_points_backup = season_points;
--
-- ALTER TABLE batch ADD COLUMN IF NOT EXISTS reviewing_price_backup integer;
-- UPDATE batch set reviewing_price_backup = reviewing_price where reviewing_price is not null;
--
-- ALTER TABLE batch ADD COLUMN IF NOT EXISTS annotating_price_backup integer;
-- UPDATE batch set annotating_price_backup = annotating_price where annotating_price is not null;
