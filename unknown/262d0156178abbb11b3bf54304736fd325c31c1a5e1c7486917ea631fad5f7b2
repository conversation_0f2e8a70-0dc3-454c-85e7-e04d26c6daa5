-- Create table user_level_from_subgraph
CREATE TABLE IF NOT EXISTS user_level_from_subgraph
(
    id              BIGSERIAL       PRIMARY KEY,
    user_id         BIGINT          NOT NULL,
    level           smallint        NOT NULL,
    block_number    BIGINT          NOT NULL,
    deleted         <PERSON><PERSON><PERSON><PERSON><PERSON>         NOT NULL DEFAULT false,
    created_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    updated_at      TIMESTAMPTZ     NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS user_level_from_subgraph_uid_idx ON user_level_from_subgraph (user_id);
