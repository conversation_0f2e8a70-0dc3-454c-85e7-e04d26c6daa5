
-- create a user table in pgsql
create table users (
    id bigserial primary key,
    wallet_address varchar(255) not null unique,
    role int not null default 0,
    active boolean not null default true,
    deleted boolean not null default false,
    first_name varchar(255) not null,
    last_name varchar(255) not null,
    avatar varchar(2048) not null default '',
    tg varchar(255) not null default '',
    imessage varchar(255) not null default '',
    whatsapp varchar(255) not null default '',
    wechat varchar(255) not null default '',
    slack varchar(255) not null default '',
    email varchar(255) not null default '',
    country varchar(255) not null default '',
    languages text not null default '',
    skills text not null default '',
    topics text not null default '',
    data_type text not null default '',
    node_id bigint not null default 0,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);


CREATE INDEX idx_user_tg
    ON users USING btree
    (tg ASC NULLS LAST)
    WITH (deduplicate_items=True);

CREATE INDEX idx_user_imessage
    ON users USING btree
    (imessage ASC NULLS LAST)
    WITH (deduplicate_items=True);

CREATE INDEX idx_user_whatsapp
    ON users USING btree
    (whatsapp ASC NULLS LAST)
    WITH (deduplicate_items=True);

CREATE INDEX idx_user_slack
    ON users USING btree
    (slack ASC NULLS LAST)
    WITH (deduplicate_items=True);

CREATE INDEX idx_user_email
    ON users USING btree
    (email ASC NULLS LAST)
    WITH (deduplicate_items=True);