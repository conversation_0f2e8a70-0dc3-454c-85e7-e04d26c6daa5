
-- create task list table
create table task_list (
  id bigserial primary key,
  batch_id bigint not null,
  owner_id bigint not null,
  list_type varchar(64) not null, -- labeling/exam/example
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_task_list_batch_id foreign key (batch_id) references batch (id),
  constraint fk_task_list_user_id foreign key (owner_id) references users (id)
);
-- add an index for list_type column
create index idx_task_list_list_type on task_list (list_type);

-- a task is formed by a resource and a list of questions
create table task (
  id bigserial primary key,
  task_list_id bigint not null,
  resource_id bigint not null,
  owner_id bigint not null,
  sort int not null default 0, -- task order in the task list
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_task_task_list_id foreign key (task_list_id) references task_list (id),
  constraint fk_task_resource_id foreign key (resource_id) references resource (id),
  constraint fk_task_user_id foreign key (owner_id) references users (id)
);

create table task_question (
  id bigserial primary key,
  task_id bigint not null,
  owner_id bigint not null,
  question text not null,
  question_type varchar(64) not null, -- single, multiple, qa, etc.
  answer text not null,
  sort int not null default 0, -- question order in the task
  deleted boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_task_question_task_id foreign key (task_id) references task (id),
  constraint fk_task_question_user_id foreign key (owner_id) references users (id)
);

create index idx_task_question_question_type on task_question (question_type);

