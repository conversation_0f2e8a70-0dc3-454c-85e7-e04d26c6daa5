CREATE TABLE IF NOT EXISTS workflow_configs
(
  id         BIGSERIAL PRIMARY KEY,
  job_id     BIGINT NOT NULL,
  config     JSONB  NOT NULL,
  created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT,
  updated_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT
);

COMMENT ON TABLE workflow_configs IS 'Table for storing workflow configurations';
COMMENT ON COLUMN workflow_configs.id IS 'Unique identifier for the workflow configuration';
COMMENT ON COLUMN workflow_configs.job_id IS 'Reference to the associated job';
COMMENT ON COLUMN workflow_configs.config IS 'JSON blob containing the workflow configuration';
COMMENT ON COLUMN workflow_configs.created_at IS 'Timestamp of when the record was created (in Unix epoch in milliseconds)';
COMMENT ON COLUMN workflow_configs.updated_at IS 'Timestamp of when the record was last updated (in Unix epoch in milliseconds)';

-- Create a composite index for sorting by updated_at desc
CREATE INDEX IF NOT EXISTS workflow_configs_updated_at_idx ON workflow_configs (updated_at DESC);
-- Add unique constraint on job_id to ensure only one config per job
CREATE UNIQUE INDEX IF NOT EXISTS workflow_configs_job_id_unique_idx ON workflow_configs (job_id);

CREATE TABLE IF NOT EXISTS workflow_runs
(
  id              BIGSERIAL PRIMARY KEY,
  job_id          BIGINT NOT NULL,
  task_session_id BIGINT NOT NULL,
  config          JSONB  NOT NULL,
  input           JSONB  NOT NULL,
  status          TEXT   NOT NULL,
  result          JSONB,
  created_at      BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT,
  updated_at      BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT
);

COMMENT ON TABLE workflow_runs IS 'Table for storing workflow runs';
COMMENT ON COLUMN workflow_runs.id IS 'Unique identifier for the workflow run';
COMMENT ON COLUMN workflow_runs.job_id IS 'Reference to the associated job';
COMMENT ON COLUMN workflow_runs.task_session_id IS 'Reference to the associated task session';
COMMENT ON COLUMN workflow_runs.config IS 'JSON blob containing the configuration used for this run';
COMMENT ON COLUMN workflow_runs.input IS 'JSON blob containing the input data for this run';
COMMENT ON COLUMN workflow_runs.status IS 'Current status of the workflow run';
COMMENT ON COLUMN workflow_runs.result IS 'JSON blob containing the result of the workflow run (nullable)';
COMMENT ON COLUMN workflow_runs.created_at IS 'Timestamp of when the record was created (in Unix epoch milliseconds)';
COMMENT ON COLUMN workflow_runs.updated_at IS 'Timestamp of when the record was last updated (in Unix epoch milliseconds)';

-- Create indexes for common query patterns
CREATE INDEX IF NOT EXISTS workflow_runs_updated_at_idx ON workflow_runs (updated_at DESC);
CREATE INDEX IF NOT EXISTS workflow_runs_job_task_idx ON workflow_runs (job_id, task_session_id);
