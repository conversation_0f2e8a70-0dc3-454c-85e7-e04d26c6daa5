package ai.saharaa.distribution;

import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.*;
import static ai.saharaa.distribution.Utils.NewDistributionUtils.getSingleQueueKey;
import static ai.saharaa.model.JobUser.JobUserRole.LABELER;

import ai.saharaa.config.LabelingMetricsController;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.daos.*;
import ai.saharaa.distribution.workload.WorkloadContext;
import ai.saharaa.distribution.workload.WorkloadSession;
import ai.saharaa.dto.job.SubmitAnswersDTO;
import ai.saharaa.enums.WorkloadType;
import ai.saharaa.model.*;
import ai.saharaa.services.*;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.primitives.Ints;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class LabelerSingleTaskLifecycleManager extends AbstractTaskLifecycleManager
    implements TaskLifecycleManager, TaskDistributor<TaskSession>, TypeProvider, TaskVisitor {

  private final TaskQueue taskQueue;
  private final CommonCounterManager counterManager;
  private final JobTaskService jobTaskService;
  private final TaskSessionDao taskSessionDao;
  private final TaskSessionService taskSessionService;
  private final BatchDao batchDao;
  private final TaskListDao taskListDao;
  private final TaskDao taskDao;
  private final JobTaskDao jobTaskDao;
  private final ProjectService projectService;
  private final JobService jobService;
  private final BatchService batchService;

  private final WorkloadLimitService workloadService;

  public LabelerSingleTaskLifecycleManager(
      TaskQueue taskQueue,
      CommonCounterManager counterManager,
      JobTaskService jobTaskService,
      TaskSessionDao taskSessionDao,
      TaskSessionService taskSessionService,
      BatchDao batchDao,
      TaskListDao taskListDao,
      TaskDao taskDao,
      JobTaskDao jobTaskDao,
      WorkloadLimitService workloadService,
      ClusterConfiguration clusterConfiguration,
      CaptchaService captchaService,
      IndividualsService individualsService,
      JobUserService jobUserService,
      HoneyPotService honeyPotService,
      LabelingMetricsController labelingMetricsController,
      TaskAndReviewSessionDao taskAndReviewSessionDao,
      TaskQuestionDao taskQuestionDao,
      ResourceDao resourceDao,
      NotificationService notificationService,
      ProjectService projectService,
      JobService jobService,
      BatchService batchService) {
    super(
        clusterConfiguration,
        captchaService,
        individualsService,
        jobUserService,
        honeyPotService,
        taskSessionService,
        labelingMetricsController,
        taskSessionDao,
        taskAndReviewSessionDao,
        workloadService,
        taskQuestionDao,
        resourceDao,
        notificationService);

    this.taskQueue = taskQueue;
    this.counterManager = counterManager;
    this.jobTaskService = jobTaskService;
    this.taskSessionDao = taskSessionDao;
    this.taskSessionService = taskSessionService;
    this.batchDao = batchDao;
    this.taskListDao = taskListDao;
    this.taskDao = taskDao;
    this.jobTaskDao = jobTaskDao;
    this.workloadService = workloadService;
    this.projectService = projectService;
    this.jobService = jobService;
    this.batchService = batchService;
  }

  //  @Override
  //  @Transactional
  //  public TaskSession submitTasks(
  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
  //    return super.submitTasks(answer, taskSessionId, userId, isTester);
  //  }

  @Override
  @Transactional
  public List<TaskSession> assignTasks(
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      String ipAddress,
      Boolean isTester) {

    boolean sybilFlag = workloadService.isSybilUser(jobUser.getUserId());
    WorkloadContext workloadContext = WorkloadContext.builder()
        .batch(batch)
        .batchSetting(batchSetting)
        .job(job)
        .role(LABELER)
        .sybilCount(workloadService.getSybilCount(job.getId(), LABELER))
        .sybil(sybilFlag)
        .dailyUserLimit(workloadService.getPlatformUserDailyWorkload())
        .commonCounterManager(counterManager)
        .build();

    List<WorkloadSession> jobWorkloadSessions =
        workloadService.getJobWorkloadSessions(LABELER, jobUser);
    List<WorkloadSession> userWorkloadSessions = workloadService.getUserWorkloadSessions(jobUser);
    var workload = workloadService.checkLabelerLimit(
        workloadContext, jobWorkloadSessions, userWorkloadSessions, isTester);
    log.info(
        "jobId: {}, userId: {}, job workload session size: {}, user workload session size: {}, workload: {}",
        job.getId(),
        jobUser.getUserId(),
        jobWorkloadSessions.size(),
        userWorkloadSessions,
        workload.toString());
    List<TaskSession> pendingTaskSessions = getPendingTaskSessions(jobUser);

    if (workload.getKey() == WorkloadType.NO_WORKLOAD) {
      var remainingQuotas =
          workload.getValue().values().stream().min(Integer::compareTo).orElse(Integer.MAX_VALUE);
      if (CollectionUtils.isNotEmpty(pendingTaskSessions)) {
        return pendingTaskSessions;
      }
      List<TaskSession> reviseTasks = getRevisedTaskSessions(job, batch, jobUser);
      if (CollectionUtils.isNotEmpty(reviseTasks)) {
        return reviseTasks;
      }
      var realLimit = Ints.min(
          this.getLimitFromWholeJob(job, counterManager.getJobCount(job.getId(), TASK_ID.value)),
          remainingQuotas,
          batch.getAnnotatingSubmitRequired());
      var result = getTaskSessions(job, batch, batchSetting, jobUser, realLimit);
      if (sybilFlag) {
        workloadService.upsertSybilUserCounts(result.size(), LABELER, jobUser, job.getId());
      }
      return result;
    }
    if (CollectionUtils.isNotEmpty(pendingTaskSessions)) {
      return pendingTaskSessions;
    }
    throw ControllerUtils.badRequest(
        "User workloads exceeded, workload: " + workload.getKey().getValue());
  }

  @Override
  @Transactional
  public Boolean submitTasks(
      SubmitAnswersDTO answers,
      Job job,
      Batch batch,
      BatchSetting batchSetting,
      JobUser jobUser,
      Boolean isTester) {
    return super.submitTasks(answers, job, batch, batchSetting, jobUser, isTester);
  }

  @Override
  public void refreshTaskQueue(Job job, Integer repeatCount) {
    List<JobTask> jobTasks = this.jobTaskDao.getTaskByJobId(job.getId());
    jobTasks.forEach(x -> {
      Object o =
          this.taskQueue.get().opsForValue().get(getSingleQueueKey(job.getId(), x.getTaskId()));
      if (Objects.isNull(o) || Long.valueOf(o.toString()) <= 0) {
        Long count =
            this.taskSessionDao.countTaskSessionsByTaskIdAndJobId(x.getTaskId(), job.getId());

        if (count < repeatCount) {
          this.taskQueue
              .get()
              .opsForValue()
              .set(getSingleQueueKey(job.getId(), x.getTaskId()), repeatCount - count);
        }
      }
    });
  }

  @Override
  @Transactional
  public void initializeTaskQueue(Batch batch, BatchSetting batchSetting) {
    projectService
        .getProjectById(batch.getProjectId())
        .filter(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
        .ifPresent(p -> jobService.assignJobToExternalBatchJob(batch));

    List<Job> jobs = this.jobService.listJobsByBatchId(batch.getId());

    var jobId = jobs.get(0).getId();

    batchSetting.setDistributeType(BatchSetting.DistributeType.SINGLE);
    batchDao.updateBatchSettingById(batchSetting);
    counterManager.setJobCount(jobId, COMPLETE_COUNT.value, 0L);
    Optional<TaskList> taskListOpt =
        taskListDao
            .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
            .stream()
            .findFirst();
    taskListOpt.ifPresent(taskList -> {
      var task = taskDao.getFirstTaskInTaskList(taskList.getId());
      task.ifPresent(taskEx -> counterManager.setJobCount(jobId, TASK_ID.value, taskEx.getId()));

      this.batchService.update(
          null,
          new UpdateWrapper<Batch>()
              .lambda()
              .eq(Batch::getId, batch.getId())
              .set(Batch::getStatus, Batch.BatchStatus.ASSIGN_JOBS)
              .set(Batch::getUpdatedAt, DateUtils.now()));

      this.refreshTaskQueue(jobs.get(0), batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
    });
  }

  @Override
  public List<BatchSetting.DistributeType> getDistributionTypes() {
    return List.of(BatchSetting.DistributeType.SINGLE);
  }

  @Override
  public List<JobUser.JobUserRole> getRoles() {
    return List.of(LABELER);
  }

  private List<TaskSession> getPendingTaskSessions(JobUser jobUser) {
    return jobTaskService.getOngoingTaskSessionsForJobUser(jobUser.getId(), LABELER);
  }

  private List<TaskSession> getRevisedTaskSessions(Job job, Batch batch, JobUser jobUser) {
    var reviseTasksExist =
        this.jobTaskService.getReviseTaskSessionsForJobUser(jobUser.getId(), LABELER, 1);
    if (reviseTasksExist.isEmpty()) return Collections.EMPTY_LIST;

    Long singleTaskId = counterManager.getJobCount(job.getId(), TASK_ID.value);
    int limit = getLimitFromWholeJob(job, singleTaskId);
    int reviseLimitBatchSize =
        Math.toIntExact(Math.min(batch.getAnnotatingSubmitRequired(), limit));

    if (reviseLimitBatchSize > 0) {
      this.taskQueue
          .get()
          .opsForValue()
          .decrement(getSingleQueueKey(job.getId(), singleTaskId), reviseLimitBatchSize);
      var reviseTasks = this.jobTaskService.getReviseTaskSessionsForJobUser(
          jobUser.getId(), LABELER, reviseLimitBatchSize);
      if (CollectionUtils.isNotEmpty(reviseTasks)) {
        this.jobTaskService.clearWaitingReverseSession(jobUser.getId(), LABELER);
        return reviseTasks.stream()
            .map(this.taskSessionService::reCreateTaskSessionWithRevisedRecord)
            .toList();
      }
    }
    return Collections.EMPTY_LIST;
  }

  private List<TaskSession> getTaskSessions(
      Job job, Batch batch, BatchSetting batchSetting, JobUser jobUser, int limit) {

    //    this.workloadService.checkIfWorkloadLimitExceeded(
    //        job.getId(), jobUser.getUserId(), LABELER, ipAddress);

    Long singleTaskId = counterManager.getJobCount(job.getId(), TASK_ID.value);

    if (limit > 0) {
      this.taskQueue
          .get()
          .opsForValue()
          .decrement(getSingleQueueKey(job.getId(), singleTaskId), limit);

      return taskSessionService.createTaskSessionForSingleData(
          jobUser,
          singleTaskId,
          LABELER,
          DateUtils.now(),
          batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
          limit,
          batch.getAnnotatingSubmitRequired());
    }
    return Collections.EMPTY_LIST;
  }

  private int getLimitFromWholeJob(Job job, Long singleTaskId) {
    return Integer.valueOf(this.taskQueue
        .get()
        .opsForValue()
        .get(getSingleQueueKey(job.getId(), singleTaskId))
        .asOpt()
        .orElse("0")
        .toString());
    //
    //    var workloadType =
    //        this.workloadLimitService.getJobUserWorkloadType(jobUser, LABELER, ipAddress);
    //
    //    int userWorkloads = Objects.isNull(total) ? 0 : (int) total;
    //
    //    int limit = Objects.isNull(batchSetting.getWorkloadMaxPerJobUser())
    //        ? batch.getAnnotatingSubmitRequired()
    //        : Math.min(
    //            batch.getAnnotatingSubmitRequired(),
    //            batchSetting.getWorkloadMaxPerJobUser() == 0
    //                ? batch.getAnnotatingSubmitRequired()
    //                : batchSetting.getWorkloadMaxPerJobUser() - userWorkloads);
    //
    //    limit = Math.min(
    //        Integer.valueOf(this.taskQueue
    //            .get()
    //            .opsForValue()
    //            .get(getKey(job.getId(), singleTaskId))
    //            .toString()),
    //        limit);
    //
    //    Long decrement =
    //        this.taskQueue.get().opsForValue().decrement(getKey(job.getId(), singleTaskId),
    // limit);
    //    if (decrement < 0) {
    //      throw ControllerUtils.notFound("No task available for this job");
    //    }
    //    return limit;
  }

  @Override
  public TaskSubmitter taskSubmitter() {
    return this;
  }

  @Override
  public TaskDistributor taskDistributor() {
    return this;
  }

  @Override
  public TaskLifecycleManager taskLifecycleManager() {
    return this;
  }
}
