-- add columns for table of cloud_storage
ALTER TABLE cloud_storage
  ADD COLUMN oci_namespace varchar(255) not null default '';

CREATE TABLE role_permissions
(
  id             SERIAL PRIMARY KEY,
  permissions_id INTEGER     NOT NULL,
  role_id        INTEGER     NOT NULL,
  deleted        <PERSON><PERSON><PERSON><PERSON><PERSON>     NOT NULL DEFAULT false,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at     TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE permissions
(
  id          SERIAL PRIMARY KEY,
  name        VARCHAR(255) NOT NULL,
  description VARCHAR(255),
  level       INTEGER,
  active      BOOLEAN      NOT NULL DEFAULT true,
  deleted     B<PERSON><PERSON>EA<PERSON>      NOT NULL DEFAULT false,
  created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
  updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_role_permissions_permissions_id ON role_permissions (permissions_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions (role_id);