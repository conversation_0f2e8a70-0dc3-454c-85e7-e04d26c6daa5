create table season_rank_status
(
  id                 bigserial primary key,
  user_id            bigint                                 not null,
  first_time_rank    bigint                                 not null,
  first_time_rank_at timestamp with time zone default now() not null,
  season_id          bigint                                 not null,
  deleted            boolean                                not null default false,
  has_read           boolean                                not null default true,
  created_at         timestamp with time zone default now() not null,
  updated_at         timestamp with time zone default now() not null
);
create index idx_season_rank_status on season_rank_status (user_id, season_id);
