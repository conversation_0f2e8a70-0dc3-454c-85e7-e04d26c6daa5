
CREATE INDEX IF NOT EXISTS task_task_list_id_index ON task (task_list_id);

-- Replace with a simpler single-column index for broader query optimization.
-- Composite indexes can be brought back later if monitoring suggests a benefit.
DROP INDEX IF EXISTS idx_notification_expire_time;
DROP INDEX IF EXISTS notification_to_user_id_idx;
CREATE INDEX IF NOT EXISTS notification_to_user_id_index ON notification (to_user_id);

CREATE INDEX IF NOT EXISTS season_name_index ON season (name);

CREATE INDEX IF NOT EXISTS exam_session_job_user_id_index ON exam_session (job_user_id);

CREATE INDEX IF NOT EXISTS aws_s3_sync_task_status_index ON aws_s3_sync_task (status);

CREATE INDEX IF NOT EXISTS zip_file_status_index ON zip_file (status);
