CREATE TABLE honey_pot_session
(
  id                BIGSERIAL PRIMARY KEY,
  user_id           bigint      NOT NULL,
  task_session_id   bigint      NOT NULL,
  question_id       bigint      NOT NULL,
  answer            text        NOT NULL,
  status            smallint    NOT NULL default 0,
  deleted           B<PERSON>OLEAN     NOT NULL DEFAULT false,

  created_at        TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at        TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_honey_pot_session_user_id ON honey_pot_session (user_id);
CREATE INDEX idx_honey_pot_session_task_session_id ON honey_pot_session (task_session_id);
CREATE INDEX idx_honey_pot_session_question_id ON honey_pot_session (question_id);
