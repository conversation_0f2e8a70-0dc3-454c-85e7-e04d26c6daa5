
create table exam_session (
    id bigserial primary key,
    job_user_id bigint not null,
    job_id bigint not null references job(id), -- 冗余字段，可以从关联的job_user拿取
    batch_id bigint not null references batch(id), -- 冗余字段，可以从关联的job拿取
    task_id bigint not null references task(id), -- 对应的exam的task id
    user_id bigint not null references users(id), -- 冗余字段，可以从关联的job_user拿取
    answer text not null default '',
    feedback text null default '',
    review_result varchar(20) default '',
    status varchar(20) default 'pending',
    submitted_at timestamptz not null default '1999-01-01', -- 未提交的时间
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now()
);
