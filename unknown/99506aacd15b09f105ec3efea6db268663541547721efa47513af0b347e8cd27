-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement(id, name, symbol, description, category, level_type, standard, standard_uri, network, feature,
                        sort, start_at, end_at, claim_start_at, claim_end_at, mint_start_at, mint_end_at, active)
VALUES (16, 'Titan’s Vigil', 'TITANS_VIGIL_0128', 'Checked in for 31 days based on UTC time during the Data Services: Early Access Program. (We have 31 days in total in Data Services)
This achievement symbolizes the vigilance of Titans who never falter in their duties, guarding the flame of progress with steadfast loyalty. ',
        'AI Data Services Achievements', 'daily-trickle', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 16,
        '2025-01-28 00:00:00', '2025-02-27 23:59:59.999', '2025-01-28 00:00:00', '2025-03-06 23:59:59.999',
        '2025-02-27 00:00:00', '2025-03-13 23:59:59.999', true),
       (17, 'Forge of Perseverance', 'FORGE_OF_PERSEVERANCE_0128', 'Earn at least 15 Sahara Points daily for 31 days based on UTC time during the Data Services (Early Access Program) (We have 31 days in total in Data Services)
This achievement honors those who labor tirelessly in the forge of innovation, shaping the tools that will light the path to discovery.',
        'AI Data Services Achievements',
        'daily-trickle', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 17, '2025-01-28 00:00:00',
        '2025-02-27 23:59:59.999', '2025-01-28 00:00:00', '2025-03-06 23:59:59.999', '2025-02-27 00:00:00',
        '2025-03-13 23:59:59.999', true),
       (18, 'Oracle of Knowledge', 'ORACLE_OF_KNOWLEDGE_0128',
        'Complete tasks to earn at least 100 Sahara Points in one of the following domains: Prompt Collection, Model Optimization, Persona Emulation. ',
        'AI Data Services Achievements',
        'disposable-active-final', 'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 18, '2025-01-28 00:00:00',
        '2025-02-27 23:59:59.999', '2025-01-28 00:00:00', '2025-03-06 23:59:59.999', '2025-01-28 00:00:00',
        '2025-03-13 23:59:59.999', true)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_level(id, achievement_id, level, exp, behavior_code, requirement, denominator, logo, note,
                              deleted)
VALUES (43, 16, 1, 0, '',
        'Checked in for 31 days based on UTC time during Data Services Season. (We have 31 days total in Data Services Season)',
        31, 'res:1216750',
        'You will be eligible to claim the NFT which represents the final degree realized in this achievement at the end of Data Services Season 2.',
        false),
       (44, 17, 1, 0, '',
        'Earn at least 20 Sahara Points for 31 days based on UTC time during Data Services Season 2. (We have 31 days total in Data Services Season 2.)',
        31, 'res:1216751',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of Data Services Season 2.',
        false),
       (45, 18, 1, 0, '',
        'Once you earn at least 100 Sahara Points in any one of the three categories: Prompt Collection, Model Optimization, Persona Emulation, you can claim 30 EXP',
        3, 'res:1216752',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of the Data Services Season 2.',
        false),
       (46, 18, 2, 0, '',
        'Once you earn at least 100 Sahara Points in any one of the three categories: Prompt Collection, Model Optimization, Persona Emulation, you can claim 30 EXP',
        3, 'res:1216752',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of the Data Services Season 2.',
        true),
       (47, 18, 3, 0, '',
        'Once you earn at least 100 Sahara Points in any one of the three categories: Prompt Collection, Model Optimization, Persona Emulation, you can claim 30 EXP',
        3, 'res:1216752',
        'You will be eligible to claim an NFT that represents your final achievement title at the end of the Data Services Season 2.',
        true)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_requirement(id, achievement_id, achievement_level_id, requirement, denominator, exp,
                                    behavior_code, name, description, logo, action_text, action_passed_text, action_uri,
                                    sort)
VALUES
    -- Titan’s Vigil
    (111, 16, null, '1', 1, 1, 'TITANS_VIGIL_0128:1', '1', '', '', '', '', '', 1),
    (112, 16, null, '2', 1, 1, 'TITANS_VIGIL_0128:2', '2', '', '', '', '', '', 2),
    (113, 16, null, '3', 1, 1, 'TITANS_VIGIL_0128:3', '3', '', '', '', '', '', 3),
    (114, 16, null, '4', 1, 1, 'TITANS_VIGIL_0128:4', '4', '', '', '', '', '', 4),
    (115, 16, null, '5', 1, 1, 'TITANS_VIGIL_0128:5', '5', '', '', '', '', '', 5),
    (116, 16, null, '6', 1, 1, 'TITANS_VIGIL_0128:6', '6', '', '', '', '', '', 6),
    (117, 16, null, '7', 1, 1, 'TITANS_VIGIL_0128:7', '7', '', '', '', '', '', 7),
    (118, 16, null, '8', 1, 1, 'TITANS_VIGIL_0128:8', '8', '', '', '', '', '', 8),
    (119, 16, null, '9', 1, 1, 'TITANS_VIGIL_0128:9', '9', '', '', '', '', '', 9),
    (120, 16, null, '10', 1, 1, 'TITANS_VIGIL_0128:10', '10', '', '', '', '', '', 10),
    (121, 16, null, '11', 1, 1, 'TITANS_VIGIL_0128:11', '11', '', '', '', '', '', 11),
    (122, 16, null, '12', 1, 1, 'TITANS_VIGIL_0128:12', '12', '', '', '', '', '', 12),
    (123, 16, null, '13', 1, 1, 'TITANS_VIGIL_0128:13', '13', '', '', '', '', '', 13),
    (124, 16, null, '14', 1, 1, 'TITANS_VIGIL_0128:14', '14', '', '', '', '', '', 14),
    (125, 16, null, '15', 1, 1, 'TITANS_VIGIL_0128:15', '15', '', '', '', '', '', 15),
    (126, 16, null, '16', 1, 1, 'TITANS_VIGIL_0128:16', '16', '', '', '', '', '', 16),
    (127, 16, null, '17', 1, 1, 'TITANS_VIGIL_0128:17', '17', '', '', '', '', '', 17),
    (128, 16, null, '18', 1, 1, 'TITANS_VIGIL_0128:18', '18', '', '', '', '', '', 18),
    (129, 16, null, '19', 1, 1, 'TITANS_VIGIL_0128:19', '19', '', '', '', '', '', 19),
    (130, 16, null, '20', 1, 1, 'TITANS_VIGIL_0128:20', '20', '', '', '', '', '', 20),
    (131, 16, null, '21', 1, 1, 'TITANS_VIGIL_0128:21', '21', '', '', '', '', '', 21),
    (132, 16, null, '22', 1, 1, 'TITANS_VIGIL_0128:22', '22', '', '', '', '', '', 22),
    (133, 16, null, '23', 1, 1, 'TITANS_VIGIL_0128:23', '23', '', '', '', '', '', 23),
    (134, 16, null, '24', 1, 1, 'TITANS_VIGIL_0128:24', '24', '', '', '', '', '', 24),
    (135, 16, null, '25', 1, 1, 'TITANS_VIGIL_0128:25', '25', '', '', '', '', '', 25),
    (136, 16, null, '26', 1, 1, 'TITANS_VIGIL_0128:26', '26', '', '', '', '', '', 26),
    (137, 16, null, '27', 1, 1, 'TITANS_VIGIL_0128:27', '27', '', '', '', '', '', 27),
    (138, 16, null, '28', 1, 1, 'TITANS_VIGIL_0128:28', '28', '', '', '', '', '', 28),
    (139, 16, null, '29', 1, 1, 'TITANS_VIGIL_0128:29', '29', '', '', '', '', '', 29),
    (140, 16, null, '30', 1, 1, 'TITANS_VIGIL_0128:30', '30', '', '', '', '', '', 30),
    (141, 16, null, '31', 1, 1, 'TITANS_VIGIL_0128:31', '31', '', '', '', '', '', 31),
    -- Forge of Perseverance
    (142, 17, null, '1', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:1', '1', '', '', '', '', '', 1),
    (143, 17, null, '2', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:2', '2', '', '', '', '', '', 2),
    (144, 17, null, '3', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:3', '3', '', '', '', '', '', 3),
    (145, 17, null, '4', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:4', '4', '', '', '', '', '', 4),
    (146, 17, null, '5', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:5', '5', '', '', '', '', '', 5),
    (147, 17, null, '6', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:6', '6', '', '', '', '', '', 6),
    (148, 17, null, '7', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:7', '7', '', '', '', '', '', 7),
    (149, 17, null, '8', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:8', '8', '', '', '', '', '', 8),
    (150, 17, null, '9', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:9', '9', '', '', '', '', '', 9),
    (151, 17, null, '10', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:10', '10', '', '', '', '', '', 10),
    (152, 17, null, '11', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:11', '11', '', '', '', '', '', 11),
    (153, 17, null, '12', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:12', '12', '', '', '', '', '', 12),
    (154, 17, null, '13', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:13', '13', '', '', '', '', '', 13),
    (155, 17, null, '14', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:14', '14', '', '', '', '', '', 14),
    (156, 17, null, '15', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:15', '15', '', '', '', '', '', 15),
    (157, 17, null, '16', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:16', '16', '', '', '', '', '', 16),
    (158, 17, null, '17', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:17', '17', '', '', '', '', '', 17),
    (159, 17, null, '18', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:18', '18', '', '', '', '', '', 18),
    (160, 17, null, '19', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:19', '19', '', '', '', '', '', 19),
    (161, 17, null, '20', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:20', '20', '', '', '', '', '', 20),
    (162, 17, null, '21', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:21', '21', '', '', '', '', '', 21),
    (163, 17, null, '22', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:22', '22', '', '', '', '', '', 22),
    (164, 17, null, '23', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:23', '23', '', '', '', '', '', 23),
    (165, 17, null, '24', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:24', '24', '', '', '', '', '', 24),
    (166, 17, null, '25', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:25', '25', '', '', '', '', '', 25),
    (167, 17, null, '26', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:26', '26', '', '', '', '', '', 26),
    (168, 17, null, '27', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:27', '27', '', '', '', '', '', 27),
    (169, 17, null, '28', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:28', '28', '', '', '', '', '', 28),
    (170, 17, null, '29', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:29', '29', '', '', '', '', '', 29),
    (171, 17, null, '30', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:30', '30', '', '', '', '', '', 30),
    (172, 17, null, '31', 1, 5, 'FORGE_OF_PERSEVERANCE_0128:31', '31', '', '', '', '', '', 31),
    -- Oracle of Knowledge
    (173, 18, null, 'Prompt Collection', 100, 30, 'ORACLE_OF_KNOWLEDGE_0128:PROMPT_COLLECTION', 'Prompt Collection', '', '',
     '',
     '', '', 1),
    (174, 18, null, 'Model Optimization', 100, 30, 'ORACLE_OF_KNOWLEDGE_0128:MODEL_OPTIMIZATION', 'Model Optimization', '',
     '',
     '', '', '', 2),
    (175, 18, null, 'Persona Emulation', 100, 30, 'ORACLE_OF_KNOWLEDGE_0128:PERSONA_EMULATION',
     'Persona Emulation', '', '', '',
     '', '', 3)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------
