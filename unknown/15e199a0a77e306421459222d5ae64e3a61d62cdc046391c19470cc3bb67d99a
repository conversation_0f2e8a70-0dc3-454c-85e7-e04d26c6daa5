-- Drop existing indexes
DROP INDEX IF EXISTS exam_session_user_task_index;
DROP INDEX IF EXISTS review_session_user_task_index;
DROP INDEX IF EXISTS spot_session_user_task_index;
DROP INDEX IF EXISTS task_session_user_task_index;
DROP INDEX IF EXISTS job_user_job_id_user_id_index;

-- Create new indexes
CREATE INDEX IF NOT EXISTS exam_session_user_id_index ON exam_session (user_id);

CREATE INDEX IF NOT EXISTS review_session_task_id_index ON review_session (task_id);

CREATE INDEX IF NOT EXISTS spot_session_user_id_index ON spot_session (user_id);

CREATE INDEX IF NOT EXISTS task_session_task_id_index ON task_session (task_id);

CREATE INDEX IF NOT EXISTS job_user_job_id_index ON job_user (task_list_session_id);