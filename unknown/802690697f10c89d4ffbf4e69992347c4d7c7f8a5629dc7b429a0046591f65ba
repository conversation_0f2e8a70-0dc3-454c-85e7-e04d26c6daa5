-- ---------------------------------------------------------------------------------------------------------------------
INSERT INTO achievement(id, name, symbol, description, category, level_type, standard, standard_uri, network, feature,
                        sort)
VALUES (19, 'Sigil of the Initiate', 'SIGIL_OF_INITIATE',
        'By completing your onboarding, you heed the Sahara AI pantheon’s call—claim this SBT to mark your first step into the realm of AI.',
        'AI Data Services Achievements', 'disposable-hidden',
        'ERC 721', '', 'Sahara Testnet', 'Non-Transferable', 19)
ON CONFLICT (id) DO NOTHING;

-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_level(id, achievement_id, level, exp, behavior_code, requirement, denominator, logo, note)
VALUES
  -- Sigil of the Initiate
  (48, 19, 1, 150, '',
   'By completing your onboarding, you heed the Sahara AI pantheon’s call—claim this SBT to mark your first step into the realm of AI.',
   1, 'res:1221330',
   'This token grants you the title of Initiate and unlocks your initial privileges within the Data Service Platform. Prepare yourself for the path ahead—greatness beckons, and only the worthy shall ascend.')
ON CONFLICT (id) DO NOTHING;

-- ---------------------------------------------------------------------------------------------------------------------

INSERT INTO achievement_requirement(id, achievement_id, achievement_level_id, requirement, denominator, exp,
                                    behavior_code,
                                    name,
                                    description, logo, action_text, action_passed_text, action_uri, sort)
VALUES
  -- Sigil of the Initiate
  (176, 19, 48, 'Complete all sigil of initiate requirement', 1, 0,
   'SAHARA_SIGIL_OF_INITIATE',
   'sahara sigil of initiate', '', '', '', '', 'SAHARA_SIGIL_OF_INITIATE', 1)
ON CONFLICT (id) DO NOTHING;
-- ---------------------------------------------------------------------------------------------------------------------
