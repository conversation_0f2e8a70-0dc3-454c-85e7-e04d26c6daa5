ALTER TABLE task_list_session RENAME TO job;
ALTER TABLE task_list_invitation RENAME TO job_invitation;
ALTER TABLE task_list_session_user RENAME TO job_user;


ALTER TABLE job
  ADD name varchar(255),
  ADD batch_id bigint,
  ADD assign_data_volume int,
  ADD time_spent_per_task bigint,
  ADD review_deadline timestamptz,
  ADD audit_deadline timestamptz,
  ADD invite_acceptance_deadline timestamptz;

CREATE TABLE job_task (
  id bigserial PRIMARY KEY,
  job_id bigint not null,
  task_id bigint not null,
  deleted boolean not null DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)


