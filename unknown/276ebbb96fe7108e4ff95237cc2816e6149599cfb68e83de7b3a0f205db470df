create table user_has_read_notification_record
(
  id              bigserial primary key,
  user_id         bigint  not null,
  notification_id bigint  not null,
  deleted         boolean not null         default false,
  has_read        boolean not null         default true,
  created_at      timestamp with time zone default now() not null,
  updated_at      timestamp with time zone default now() not null
);
create index idx_user_has_read_notification_record on user_has_read_notification_record (notification_id, user_id);
