-- create season user points detail table in pgsql
create table season_user_points_detail
(
    id bigserial primary key,
    season_id bigint not null,
    user_id bigint not null,
    job_id bigint not null,
    points bigint default 0 not null,
    detail json not null,
    deleted boolean not null default false,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null,
    constraint fk_season_user_points_detail_user_id foreign key (user_id) references users (id),
    constraint fk_season_user_points_detail_season_id foreign key (season_id) references season (id),
    constraint fk_season_user_points_detail_job_id foreign key (job_id) references job (id)
);

create index season_user_points_detail_user_id_idx on season_user_points_detail (user_id);
create index season_user_points_detail_season_id_idx on season_user_points_detail (season_id);
create index season_user_points_detail_job_id_idx on season_user_points_detail (job_id);