CREATE TABLE notification_targetuser
(
  id              bigserial NOT NULL,
  user_id         bigserial NOT NULL,
  notification_id bigserial NOT NULL,
  status          int8 NULL DEFAULT 0, -- 0 is Waiting sent,1 is Successfully sent,2 is Failed sent
  CONSTRAINT notification_targetuser_pk PRIMARY KEY (id)
);
CREATE INDEX notification_targetuser_notification_id_idx ON public.notification_targetuser USING btree (notification_id);

COMMENT
ON COLUMN notification_targetuser.status IS '0 is Waiting sent,1 is Successfully sent,2 is Failed sent';