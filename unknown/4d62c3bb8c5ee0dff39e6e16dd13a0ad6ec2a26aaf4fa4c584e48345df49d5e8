INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already, resource_id)
  VALUES (1001, 'IO', 9, true, 1733713) ON CONFLICT (id) DO NOTHING;

INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already, resource_id)
  VALUES (1002, 'SHELL', 9, true, 1733712) ON CONFLICT (id) DO NOTHING;

INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already, resource_id)
  VALUES (1003, 'SOLO', 9, false, 1733714) ON CONFLICT (id) DO NOTHING;

INSERT INTO reward_token_info(id, token_name, reward_token_type, tge_already, resource_id)
  VALUES (1004, 'CAMP', 9, false, 1733711) ON CONFLICT (id) DO NOTHING;

update resource set visibility = 'public' where id in (1733711,1733712,1733713,1733714);