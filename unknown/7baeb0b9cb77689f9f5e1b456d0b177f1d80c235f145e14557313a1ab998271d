
create table task_session (
    id bigserial primary key,
    job_user_id bigint not null,
    job_id bigint not null, -- 冗余字段，可以从关联的job_user拿取
    task_id bigint not null, -- 冗余字段，可以从关联的job拿取
    user_id bigint not null, -- 冗余字段，可以从关联的job_user拿取
    user_role varchar(20) not null, -- 同job_user中的role
    answer text not null,
    feedback text null,
    deleted boolean not null default false,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now(),
    constraint fk_task_session_job_id foreign key (job_id) references job (id),
    constraint fk_task_session_job_user_id foreign key (job_user_id) references job_user (id)
);