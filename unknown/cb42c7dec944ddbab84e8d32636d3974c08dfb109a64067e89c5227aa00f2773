-- create data tasks table
create table data_tasks (
  id bigserial primary key,
  owner_id bigint references users(id),
  task_type varchar(32) not null,
  config varchar(2048) not null,
  task_key varchar(32) not null,
  retries int not null default 0,
  result text not null default '',
  status varchar(16) not null default 'pending',
  resource_id bigint references resource(id),
  deleted boolean not null default false,
  created_at timestamp not null default now(),
  updated_at timestamp not null default now()
)
