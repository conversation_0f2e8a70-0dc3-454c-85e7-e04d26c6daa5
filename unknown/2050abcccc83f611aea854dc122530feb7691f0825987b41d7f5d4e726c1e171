CREATE TABLE ban_user_reason
(
    id          SERIAL          PRIMARY KEY,
    user_id     bigint          NOT NULL,
    job_id     bigint          NOT NULL,
    reason      varchar(255)    NOT NULL DEFAULT '',
    banned      BOOLEAN         NOT NULL DEFAULT false,
    deleted     BOOLEAN         NOT NULL DEFAULT false,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);
CREATE INDEX idx_ban_user_reason_user_id ON ban_user_reason (user_id);
CREATE INDEX idx_ban_user_reason_job_id ON ban_user_reason (job_id);
