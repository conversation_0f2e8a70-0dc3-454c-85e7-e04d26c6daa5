-- This script migrates timestamp columns to 'timestamptz' using the 'us-west-2' time zone (America/Los_Angeles).
-- Note: This conversion might be incorrect for pre-prod and dev environments if they are in different time zones.
-- Testing in pre-prod and dev environments will help identify and address any discrepancies early.

ALTER TABLE data_tasks
  ALTER COLUMN created_at TYPE timestamptz USING created_at AT TIME ZONE 'America/Los_Angeles',
  ALTER COLUMN updated_at TYPE timestamptz USING updated_at AT TIME ZONE 'America/Los_Angeles';

ALTER TABLE node_stat
  ALTER COLUMN created_at TYPE timestamptz USING created_at AT TIME ZONE 'America/Los_Angeles',
  ALTER COLUMN updated_at TYPE timestamptz USING updated_at AT TIME ZONE 'America/Los_Angeles';

ALTER TABLE user_setting
  ALTER COLUMN updated_at TYPE timestamptz USING updated_at AT TIME ZONE 'America/Los_Angeles',
  ALTER COLUMN created_at TYPE timestamptz USING created_at AT TIME ZONE 'America/Los_Angeles';
