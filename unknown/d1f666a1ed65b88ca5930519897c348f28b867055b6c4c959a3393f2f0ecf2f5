CREATE TABLE IF NOT EXISTS onchain_oracle_log
(
  id           bigserial PRIMARY KEY,
  user_address VARCHAR(255) NOT NULL,
  onchain_tag  BOOLEAN      NOT NULL DEFAULT false,
  tx_hash      VARCHAR(255) NOT NULL DEFAULT '',
  oracle_type  VARCHAR(255) NOT NULL DEFAULT '',
  detail       jsonb        NOT NULL DEFAULT '{}',
  deleted      boolean               default false,
  created_at   timestamptz  not null default now(),
  updated_at   timestamptz  not null default now()
);