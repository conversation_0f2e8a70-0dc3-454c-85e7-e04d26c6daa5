name: Clear cache

on:
  schedule:
    - cron: '0 0 1 * *'
  workflow_dispatch:

permissions:
  actions: write

jobs:
  clear-cache:
    runs-on: ubuntu-latest
    steps:
      - name: Clear cache
        uses: actions/github-script@v7
        with:
          script: |
            console.log("About to clear caches older than 1 day")
            const caches = await github.rest.actions.getActionsCacheList({
              owner: context.repo.owner,
              repo: context.repo.repo,
            })
            
            for (const cache of caches.data.actions_caches) {
              if (new Date() - new Date(cache.created_at) > 24 * 60 * 60 * 1000) {
                console.log(`Deleting cache with ID: ${cache.id}, created at: ${cache.created_at}`);
                await github.rest.actions.deleteActionsCacheById({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  cache_id: cache.id,
                })
              }
            }
            
            console.log("Clear completed")