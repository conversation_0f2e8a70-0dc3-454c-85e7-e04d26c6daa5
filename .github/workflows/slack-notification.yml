name: Slack Pull Request Open Notification

on:
  pull_request:
    types: [opened, reopened]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Notify slack pr open
        env:
          SLACK_WEBHOOK_URL : ${{ secrets.SLACK_WEBHOOK_URL }}
          PULL_REQUEST_NUMBER : ${{ github.event.pull_request.number }}
          PULL_REQUEST_TITLE : ${{ github.event.pull_request.title }}
          PULL_REQUEST_AUTHOR_NAME : ${{ github.event.pull_request.user.login }}
          PULL_REQUEST_AUTHOR_ICON_URL : ${{ github.event.pull_request.user.avatar_url }}
          PULL_REQUEST_URL : ${{ github.event.pull_request.html_url }}
          PULL_REQUEST_BODY : ${{ github.event.pull_request.body }}
          PULL_REQUEST_COMPARE_BRANCH_OWNER: ${{ github.event.pull_request.head.repo.owner.login }}
          PULL_REQUEST_COMPARE_BRANCH_NAME : ${{ github.event.pull_request.head.ref }}
          PULL_REQUEST_BASE_BRANCH_OWNER: ${{ github.event.pull_request.base.repo.owner.login }}
          PULL_REQUEST_BASE_BRANCH_NAME : ${{ github.event.pull_request.base.ref }}
          IS_SEND_HERE_MENTION : false
          MAKE_PRETTY : false
          MAKE_COMPACT : false
          IS_PR_FROM_FORK: false
        uses: jun3453/slack-pr-open-notification-action@v1.3.0