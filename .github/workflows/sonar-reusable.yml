name: SonarQube Analysis

on:
  workflow_call:
    inputs:
      base_branch:
        description: 'The base branch for the pull request (e.g., develop, main)'
        required: true
        type: string
      project_key:
        description: 'The SonarQube project key'
        required: true
        type: string
    secrets:
      sonar_token:
        description: 'SonarQube token'
        required: true

env:
  SONAR_HOST_URL: http://**************:9000

concurrency:
  group:
    ${{ github.repository }}-${{ github.event.number || github.head_ref ||
    github.sha }}-${{ github.workflow }}-${{ github.event_name ==
    'pull_request_review_comment' && 'pr_comment' || 'pr' }}
  cancel-in-progress: ${{ github.event_name != 'pull_request_review_comment' }}

jobs:
  build:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          add-job-summary-as-pr-comment: on-failure
          cache-encryption-key: ${{ secrets.GRADLE_ENCRYPTION_KEY }}

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Compile Java
        id: compile-java
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.sonar_token }}
          SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
        run: |
          ./gradlew compileJava --build-cache --info
        continue-on-error: true

      - name: SonarQube Scan
        if: steps.compile-java.outcome == 'success'
        uses: sonarsource/sonarqube-scan-action@v4.2.1
        env:
          SONAR_TOKEN: ${{ secrets.sonar_token }}
          SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
        with:
          args: >
            -Dsonar.pullrequest.key=${{ github.event.pull_request.number }}
            -Dsonar.pullrequest.base=${{ inputs.base_branch }}
            -Dsonar.pullrequest.branch=${{ github.head_ref }}

      - name: SonarQube Quality Gate check
        if: steps.compile-java.outcome == 'success'
        id: sonarqube-quality-gate-check
        uses: sonarsource/sonarqube-quality-gate-action@master
        with:
          pollingTimeoutSec: 600
          scanMetadataReportFile: '.scannerwork/report-task.txt'
        env:
          SONAR_TOKEN: ${{ secrets.sonar_token }}
          SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}

      - name: "Show SonarQube Quality Gate Status"
        if: ${{ always() }}
        run: |
          if [ "${{ steps.compile-java.outcome }}" == "success" ]; then
            STATUS=${{ steps.sonarqube-quality-gate-check.outputs.quality-gate-status }}
            # if STATUS is null or empty, it means sonarqube-quality-gate-check is interrupted, comment interrupted
            echo "The Quality Gate status is $STATUS"
            COMMENT_BODY=""
            if [ -z "$STATUS" ]; then
              COMMENT_BODY=":warning: Quality Gate status is empty. SonarQube Quality Gate check was interrupted."
            elif [ "$STATUS" != "PASSED" ]; then
              COMMENT_BODY=":x: SonarQube Quality Gate failed with status: $STATUS. \
              Please check the results [here](${{ env.SONAR_HOST_URL }}/dashboard?id=${{ inputs.project_key }}&pullRequest=${{ github.event.pull_request.number }}). "
            else
              COMMENT_BODY=":white_check_mark: SonarQube Quality Gate passed. \
              Please check the results [here](${{ env.SONAR_HOST_URL }}/dashboard?id=${{ inputs.project_key }}&pullRequest=${{ github.event.pull_request.number }}). "
            fi
          elif [ "${{ steps.compile-java.outcome }}" == "failure" ]; then
            COMMENT_BODY=":x: Java compilation failed.  Please check the build logs for details."
          elif [ "${{ steps.compile-java.outcome }}" == "cancelled" ]; then
            COMMENT_BODY=":warning: Java compilation was cancelled. Please check the build logs for details."
          fi
          echo "COMMENT_BODY=$COMMENT_BODY" >> $GITHUB_ENV

      - name: Comment Pull Request
        if: ${{ always() }}
        uses: thollander/actions-comment-pull-request@v3.0.1
        with:
          message: ${{ env.COMMENT_BODY }}