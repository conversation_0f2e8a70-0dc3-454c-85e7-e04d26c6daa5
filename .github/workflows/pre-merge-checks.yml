name: Checks

on:
  # Necessary to trigger pre-merge runs, even if skipped later.
  # More info: https://github.com/orgs/community/discussions/51120
  pull_request:

  merge_group: # Enables pre-merge runs.

  workflow_dispatch: # Allows manual triggering of the workflow.

  push:
    branches:
      - develop # Run again after merging into develop to generate cache.

permissions:
  contents: read
  pull-requests: write
  checks: write

jobs:
  static-analysis:
    # Run checks only on merge, skip on PR updates, to save usage.
    # Note: PR updates must still trigger the workflow, for this to work.
    # https://github.com/orgs/community/discussions/51120
    if: (github.event_name != 'pull_request' || github.event.action == 'enqueued') && github.ref_name != 'develop'
    name: Static analysis (Spotless, ErrorProne, Checkstyle)

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
          fetch-depth: 0

      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          add-job-summary-as-pr-comment: on-failure
          # Required for supporting configuration cache in actions.
          cache-encryption-key: ${{ secrets.GRADLE_ENCRYPTION_KEY }}

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: src/**/*.java
          separator: ","

      - name: Run static analysis
        # Simply compiling the code will run the ErrorProne check.
        # Spotless uses a list of changed files instead of the ratchetFrom config,
        # to be compatible with Gradle configuration cache on CI.
        # https://github.com/diffplug/spotless/issues/2249
        run: |
          ./gradlew compileTestJava checkstyleMain checkstyleTest spotlessCheck \
          -PspotlessFiles=${{ steps.changed-files.outputs.all_changed_files }}

  # This job only runs tests, and disables all other checks to speed up. Running static analysis and tests in two
  # parallel jobs makes the action faster, though the combined minutes can be longer.
  test:
    if: github.event_name != 'pull_request' || github.event.action == 'enqueued'
    name: Run Tests

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # Without this, .git/config can change every time, potentially preventing a gradle configuration cache hit.
          persist-credentials: false

      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          add-job-summary-as-pr-comment: on-failure
          cache-encryption-key: ${{ secrets.GRADLE_ENCRYPTION_KEY }}

      - name: Run Tests
        # Run tests with ErrorProne disabled to speed it up.
        run: |
          ./gradlew test -x spotlessApply -PdisableErrorProne --info

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v4
        if: success() || failure() # always run even if the previous step fails
        with:
          report_paths: '**/build/test-results/test/TEST-*.xml'