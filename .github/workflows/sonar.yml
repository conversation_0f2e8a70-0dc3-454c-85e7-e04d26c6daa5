name: SonarQube Analysis

on:
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review ]
    branches:
      - develop

jobs:
  call-sonarqube-analysis:
    uses: SaharaaQuestLab/campfire-platform/.github/workflows/sonar-reusable.yml@develop
    permissions: # Add permissions here, at the job level
      contents: read  # good practice to be explicit, even if it's the default
      pull-requests: write
    with:
      base_branch: develop
      project_key: campfire-platform
    secrets:
      sonar_token: ${{ secrets.SONAR_TOKEN }}