name: deploy-to-staging

on:
  push:
    tags:
      - "v*"

jobs:
  deploy:
    name: Deploy to staging environment(EKS)
    runs-on: ubuntu-latest
    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      NAMESPACE: ${{ vars.TEST_NAMESPACE }}
      APP_NAME: ${{ vars.TEST_APP_NAME }}
      CONTAINER_REPO_NAME: ${{ vars.CONTAINER_REPO_NAME }}
      STAGING_RELEASE_NAME: ${{ vars.STAGING_RELEASE_NAME }}
      CF_SIGN_KEY: ${{ secrets.STAGING_CF_SIGN_KEY }}
    steps:
      - id: commit
        uses: prompt/actions-commit-hash@v2

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      # login aws
      - name: AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: "17"
          distribution: "temurin"
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-encryption-key: ${{ secrets.GRADLE_ENCRYPTION_KEY }}
          cache-read-only: true
      - name: Build with Gradle
        run: ./gradlew build -x check -x spotlessApply --info
      - name: Copy CF PrivateKey to repo dir
        run: echo "${{ env.CF_SIGN_KEY }}" > build/cfpk.pem
      # build and push image
      - name: AWS ECR
        uses: kciter/aws-ecr-action@v4
        id: ECR
        with:
          dockerfile: ./docker/Dockerfile
          access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          account_id: ${{ secrets.AWS_ACCOUNT_ID }}
          repo: ${{ env.CONTAINER_REPO_NAME }}
          region: ${{ env.AWS_REGION }}
          tags: ${{ github.ref_name }}

      - name: Install Helm
        run: |
          curl https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 | bash

      - name: Set up kubeconfig
        run: echo "${{ secrets.KUBE_CONFIG_DATA_STAGING }}" > $GITHUB_WORKSPACE/kubeconfig.yaml

      - name: Update Helm chart with new image
        run: |
          helm upgrade ${{ env.STAGING_RELEASE_NAME }} ./helm \
          --kubeconfig kubeconfig.yaml \
          --reuse-values \
          --set image.url=${{ steps.ECR.outputs.image }} \
          --wait

      - name: Update version value
        uses: jossef/action-set-json-field@v2.1
        with:
          file: scripts/notification.json
          field: version
          value: ${{ github.ref_name }}

      - name: Update trigger name
        uses: jossef/action-set-json-field@v2.1
        with:
          file: scripts/notification.json
          field: who
          value: ${{ github.actor }}

      - name: extract commit message
        id: commit_message
        run: |
          git fetch --tags -f
          echo 'COMMIT_MESSAGE<<EOF' >> $GITHUB_ENV
          git show --no-walk --format=%b --no-patch ${{ github.ref_name }} | tail -n +6 >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Update notifcation message
        uses: jossef/action-set-json-field@v2.1
        with:
          file: scripts/notification.json
          field: message
          value: ${{ env.COMMIT_MESSAGE }}

      - name: Send version notification to slack
        id: slack
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload-file-path: "./scripts/notification.json"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_VERSION_WEBHOOK_URL }}
