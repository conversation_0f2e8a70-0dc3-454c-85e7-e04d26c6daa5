services:
  postgres:
    image: 'postgres:latest'
    environment:
      - 'POSTGRES_DB=ai'
      - 'POSTGRES_PASSWORD=123456'
      - 'POSTGRES_USER=root'
      - 'PGDATA=/var/lib/postgresql/data/pgdata'
    volumes:
      - ./data/pg_data/data:/var/lib/postgresql/data/pgdata
    ports:
      - 15432:5432

  redis:
    image: 'redis:latest'
    container_name: redis
    command: redis-server --requirepass 123456
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis/data:/data

  ai-platform:
    image: test:v0.0.1
    environment:
      - AI_DATABASE=**********************************
      - AI_DB_PASS=123456
      - AI_DB_USER=root
      - AI_REDIS_PASSWORD=123456
      - HOST_NAME=ai-platform
      - AI_PLATFORM_ENV=dev-cluster
      - CAMPFIRE_MACHINE_REVIEW_URL=http://localhost:8888
    ports:
      - 18080:8080
  ai-platform-b:
    image: test:v0.0.1
    environment:
      - AI_DATABASE=**********************************
      - AI_DB_PASS=123456
      - AI_DB_USER=root
      - AI_REDIS_PASSWORD=123456
      - HOST_NAME=ai-platform-b
      - AI_PLATFORM_ENV=dev-cluster
      - CAMPFIRE_MACHINE_REVIEW_URL=http://localhost:8888
