diff --git a/helm/values.yaml b/helm/values.yaml
index 1aa75b2b..01cfc6a4 100644
--- a/helm/values.yaml
+++ b/helm/values.yaml
@@ -26,7 +26,7 @@ platform:
     AWS_SECRTE_ACCESS_KEY: ""
     KAFKA_SASL_ENABLE: "true"
     KAFKA_SERVERS: ""
-    WORKLOAD_LIMIT_PLATFORM_IP_DAILY: 99999999
+    WORKLOAD_LIMIT_PLATFORM_IP_DAILY: 75
     WORKLOAD_LIMIT_PLATFORM_USER_DAILY: 25
   database:
     url: ""
diff --git a/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java b/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java
index c03a83af..bcabfd48 100644
--- a/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionActor.java
@@ -148,7 +148,7 @@ public class IndividualJobSessionActor extends ActorBase {
     }
     List<JobTask> jobTasks;
     var jobId = opGetTask.getJobUser().getTaskListSessionId();
-    var cacheKeyOfTaskIdQueue = "jobOpt_${jobId}:cachedTaskIds";
+    var cacheKeyOfTaskIdQueue = "job_${jobId}:cachedTaskIds";
     if (redisCache.hasKey(cacheKeyOfTaskIdQueue)
         && redisCache.lGetListSize(cacheKeyOfTaskIdQueue) > 0) {
       var takingSize =
@@ -174,7 +174,7 @@ public class IndividualJobSessionActor extends ActorBase {
     }
     var res = createTaskSessionCanDuplicate(
         jobUser,
-        jobTasks,
+        jobTasks.map(JobTask::getTaskId).toList(),
         role,
         opGetTask.getBatchCreateAt(),
         targetBatchSetting,
@@ -202,7 +202,7 @@ public class IndividualJobSessionActor extends ActorBase {
 
   private List<TaskSession> createTaskSessionCanDuplicate(
       JobUser jobUser,
-      List<JobTask> jobTasks,
+      List<Long> jobTasks,
       JobUser.JobUserRole role,
       Timestamp timestamp,
       BatchSetting batchSetting,
@@ -220,12 +220,13 @@ public class IndividualJobSessionActor extends ActorBase {
       case SINGLE -> { // removed from akka already
         var userWorkloads = redisCache
             .hget(
-                "job_${jobTasks.first().getJobId()}:user_${jobUser.getUserId()}:workloads", "total")
+                "job_${jobUser.getTaskListSessionId()}:user_${jobUser.getUserId()}:workloads",
+                "total")
             .asOpt()
             .orElse(0);
         yield new ArrayList<>(taskSessionService.createTaskSessionForSingleData(
             jobUser,
-            jobTasks.first().getTaskId(),
+            jobTasks.first(),
             role,
             timestamp,
             batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
@@ -235,14 +236,15 @@ public class IndividualJobSessionActor extends ActorBase {
                 : batchSetting.getWorkloadMaxPerJobUser() - (int) userWorkloads,
             batchAnnotationBatchSize));
       }
-      case ASSEMBLED -> new ArrayList<>(
-          taskSessionService.createTaskSessionForAssembledData( // not in use
-              jobUser, jobTasks.first(), role, timestamp, batchSetting, batchAnnotationBatchSize));
+      case ASSEMBLED -> new ArrayList<>();
+        //          taskSessionService.createTaskSessionForAssembledData( // not in use
+        //              jobUser, jobTasks.first(), role, timestamp, batchSetting,
+        // batchAnnotationBatchSize)
     };
   }
 
   private Integer handleCheckIndividualJobReopen() {
-    var jobs = jobService.getIndividualJobByStatus(Job.JobStatus.COMMITTED);
+    var jobs = jobService.getIndividualJobByStatus(List.of(Job.JobStatus.COMMITTED));
     if (!jobs.isEmpty()) {
       jobs.forEach(job -> {
         if (DateUtils.now().after(job.getReviewDeadline())) {
@@ -254,7 +256,11 @@ public class IndividualJobSessionActor extends ActorBase {
           if (updatedMoreThanOneDay) {
             var jobSpotTaskSessionCount =
                 taskSessionDao.countPendingSpotTaskSessionsByJobId(job.getId());
-            var notDoneCount = job.getAssignDataVolume() - jobSpotTaskSessionCount;
+            var batchSetting = batchService.getBatchSettingByBatchId(job.getBatchId());
+            var datapointRepeatCount =
+                batchSetting.getAnnotatingTimesAnnotationPerDatapoint().longValue();
+            var notDoneCount =
+                job.getAssignDataVolume() * datapointRepeatCount - jobSpotTaskSessionCount;
             if (notDoneCount > 0) {
               job.setStatus(Job.JobStatus.WORKING);
               jobService.updateById(job);
@@ -283,7 +289,8 @@ public class IndividualJobSessionActor extends ActorBase {
         .getBatchById(targetJob.getBatchId())
         .orElseThrow(() -> ControllerUtils.notFound("wrong data"));
 
-    op = workloadService.filterAnswersByWorkloadLimit(op);
+    workloadService.filterAnswersByWorkloadLimit(
+        op.getSubmitAnswers(), targetJob.getId(), op.getUserId(), op.getIp());
 
     labelingMetricsController.handleAnnotate(op.getSubmitAnswers().count());
 
@@ -291,13 +298,13 @@ public class IndividualJobSessionActor extends ActorBase {
     List<TaskSession> result = taskSessionService.submitAnswers(
         op.getSubmitAnswers(), targetBatch, op.getRejectIdList(), op.getIsTester());
 
-    if (!op.getRejectIdList().isEmpty()) {
-      var rejectedTs = taskSessionDao.getTaskSessionsByIds(op.getRejectIdList());
-      if (!rejectedTs.isEmpty()) {
-        taskSessionDao.updateTaskSessionDisCount(
-            rejectedTs.map(TaskSession::getTaskId).toSet(), rejectedTs.first().getJobId());
-      }
-    }
+    //    if (!op.getRejectIdList().isEmpty()) { // todo: DisCount remove temp
+    //      var rejectedTs = taskSessionDao.getTaskSessionsByIds(op.getRejectIdList());
+    //      if (!rejectedTs.isEmpty()) {
+    //        taskSessionDao.updateTaskSessionDisCount(
+    //            rejectedTs.map(TaskSession::getTaskId).toSet(), rejectedTs.first().getJobId());
+    //      }
+    //    }
     if (!targetBatch.getReviewerRequired()) {
       taskAndReviewSessionDao.closeNoReviewRequiredJobIfNeeded(targetBatch, targetJob);
     }
diff --git a/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionForReviewerActor.java b/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionForReviewerActor.java
index 94c342db..e6b6eb2c 100644
--- a/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionForReviewerActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/IndividualJobSessionForReviewerActor.java
@@ -1,8 +1,12 @@
 package ai.saharaa.actors.jobs;
 
+import static ai.saharaa.utils.Constants.CommonErrorContent.BATCH_NOT_FOUND;
+
 import ai.saharaa.actors.ActorBase;
 import ai.saharaa.config.LabelingMetricsController;
 import ai.saharaa.daos.*;
+import ai.saharaa.distribution.TaskVisitorProvider;
+import ai.saharaa.dto.batch.BatchDetailsDTO;
 import ai.saharaa.dto.job.SubmitReviewDTO;
 import ai.saharaa.model.*;
 import ai.saharaa.services.*;
@@ -27,6 +31,7 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
   private final TaskSessionDao taskSessionDao;
   private final LabelingMetricsController labelingMetricsController;
   private final WorkloadLimitService workloadService;
+  private final TaskVisitorProvider taskVisitorProvider;
 
   public IndividualJobSessionForReviewerActor(
       BatchService batchService,
@@ -36,6 +41,7 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
       TaskSessionService taskSessionService,
       TaskSessionDao taskSessionDao,
       LabelingMetricsController labelingMetricsController,
+      TaskVisitorProvider taskVisitorProvider,
       WorkloadLimitService workloadService) {
     this.batchService = batchService;
     this.jobTaskService = jobTaskService;
@@ -43,6 +49,7 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
     this.reviewSessionService = reviewSessionService;
     this.taskSessionService = taskSessionService;
     this.taskSessionDao = taskSessionDao;
+    this.taskVisitorProvider = taskVisitorProvider;
     this.labelingMetricsController = labelingMetricsController;
     this.workloadService = workloadService;
   }
@@ -56,6 +63,10 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
     try {
       if (message instanceof OpGetReviewTask opGetReviewTask) {
         process(() -> handleOpGetReviewTask(opGetReviewTask));
+      } else if (message instanceof RefreshReviewQueue op) {
+        process(() -> reSizeTaskForReview(op.getJobId()));
+      } else if (message instanceof RefreshTaskQueue op) {
+        process(() -> reSizeTask(op.getJobId()));
       } else if (message instanceof OpSubmitReviews op) {
         process(() -> handleSubmitReviews(op));
       } else {
@@ -68,6 +79,46 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
     }
   }
 
+  private Void reSizeTask(Long jobId) {
+    var jobOpt = this.jobService.getJobById(jobId);
+    if (jobOpt.isEmpty()) return null;
+    var job = jobOpt.get();
+    if (job.getStatus().equals(Job.JobStatus.WORKING)
+        || job.getStatus().equals(Job.JobStatus.COMMITTED)) {
+      BatchDetailsDTO batch = this.getAndCheckBatch(job.getBatchId());
+      this.taskVisitorProvider
+          .getTaskVisitor(batch.getBatchSetting().getDistributeType(), JobUser.JobUserRole.LABELER)
+          .taskLifecycleManager()
+          .refreshTaskQueue(
+              job, batch.getBatchSetting().getAnnotatingTimesAnnotationPerDatapoint());
+    }
+    return null;
+  }
+
+  private Void reSizeTaskForReview(Long jobId) {
+    var jobOpt = this.jobService.getJobById(jobId);
+    if (jobOpt.isEmpty()) return null;
+    var job = jobOpt.get();
+    if (job.getStatus().equals(Job.JobStatus.WORKING)
+        || job.getStatus().equals(Job.JobStatus.COMMITTED)) {
+      BatchDetailsDTO batch = this.getAndCheckBatch(job.getBatchId());
+      this.taskVisitorProvider
+          .getTaskVisitor(batch.getBatchSetting().getDistributeType(), JobUser.JobUserRole.REVIEWER)
+          .taskLifecycleManager()
+          .refreshTaskQueue(job, batch.getBatch().getReviewingTimesReviewPerDatapoint());
+    }
+    return null;
+  }
+
+  private BatchDetailsDTO getAndCheckBatch(Long id) {
+    BatchDetailsDTO batch = batchService.getBatchDetails(id);
+    if (Objects.isNull(batch)) {
+      throw ControllerUtils.notFound(BATCH_NOT_FOUND);
+    }
+
+    return batch;
+  }
+
   private List<ReviewSession> handleOpGetReviewTask(OpGetReviewTask opGetTask) {
     var targetJobUser = opGetTask.getJobUser();
 
@@ -149,7 +200,13 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
     labelingMetricsController.reviewAnnotate(op.getSubmitReviews().count());
     List<ReviewSession> result = reviewSessionService.handleSubmitReviews(
         op.getSubmitReviews(), targetBatch, targetJob, op.getUserId(), op.getRejectIdList());
-    workloadService.updateJobUserWorkloadsForReview(op, result);
+    workloadService.updateJobUserWorkloadsForReview(
+        op.getJobId(),
+        op.getUserId(),
+        (long) op.getSubmitReviews().size(),
+        JobUser.JobUserRole.REVIEWER,
+        op.getIp(),
+        result);
     return result;
   }
 
@@ -168,6 +225,22 @@ public class IndividualJobSessionForReviewerActor extends ActorBase {
     }
   }
 
+  @Getter
+  @NoArgsConstructor
+  public static class RefreshTaskQueue extends OpBase {
+    public RefreshTaskQueue(Long jobId) {
+      super(jobId);
+    }
+  }
+
+  @Getter
+  @NoArgsConstructor
+  public static class RefreshReviewQueue extends OpBase {
+    public RefreshReviewQueue(Long jobId) {
+      super(jobId);
+    }
+  }
+
   @Getter
   @NoArgsConstructor
   public static class OpGetReviewTask extends OpBase {
diff --git a/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java b/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java
index 127591f6..78cb9a0d 100644
--- a/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java
+++ b/src/main/java/ai/saharaa/actors/jobs/JobSessionActor.java
@@ -215,7 +215,7 @@ public class JobSessionActor extends ActorBase {
     return null;
   }
 
-  private TaskSession handleSubmitAnswer(OpSubmitAnswer op) {
+  private TaskSession handleSubmitAnswer(OpSubmitAnswer op) { // not in use
     if (!op.getJobId().equals(op.getSession().getJobId())) {
       throw new SimpleError("job id mismatch, expected ${op.getJobId()} ${op.getSession()}");
     }
@@ -386,18 +386,19 @@ public class JobSessionActor extends ActorBase {
         .set(TaskSession::getDeleted, true)
         .set(TaskSession::getUpdatedAt, DateUtils.now())
         .update();
-    var sessionTaskIdSet1 = sessions.map(TaskSession::getTaskId).toSet();
-    var sessionTaskIdSet2 = pinningSessions.map(TaskSession::getTaskId).toSet();
-    var sessionTaskIdSet3 = revisingSessions.map(TaskSession::getTaskId).toSet();
-    var sessionTaskIdSet = Stream.of(sessionTaskIdSet1, sessionTaskIdSet2, sessionTaskIdSet3)
-        .flatMap(Collection::stream)
-        .toSet();
-    this.clusterConfiguration
-        .getIndividualJobSessionActor()
-        .tell(
-            new IndividualJobSessionActor.UpdateTaskSessionDisCount(
-                op.getJobId(), sessionTaskIdSet),
-            ActorRef.noSender());
+    // todo: DisCount remove temp
+    //    var sessionTaskIdSet1 = sessions.map(TaskSession::getTaskId).toSet();
+    //    var sessionTaskIdSet2 = pinningSessions.map(TaskSession::getTaskId).toSet();
+    //    var sessionTaskIdSet3 = revisingSessions.map(TaskSession::getTaskId).toSet();
+    //    var sessionTaskIdSet = Stream.of(sessionTaskIdSet1, sessionTaskIdSet2, sessionTaskIdSet3)
+    //        .flatMap(Collection::stream)
+    //        .toSet();
+    //    this.clusterConfiguration
+    //        .getIndividualJobSessionActor()
+    //        .tell(
+    //            new IndividualJobSessionActor.UpdateTaskSessionDisCount(
+    //                op.getJobId(), sessionTaskIdSet),
+    //            ActorRef.noSender());
     log.info("expired ${sessionIds.size()} task sessions for job: ${op.getJobId()}}");
     return null;
   }
@@ -551,12 +552,7 @@ public class JobSessionActor extends ActorBase {
       batchService.updateById(batch);
       jobService.updateById(job);
       seasonService.setSeasonJobStatusToPending(job.getId());
-      //      this.clusterConfiguration // todo: gonna remove
-      //          .getIndividualJobSessionActor()
-      //          .tell(
-      //              new IndividualJobSessionActor.UpdateJobUserPoints(
-      //                  job.getId(), "bonus", DateUtils.now()),
-      //              self());
+
       this.clusterConfiguration
           .getIndividualJobUserPointsActor()
           .tell(
@@ -566,6 +562,7 @@ public class JobSessionActor extends ActorBase {
     }
 
     log.info("job ${job.getId()} audit state is expired, change to state");
+    // gonna remove
     reviewSessionService.expireJobStatusToAuditing(op.getJobId());
     return null;
   }
diff --git a/src/main/java/ai/saharaa/actors/jobs/TaskQueueScheduler.java b/src/main/java/ai/saharaa/actors/jobs/TaskQueueScheduler.java
new file mode 100644
index 00000000..9d08d343
--- /dev/null
+++ b/src/main/java/ai/saharaa/actors/jobs/TaskQueueScheduler.java
@@ -0,0 +1,64 @@
+package ai.saharaa.actors.jobs;
+
+import ai.saharaa.config.cluster.ClusterConfiguration;
+import ai.saharaa.model.Job;
+import ai.saharaa.model.TraceableMessage;
+import ai.saharaa.services.JobService;
+import akka.actor.AbstractActorWithTimers;
+import akka.actor.ActorRef;
+import java.time.Duration;
+import java.util.List;
+import lombok.NoArgsConstructor;
+import org.apache.commons.collections4.CollectionUtils;
+import org.springframework.beans.factory.config.ConfigurableBeanFactory;
+import org.springframework.context.annotation.Scope;
+import org.springframework.stereotype.Component;
+
+@Component
+@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
+public class TaskQueueScheduler extends AbstractActorWithTimers {
+  private static final Object TASK_QUEUE_KEY = "task_queue_key";
+  private final JobService jobService;
+  private final ClusterConfiguration clusterConfiguration;
+
+  public TaskQueueScheduler(JobService jobService, ClusterConfiguration clusterConfiguration) {
+    this.clusterConfiguration = clusterConfiguration;
+    this.jobService = jobService;
+
+    getTimers()
+        .startTimerWithFixedDelay(TASK_QUEUE_KEY, new TaskQueueResize(), Duration.ofSeconds(10));
+  }
+
+  @Override
+  public Receive createReceive() {
+    return receiveBuilder()
+        .match(TaskQueueResize.class, message -> {
+          reSize();
+        })
+        .build();
+  }
+
+  private void reSize() {
+    // TODO: 2025/2/21 exp handler ？
+    List<Job> jobs = this.jobService.reQueue();
+    if (CollectionUtils.isEmpty(jobs)) {
+      return;
+    }
+
+    jobs.forEach(job -> {
+      clusterConfiguration
+          .getIndividualJobSessionForReviewerActor()
+          .tell(
+              new IndividualJobSessionForReviewerActor.RefreshTaskQueue(job.getId()),
+              ActorRef.noSender());
+      clusterConfiguration
+          .getIndividualJobSessionForReviewerActor()
+          .tell(
+              new IndividualJobSessionForReviewerActor.RefreshReviewQueue(job.getId()),
+              ActorRef.noSender());
+    });
+  }
+
+  @NoArgsConstructor
+  private static final class TaskQueueResize extends TraceableMessage {}
+}
diff --git a/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java b/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java
index bcee55ad..9fc9f898 100644
--- a/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java
+++ b/src/main/java/ai/saharaa/actors/tasks/MachineReviewBehavior.java
@@ -1,5 +1,8 @@
 package ai.saharaa.actors.tasks;
 
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT;
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.CORRECT_COUNT;
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.SUBMITTED_COUNT;
 import static org.apache.commons.lang3.BooleanUtils.isTrue;
 import static org.apache.commons.lang3.StringUtils.isNotBlank;
 
@@ -9,6 +12,8 @@ import ai.saharaa.daos.JobDao;
 import ai.saharaa.daos.JobUserPointsDao;
 import ai.saharaa.daos.TaskAndReviewSessionDao;
 import ai.saharaa.daos.TaskSessionDao;
+import ai.saharaa.distribution.CommonCounterManager;
+import ai.saharaa.distribution.Contants.Constants;
 import ai.saharaa.dto.job.SubmitAnswersItemDTO;
 import ai.saharaa.dto.task.AnswerDetailsDTO;
 import ai.saharaa.mappers.TaskQuestionMapper;
@@ -90,6 +95,7 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
   private final TaskSessionDao taskSessionDao;
   private final JobDao jobDao;
   private final JobUserPointsDao jobUserPointsDao;
+  private final CommonCounterManager counterManager;
   private final BatchDao batchDao;
   private final TaskAndReviewSessionDao taskAndReviewSessionDao;
   private final TaskQuestionMapper taskQuestionMapper;
@@ -103,6 +109,7 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
       JobUserPointsDao jobUserPointsDao,
       TaskAndReviewSessionDao taskAndReviewSessionDao,
       BatchDao batchDao,
+      CommonCounterManager counterManager,
       RestClient restClient,
       TaskQuestionMapper taskQuestionMapper,
       ObjectMapper jacksonObjectMapper,
@@ -112,6 +119,7 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
     this.jobDao = jobDao;
     this.jobUserPointsDao = jobUserPointsDao;
     this.batchDao = batchDao;
+    this.counterManager = counterManager;
     if (isNotBlank(machineReviewUrl)) {
       ai.saharaa.client.ApiClient apiClient = new ai.saharaa.client.ApiClient(restClient);
       apiClient.setBasePath(machineReviewUrl);
@@ -386,6 +394,15 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
             .update(new TaskSession());
         if (TaskSession.TaskSessionStatus.PendingSpot.equals(targetStatus)) {
           jobUserPointsDao.recordJobUserPointPreCalc(taskSession);
+          counterManager.setJobUserCountIncrement(
+              taskSession.getJobId(), taskSession.getUserId(), CORRECT_COUNT.value, 1L);
+          counterManager.setJobCountIncrement(taskSession.getJobId(), CORRECT_COUNT.value, 1L);
+          counterManager.setJobUserCountIncrement(
+              taskSession.getJobId(), taskSession.getUserId(), COMPLETE_COUNT.value, 1L);
+        } else if (TaskSession.TaskSessionStatus.REJECTED.equals(targetStatus)) {
+          // REJECTED by machine, do not count submit
+          counterManager.setJobUserCountDecrease(
+              taskSession.getJobId(), taskSession.getUserId(), SUBMITTED_COUNT.value, 1L);
         }
       }
     } catch (RestClientException e) {
@@ -418,6 +435,14 @@ public class MachineReviewBehavior extends AbstractBehavior<MachineReviewBehavio
         .update();
     if (TaskSession.TaskSessionStatus.PendingSpot.equals(nextStatus)) {
       taskSession.setFinalJudgeAt(dateNow);
+      counterManager.setJobCountIncrement(taskSession.getJobId(), COMPLETE_COUNT.value, 1L);
+      counterManager.setJobUserCountIncrement(
+          taskSession.getJobId(), taskSession.getUserId(), CORRECT_COUNT.value, 1L);
+      counterManager.setJobUserCountIncrement(
+          taskSession.getJobId(),
+          taskSession.getUserId(),
+          Constants.CACHE_KEY_FOR_JOB_USER.COMPLETE_COUNT.value,
+          1L);
       jobUserPointsDao.recordJobUserPointPreCalc(taskSession);
     }
   }
diff --git a/src/main/java/ai/saharaa/config/RedisConfig.java b/src/main/java/ai/saharaa/config/RedisConfig.java
index 760bbe31..c4cba76f 100644
--- a/src/main/java/ai/saharaa/config/RedisConfig.java
+++ b/src/main/java/ai/saharaa/config/RedisConfig.java
@@ -51,8 +51,8 @@ public class RedisConfig {
   @Value("${cache.rateLimit.db.index}")
   private Integer cacheRateLimitDbIndex;
 
-  @Value("${cache.auth.db.index}")
-  private Integer cacheAuthDbIndex;
+  @Value("${cache.taskQueue.db.index}")
+  private Integer cacheTaskQueueDbIndex;
 
   public RedisConfig(@Value("${spring.data.redis.ssl.enabled}") Boolean sslEnabled) {
     this.sslEnabled = sslEnabled;
@@ -117,13 +117,14 @@ public class RedisConfig {
     return new JedisConnectionFactory(redisStandaloneConfiguration, jedisClientConfiguration);
   }
 
-  @Bean(name = "redisConnectionFactoryForAuth")
-  public RedisConnectionFactory redisConnectionFactoryForAuth(JedisPoolConfig jedisPoolConfig) {
+  @Bean(name = "redisConnectionFactoryForTaskQueue")
+  public RedisConnectionFactory redisConnectionFactoryForTaskQueue(
+      JedisPoolConfig jedisPoolConfig) {
     RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
     redisStandaloneConfiguration.setHostName(this.host);
     redisStandaloneConfiguration.setPassword(this.pwd);
     redisStandaloneConfiguration.setPort(this.port);
-    redisStandaloneConfiguration.setDatabase(this.cacheAuthDbIndex);
+    redisStandaloneConfiguration.setDatabase(this.cacheTaskQueueDbIndex);
     var jpcb = JedisClientConfiguration.builder();
     if (this.sslEnabled) {
       jpcb.useSsl().hostnameVerifier((s, sslSession) -> true);
@@ -176,9 +177,9 @@ public class RedisConfig {
     return template;
   }
 
-  @Bean(name = "redisTemplateForAuth")
+  @Bean(name = "redisTemplateForTaskQueue")
   public RedisTemplate<String, Object> redisTemplateForAuth(
-      @Qualifier("redisConnectionFactoryForAuth") RedisConnectionFactory factory) {
+      @Qualifier("redisConnectionFactoryForTaskQueue") RedisConnectionFactory factory) {
     RedisTemplate<String, Object> template = new RedisTemplate<>();
     template.setConnectionFactory(factory);
     StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
diff --git a/src/main/java/ai/saharaa/config/cluster/ActorConfiguration.java b/src/main/java/ai/saharaa/config/cluster/ActorConfiguration.java
index 4eaa7a38..752d78e4 100644
--- a/src/main/java/ai/saharaa/config/cluster/ActorConfiguration.java
+++ b/src/main/java/ai/saharaa/config/cluster/ActorConfiguration.java
@@ -2,6 +2,7 @@ package ai.saharaa.config.cluster;
 
 import ai.saharaa.actors.tasks.MachineReviewBehavior;
 import ai.saharaa.daos.*;
+import ai.saharaa.distribution.CommonCounterManager;
 import ai.saharaa.mappers.TaskQuestionMapper;
 import ai.saharaa.services.FeatureFlagService;
 import akka.actor.ActorSystem;
@@ -30,6 +31,7 @@ public class ActorConfiguration {
       JobUserPointsDao jobUserPointsDao,
       TaskAndReviewSessionDao taskAndReviewSessionDao,
       BatchDao batchDao,
+      CommonCounterManager counterManager,
       TaskQuestionMapper taskQuestionMapper,
       ObjectMapper jacksonObjectMapper,
       FeatureFlagService featureFlagService) {
@@ -42,6 +44,7 @@ public class ActorConfiguration {
             jobUserPointsDao,
             taskAndReviewSessionDao,
             batchDao,
+            counterManager,
             restClient,
             taskQuestionMapper,
             jacksonObjectMapper,
diff --git a/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java b/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java
index 1efc9c6d..d28763bb 100644
--- a/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java
+++ b/src/main/java/ai/saharaa/config/cluster/ClusterConfiguration.java
@@ -468,6 +468,14 @@ public class ClusterConfiguration implements SmartInitializingSingleton {
             PoisonPill.getInstance(),
             singletonSettings),
         "achievement-record");
+
+    system.actorOf(
+        ClusterSingletonManager.props(
+            Props.create(TaskQueueScheduler.class, () -> context.getBean(TaskQueueScheduler.class))
+                .withDispatcher("akka.actor.blocking-io-dispatcher"),
+            PoisonPill.getInstance(),
+            singletonSettings),
+        "task-queue-scheduler");
   }
 
   private void initializeProxyActors() {
diff --git a/src/main/java/ai/saharaa/controllers/BatchController.java b/src/main/java/ai/saharaa/controllers/BatchController.java
index 83835d88..3500444f 100644
--- a/src/main/java/ai/saharaa/controllers/BatchController.java
+++ b/src/main/java/ai/saharaa/controllers/BatchController.java
@@ -16,6 +16,7 @@ import ai.saharaa.daos.BatchExampleDao;
 import ai.saharaa.daos.BatchNDADao;
 import ai.saharaa.daos.TaskListDao;
 import ai.saharaa.daos.TaskQuestionDao;
+import ai.saharaa.distribution.TaskVisitorProvider;
 import ai.saharaa.dto.ApiResult;
 import ai.saharaa.dto.Chart2;
 import ai.saharaa.dto.SimpleDataDTO;
@@ -95,6 +96,7 @@ public class BatchController {
   private final TaskQuestionDao taskQuestionDao;
   private final BatchChartService batchChartService;
   private final ClusterConfiguration clusterConfiguration;
+  private final TaskVisitorProvider taskVisitorProvider;
 
   public BatchController(
       ClusterConfiguration clusterConfiguration,
@@ -118,7 +120,8 @@ public class BatchController {
       NotificationService notificationService,
       VaultService vaultService,
       TaskQuestionDao taskQuestionDao,
-      BatchChartService batchChartService) {
+      BatchChartService batchChartService,
+      TaskVisitorProvider taskVisitorProvider) {
     this.clusterConfiguration = clusterConfiguration;
     this.batchService = batchService;
     this.projectService = projectService;
@@ -140,6 +143,7 @@ public class BatchController {
     this.vaultService = vaultService;
     this.taskQuestionDao = taskQuestionDao;
     this.batchChartService = batchChartService;
+    this.taskVisitorProvider = taskVisitorProvider;
   }
 
   @PostMapping("/v2")
@@ -852,11 +856,15 @@ public class BatchController {
         .filter(
             v -> Objects.equals(v.getOwnerId(), ControllerUtils.currentUid()) || passForManagers())
         .orElseThrow(() -> ControllerUtils.forbidden("no permission"));
-    projectService
-        .getProjectById(batch.getProjectId())
-        .filter(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
-        .ifPresent(p -> jobService.assignJobToExternalBatchJob(batch));
-    return batchService.launchBatch(batch).toApiResult();
+
+    BatchSetting setting = this.batchService.getBatchSettingByBatchId(id);
+
+    this.taskVisitorProvider
+        .getTaskVisitor(setting.getDistributeType(), JobUser.JobUserRole.LABELER)
+        .taskLifecycleManager()
+        .initializeTaskQueue(batch, setting);
+
+    return this.batchService.getBatchById(id).get().toApiResult();
   }
 
   @GetMapping("/{id}/job")
diff --git a/src/main/java/ai/saharaa/controllers/JobController.java b/src/main/java/ai/saharaa/controllers/JobController.java
index fa3f30e5..2717df68 100644
--- a/src/main/java/ai/saharaa/controllers/JobController.java
+++ b/src/main/java/ai/saharaa/controllers/JobController.java
@@ -5,14 +5,16 @@ import static ai.saharaa.model.JobUser.JobUserRole.*;
 import static ai.saharaa.utils.Constants.CommonErrorContent.BATCH_NOT_FOUND;
 import static ai.saharaa.utils.Constants.ROLE_EXTERNAL_USER;
 import static ai.saharaa.utils.Constants.ROLE_USER;
+import static java.util.stream.Collectors.toList;
+import static java.util.stream.Collectors.toMap;
 
-import ai.saharaa.actors.jobs.IndividualJobSessionActor;
-import ai.saharaa.actors.jobs.IndividualJobSessionForReviewerActor;
 import ai.saharaa.actors.jobs.JobSessionActor;
 import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.config.cluster.ClusterConfiguration;
 import ai.saharaa.config.perms.*;
 import ai.saharaa.daos.*;
+import ai.saharaa.distribution.TaskDistributor;
+import ai.saharaa.distribution.TaskVisitorProvider;
 import ai.saharaa.dto.ApiResult;
 import ai.saharaa.dto.SimpleDataDTO;
 import ai.saharaa.dto.batch.BatchDetailsDTO;
@@ -21,11 +23,11 @@ import ai.saharaa.dto.job.*;
 import ai.saharaa.dto.sign.*;
 import ai.saharaa.dto.task.CreateJobByPreTaskDTO;
 import ai.saharaa.dto.task.CreateJobDTO;
+import ai.saharaa.dto.task.HybridResourceDTO;
 import ai.saharaa.dto.task.TaskListDetailsDTO;
 import ai.saharaa.enums.AuditSubmissionType;
 import ai.saharaa.enums.MarketSortType;
 import ai.saharaa.enums.SortType;
-import ai.saharaa.enums.WorkloadType;
 import ai.saharaa.model.*;
 import ai.saharaa.model.achievement.Achievement;
 import ai.saharaa.services.*;
@@ -40,9 +42,9 @@ import jakarta.validation.Valid;
 import java.sql.Timestamp;
 import java.util.*;
 import java.util.concurrent.CompletableFuture;
+import java.util.function.Function;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections4.CollectionUtils;
-import org.apache.commons.lang3.time.StopWatch;
 import org.springframework.validation.annotation.Validated;
 import org.springframework.web.bind.annotation.*;
 import org.springframework.web.context.request.RequestContextHolder;
@@ -55,9 +57,6 @@ public class JobController {
   private final JobService jobService;
   private final AchievementService achievementService;
   private final JobTaskService jobTaskService;
-  private final TaskSessionDao taskSessionDao;
-  private final JobTaskDao jobTaskDao;
-  private final IGlobalCache redisCache;
   private final JobUserService jobUserService;
   private final HoneyPotService honeyPotService;
   private final PreTaskService preTaskService;
@@ -79,7 +78,7 @@ public class JobController {
 
   private final NotificationService notificationService;
   private final IndividualsService individualsService;
-  private final WorkloadLimitService workloadLimitService;
+  private final TaskVisitorProvider taskVisitorProvider;
 
   public JobController(
       JobService jobService,
@@ -108,13 +107,10 @@ public class JobController {
       BatchDao batchDao,
       IndividualsService individualsService,
       HoneyPotService honeyPotService,
-      WorkloadLimitService workloadLimitService) {
+      TaskVisitorProvider taskVisitorProvider) {
     this.jobService = jobService;
     this.achievementService = achievementService;
     this.jobTaskService = jobTaskService;
-    this.taskSessionDao = taskSessionDao;
-    this.jobTaskDao = jobTaskDao;
-    this.redisCache = redisCache;
     this.jobUserService = jobUserService;
     this.honeyPotService = honeyPotService;
     this.batchDataService = batchDataService;
@@ -135,7 +131,7 @@ public class JobController {
     this.preTaskService = preTaskService;
     this.examSessionDao = examSessionDao;
     this.individualsService = individualsService;
-    this.workloadLimitService = workloadLimitService;
+    this.taskVisitorProvider = taskVisitorProvider;
   }
 
   @PostMapping("/{id}/assign")
@@ -523,177 +519,50 @@ public class JobController {
   @GetMapping("/{id}/take-job/for-individuals")
   @IsLabeler
   public ApiResult<Optional<List<TakeJobResultDTO>>> takeJob2(@PathVariable Long id) {
-    // var job = jobService.validateJobPermission(id);
+
+    HttpServletRequest request =
+        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
+    var ip = Web2AuthService.getClientIP(request);
 
     var curId = ControllerUtils.currentUid();
-    log.info(
-        "[campfire-platform]-[individuals]-[takeJob2]-[begin]-[jobId:{}]-[userId:{}]", id, curId);
-    StopWatch stopWatch = new StopWatch();
-    stopWatch.start();
 
-    var j = jobService
+    if (individualsService.checkUserBannedInPlatform(curId))
+      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
+
+    Job job = jobService
         .getJobById(id)
         .orThrow(() -> ControllerUtils.notFound("Task not found."))
         .filter(jb -> jb.getStatus().equals(Job.JobStatus.WORKING)
             || jb.getStatus().equals(Job.JobStatus.COMMITTED))
         .orElseThrow(() ->
             ControllerUtils.notFound("This task has ended. Thank you for your participation."));
-    var b = batchService
-        .getBatchById(j.getBatchId())
+    var batch = batchService
+        .getBatchById(job.getBatchId())
         .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
-    if (DateUtils.now().after(b.getDeadline())) {
-      throw ControllerUtils.notFound("Task has expired");
+    if (DateUtils.now().after(batch.getDeadline())) {
+      throw ControllerUtils.notFound("This task has ended. Thank you for your participation.");
     }
-    if (individualsService.checkUserBannedInPlatform(curId))
-      throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
 
     var jobUser = jobUserService
         .getJobUserByJobAndUserId(id, curId)
         .filter(ju -> ju.getActive().equals(true))
         .orElseThrow(() -> ControllerUtils.badRequest(
             "You've been banned from this task because we've detected malicious activity."));
-    BatchSetting batchSetting = batchDao.getBatchSettingById(b.getId());
-    stopWatch.split();
-    log.info(
-        "[campfire-platform]-[individuals]-[takeJob2]-[get setting]-[execTime: {}]",
-        stopWatch.getSplitDuration().toMillis());
-
-    var pendingTaskSession =
-        jobTaskService.getOngoingTaskSessionsForJobUser(jobUser.getId(), LABELER);
-    var batchAnnotationBatchSize = b.getAnnotatingSubmitRequired();
-    HttpServletRequest request =
-        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
-    var ip = Web2AuthService.getClientIP(request);
-    if (CollectionUtils.isNotEmpty(pendingTaskSession)) { // have pending taskSession
-      if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.SINGLE)) {
-        stopWatch.stop();
-        log.info(
-            "[campfire-platform]-[individuals]-[takeJob2]-[pending tasks(SINGLE)]-[execTime: {}]",
-            stopWatch.getDuration().toMillis());
-        return takeTaskSessionResult(
-            new SimpleResult<List<TaskSession>>(pendingTaskSession),
-            b,
-            batchSetting,
-            jobUser,
-            curId);
-      } else { // must be RAW, and assembled is not in use.
-        if (pendingTaskSession.size() >= batchAnnotationBatchSize) {
-          stopWatch.stop();
-          log.info(
-              "[campfire-platform]-[individuals]-[takeJob2]-[pending tasks(AnnotationBatchSize)]-[execTime: {}]",
-              stopWatch.getDuration().toMillis());
-          return takeTaskSessionResult(
-              new SimpleResult<List<TaskSession>>(pendingTaskSession),
-              b,
-              batchSetting,
-              jobUser,
-              curId);
-        }
-        var limit = batchAnnotationBatchSize - pendingTaskSession.size();
-        CompletableFuture<SimpleResult<List<TaskSession>>> r = ActorUtils.askWithDefault(
-            this.clusterConfiguration.getIndividualJobSessionActor(),
-            new IndividualJobSessionActor.OpGetTask(
-                id,
-                curId,
-                jobUser,
-                b,
-                batchSetting,
-                limit,
-                pendingTaskSession.first().getCreatedAt(),
-                ip));
-        var result = r.get().getResult();
-        if (result != null && !result.isEmpty()) {
-          pendingTaskSession.addAll(result);
-        }
+    BatchSetting batchSetting = batchDao.getBatchSettingById(batch.getId());
 
-        stopWatch.stop();
-        log.info(
-            "[campfire-platform]-[individuals]-[takeJob2]-[pending tasks]-[execTime: {}]",
-            stopWatch.getDuration().toMillis());
-        return takeTaskSessionResult(
-            new SimpleResult<List<TaskSession>>(pendingTaskSession),
-            b,
-            batchSetting,
-            jobUser,
-            curId);
-      }
-    } else { // no pending taskSession, so check revising session
-      var reviseLimitBatchSize = batchAnnotationBatchSize;
-      if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.SINGLE)) {
-        Long totalTaskSessionsCount = taskSessionDao.countTaskSessionsByJobId(j.getId());
-        reviseLimitBatchSize = Math.toIntExact(Math.min(
-            reviseLimitBatchSize,
-            (batchSetting.getAnnotatingTimesAnnotationPerDatapoint() - totalTaskSessionsCount)));
-      }
-      if (reviseLimitBatchSize > 0) {
-        var reviseTasks = jobTaskService.getReviseTaskSessionsForJobUser(
-            jobUser.getId(), LABELER, reviseLimitBatchSize);
-        jobTaskService.clearWaitingReverseSession(jobUser.getId(), LABELER);
-        if (!reviseTasks.isEmpty()) {
-          var res = reviseTasks
-              .map(taskSessionService::reCreateTaskSessionWithRevisedRecord)
-              .toList();
-
-          stopWatch.stop();
-          log.info(
-              "[campfire-platform]-[individuals]-[takeJob2]-[revise tasks]-[execTime: {}]",
-              stopWatch.getDuration().toMillis());
-          return takeTaskSessionResult(
-              new SimpleResult<List<TaskSession>>(res), b, batchSetting, jobUser, curId);
-        }
-      }
-    }
-    var batchCreateAt = DateUtils.now();
-    if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.SINGLE)) {
-      workloadLimitService.checkIfWorkloadLimitExceeded(
-          j.getId(), jobUser.getUserId(), LABELER, ip);
-      var singleTaskId = redisCache.hasKey("jobOpt_${j.getId()}:taskId")
-          ? Long.valueOf(redisCache.get("jobOpt_${j.getId()}:taskId").toString())
-          : jobTaskDao.getSingleDatapointJobTaskByJobId(j.getId()).first().getTaskId();
-      var userWorkloads = redisCache
-          .hget("job_${j.getId()}:user_${jobUser.getUserId()}:workloads", "total")
-          .asOpt()
-          .orElse(0);
-      var result = taskSessionService.createTaskSessionForSingleData(
-          jobUser,
-          singleTaskId,
-          LABELER,
-          batchCreateAt,
-          batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
-          (null == batchSetting.getWorkloadMaxPerJobUser()
-                  || batchSetting.getWorkloadMaxPerJobUser() <= 0)
-              ? batchSetting.getAnnotatingTimesAnnotationPerDatapoint()
-              : batchSetting.getWorkloadMaxPerJobUser() - (int) userWorkloads,
-          batchAnnotationBatchSize);
-      stopWatch.stop();
-      log.info(
-          "[campfire-platform]-[individuals]-[takeJob2]-[Distribute single tasks]-[execTime: {}]",
-          stopWatch.getDuration().toMillis());
-      return takeTaskSessionResult(
-          new SimpleResult<List<TaskSession>>(result), b, batchSetting, jobUser, curId);
+    TaskDistributor<TaskSession> taskDistributor = this.taskVisitorProvider
+        .getTaskVisitor(batchSetting.getDistributeType(), jobUser.getRole())
+        .taskDistributor();
+    var isTester = ControllerUtils.isTesterUser();
 
-    } else {
-      CompletableFuture<SimpleResult<List<TaskSession>>> r = ActorUtils.askWithDefault(
-          this.clusterConfiguration.getIndividualJobSessionActor(),
-          new IndividualJobSessionActor.OpGetTask(
-              id, curId, jobUser, b, batchSetting, batchAnnotationBatchSize, batchCreateAt, ip));
-      var result = r.get().getResult();
-
-      stopWatch.stop();
-      log.info(
-          "[campfire-platform]-[individuals]-[takeJob2]-[Distribute tasks]-[execTime: {}]",
-          stopWatch.getDuration().toMillis());
-      return takeTaskSessionResult(
-          new SimpleResult<List<TaskSession>>(result), b, batchSetting, jobUser, curId);
-    }
+    List<TaskSession> taskSessions =
+        taskDistributor.assignTasks(job, batch, batchSetting, jobUser, ip, isTester);
+
+    return takeTaskSessionResult(new SimpleResult(taskSessions), batch, batchSetting, jobUser);
   }
 
   private ApiResult<Optional<List<TakeJobResultDTO>>> takeTaskSessionResult(
-      SimpleResult<List<TaskSession>> result,
-      Batch b,
-      BatchSetting batchSetting,
-      JobUser jobUser,
-      Long curId) {
+      SimpleResult<List<TaskSession>> result, Batch b, BatchSetting batchSetting, JobUser jobUser) {
     var res = result.asApiResult().map(ts -> Optional.ofNullable(ts).map(session -> session.stream()
         .map(s -> TakeJobResultDTO.builder()
             .taskSession(s)
@@ -714,8 +583,7 @@ public class JobController {
     if (CollectionUtils.isNotEmpty(batchSetting.getHoneyPotBatches())) {
       res.getData().ifPresent(taskSessionResults -> {
         if (!taskSessionResults.isEmpty()) {
-          honeyPotService.adaptHoneyPotSession(
-              taskSessionResults, b, curId, jobUser.getId(), false);
+          honeyPotService.adaptHoneyPotSession(taskSessionResults, b, jobUser, false);
         }
       });
     }
@@ -754,8 +622,7 @@ public class JobController {
       var jobUser = jobUserService.getJobUserByJobAndUserId(id, curId);
       res.getData().ifPresent(taskSessionResults -> {
         if (!taskSessionResults.isEmpty()) {
-          honeyPotService.adaptHoneyPotSession(
-              taskSessionResults, b, curId, jobUser.get().getId(), true);
+          honeyPotService.adaptHoneyPotSession(taskSessionResults, b, jobUser.get(), true);
         }
       });
     }
@@ -822,16 +689,17 @@ public class JobController {
     if (batchSetting.getDistributeType() == BatchSetting.DistributeType.RAW) {
       taskSessionService.skipTaskSession(session, curId);
     }
-    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
-
-      HttpServletRequest request =
-          ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
-      var ip = Web2AuthService.getClientIP(request);
-      var workloadRec = workloadLimitService.getJobUserWorkloadType(jobUser, LABELER, ip);
-      if (WorkloadType.NO_WORKLOAD.equals(workloadRec)) {
-        taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(session);
-      }
-    }
+    //    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
+    //
+    //      HttpServletRequest request =
+    //          ((ServletRequestAttributes)
+    // RequestContextHolder.getRequestAttributes()).getRequest();
+    //      var ip = Web2AuthService.getClientIP(request);
+    //      var workloadRec = workloadLimitService.getJobUserWorkloadType(jobUser, LABELER, ip);
+    //      if (WorkloadType.NO_WORKLOAD.equals(workloadRec)) {
+    //        taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(session, jobUser);
+    //      }
+    //    }
     return jobService.reportTask(id, curId, body).toApiResult();
   }
 
@@ -895,8 +763,10 @@ public class JobController {
   @GetMapping("/{id}/take-review-jobs/for-individuals")
   public ApiResult<Optional<List<TakeJobResultDTO>>> takeReviewJobs(@PathVariable Long id) {
     var curId = ControllerUtils.currentUid();
-    if (individualsService.checkUserBannedInPlatform(curId))
+    var isTester = ControllerUtils.isTesterUser();
+    if (individualsService.checkUserBannedInPlatform(curId)) {
       throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
+    }
     var job = jobService
         .getJobById(id)
         .filter(j -> j.getStatus().equals(Job.JobStatus.WORKING)
@@ -912,50 +782,64 @@ public class JobController {
         .filter(ju -> ju.getActive().equals(true))
         .orElseThrow(() -> ControllerUtils.badRequest(
             "You've been banned from this task because we've detected malicious activity."));
-    var reviewSessions = jobTaskService.getOngoingReviewSessionsForJobUser(jobUser.getId());
+
     HttpServletRequest request =
         ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
     var ip = Web2AuthService.getClientIP(request);
-    SimpleResult<List<ReviewSession>> result = null;
-    if (!reviewSessions.isEmpty()
-        && reviewSessions.size() >= batch.getReviewingRequiredDatapoint()) {
-      log.info("user has ongoing job tasks, size is ${reviewSessions.size()}");
-      result = new SimpleResult<>(reviewSessions);
-    } else {
-      CompletableFuture<SimpleResult<List<ReviewSession>>> r = ActorUtils.askWithDefault(
-          this.clusterConfiguration.getIndividualJobSessionForReviewerActor(),
-          new IndividualJobSessionForReviewerActor.OpGetReviewTask(id, curId, batch, jobUser, ip));
-      result = r.get();
+
+    TaskDistributor<ReviewSession> distribution = this.taskVisitorProvider
+        .getTaskVisitor(batchSetting.getDistributeType(), jobUser.getRole())
+        .taskDistributor();
+    var reviewSessions = distribution.assignTasks(job, batch, batchSetting, jobUser, ip, isTester);
+
+    if (reviewSessions.isEmpty()) {
+      return ApiResult.success(Optional.ofNullable(Collections.EMPTY_LIST));
     }
-    var res = result.asApiResult().map(ts -> Optional.ofNullable(ts)
-        .map(sessions -> sessions.stream()
-            .map(session -> TakeJobResultDTO.builder()
-                .taskSession(taskSessionService
-                    .getTaskSessionById(session.getTaskSessionId())
-                    .orElse(null))
-                .reviewSession(session)
-                .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null))
-                .build())
-            .toList()));
-    res.getData()
-        .ifPresent(resData -> resData.forEach(resDat -> {
-          var taskOption = taskService.getTaskById(resDat.getTaskSession().getTaskId());
-          taskOption.ifPresent(task -> {
+    Map<Long, TaskSession> taskSessionMap = this.taskSessionService
+        .getTaskSessionByIdList(
+            reviewSessions.stream().map(ReviewSession::getTaskSessionId).collect(toList()))
+        .stream()
+        .collect(toMap(TaskSession::getId, Function.identity()));
+
+    Map<Long, Task> taskMap = this.taskService
+        .getTaskByIds(reviewSessions.stream().map(ReviewSession::getTaskId).toList())
+        .stream()
+        .collect(toMap(Task::getId, Function.identity()));
+
+    Map<Long, Resource> resourceMap =
+        taskService.getResourceByTaskIds(taskMap.values().mapToList(Task::getResourceId)).stream()
+            .collect(toMap(Resource::getId, Function.identity()));
+
+    List<TakeJobResultDTO> res = reviewSessions.stream()
+        .map(x -> {
+          Task task = taskMap.get(x.getTaskId());
+
+          TakeJobResultDTO dto = TakeJobResultDTO.builder()
+              .taskSession(taskSessionMap.get(x.getTaskSessionId()))
+              .reviewSession(x)
+              .build();
+
+          if (Objects.nonNull(task)) {
+            dto.setResource(resourceMap.get(task.getResourceId()));
             if (task.getIsHybrid()) {
-              var hybridResources = taskService.getHybridTaskResource(task.getId());
-              hybridResources.ifPresent(resDat::setHybridTaskResources);
+
+              Optional<List<HybridResourceDTO>> hybridTaskResource =
+                  this.taskService.getHybridTaskResource(task.getId());
+              dto.setHybridTaskResources(hybridTaskResource.orElse(null));
             }
-          });
-        }));
+          }
+
+          return dto;
+        })
+        .collect(toList());
+
     if (CollectionUtils.isNotEmpty(batchSetting.getHoneyPotReviewBatches())) {
-      res.getData().ifPresent(takeJobResults -> {
-        if (!takeJobResults.isEmpty()) {
-          honeyPotService.adaptHpReviewSession(
-              takeJobResults, batch, curId, jobUser.getId(), false);
-        }
-      });
+      if (CollectionUtils.isNotEmpty(res)) {
+        honeyPotService.adaptHpReviewSession(res, batch, jobUser, false);
+      }
     }
-    return res;
+
+    return ApiResult.success(Optional.ofNullable(res));
   }
 
   @GetMapping("/{id}/has-pending-review-jobs")
diff --git a/src/main/java/ai/saharaa/controllers/ReviewSessionController.java b/src/main/java/ai/saharaa/controllers/ReviewSessionController.java
index e8125672..3c5e6372 100644
--- a/src/main/java/ai/saharaa/controllers/ReviewSessionController.java
+++ b/src/main/java/ai/saharaa/controllers/ReviewSessionController.java
@@ -1,27 +1,16 @@
 package ai.saharaa.controllers;
 
 import ai.saharaa.actors.jobs.IndividualJobOtherProcessActor;
-import ai.saharaa.actors.jobs.IndividualJobSessionForReviewerActor;
 import ai.saharaa.actors.jobs.JobSessionActor;
 import ai.saharaa.config.cluster.ClusterConfiguration;
+import ai.saharaa.distribution.TaskSubmitter;
+import ai.saharaa.distribution.TaskVisitorProvider;
 import ai.saharaa.dto.ApiResult;
 import ai.saharaa.dto.job.PreSignedUrlTaskSessionResultDTO;
 import ai.saharaa.dto.job.SubmitAnswerDTO;
-import ai.saharaa.dto.job.SubmitHoneyPotReviewsItemDTO;
-import ai.saharaa.dto.job.SubmitReviewDTO;
 import ai.saharaa.dto.job.SubmitReviewsDTO;
-import ai.saharaa.model.HoneyPot.HoneyPotReviewSession;
-import ai.saharaa.model.JobUser;
-import ai.saharaa.model.ReviewSession;
-import ai.saharaa.model.SimpleResult;
-import ai.saharaa.model.TaskSession;
-import ai.saharaa.services.CaptchaService;
-import ai.saharaa.services.HoneyPotService;
-import ai.saharaa.services.IndividualsService;
-import ai.saharaa.services.JobUserService;
-import ai.saharaa.services.ReviewSessionService;
-import ai.saharaa.services.TaskSessionService;
-import ai.saharaa.services.Web2AuthService;
+import ai.saharaa.model.*;
+import ai.saharaa.services.*;
 import ai.saharaa.utils.ActorUtils;
 import ai.saharaa.utils.ControllerUtils;
 import ai.saharaa.utils.DateUtils;
@@ -36,7 +25,6 @@ import java.util.ArrayList;
 import java.util.List;
 import java.util.Optional;
 import java.util.concurrent.CompletableFuture;
-import java.util.function.Function;
 import org.springframework.web.bind.annotation.GetMapping;
 import org.springframework.web.bind.annotation.PathVariable;
 import org.springframework.web.bind.annotation.PostMapping;
@@ -57,6 +45,9 @@ public class ReviewSessionController {
   private final HoneyPotService honeyPotService;
   private final JobUserService jobUserService;
   private final ClusterConfiguration clusterConfiguration;
+  private final TaskVisitorProvider taskDistributor;
+  private final BatchService batchService;
+  private final JobService jobService;
 
   public ReviewSessionController(
       ReviewSessionService reviewSessionService,
@@ -65,7 +56,10 @@ public class ReviewSessionController {
       HoneyPotService honeyPotService,
       JobUserService jobUserService,
       CaptchaService captchaService,
-      ClusterConfiguration clusterConfiguration) {
+      ClusterConfiguration clusterConfiguration,
+      TaskVisitorProvider taskDistributor,
+      BatchService batchService,
+      JobService jobService) {
     this.reviewSessionService = reviewSessionService;
     this.captchaService = captchaService;
     this.individualsService = individualsService;
@@ -73,6 +67,9 @@ public class ReviewSessionController {
     this.honeyPotService = honeyPotService;
     this.jobUserService = jobUserService;
     this.clusterConfiguration = clusterConfiguration;
+    this.taskDistributor = taskDistributor;
+    this.batchService = batchService;
+    this.jobService = jobService;
   }
 
   @PostMapping("/{id}/approve")
@@ -104,13 +101,22 @@ public class ReviewSessionController {
   public ApiResult<Boolean> submitRevisions(@Valid @RequestBody SubmitReviewsDTO reviews) {
     var curId = ControllerUtils.currentUid();
     var isTester = ControllerUtils.isTesterUser();
+
+    if (individualsService.checkUserBannedInPlatform(curId)) {
+      if (isTester) {
+        jobUserService.removeUserBanningInPlatform(curId);
+      } else {
+        throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
+      }
+    }
+
     HttpServletRequest request =
         ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
     var jobUser = jobUserService
         .getJobUserByJobAndUserId(reviews.getReviews().first().getJobId(), curId)
         .filter(ju -> !ju.getDeleted())
         .orElseThrow(() -> ControllerUtils.notFound("Job user not found"));
-    var firstSessionId = reviews.getReviews().first().getTaskSessionId();
+
     if (!isTester) {
       var submitTimeInHeader = request.getHeader("X-Submit-Time");
       StringBuilder stringBuilder = new StringBuilder();
@@ -139,75 +145,29 @@ public class ReviewSessionController {
       }
     }
     var ip = Web2AuthService.getClientIP(request);
+    var firstSessionId = reviews.getReviews().first().getTaskSessionId();
     if (!isTester
         && captchaService.isRecaptchaRequired(
             firstSessionId, jobUser.getId(), JobUser.JobUserRole.REVIEWER)) {
       captchaService.verifyRecaptchaToken(reviews.getCaptchaToken(), ip);
     }
-    if (individualsService.checkUserBannedInPlatform(curId)) {
-      if (isTester) {
-        jobUserService.removeUserBanningInPlatform(curId);
-      } else {
-        throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
-      }
-    }
-    var reviewSessionIds =
-        reviews.getReviews().stream().map(SubmitReviewDTO::getReviewSessionId).toList();
-    var sessions = reviewSessionService
-        .getReviewSessionByIdList(reviewSessionIds)
-        .filter(s -> s.getUserId().equals(curId))
-        .filter(s -> s.getStatus().equals(ReviewSession.ReviewSessionStatus.PENDING))
-        .toList();
-    if (sessions.isEmpty() && !reviewSessionIds.isEmpty()) {
-      throw ControllerUtils.notFound("You are not allowed to approve these submissions");
-    }
-    if (sessions.size() < reviewSessionIds.size()) {
-      throw ControllerUtils.notFound("Review session may expired, please reload");
-    }
-    var taskSessionIds =
-        reviews.getReviews().stream().map(SubmitReviewDTO::getTaskSessionId).toList();
 
-    var taskSessions = taskSessionService
-        .getTaskSessionByIdList(taskSessionIds)
-        .filter(s -> !s.getDeleted())
-        .filter(s -> s.getStatus().equals(TaskSession.TaskSessionStatus.PendingReview))
-        .toList();
-    if (taskSessions.isEmpty() && !taskSessionIds.isEmpty()) {
-      throw ControllerUtils.notFound("You are not allowed to approve these sessions");
-    }
-    if (taskSessions.size() < reviewSessionIds.size()) {
-      throw ControllerUtils.notFound("task session may expired, please reload");
-    }
+    Job job = this.jobService
+        .getJobById(jobUser.getTaskListSessionId())
+        .orElseThrow(() -> ControllerUtils.notFound("Job not found"));
+    Batch batch = this.batchService
+        .getBatchById(job.getBatchId())
+        .orElseThrow(() -> ControllerUtils.notFound("Batch not found"));
 
-    var hpReviews = reviews.getHpReviews() == null
-        ? List.<SubmitHoneyPotReviewsItemDTO>of()
-        : reviews.getHpReviews();
-    var hpSessions = honeyPotService
-        .getSessionByReviewSessionIdList(reviewSessionIds)
-        .filter(s -> !s.getDeleted())
-        .toList();
-    if (reviewSessionIds.size() < hpSessions.size()) {
-      throw ControllerUtils.notFound("Invalid data.");
-    }
-    var id2HpReviewSession =
-        hpSessions.toMap(HoneyPotReviewSession::getReviewSessionId, Function.identity());
-    var notIncluded = hpReviews.stream()
-        .filter(
-            r -> !id2HpReviewSession.containsKey(r.getHoneyPotReviewSession().getReviewSessionId()))
-        .findAny();
-    if (notIncluded.isPresent()) {
-      throw ControllerUtils.notFound("Invalid data.");
-    }
-    var rejectIdList = honeyPotService.submitReviewAndJudge(hpReviews, jobUser, isTester);
-    if (rejectIdList.getRight()) {
-      return Boolean.valueOf(false).toApiResult();
-    }
+    BatchSetting batchSetting = this.batchService.getBatchSettingByBatchId(batch.getId());
+
+    TaskSubmitter<SubmitReviewsDTO, Boolean> taskSubmitter = this.taskDistributor
+        .getTaskVisitor(batchSetting.getDistributeType(), jobUser.getRole())
+        .taskSubmitter();
+
+    taskSubmitter.submitTasks(reviews, job, batch, batchSetting, jobUser, isTester);
 
-    CompletableFuture<SimpleResult<List<ReviewSession>>> unused = ActorUtils.askWithDefault(
-        this.clusterConfiguration.getIndividualJobSessionForReviewerActor(),
-        new IndividualJobSessionForReviewerActor.OpSubmitReviews(
-            reviews.getReviews(), DateUtils.now(), curId, rejectIdList.getLeft(), ip));
-    return Boolean.valueOf(true).toApiResult();
+    return ApiResult.success(true);
   }
 
   @PostMapping("/{id}/submit-revision")
diff --git a/src/main/java/ai/saharaa/controllers/TaskSessionController.java b/src/main/java/ai/saharaa/controllers/TaskSessionController.java
index dc1d2b79..4afc957e 100644
--- a/src/main/java/ai/saharaa/controllers/TaskSessionController.java
+++ b/src/main/java/ai/saharaa/controllers/TaskSessionController.java
@@ -1,16 +1,13 @@
 package ai.saharaa.controllers;
 
-import static ai.saharaa.model.TaskSession.TaskSessionStatus.PINNED;
+import static ai.saharaa.utils.Constants.CommonErrorContent.BATCH_NOT_FOUND;
 
-import ai.saharaa.actors.jobs.IndividualJobOtherProcessActor;
-import ai.saharaa.actors.jobs.IndividualJobSessionActor;
-import ai.saharaa.actors.jobs.JobSessionActor;
-import ai.saharaa.config.cluster.ClusterConfiguration;
 import ai.saharaa.config.perms.IsAccountManager;
 import ai.saharaa.config.perms.IsApiKeyClient;
 import ai.saharaa.config.perms.IsLabeler;
 import ai.saharaa.config.perms.IsNodeManager;
-import ai.saharaa.daos.JobDao;
+import ai.saharaa.distribution.TaskSubmitter;
+import ai.saharaa.distribution.TaskVisitorProvider;
 import ai.saharaa.dto.ApiResult;
 import ai.saharaa.dto.job.*;
 import ai.saharaa.dto.task.AnswerDetailsDTO;
@@ -18,26 +15,17 @@ import ai.saharaa.dto.task.MyShellBatchDownloadUrlDTO;
 import ai.saharaa.dto.task.MyShellSubmissionFileDTO;
 import ai.saharaa.dto.task.PreSignedUrlUploadDTO;
 import ai.saharaa.model.*;
-import ai.saharaa.model.HoneyPot.HoneyPotSession;
 import ai.saharaa.model.api.ApiKeys;
 import ai.saharaa.services.*;
-import ai.saharaa.utils.ActorUtils;
 import ai.saharaa.utils.ControllerUtils;
-import ai.saharaa.utils.DateUtils;
 import ai.saharaa.utils.ObjectUtils;
-import akka.actor.ActorRef;
-import jakarta.servlet.http.HttpServletRequest;
 import jakarta.validation.Valid;
-import java.io.BufferedReader;
-import java.io.IOException;
-import java.util.ArrayList;
 import java.util.List;
 import java.util.Objects;
-import java.util.concurrent.CompletableFuture;
 import java.util.function.Function;
+import org.apache.commons.collections4.CollectionUtils;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.cache.annotation.Cacheable;
-import org.springframework.util.CollectionUtils;
 import org.springframework.web.bind.annotation.GetMapping;
 import org.springframework.web.bind.annotation.PathVariable;
 import org.springframework.web.bind.annotation.PostMapping;
@@ -45,8 +33,6 @@ import org.springframework.web.bind.annotation.RequestBody;
 import org.springframework.web.bind.annotation.RequestMapping;
 import org.springframework.web.bind.annotation.RequestParam;
 import org.springframework.web.bind.annotation.RestController;
-import org.springframework.web.context.request.RequestContextHolder;
-import org.springframework.web.context.request.ServletRequestAttributes;
 
 @RestController
 @RequestMapping("/api/task-sessions")
@@ -54,47 +40,36 @@ public class TaskSessionController {
   private final BatchService batchService;
   private final TaskService taskService;
   private final UserService userService;
-  private final JobDao jobDao;
   private final TaskSessionService taskSessionService;
-  private final HoneyPotService honeyPotService;
   private final JobUserService jobUserService;
-  private final IndividualsService individualsService;
-  private final CaptchaService captchaService;
   private final SpotSessionService spotSessionService;
   private final ReviewSessionService reviewSessionService;
-  private final ClusterConfiguration clusterConfiguration;
-  private final NotificationService notificationService;
   private final ResourceService resourceService;
+  private final TaskVisitorProvider taskVisitorProvider;
+  private final JobService jobService;
 
   public TaskSessionController(
       BatchService batchService,
       TaskService taskService,
       UserService userService,
       TaskSessionService taskSessionService,
-      JobDao jobDao,
       SpotSessionService spotSessionService,
       ReviewSessionService reviewSessionService,
-      ClusterConfiguration clusterConfiguration,
-      NotificationService notificationService,
       JobUserService jobUserService,
       HoneyPotService honeyPotService,
-      CaptchaService captchaService,
-      IndividualsService individualsService,
-      ResourceService resourceService) {
-    this.jobDao = jobDao;
+      ResourceService resourceService,
+      TaskVisitorProvider taskVisitorProvider,
+      JobService jobService) {
     this.batchService = batchService;
-    this.captchaService = captchaService;
-    this.honeyPotService = honeyPotService;
     this.taskService = taskService;
     this.taskSessionService = taskSessionService;
     this.reviewSessionService = reviewSessionService;
     this.spotSessionService = spotSessionService;
     this.userService = userService;
     this.jobUserService = jobUserService;
-    this.individualsService = individualsService;
-    this.clusterConfiguration = clusterConfiguration;
-    this.notificationService = notificationService;
     this.resourceService = resourceService;
+    this.taskVisitorProvider = taskVisitorProvider;
+    this.jobService = jobService;
   }
 
   @GetMapping("/{id}")
@@ -186,59 +161,20 @@ public class TaskSessionController {
   //    return pinRes.toApiResult();
   //  }
 
-  @PostMapping("/{id}/submit-answer")
-  @IsLabeler
-  public ApiResult<TaskSession> submitAnswer(
-      @PathVariable Long id, @Valid @RequestBody SubmitAnswerDTO answer) {
-    var hpAnswerSubmitted = answer.getSubmitHPAnswer();
-    var isTester = ControllerUtils.isTesterUser();
-    var session = taskSessionService
-        .getTaskSessionById(id)
-        .filter(v -> !v.getDeleted())
-        .orThrow(() -> ControllerUtils.notFound("task session not found"))
-        .filter(v -> TaskSession.TaskSessionStatus.PENDING.equals(v.getStatus())
-            || PINNED.equals(v.getStatus()))
-        .orThrow(() -> ControllerUtils.notFound("task session not pending or pinning"))
-        .filter(v -> v.getUserId().equals(ControllerUtils.currentUid()))
-        .orElseThrow(() -> ControllerUtils.forbidden(
-            "you are not allowed to submit answer for this task session"));
-    var rejectIdList = new ArrayList<Long>();
-    Boolean bannedInHp = false;
-    if (hpAnswerSubmitted != null) {
-      if (!hpAnswerSubmitted.getHoneyPotSession().getTaskSessionId().equals(session.getId())) {
-        throw ControllerUtils.forbidden("invalid data");
-      }
-      var jobUser = jobUserService
-          .getById(session.getJobUserId())
-          .asOpt()
-          .orElseThrow(() -> ControllerUtils.notFound("job user not found"));
-      var hpJudgeRes = honeyPotService.submitAnswerAndJudgeCorrection(
-          List.of(hpAnswerSubmitted), jobUser, isTester);
-      rejectIdList.addAll(hpJudgeRes.getLeft());
-      bannedInHp = hpJudgeRes.getRight();
-    }
-    if (bannedInHp) {
-      return session.toApiResult();
-    }
-    CompletableFuture<SimpleResult<TaskSession>> r = ActorUtils.askWithDefault(
-        this.clusterConfiguration.getJobSessionActor(),
-        new JobSessionActor.OpSubmitAnswer(
-            session,
-            answer.getAnswers(),
-            answer.getFeedback(),
-            DateUtils.now(),
-            rejectIdList,
-            isTester));
-    var resp = r.get().asApiResult();
-    if (answer.getFeedback() != null && !answer.getFeedback().isBlank()) {
-      notificationService.noticeNMLabelerJobFeedback(
-          ControllerUtils.currentUid(),
-          session.getJobId(),
-          session.getTaskId(),
-          answer.getFeedback());
-    }
-    return resp;
-  }
+  //  @PostMapping("/{id}/submit-answer")
+  //  @IsLabeler
+  //  public ApiResult<TaskSession> submitAnswer(
+  //      @PathVariable Long id, @Valid @RequestBody SubmitAnswerDTO answer) {
+  //
+  //    TaskSession taskSession = this.taskDispatcher.submitTasks(
+  //        answer,
+  //        id,
+  //        ControllerUtils.currentUid(),
+  //        ControllerUtils.isTesterUser(),
+  //        JobUser.JobUserRole.LABELER);
+  //
+  //    return ApiResult.success(taskSession);
+  //  }
 
   @PostMapping("/submit-answers/pipeline-result")
   @IsApiKeyClient(ApiKeys.Type.PIPELINE)
@@ -250,119 +186,41 @@ public class TaskSessionController {
   @PostMapping("/submit-answers")
   @IsLabeler
   public ApiResult<Boolean> submitAnswers(@Valid @RequestBody SubmitAnswersDTO answers) {
-    var curId = ControllerUtils.currentUid();
-    var isTester = ControllerUtils.isTesterUser();
-    HttpServletRequest request =
-        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
 
-    var answerList = answers.getSubmitAnswers();
+    if (CollectionUtils.isEmpty(answers.getSubmitAnswers())) {
+      throw ControllerUtils.badRequest("No answers submitted");
+    }
 
-    var jobId = answerList.first().getTaskSession().getJobId();
-    var jobUserOptional = jobUserService
-        .getJobUserByJobAndUserId(jobId, curId)
-        .orThrow(() -> ControllerUtils.notFound("task session not found"))
-        .filter(ju -> ju.getActive().equals(Boolean.TRUE));
-    if (jobUserOptional.isEmpty()) {
+    var userId = ControllerUtils.currentUid();
+    var isTester = ControllerUtils.isTesterUser();
+
+    var jobId = answers.getSubmitAnswers().get(0).getTaskSession().getJobId();
+    var jobUser = jobUserService
+        .getJobUserByJobAndUserId(jobId, userId)
+        .orElseThrow(() -> ControllerUtils.notFound("task session not found"));
+    if (!jobUser.getActive()) {
       if (!isTester) {
         throw ControllerUtils.badRequest(
             "You've been banned from this task because we've detected malicious activity.");
       } else {
-        jobUserService.unbanInJob(curId, jobId);
+        jobUserService.unbanInJob(userId, jobId);
       }
     }
-    if (!isTester) {
-      StringBuilder stringBuilder = new StringBuilder();
-      var submitTimeInHeader = request.getHeader("X-Submit-Time");
-      try (BufferedReader reader = request.getReader()) {
-        String line;
-        while ((line = reader.readLine()) != null) {
-          stringBuilder.append(line).append('\n');
-        }
-      } catch (IOException e) {
-        throw ControllerUtils.notFound("Job user not found");
-      }
-      String requestBody = stringBuilder.toString();
-      var reqBodyMap = ObjectUtils.fromJsonToMapBasic(requestBody);
-      if (reqBodyMap.isPresent()) {
-        var newDetailString = String.join(",", reqBodyMap.get().keySet());
-        this.clusterConfiguration
-            .getIndividualJobOtherProcessActor()
-            .tell(
-                new IndividualJobOtherProcessActor.OpSubmitAnswersCheckRisk(
-                    jobId,
-                    curId,
-                    newDetailString,
-                    submitTimeInHeader,
-                    answerList.last().getTaskSession().getCreatedAt()),
-                ActorRef.noSender());
-      }
-    }
-    var jobUser = jobUserOptional.get();
-    var firstSessionId = answerList.first().getTaskSession().getId();
+    Job job =
+        jobService.getJobById(jobId).orElseThrow(() -> ControllerUtils.notFound("Task not found."));
+    var batch = batchService
+        .getBatchById(job.getBatchId())
+        .orElseThrow(() -> ControllerUtils.notFound(BATCH_NOT_FOUND));
 
-    var ip = Web2AuthService.getClientIP(request);
-    if (!isTester
-        && captchaService.isRecaptchaRequired(
-            firstSessionId, jobUser.getId(), JobUser.JobUserRole.LABELER)) {
+    BatchSetting batchSetting = batchService.getBatchSettingByBatchId(batch.getId());
 
-      captchaService.verifyRecaptchaToken(answers.getCaptchaToken(), ip);
-    }
-    var hpAnswerList = answers.getSubmitHPAnswers();
-    if (individualsService.checkUserBannedInPlatform(curId)) {
-      if (isTester) {
-        jobUserService.removeUserBanningInPlatform(curId);
-      } else {
-        throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(curId));
-      }
-    }
+    TaskSubmitter<SubmitAnswersDTO, Boolean> taskSubmitter = this.taskVisitorProvider
+        .getTaskVisitor(BatchSetting.DistributeType.SINGLE, JobUser.JobUserRole.LABELER)
+        .taskSubmitter();
 
-    answerList.forEach(answer -> answer.setTaskSession(taskSessionService
-        .getTaskSessionById(answer.getTaskSession().getId())
-        .filter(v -> !v.getDeleted())
-        .orThrow(() -> ControllerUtils.notFound("task session not found"))
-        .filter(v -> TaskSession.TaskSessionStatus.PENDING.equals(v.getStatus())
-            || PINNED.equals(v.getStatus())
-            || (TaskSession.TaskSessionStatus.REJECTED.equals(v.getStatus())
-                && TaskSession.TaskSessionReviewStatus.REVISED.equals(v.getReviewerStatus())))
-        .orThrow(() -> ControllerUtils.notFound("task session not pending"))
-        .filter(v -> v.getUserId().equals(curId))
-        .orElseThrow(() -> ControllerUtils.forbidden(
-            "you are not allowed to submit answer for this task session"))));
-    var taskSessionIdList = answerList.map(a -> a.getTaskSession().getId()).toList();
-    var hpSessions = honeyPotService.getSessionByTaskSessionIdList(taskSessionIdList);
-    if (taskSessionIdList.size() < hpSessions.size()) {
-      throw ControllerUtils.badRequest("invalid data"); // hp session submitted are not enough.
-    }
-    var hpSessionsMap = hpSessions.toMap(HoneyPotSession::getTaskSessionId, Function.identity());
+    Boolean res = taskSubmitter.submitTasks(answers, job, batch, batchSetting, jobUser, isTester);
 
-    var notIncludeRec = hpAnswerList.stream()
-        .filter(ha -> !hpSessionsMap.containsKey(ha.getTaskSessionId()))
-        .findAny();
-    if (notIncludeRec.isPresent()) {
-      throw ControllerUtils.badRequest("invalid data"); // wrong hp session submitted.
-    }
-    var rejectIdList =
-        honeyPotService.submitAnswerAndJudgeCorrection(hpAnswerList, jobUser, isTester);
-    if (rejectIdList.getRight()) {
-      return Boolean.valueOf(false).toApiResult();
-    }
-    var unused = ActorUtils.askWithDefault(
-        this.clusterConfiguration.getIndividualJobSessionActor(),
-        new IndividualJobSessionActor.OpSubmitAnswers(
-            answerList, DateUtils.now(), rejectIdList.getLeft(), isTester, ip, curId));
-    answerList.stream()
-        .filter(a -> !a.getSubmitAnswer().getFeedback().isBlank())
-        .findFirst()
-        .ifPresent(firstFeedBack -> {
-          var sessionJob = jobDao.getJobById(firstFeedBack.getTaskSession().getJobId());
-          notificationService.noticeAMLabelerJobFeedback(
-              curId,
-              sessionJob.get().getOwnerId(),
-              firstFeedBack.getTaskSession().getJobId(),
-              firstFeedBack.getTaskSession().getTaskId(),
-              firstFeedBack.getSubmitAnswer().getFeedback());
-        });
-    return Boolean.valueOf(true).toApiResult();
+    return ApiResult.success(res);
   }
 
   @PostMapping("/{id}/submit-am-review")
@@ -436,13 +294,14 @@ public class TaskSessionController {
         throw ControllerUtils.badRequest("you have no skip quota, please do more review");
       }
     }
-    var skippingPinnedSession = session.getStatus().equals(PINNED);
-    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER) && !skippingPinnedSession) {
-      taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(session);
-    }
-    if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
-      reviewSessionService.generateNewReviewSessionIfNecessaryCauseOfHoneyPot(session, curId);
-    }
+    //    var skippingPinnedSession = session.getStatus().equals(PINNED);
+    //    if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER) && !skippingPinnedSession) {
+    //      taskSessionService.generateNewTaskSessionIfNecessaryCauseOfHoneyPot(session, jobUser);
+    //    }
+    //    if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
+    //      reviewSessionService.generateNewReviewSessionIfNecessaryCauseOfHoneyPot(session, curId,
+    // jobUser);
+    //    }
     taskSessionService.skipTaskSession(session, curId);
     return ApiResult.success(true);
   }
diff --git a/src/main/java/ai/saharaa/daos/BatchDao.java b/src/main/java/ai/saharaa/daos/BatchDao.java
index a48c9246..25a213af 100644
--- a/src/main/java/ai/saharaa/daos/BatchDao.java
+++ b/src/main/java/ai/saharaa/daos/BatchDao.java
@@ -197,7 +197,7 @@ public class BatchDao {
       if (status == BatchStatus.DRAFT) {
         queryStatus = Batch.beforeLaunchBatchStatusList;
       } else {
-        queryStatus = Arrays.asList(status);
+        queryStatus = List.of(status);
       }
     }
 
@@ -275,7 +275,6 @@ public class BatchDao {
 
       if (Objects.equals(category, CertificateCategory.COURSE)) {
         userStatus.add(UserRequirementsStatus.UserRequireStatus.APPROVED);
-        ;
       } else if (Objects.equals(category, CertificateCategory.VETTING_TEST)) {
         userStatus.add(UserRequirementsStatus.UserRequireStatus.REJECTED);
         userStatus.add(UserRequirementsStatus.UserRequireStatus.APPROVED);
@@ -407,6 +406,10 @@ public class BatchDao {
     return getBatchSettingById(batchSetting.getBatchId());
   }
 
+  public void saveBatchSetting(BatchSetting batchSetting) {
+    this.batchSettingMapper.insert(batchSetting);
+  }
+
   public BatchSetting getBatchSettingById(Long batchId) {
     var batchSetting = this.batchSettingMapper
         .selectOne(new QueryWrapper<BatchSetting>()
diff --git a/src/main/java/ai/saharaa/daos/JobTaskDao.java b/src/main/java/ai/saharaa/daos/JobTaskDao.java
index 730a5755..7cf81449 100644
--- a/src/main/java/ai/saharaa/daos/JobTaskDao.java
+++ b/src/main/java/ai/saharaa/daos/JobTaskDao.java
@@ -3,7 +3,9 @@ package ai.saharaa.daos;
 import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
 
 import ai.saharaa.common.cache.IGlobalCache;
+import ai.saharaa.dto.job.GroupByTaskIdAndCountResultDTO;
 import ai.saharaa.mappers.JobTaskMapper;
+import ai.saharaa.mappers.sessions.TaskSessionDisCountMapper;
 import ai.saharaa.model.JobTask;
 import ai.saharaa.utils.DateUtils;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
@@ -17,15 +19,20 @@ import org.springframework.util.CollectionUtils;
 public class JobTaskDao extends ServiceImpl<JobTaskMapper, JobTask> {
 
   private final JobTaskMapper jobTaskMapper;
+  private final TaskSessionDisCountMapper taskSessionDisCountMapper;
+
   private final IGlobalCache cache;
+  private final Integer JOB_HAS_AVALIABLE_TASK_RES_TTL = 15;
 
-  public JobTaskDao(JobTaskMapper jobTaskMapper, IGlobalCache cache) {
+  public JobTaskDao(
+      JobTaskMapper jobTaskMapper,
+      IGlobalCache cache,
+      TaskSessionDisCountMapper taskSessionDisCountMapper) {
     this.jobTaskMapper = jobTaskMapper;
+    this.taskSessionDisCountMapper = taskSessionDisCountMapper;
     this.cache = cache;
   }
 
-  private final Integer JOB_HAS_AVALIABLE_TASK_RES_TTL = 15;
-
   private String getJobHasPendingTaskCacheKey(Long jobId) {
     return "JOB_HAS_AVALIABLE_TASK:${String.valueOf(jobId)}";
   }
@@ -56,6 +63,7 @@ public class JobTaskDao extends ServiceImpl<JobTaskMapper, JobTask> {
             .lambda()
             .select(JobTask::getTaskId)
             .eq(JobTask::getJobId, jobId)
+            .eq(JobTask::getDeleted, false)
             .orderByAsc(JobTask::getTaskId)
             .last(" limit ${limit}"))
         .map(JobTask::getTaskId)
@@ -97,6 +105,14 @@ public class JobTaskDao extends ServiceImpl<JobTaskMapper, JobTask> {
     return this.jobTaskMapper.takeJobForJobUserAvoidSkipped(jobId, userId, limit, count);
   }
 
+  public List<Long> refreshTaskQueue(Long jobId, Integer repeatCount, Integer limit) {
+    return this.jobTaskMapper.refreshTaskQueue(jobId, repeatCount, limit);
+  }
+
+  public List<GroupByTaskIdAndCountResultDTO> refreshTaskDistributionStatus(Long jobId) {
+    return this.taskSessionDisCountMapper.countTaskIdGroupByCount(jobId);
+  }
+
   public Long countMyLeftTaskCount(Long jobId, Long userId, Integer repeatCount) {
     return jobTaskMapper.countMyAvailableLeftTask(jobId, userId, repeatCount);
   }
@@ -116,4 +132,11 @@ public class JobTaskDao extends ServiceImpl<JobTaskMapper, JobTask> {
         .eq(JobTask::getDeleted, false)
         .last("limit 1"));
   }
+
+  public List<JobTask> getTaskByJobId(Long id) {
+    return this.jobTaskMapper.selectList(new QueryWrapper<JobTask>()
+        .lambda()
+        .eq(JobTask::getDeleted, false)
+        .eq(JobTask::getJobId, id));
+  }
 }
diff --git a/src/main/java/ai/saharaa/daos/ResourceDao.java b/src/main/java/ai/saharaa/daos/ResourceDao.java
index 0f25f345..d2a36b6e 100644
--- a/src/main/java/ai/saharaa/daos/ResourceDao.java
+++ b/src/main/java/ai/saharaa/daos/ResourceDao.java
@@ -66,4 +66,11 @@ public class ResourceDao {
     newRes.setId(null);
     return createResource(newRes);
   }
+
+  public List<Resource> getResourceByIds(List<Long> ids) {
+    return this.resourceMapper.selectList(new QueryWrapper<Resource>()
+        .lambda()
+        .in(Resource::getId, ids)
+        .eq(Resource::getDeleted, false));
+  }
 }
diff --git a/src/main/java/ai/saharaa/daos/ReviewSessionDao.java b/src/main/java/ai/saharaa/daos/ReviewSessionDao.java
index 490ac8dd..5114ad03 100644
--- a/src/main/java/ai/saharaa/daos/ReviewSessionDao.java
+++ b/src/main/java/ai/saharaa/daos/ReviewSessionDao.java
@@ -1,8 +1,11 @@
 package ai.saharaa.daos;
 
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.COMPLETE_COUNT;
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.CORRECT_COUNT;
 import static ai.saharaa.model.ReviewSession.ReviewSessionStatus.*;
 import static ai.saharaa.model.season.JobUserPointsPreCalc.JobUserPointsPreCalcRecordStatus.NOT_SETTLED;
 
+import ai.saharaa.distribution.CommonCounterManager;
 import ai.saharaa.mappers.ReviewSessionMapper;
 import ai.saharaa.mappers.season.JobUserPointsPreCalcMapper;
 import ai.saharaa.mappers.sessions.TaskSessionReviewCountMapper;
@@ -23,12 +26,15 @@ public class ReviewSessionDao {
 
   private final ReviewSessionMapper reviewSessionMapper;
   private final JobUserPointsPreCalcMapper jobUserPointsPreCalcMapper;
+  private final CommonCounterManager counterManager;
   private final TaskSessionReviewCountMapper taskSessionReviewCountMapper;
 
   public ReviewSessionDao(
       ReviewSessionMapper reviewSessionMapper,
+      CommonCounterManager counterManager,
       TaskSessionReviewCountMapper taskSessionReviewCountMapper,
       JobUserPointsPreCalcMapper jobUserPointsPreCalcMapper) {
+    this.counterManager = counterManager;
     this.reviewSessionMapper = reviewSessionMapper;
     this.taskSessionReviewCountMapper = taskSessionReviewCountMapper;
     this.jobUserPointsPreCalcMapper = jobUserPointsPreCalcMapper;
@@ -177,7 +183,11 @@ public class ReviewSessionDao {
       reviewSession.setUpdatedAt(dateNow);
       reviewSession.setFinalJudgeAt(dateNow);
       reviewSessionMapper.updateById(reviewSession);
+      counterManager.setJobUserCountIncrement(
+          reviewSession.getJobId(), reviewSession.getUserId(), COMPLETE_COUNT.value, 1L);
       if (!reviewSession.getWrongReview()) {
+        counterManager.setJobUserCountIncrement(
+            reviewSession.getJobId(), reviewSession.getUserId(), CORRECT_COUNT.value, 1L);
         recordJobUserPointPreCalc(reviewSession, dateNow);
       }
     });
@@ -486,4 +496,14 @@ public class ReviewSessionDao {
       taskSessionReviewCountMapper.insert(newRecs);
     }
   }
+
+  public List<ReviewSession> getWorkingReviewSessionByJobIdAndUserId(Long jobId, Long userId) {
+    return this.reviewSessionMapper.selectList(new QueryWrapper<ReviewSession>()
+        .lambda()
+        .select(ReviewSession::getTaskSessionId)
+        .eq(ReviewSession::getJobId, jobId)
+        .eq(ReviewSession::getUserId, userId)
+        .in(ReviewSession::getStatus, List.of(PENDING, FINISH))
+        .eq(ReviewSession::getDeleted, false));
+  }
 }
diff --git a/src/main/java/ai/saharaa/daos/SkipTaskSessionDao.java b/src/main/java/ai/saharaa/daos/SkipTaskSessionDao.java
index 2725680f..2412550c 100644
--- a/src/main/java/ai/saharaa/daos/SkipTaskSessionDao.java
+++ b/src/main/java/ai/saharaa/daos/SkipTaskSessionDao.java
@@ -1,7 +1,7 @@
 package ai.saharaa.daos;
 
 import ai.saharaa.mappers.SkipTaskSessionMapper;
-import ai.saharaa.model.*;
+import ai.saharaa.model.SkipTaskSession;
 import ai.saharaa.utils.DateUtils;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
@@ -37,15 +37,13 @@ public class SkipTaskSessionDao {
             .set(SkipTaskSession::getUpdatedAt, DateUtils.now()));
   }
 
-  public List<Long> getUserSkippedTaskSessionIdListInJob(Long userId, Long jobId) {
-    return skipTaskSessionMapper
-        .selectList(new QueryWrapper<SkipTaskSession>()
-            .lambda()
-            .eq(SkipTaskSession::getJobId, jobId)
-            .eq(SkipTaskSession::getUserId, userId)
-            .eq(SkipTaskSession::getDeleted, false))
-        .map(SkipTaskSession::getTaskSessionId)
-        .toList();
+  public List<SkipTaskSession> getUserSkippedTaskSessionIdListInJob(Long jobId, Long userId) {
+    return skipTaskSessionMapper.selectList(new QueryWrapper<SkipTaskSession>()
+        .lambda()
+        .select(SkipTaskSession::getTaskSessionId, SkipTaskSession::getTaskId)
+        .eq(SkipTaskSession::getJobId, jobId)
+        .eq(SkipTaskSession::getUserId, userId)
+        .eq(SkipTaskSession::getDeleted, false));
   }
 
   public Long getSkippedCount(Long jobId, Long userId) {
@@ -64,7 +62,7 @@ public class SkipTaskSessionDao {
             .eq(SkipTaskSession::getJobId, jobId)
             .eq(SkipTaskSession::getUserId, userId)
             .eq(SkipTaskSession::getDeleted, false))
-        .map(SkipTaskSession::getTaskSessionId)
+        .map(SkipTaskSession::getTaskId)
         .toList();
   }
 }
diff --git a/src/main/java/ai/saharaa/daos/TaskDao.java b/src/main/java/ai/saharaa/daos/TaskDao.java
index f3c2b252..54e5b53f 100644
--- a/src/main/java/ai/saharaa/daos/TaskDao.java
+++ b/src/main/java/ai/saharaa/daos/TaskDao.java
@@ -11,6 +11,7 @@ import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import com.github.yulichang.toolkit.JoinWrappers;
+import java.util.Collection;
 import java.util.List;
 import java.util.Objects;
 import java.util.Optional;
@@ -61,6 +62,10 @@ public class TaskDao extends ServiceImpl<TaskMapper, Task> {
         .asOpt();
   }
 
+  public List<Task> getTaskByIds(Collection<Long> ids) {
+    return taskMapper.selectByIds(ids);
+  }
+
   public List<Task> getExamTaskByTaskListId(Long taskListId) {
     return this.taskMapper.selectList(new QueryWrapper<Task>()
         .lambda()
diff --git a/src/main/java/ai/saharaa/daos/TaskSessionDao.java b/src/main/java/ai/saharaa/daos/TaskSessionDao.java
index 8e7d237b..b090caab 100644
--- a/src/main/java/ai/saharaa/daos/TaskSessionDao.java
+++ b/src/main/java/ai/saharaa/daos/TaskSessionDao.java
@@ -1,8 +1,7 @@
 package ai.saharaa.daos;
 
-import ai.saharaa.actors.jobs.IndividualJobSessionActor;
 import ai.saharaa.common.cache.IGlobalCache;
-import ai.saharaa.config.cluster.ClusterConfiguration;
+import ai.saharaa.dto.job.GroupByTaskSessionIdAndCountResultDTO;
 import ai.saharaa.dto.task.TaskSessionGroupedTaskIdDTO;
 import ai.saharaa.enums.TaskSessionAmReviewStatus;
 import ai.saharaa.mappers.TaskSessionMapper;
@@ -10,7 +9,6 @@ import ai.saharaa.mappers.sessions.TaskSessionDisCountMapper;
 import ai.saharaa.model.*;
 import ai.saharaa.model.TaskSession.TaskSessionStatus;
 import ai.saharaa.utils.DateUtils;
-import akka.actor.ActorRef;
 import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
@@ -29,23 +27,19 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
   private final SkipTaskSessionDao skipTaskSessionDao;
   private final TaskSessionDisCountMapper taskSessionDisCountMapper;
   private final IGlobalCache cache;
-  private final ClusterConfiguration clusterConfiguration;
+  private final Integer JOB_HAS_AVALIABLE_REVIEW_TASK_RES_TTL = 15;
 
   public TaskSessionDao(
       TaskSessionMapper taskSessionMapper,
       SkipTaskSessionDao skipTaskSessionDao,
       TaskSessionDisCountMapper taskSessionDisCountMapper,
-      IGlobalCache cache,
-      ClusterConfiguration clusterConfiguration) {
+      IGlobalCache cache) {
     this.taskSessionMapper = taskSessionMapper;
     this.skipTaskSessionDao = skipTaskSessionDao;
-    this.clusterConfiguration = clusterConfiguration;
     this.taskSessionDisCountMapper = taskSessionDisCountMapper;
     this.cache = cache;
   }
 
-  private final Integer JOB_HAS_AVALIABLE_REVIEW_TASK_RES_TTL = 15;
-
   private String getJobHasPendingReviewTaskCacheKey(Long jobId) {
     return "JOB_HAS_AVALIABLE_REVIEW_TASK:${String.valueOf(jobId)}";
   }
@@ -288,6 +282,21 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
         .eq(TaskSession::getStatus, status));
   }
 
+  public List<GroupByTaskSessionIdAndCountResultDTO> refreshTaskQueueForReview(
+      Long jobId, String pendingIds) {
+    return taskSessionMapper.refreshTaskQueueForReview(jobId, pendingIds);
+  }
+
+  public List<TaskSession> getTaskIdsByJobIdAndStatus(
+      Long jobId, TaskSession.TaskSessionStatus status) {
+    return taskSessionMapper.selectList(new QueryWrapper<TaskSession>()
+        .lambda()
+        .select(TaskSession::getId)
+        .eq(TaskSession::getJobId, jobId)
+        .eq(TaskSession::getDeleted, false)
+        .eq(TaskSession::getStatus, status));
+  }
+
   public Long countHumanReviewSessionsByJobId(Long jobId) {
     return taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
         .lambda()
@@ -493,7 +502,7 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
   }
 
   public void looseUserPendingSessionInJob(Long jobId, Long userId) {
-    var res = taskSessionMapper
+    var unused = taskSessionMapper
         .selectList(new QueryWrapper<TaskSession>()
             .lambda()
             .select(TaskSession::getTaskId, TaskSession::getStatus, TaskSession::getDeleted)
@@ -526,11 +535,12 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
                     TaskSession.TaskSessionStatus.REJECTED,
                     TaskSession.TaskSessionStatus.PINNED))
             .set(TaskSession::getDeleted, true));
-    clusterConfiguration
-        .getIndividualJobSessionActor()
-        .tell(
-            new IndividualJobSessionActor.UpdateTaskSessionDisCount(jobId, res),
-            ActorRef.noSender());
+    // todo: DisCount remove temp
+    //    clusterConfiguration
+    //        .getIndividualJobSessionActor()
+    //        .tell(
+    //            new IndividualJobSessionActor.UpdateTaskSessionDisCount(jobId, res),
+    //            ActorRef.noSender());
   }
 
   private LambdaQueryWrapper<TaskSession> countUserPointsQuery(
@@ -619,6 +629,15 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
         .eq(TaskSession::getReviewerStatus, TaskSession.TaskSessionReviewStatus.REVISED));
   }
 
+  public List<TaskSession> getTaskIdsByJobUserIdAndStatus(
+      Long jobUserId, TaskSessionStatus status) {
+    return taskSessionMapper.selectList(new QueryWrapper<TaskSession>()
+        .lambda()
+        .eq(TaskSession::getJobUserId, jobUserId)
+        .eq(TaskSession::getStatus, status)
+        .eq(TaskSession::getDeleted, false));
+  }
+
   public List<TaskSession> getAllTaskSessionByJobId(Long id) {
     return taskSessionMapper.selectList(
         new QueryWrapper<TaskSession>().lambda().eq(TaskSession::getJobId, id));
@@ -660,6 +679,10 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
     return taskSessionMapper.takeReviewJobsForUser(jobId, userId, count, repeatCount);
   }
 
+  public List<Long> refreshTaskQueue(Long jobId, Integer repeatCount, Integer limit) {
+    return taskSessionMapper.refreshTaskQueue(jobId, repeatCount, limit);
+  }
+
   public Boolean existsJustSubmittedTasksByJobUserId(Long jobUserId) {
     return taskSessionMapper.exists(new QueryWrapper<TaskSession>()
         .lambda()
@@ -787,4 +810,15 @@ public class TaskSessionDao extends ServiceImpl<TaskSessionMapper, TaskSession>
       }
     }
   }
+
+  public List<Long> getMyEffectTaskSessionTaskIds(Long jobId, Long userId) {
+    return taskSessionMapper
+        .selectList(new QueryWrapper<TaskSession>()
+            .lambda()
+            .select(TaskSession::getTaskId)
+            .eq(TaskSession::getJobId, jobId)
+            .eq(TaskSession::getUserId, userId)
+            .eq(TaskSession::getDeleted, false))
+        .mapToList(TaskSession::getTaskId);
+  }
 }
diff --git a/src/main/java/ai/saharaa/distribution/AbstractTaskLifecycleManager.java b/src/main/java/ai/saharaa/distribution/AbstractTaskLifecycleManager.java
new file mode 100644
index 00000000..82c2ad4e
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/AbstractTaskLifecycleManager.java
@@ -0,0 +1,359 @@
+package ai.saharaa.distribution;
+
+import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
+import static java.util.stream.Collectors.toMap;
+
+import ai.saharaa.actors.jobs.IndividualJobOtherProcessActor;
+import ai.saharaa.config.LabelingMetricsController;
+import ai.saharaa.config.cluster.ClusterConfiguration;
+import ai.saharaa.daos.ResourceDao;
+import ai.saharaa.daos.TaskAndReviewSessionDao;
+import ai.saharaa.daos.TaskQuestionDao;
+import ai.saharaa.daos.TaskSessionDao;
+import ai.saharaa.dto.job.SubmitAnswersDTO;
+import ai.saharaa.dto.job.SubmitAnswersItemDTO;
+import ai.saharaa.model.*;
+import ai.saharaa.model.HoneyPot.HoneyPotSession;
+import ai.saharaa.services.*;
+import ai.saharaa.utils.ControllerUtils;
+import ai.saharaa.utils.ObjectUtils;
+import akka.actor.ActorRef;
+import jakarta.servlet.http.HttpServletRequest;
+import java.io.BufferedReader;
+import java.io.IOException;
+import java.util.List;
+import java.util.Objects;
+import java.util.concurrent.CompletableFuture;
+import java.util.function.Function;
+import org.apache.commons.lang3.tuple.Pair;
+import org.springframework.web.context.request.RequestContextHolder;
+import org.springframework.web.context.request.ServletRequestAttributes;
+
+public abstract class AbstractTaskLifecycleManager
+    implements TaskSubmitter<SubmitAnswersDTO, Boolean> {
+
+  private final ClusterConfiguration clusterConfiguration;
+  private final CaptchaService captchaService;
+  private final IndividualsService individualsService;
+  private final JobUserService jobUserService;
+  private final HoneyPotService honeyPotService;
+  private final TaskSessionService taskSessionService;
+  private final LabelingMetricsController labelingMetricsController;
+
+  @SuppressWarnings("unused")
+  private final TaskSessionDao taskSessionDao;
+
+  private final TaskAndReviewSessionDao taskAndReviewSessionDao;
+
+  private final WorkloadLimitService workloadService;
+
+  private final TaskQuestionDao taskQuestionDao;
+  private final ResourceDao resourceDao;
+  private final NotificationService notificationService;
+
+  protected AbstractTaskLifecycleManager(
+      ClusterConfiguration clusterConfiguration,
+      CaptchaService captchaService,
+      IndividualsService individualsService,
+      JobUserService jobUserService,
+      HoneyPotService honeyPotService,
+      TaskSessionService taskSessionService,
+      LabelingMetricsController labelingMetricsController,
+      TaskSessionDao taskSessionDao,
+      TaskAndReviewSessionDao taskAndReviewSessionDao,
+      WorkloadLimitService workloadService,
+      TaskQuestionDao taskQuestionDao,
+      ResourceDao resourceDao,
+      NotificationService notificationService) {
+    this.clusterConfiguration = clusterConfiguration;
+    this.captchaService = captchaService;
+    this.individualsService = individualsService;
+    this.jobUserService = jobUserService;
+    this.honeyPotService = honeyPotService;
+    this.taskSessionService = taskSessionService;
+    this.labelingMetricsController = labelingMetricsController;
+    this.taskSessionDao = taskSessionDao;
+    this.taskAndReviewSessionDao = taskAndReviewSessionDao;
+    this.workloadService = workloadService;
+    this.taskQuestionDao = taskQuestionDao;
+    this.resourceDao = resourceDao;
+    this.notificationService = notificationService;
+  }
+
+  //  @Override
+  //  public TaskSession submitTasks(
+  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
+  //
+  //    TaskSession taskSession =
+  //        this.taskSessionService
+  //            .getTaskSessionById(taskSessionId)
+  //            .orElseThrow(() -> ControllerUtils.badRequest("task session not found"));
+  //
+  //    this.checkTaskSession(taskSession, userId);
+  //
+  //    var hpAnswerSubmitted = answer.getSubmitHPAnswer();
+  //    var rejectIdList = new ArrayList<Long>();
+  //    Boolean bannedInHp = false;
+  //    if (hpAnswerSubmitted != null) {
+  //      if
+  // (!hpAnswerSubmitted.getHoneyPotSession().getTaskSessionId().equals(taskSession.getId())) {
+  //        throw ControllerUtils.forbidden("invalid data");
+  //      }
+  //      var jobUser =
+  //          jobUserService
+  //              .getById(taskSession.getJobUserId())
+  //              .asOpt()
+  //              .orElseThrow(() -> ControllerUtils.notFound("job user not found"));
+  //      var hpJudgeRes =
+  //          honeyPotService.submitAnswerAndJudgeCorrection(
+  //              List.of(hpAnswerSubmitted), jobUser, isTester);
+  //      rejectIdList.addAll(hpJudgeRes.getLeft());
+  //      bannedInHp = hpJudgeRes.getRight();
+  //    }
+  //    if (bannedInHp) {
+  //      return taskSession;
+  //    }
+  //
+  //    taskSession = this.handleSubmitAnswer(answer, taskSession, rejectIdList, isTester);
+  //
+  //    if (answer.getFeedback() != null && !answer.getFeedback().isBlank()) {
+  //      notificationService.noticeNMLabelerJobFeedback(
+  //          ControllerUtils.currentUid(),
+  //          taskSession.getJobId(),
+  //          taskSession.getTaskId(),
+  //          answer.getFeedback());
+  //    }
+  //
+  //    return taskSession;
+  //  }
+
+  @Override
+  public Boolean submitTasks(
+      SubmitAnswersDTO answers,
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      Boolean isTester) {
+
+    HttpServletRequest request =
+        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
+    var ip = Web2AuthService.getClientIP(request);
+
+    this.checkFrontendHoneyPot(answers, jobUser.getUserId(), job.getId(), isTester);
+    this.checkCaptcha(answers, isTester, jobUser, ip);
+    this.checkBanned(jobUser, isTester);
+
+    Pair<List<Long>, Boolean> rejectIdList = this.checkHoneypot(answers, jobUser, isTester);
+    if (rejectIdList.getRight()) {
+      return Boolean.valueOf(false);
+    }
+
+    List<TaskSession> taskSessions = taskSessionService.getTaskSessionByIdList(
+        answers.getSubmitAnswers().stream().map(a -> a.getTaskSession().getId()).toList());
+
+    taskSessions.forEach(ts -> this.checkTaskSession(ts, jobUser.getUserId()));
+    this.submitAnswers(answers, rejectIdList, batch, job, jobUser, ip, isTester);
+    this.updateResourcesStatus(answers.getSubmitAnswers());
+
+    answers.getSubmitAnswers().stream()
+        .filter(a -> !a.getSubmitAnswer().getFeedback().isBlank())
+        .findFirst()
+        .ifPresent(firstFeedBack -> {
+          notificationService.noticeAMLabelerJobFeedback(
+              jobUser.getUserId(),
+              job.getOwnerId(),
+              firstFeedBack.getTaskSession().getJobId(),
+              firstFeedBack.getTaskSession().getTaskId(),
+              firstFeedBack.getSubmitAnswer().getFeedback());
+        });
+    return Boolean.TRUE;
+  }
+
+  private List<TaskSession> submitAnswers(
+      SubmitAnswersDTO answers,
+      Pair<List<Long>, Boolean> rejectIdList,
+      Batch batch,
+      Job job,
+      JobUser jobUser,
+      String ip,
+      Boolean isTester) {
+    workloadService.filterAnswersByWorkloadLimit(
+        answers.getSubmitAnswers(), job.getId(), jobUser.getUserId(), ip);
+    labelingMetricsController.handleAnnotate(answers.getSubmitAnswers().size());
+
+    List<TaskSession> result = taskSessionService.submitAnswers(
+        answers.getSubmitAnswers(), batch, rejectIdList.getLeft(), isTester);
+
+    //    if (CollectionUtils.isNotEmpty(rejectIdList.getLeft())) { // todo: DisCount remove temp
+    //      var rejectedTs = this.taskSessionService.getTaskSessionByIdList(rejectIdList.getLeft());
+    //      if (!rejectedTs.isEmpty()) {
+    //        taskSessionDao.updateTaskSessionDisCount(
+    //            rejectedTs.stream().map(TaskSession::getTaskId).collect(toSet()), job.getId());
+    //      }
+    //    }
+    if (!batch.getReviewerRequired()) {
+      taskAndReviewSessionDao.closeNoReviewRequiredJobIfNeeded(batch, job);
+    }
+    workloadService.updateJobUserWorkloads(
+        job.getId(),
+        jobUser.getUserId(),
+        answers.getSubmitAnswers().stream()
+            .filter(s -> !TaskSession.TaskSessionReviewStatus.REVISED.equals(
+                s.getTaskSession().getSpotterStatus()))
+            .count(),
+        LABELER,
+        ip);
+    return result;
+  }
+
+  private void updateResourcesStatus(List<SubmitAnswersItemDTO> submitAnswers) {
+    // handle if question type is image-upload, will set cloud storage deleted to false
+    CompletableFuture.allOf(submitAnswers.stream()
+            .map(submitAnswer -> CompletableFuture.runAsync(() -> {
+              submitAnswer.getSubmitAnswer().getAnswers().forEach(answerDetailsDTO -> {
+                var questionId = answerDetailsDTO.getQuestionId();
+                TaskQuestion taskQuestion =
+                    taskQuestionDao.getTaskQuestionById(questionId).orElse(null);
+                if (taskQuestion != null
+                    && TaskQuestion.QuestionType.IMAGE_UPLOAD.equals(
+                        taskQuestion.getQuestionType())) {
+                  resourceDao.unDeleteTaskQuestionByResourceId(
+                      Long.parseLong(answerDetailsDTO.getAnswer().replaceAll("\"", "")));
+                  answerDetailsDTO.setAnswerImageUploaded(true);
+                }
+              });
+            }))
+            .toArray(CompletableFuture[]::new))
+        .join();
+  }
+
+  private void checkTaskSession(TaskSession taskSession, Long userId) {
+    if (taskSession.getDeleted()) {
+      throw ControllerUtils.badRequest("task session not found");
+    }
+    if (!Objects.equals(taskSession.getStatus(), TaskSession.TaskSessionStatus.PENDING)) {
+      throw ControllerUtils.badRequest("task session not pending");
+    }
+
+    if (!Objects.equals(taskSession.getUserId(), userId)) {
+      throw ControllerUtils.badRequest(
+          "you are not allowed to submit answer for this task session");
+    }
+  }
+
+  private Pair<List<Long>, Boolean> checkHoneypot(
+      SubmitAnswersDTO answers, JobUser jobUser, Boolean isTester) {
+    var answerList = answers.getSubmitAnswers();
+    var taskSessionIdList =
+        answerList.stream().map(a -> a.getTaskSession().getId()).toList();
+    var hpSessions = honeyPotService.getSessionByTaskSessionIdList(taskSessionIdList);
+    if (taskSessionIdList.size() < hpSessions.size()) {
+      throw ControllerUtils.badRequest("invalid data"); // hp session submitted are not enough.
+    }
+    var hpSessionsMap =
+        hpSessions.stream().collect(toMap(HoneyPotSession::getTaskSessionId, Function.identity()));
+
+    var hpAnswerList = answers.getSubmitHPAnswers();
+    var notIncludeRec = hpAnswerList.stream()
+        .filter(ha -> !hpSessionsMap.containsKey(ha.getTaskSessionId()))
+        .findAny();
+    if (notIncludeRec.isPresent()) {
+      throw ControllerUtils.badRequest("invalid data"); // wrong hp session submitted.
+    }
+    var rejectIdList =
+        honeyPotService.submitAnswerAndJudgeCorrection(hpAnswerList, jobUser, isTester);
+    return rejectIdList;
+  }
+
+  private void checkBanned(JobUser jobUser, Boolean isTester) {
+    if (individualsService.checkUserBannedInPlatform(jobUser.getUserId())) {
+      if (isTester) {
+        jobUserService.removeUserBanningInPlatform(jobUser.getUserId());
+      } else {
+        throw ControllerUtils.badRequest(individualsService.getUserBannedCopy(jobUser.getUserId()));
+      }
+    }
+  }
+
+  private void checkFrontendHoneyPot(
+      SubmitAnswersDTO answers, Long userId, Long jobId, Boolean isTester) {
+    if (isTester) {
+      return;
+    }
+
+    HttpServletRequest request =
+        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
+
+    var submitTimeInHeader = request.getHeader("X-Submit-Time");
+    StringBuilder stringBuilder = new StringBuilder();
+    try (BufferedReader reader = request.getReader()) {
+      String line;
+      while ((line = reader.readLine()) != null) {
+        stringBuilder.append(line).append('\n');
+      }
+    } catch (IOException e) {
+      throw ControllerUtils.notFound("Job user not found");
+    }
+
+    String requestBody = stringBuilder.toString();
+    var reqBodyMap = ObjectUtils.fromJsonToMapBasic(requestBody);
+    if (reqBodyMap.isPresent()) {
+      var newDetailString = String.join(",", reqBodyMap.get().keySet());
+      this.clusterConfiguration
+          .getIndividualJobOtherProcessActor()
+          .tell(
+              new IndividualJobOtherProcessActor.OpSubmitAnswersCheckRisk(
+                  jobId,
+                  userId,
+                  newDetailString,
+                  submitTimeInHeader,
+                  answers
+                      .getSubmitAnswers()
+                      .get(answers.getSubmitAnswers().size() - 1)
+                      .getTaskSession()
+                      .getCreatedAt()),
+              ActorRef.noSender());
+    }
+  }
+
+  private void checkCaptcha(
+      SubmitAnswersDTO answers, Boolean isTester, JobUser jobUser, String ip) {
+    var firstSessionId =
+        answers.getSubmitAnswers().stream().findFirst().get().getTaskSession().getId();
+
+    if (!isTester && captchaService.isRecaptchaRequired(firstSessionId, jobUser.getId(), LABELER)) {
+
+      captchaService.verifyRecaptchaToken(answers.getCaptchaToken(), ip);
+    }
+  }
+
+  //  private TaskSession handleSubmitAnswer(
+  //      SubmitAnswerDTO answer, TaskSession taskSession, List<Long> rejectIdList, Boolean
+  // isTester) {
+  //
+  //    var j = jobService.getJobById(taskSession.getJobId()).get();
+  //    var b = batchService.getBatchById(j.getBatchId()).get();
+  //    labelingMetricsController.handleAnnotate(1);
+  //    var submit = SubmitAnswersItemDTO.builder()
+  //        .taskSession(taskSession)
+  //        .submitAnswer(SubmitAnswerDTO.builder()
+  //            .answers(answer.getAnswers())
+  //            .feedback(answer.getFeedback())
+  //            .build())
+  //        .time(DateUtils.diff(DateUtils.now(), taskSession.getCreatedAt()))
+  //        .build();
+  //    if (b.getReviewerRequired()) {
+  //      return taskSessionService
+  //          .submitAnswers(List.of(submit), b.getProjectId(), rejectIdList, isTester)
+  //          .first();
+  //    } else {
+  //
+  //      var session = taskSessionService
+  //          .submitNoReviewAnswers(List.of(submit), b.getProjectId(), rejectIdList, isTester)
+  //          .first();
+  //      taskAndReviewSessionDao.closeNoReviewRequiredJobIfNeeded(b, j);
+  //      return session;
+  //    }
+  //  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/CommonCounterManager.java b/src/main/java/ai/saharaa/distribution/CommonCounterManager.java
new file mode 100644
index 00000000..e21a7dba
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/CommonCounterManager.java
@@ -0,0 +1,133 @@
+package ai.saharaa.distribution;
+
+import java.time.Duration;
+import java.util.Map;
+import java.util.Objects;
+import org.springframework.beans.factory.annotation.Qualifier;
+import org.springframework.data.redis.core.RedisTemplate;
+import org.springframework.stereotype.Component;
+
+@Component
+public class CommonCounterManager implements TaskCounter {
+
+  public final String KEY_TOP = "job_%d";
+  public final String KEY_FOR_JOB = "job_%d:counterForSingle:%s";
+  public final String KEY_FOR_JOB_USER = "job_%d:counterForSingle:user_%d:%s";
+  private final RedisTemplate<String, Object> taskCounter;
+
+  public CommonCounterManager(
+      @Qualifier("redisTemplateForTaskQueue") RedisTemplate<String, Object> taskCounter) {
+    this.taskCounter = taskCounter;
+  }
+
+  @Override
+  public void removeJobData(Long jobId) {
+    var key = String.format(KEY_TOP, jobId);
+    this.taskCounter.opsForValue().getAndExpire(key, Duration.ofDays(7));
+  }
+
+  @Override
+  public Long getJobCount(Long jobId, String subKey) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    if (this.taskCounter.hasKey(key)) {
+      return Long.valueOf(this.taskCounter.opsForValue().get(key).toString());
+    }
+    return 0L;
+  }
+
+  @Override
+  public Long hgetJobCount(Long jobId, String subKey, String hashKey) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    if (this.taskCounter.opsForHash().hasKey(key, hashKey)) {
+      return Long.valueOf(this.taskCounter.opsForHash().get(key, hashKey).toString());
+    }
+    return 0L;
+  }
+
+  @Override
+  public Long hgetJobHashSize(Long jobId, String subKey) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    return this.taskCounter.opsForHash().size(key);
+  }
+
+  @Override
+  public Long setJobCount(Long jobId, String subKey, Long value) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForValue().set(key, value);
+    return value;
+  }
+
+  @Override
+  public void hsetJobCount(Long jobId, String subKey, String hashKey, Long value) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForHash().put(key, hashKey, value);
+  }
+
+  @Override
+  public void hsetAllJobCount(Long jobId, String subKey, Map<String, Long> values) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForHash().putAll(key, values);
+  }
+
+  @Override
+  public Map<Object, Object> hgetAllJobCount(Long jobId, String subKey) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    return this.taskCounter.opsForHash().entries(key);
+  }
+
+  @Override
+  public void hIncrementJobCount(Long jobId, String subKey, String hashKey, Integer value) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForHash().increment(key, hashKey, Objects.nonNull(value) ? value : 1);
+  }
+
+  @Override
+  public void hDecreaseJobCount(Long jobId, String subKey, String hashKey, Integer value) {
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForHash().increment(key, hashKey, -(Objects.nonNull(value) ? value : 1));
+  }
+
+  @Override
+  public void setJobCountIncrement(Long jobId, String subKey, Long value) {
+    if (value == null) value = 1L;
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForValue().increment(key, value);
+  }
+
+  @Override
+  public void setJobCountDecrease(Long jobId, String subKey, Long value) {
+    if (value == null) value = 1L;
+    var key = String.format(KEY_FOR_JOB, jobId, subKey);
+    this.taskCounter.opsForValue().decrement(key, value);
+  }
+
+  @Override
+  public Long getJobUserCount(Long jobId, Long userId, String subKey) {
+    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
+    if (this.taskCounter.hasKey(key)) {
+      return Long.valueOf(this.taskCounter.opsForValue().get(key).toString());
+    }
+    return 0L;
+  }
+
+  @Override
+  public Long setJobUserCount(Long jobId, Long userId, String subKey, Long value) {
+    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
+    this.taskCounter.opsForValue().set(key, value);
+    return value;
+  }
+
+  @Override
+  public void setJobUserCountIncrement(Long jobId, Long userId, String subKey, Long value) {
+    if (value == null) value = 1L;
+    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
+    this.taskCounter.opsForValue().increment(key, value);
+  }
+
+  @Override
+  public void setJobUserCountDecrease(Long jobId, Long userId, String subKey, Long value) {
+    if (value == null) value = 1L;
+    var key = String.format(KEY_FOR_JOB_USER, jobId, userId, subKey);
+    this.taskCounter.opsForValue().decrement(key, value);
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/Contants/Constants.java b/src/main/java/ai/saharaa/distribution/Contants/Constants.java
new file mode 100644
index 00000000..85431dbe
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/Contants/Constants.java
@@ -0,0 +1,50 @@
+package ai.saharaa.distribution.Contants;
+
+import ai.saharaa.enums.BaseEnum;
+import com.baomidou.mybatisplus.annotation.EnumValue;
+import com.fasterxml.jackson.annotation.JsonValue;
+
+public class Constants {
+
+  public enum CACHE_KEY_FOR_JOB {
+    COMPLETE_COUNT("COMPLETE_COUNT"),
+    //    EFFECT_COUNT("EFFECT_COUNT"),
+    TASK_COUNTS("TASK_COUNTS"), // hash in redis, only for raw
+    TASK_ID("TASK_ID"), // only for single
+    TOTAL_COUNT("TOTAL_COUNT");
+
+    @EnumValue
+    @JsonValue
+    public final String value;
+
+    CACHE_KEY_FOR_JOB(String value) {
+      this.value = value;
+    }
+
+    public String getValue() {
+      return value;
+    }
+  }
+
+  public enum CACHE_KEY_FOR_JOB_USER implements BaseEnum {
+    SUBMITTED_COUNT("SUBMITTED_COUNT"),
+    CORRECT_COUNT("CORRECT_COUNT"),
+    COMPLETE_COUNT("COMPLETE_COUNT");
+    //    COMPLETE_COUNT("COMPLETE_COUNT"),
+    //    COMPLETE_LIST("COMPLETE_LIST"),
+    //    SKIP_LIST("SKIP_LIST");
+
+    @EnumValue
+    @JsonValue
+    public final String value;
+
+    CACHE_KEY_FOR_JOB_USER(String value) {
+      this.value = value;
+    }
+
+    @Override
+    public String getValue() {
+      return value;
+    }
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/DefaultTaskQueue.java b/src/main/java/ai/saharaa/distribution/DefaultTaskQueue.java
new file mode 100644
index 00000000..e684bff5
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/DefaultTaskQueue.java
@@ -0,0 +1,52 @@
+package ai.saharaa.distribution;
+
+import java.util.List;
+import java.util.Objects;
+import org.springframework.beans.factory.annotation.Qualifier;
+import org.springframework.data.redis.core.RedisTemplate;
+import org.springframework.stereotype.Component;
+
+@Component
+public class DefaultTaskQueue implements TaskQueue {
+
+  private final RedisTemplate<String, Object> taskQueue;
+
+  public DefaultTaskQueue(
+      @Qualifier("redisTemplateForTaskQueue") RedisTemplate<String, Object> taskQueue) {
+    this.taskQueue = taskQueue;
+  }
+
+  @Override
+  public RedisTemplate<String, Object> get() {
+    return this.taskQueue;
+  }
+
+  @Override
+  public Long push(String key, Long value) {
+    return this.taskQueue.opsForList().rightPush(key, value.toString());
+  }
+
+  @Override
+  public Long pushAll(String key, List<Long> value) {
+    return this.taskQueue.opsForList().rightPushAll(key, value.toArray());
+  }
+
+  @Override
+  public Long pop(String key) {
+    Object obj = this.taskQueue.opsForList().leftPop(key);
+    return Objects.isNull(obj) ? Long.MIN_VALUE : Long.valueOf(obj.toString());
+  }
+
+  @Override
+  public List<Long> popList(String key, Long count) {
+    List<Object> objs = this.taskQueue.opsForList().leftPop(key, count);
+    return objs.isEmpty()
+        ? List.of()
+        : objs.map(obj -> Long.valueOf(obj.toString())).toList();
+  }
+
+  @Override
+  public Long length(String key) {
+    return this.taskQueue.opsForList().size(key);
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/DefaultTaskVisitorProvider.java b/src/main/java/ai/saharaa/distribution/DefaultTaskVisitorProvider.java
new file mode 100644
index 00000000..a16a98ad
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/DefaultTaskVisitorProvider.java
@@ -0,0 +1,94 @@
+package ai.saharaa.distribution;
+
+import ai.saharaa.model.*;
+import ai.saharaa.services.*;
+import java.util.*;
+import lombok.extern.slf4j.Slf4j;
+import org.apache.commons.collections4.CollectionUtils;
+import org.springframework.beans.BeansException;
+import org.springframework.context.ApplicationContext;
+import org.springframework.context.ApplicationContextAware;
+import org.springframework.stereotype.Component;
+
+@Slf4j
+@Component
+public class DefaultTaskVisitorProvider implements TaskVisitorProvider, ApplicationContextAware {
+
+  private ApplicationContext applicationContext;
+
+  @Override
+  public TaskVisitor getTaskVisitor(
+      BatchSetting.DistributeType distributeType, JobUser.JobUserRole role) {
+
+    Map<String, TypeProvider> beansOfType =
+        this.applicationContext.getBeansOfType(TypeProvider.class);
+
+    List<TypeProvider> typeProviders =
+        beansOfType.values().stream().filter(x -> x.getRoles().contains(role)).toList();
+
+    if (CollectionUtils.isEmpty(typeProviders)) {
+      throw new IllegalStateException("No distribution strategy found for role: " + role);
+    }
+
+    TypeProvider typeProvider = typeProviders.stream()
+        .filter(x -> x.getDistributionTypes().contains(distributeType))
+        .findAny()
+        .orElseThrow(() -> new IllegalStateException(
+            "No distribution strategy found for type: " + distributeType));
+
+    Class<TaskVisitor> aClass = TaskVisitor.class;
+    return aClass.cast(typeProvider);
+  }
+
+  //  @Override
+  //  @SuppressWarnings("TypeParameterUnusedInFormals")
+  //  public <T> T submitTasks(
+  //      SubmitAnswersDTO answers, Long userId, Boolean isTester, String submitTimeInHeader) {
+  //
+  //    if (CollectionUtils.isEmpty(answers.getSubmitAnswers())) {
+  //      throw ControllerUtils.badRequest("No answers submitted");
+  //    }
+  //
+  //    var jobId = answers.getSubmitAnswers().stream().findAny().get().getTaskSession().getJobId();
+  //    Job job = this.getAndCheckJob(jobId);
+  //    BatchDetailsDTO batch = this.getAndCheckBatch(job.getBatchId());
+  //    JobUser jobUser = this.getJobUser(jobId, userId);
+  //    if (Objects.equals(jobUser.getActive(), Boolean.FALSE)) {
+  //      if (!isTester) {
+  //        throw ControllerUtils.badRequest(
+  //            "You've been banned from this task because we've detected malicious activity.");
+  //      } else {
+  //        jobUserService.unbanInJob(userId, jobId);
+  //      }
+  //    }
+  //
+  //    TaskSubmitter taskSubmitter =
+  //        this.getDistribution(
+  //            batch.getBatchSetting().getDistributeType(), jobUser.getRole(),
+  // TaskSubmitter.class);
+  //
+  //    Object o =
+  //        taskSubmitter.submitTasks(
+  //            answers, job, batch.getBatch(), batch.getBatchSetting(), jobUser, isTester);
+  //
+  //    return (T) o;
+  //  }
+
+  //  @Override
+  //  public TaskSession submitTasks(
+  //      SubmitAnswerDTO answer,
+  //      Long taskSessionId,
+  //      Long userId,
+  //      Boolean isTester,
+  //      JobUser.JobUserRole role) {
+  //
+  //    // There is no need to distinguish between types
+  //    return this.getDistribution(BatchSetting.DistributeType.SINGLE, role, TaskSubmitter.class)
+  //        .submitTasks(answer, taskSessionId, userId, isTester);
+  //  }
+  @Override
+  public void setApplicationContext(
+      org.springframework.context.ApplicationContext applicationContext) throws BeansException {
+    this.applicationContext = applicationContext;
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/LabelerRawTaskLifecycleManager.java b/src/main/java/ai/saharaa/distribution/LabelerRawTaskLifecycleManager.java
new file mode 100644
index 00000000..12cfbdfb
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/LabelerRawTaskLifecycleManager.java
@@ -0,0 +1,338 @@
+package ai.saharaa.distribution;
+
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.*;
+import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
+
+import ai.saharaa.config.LabelingMetricsController;
+import ai.saharaa.config.cluster.ClusterConfiguration;
+import ai.saharaa.daos.*;
+import ai.saharaa.dto.job.GroupByTaskIdAndCountResultDTO;
+import ai.saharaa.dto.job.SubmitAnswersDTO;
+import ai.saharaa.model.*;
+import ai.saharaa.services.*;
+import ai.saharaa.utils.DateUtils;
+import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
+import java.sql.Timestamp;
+import java.util.*;
+import java.util.stream.Collectors;
+import java.util.stream.Stream;
+import org.apache.commons.collections4.CollectionUtils;
+import org.springframework.stereotype.Component;
+import org.springframework.transaction.annotation.Transactional;
+
+@Component
+public class LabelerRawTaskLifecycleManager extends AbstractTaskLifecycleManager
+    implements TaskLifecycleManager, TaskDistributor<TaskSession>, TypeProvider, TaskVisitor {
+  private static final String key = "taskQueue:";
+  private final TaskQueue taskQueue;
+  private final CommonCounterManager counterManager;
+  private final JobTaskService jobTaskService;
+  private final HoneyPotService honeyPotService;
+  private final JobTaskDao jobTaskDao;
+  private final TaskSessionService taskSessionService;
+
+  @SuppressWarnings("unused")
+  private final TaskSessionDao taskSessionDao;
+
+  private final ProjectService projectService;
+  private final JobService jobService;
+  private final BatchService batchService;
+  private final WorkloadLimitService workloadLimitService;
+
+  public LabelerRawTaskLifecycleManager(
+      TaskQueue taskQueue,
+      CommonCounterManager counterManager,
+      JobTaskService jobTaskService,
+      JobTaskDao jobTaskDao,
+      TaskSessionDao taskSessionDao,
+      TaskSessionService taskSessionService,
+      WorkloadLimitService workloadLimitService,
+      ClusterConfiguration clusterConfiguration,
+      CaptchaService captchaService,
+      IndividualsService individualsService,
+      JobUserService jobUserService,
+      TaskAndReviewSessionDao taskAndReviewSessionDao,
+      TaskQuestionDao taskQuestionDao,
+      ResourceDao resourceDao,
+      HoneyPotService honeyPotService,
+      LabelingMetricsController labelingMetricsController,
+      NotificationService notificationService,
+      ProjectService projectService,
+      JobService jobService,
+      BatchService batchService) {
+
+    super(
+        clusterConfiguration,
+        captchaService,
+        individualsService,
+        jobUserService,
+        honeyPotService,
+        taskSessionService,
+        labelingMetricsController,
+        taskSessionDao,
+        taskAndReviewSessionDao,
+        workloadLimitService,
+        taskQuestionDao,
+        resourceDao,
+        notificationService);
+    this.taskQueue = taskQueue;
+    this.counterManager = counterManager;
+    this.jobTaskService = jobTaskService;
+    this.honeyPotService = honeyPotService;
+    this.jobTaskDao = jobTaskDao;
+    this.taskSessionService = taskSessionService;
+    this.taskSessionDao = taskSessionDao;
+    this.workloadLimitService = workloadLimitService;
+    this.projectService = projectService;
+    this.jobService = jobService;
+    this.batchService = batchService;
+  }
+
+  @Override
+  @Transactional
+  public List<TaskSession> assignTasks(
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      String ipAddress,
+      Boolean isTester) {
+    if (isTester) ipAddress = null;
+    var pendingTaskSessions = this.getPendingTaskSessions(job, batch, batchSetting, jobUser);
+    if (CollectionUtils.isNotEmpty(pendingTaskSessions)) {
+      return pendingTaskSessions;
+    }
+    List reviseTaskSessions = this.getReviseTaskSessions(batch, jobUser);
+    if (CollectionUtils.isNotEmpty(reviseTaskSessions)) {
+      return reviseTaskSessions;
+    }
+
+    return getTaskSessions(job, batch, batchSetting, jobUser, ipAddress);
+  }
+
+  @Override
+  @Transactional
+  public Boolean submitTasks(
+      SubmitAnswersDTO answers,
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      Boolean isTester) {
+    return super.submitTasks(answers, job, batch, batchSetting, jobUser, isTester);
+  }
+
+  //  @Override
+  //  @Transactional
+  //  public TaskSession submitTasks(
+  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
+  //    return super.submitTasks(answer, taskSessionId, userId, isTester);
+  //  }
+
+  @Override
+  public void refreshTaskQueue(Job job, Integer repeatCount) {
+    Long length = this.taskQueue.length(key.concat(job.getId().toString()));
+    if (length > 0) {
+      return;
+    }
+    var taskIdList = jobTaskDao.selectTasksInJobInLimit(job.getId(), job.getAssignDataVolume());
+    List<GroupByTaskIdAndCountResultDTO> refreshTaskDistributionStatus =
+        jobTaskDao.refreshTaskDistributionStatus(job.getId());
+    var refreshTaskDistributionStatusMap = refreshTaskDistributionStatus.toMap(
+        GroupByTaskIdAndCountResultDTO::getTaskId, GroupByTaskIdAndCountResultDTO::getCount);
+
+    Map<String, Long> taskIdMap = refreshTaskDistributionStatus.stream()
+        .collect(Collectors.toMap(
+            k -> k.getTaskId().toString(), GroupByTaskIdAndCountResultDTO::getCount));
+
+    counterManager.hsetAllJobCount(job.getId(), TASK_COUNTS.value, taskIdMap);
+
+    for (int i = 0; i < repeatCount; i++) {
+      int finalI = i;
+      var taskIds = taskIdList
+          .filter(id -> !refreshTaskDistributionStatusMap.containsKey(id)
+              || (refreshTaskDistributionStatusMap.get(id) + finalI) < repeatCount)
+          .toList();
+      if (!taskIds.isEmpty()) {
+        this.taskQueue.pushAll(key.concat(job.getId().toString()), taskIds);
+      }
+    }
+  }
+
+  @Override
+  @Transactional
+  public void initializeTaskQueue(Batch batch, BatchSetting batchSetting) {
+
+    projectService
+        .getProjectById(batch.getProjectId())
+        .filter(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
+        .ifPresent(p -> jobService.assignJobToExternalBatchJob(batch));
+
+    List<Job> jobs = this.jobService.listJobsByBatchId(batch.getId());
+    var jobId = jobs.get(0).getId();
+
+    var taskIdList =
+        jobTaskDao.selectTasksInJobInLimit(jobs.get(0).getId(), jobs.get(0).getAssignDataVolume());
+
+    for (int i = 0; i < batchSetting.getAnnotatingTimesAnnotationPerDatapoint(); i++) {
+      this.taskQueue.pushAll(key.concat(jobs.get(0).getId().toString()), taskIdList);
+    }
+    counterManager.setJobCount(jobId, COMPLETE_COUNT.value, 0L);
+    Map<String, Long> taskIdMap =
+        taskIdList.stream().collect(Collectors.toMap(Object::toString, v -> 0L));
+    counterManager.hsetAllJobCount(jobId, TASK_COUNTS.value, taskIdMap);
+
+    counterManager.setJobCount(
+        jobId,
+        TOTAL_COUNT.value,
+        batchSetting.getAnnotatingTimesAnnotationPerDatapoint().longValue()
+            * jobs.get(0).getAssignDataVolume());
+    this.batchService.update(
+        null,
+        new UpdateWrapper<Batch>()
+            .lambda()
+            .eq(Batch::getId, batch.getId())
+            .set(Batch::getStatus, Batch.BatchStatus.ASSIGN_JOBS)
+            .set(Batch::getUpdatedAt, DateUtils.now()));
+  }
+
+  @Override
+  public List<BatchSetting.DistributeType> getDistributionTypes() {
+    return Arrays.asList(BatchSetting.DistributeType.RAW, BatchSetting.DistributeType.ASSEMBLED);
+  }
+
+  @Override
+  public List<JobUser.JobUserRole> getRoles() {
+    return List.of(LABELER);
+  }
+
+  private List getPendingTaskSessions(
+      Job job, Batch batch, BatchSetting batchSetting, JobUser jobUser) {
+    var pendingTaskSession =
+        jobTaskService.getOngoingTaskSessionsForJobUser(jobUser.getId(), LABELER);
+    var batchAnnotationBatchSize = batch.getAnnotatingSubmitRequired();
+
+    if (CollectionUtils.isEmpty(pendingTaskSession)) {
+      return Collections.EMPTY_LIST;
+    }
+
+    if (pendingTaskSession.size() >= batchAnnotationBatchSize) {
+      return pendingTaskSession;
+    }
+
+    var limit = batchAnnotationBatchSize - pendingTaskSession.size();
+    List<TaskSession> taskSessions = this.getCacheTaskId(job, batch, jobUser, batchSetting, limit);
+    if (!taskSessions.isEmpty()) {
+      honeyPotService.adaptHoneyPotSessionWithSessionIds(
+          taskSessions.mapToList(TaskSession::getId), batch, jobUser, false);
+    }
+    return Stream.concat(pendingTaskSession.stream(), taskSessions.stream())
+        .collect(Collectors.toList());
+  }
+
+  private List<TaskSession> getTaskSessions(
+      Job job, Batch batch, BatchSetting batchSetting, JobUser jobUser, String ipAddress) {
+    this.workloadLimitService.checkIfWorkloadLimitExceeded(
+        job.getId(), jobUser.getUserId(), LABELER, ipAddress);
+    return this.getCacheTaskId(
+        job, batch, jobUser, batchSetting, batch.getAnnotatingSubmitRequired());
+  }
+
+  private List getReviseTaskSessions(Batch batch, JobUser jobUser) {
+    var reviseLimitBatchSize = batch.getAnnotatingSubmitRequired();
+    if (reviseLimitBatchSize > 0) {
+      var reviseTasks = jobTaskService.getReviseTaskSessionsForJobUser(
+          jobUser.getId(), LABELER, reviseLimitBatchSize);
+      jobTaskService.clearWaitingReverseSession(jobUser.getId(), LABELER);
+      if (CollectionUtils.isNotEmpty(reviseTasks)) {
+        return reviseTasks
+            .map(taskSessionService::reCreateTaskSessionWithRevisedRecord)
+            .toList();
+      }
+    }
+
+    return Collections.EMPTY_LIST;
+  }
+
+  @SuppressWarnings("unused")
+  private List<TaskSession> getCacheTaskId(
+      Job job, Batch batch, JobUser jobUser, BatchSetting batchSetting, int limit) {
+    Set<Long> notIncluded = Stream.concat(
+            this.taskSessionService
+                .getTaskSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
+                .stream(),
+            this.taskSessionService
+                .getSkipTaskSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
+                .stream()
+                .map(SkipTaskSession::getTaskId))
+        .toSet();
+
+    Set<Long> jobTaskIds = new HashSet<>(limit);
+    var maxTry = job.getAssignDataVolume() - notIncluded.size();
+    int retryCount = 0;
+    while (limit > 0 && retryCount < maxTry) { // todo: use popList to take by batch
+      retryCount++;
+      Long taskId = this.taskQueue.pop(key.concat(job.getId().toString()));
+      if (Objects.equals(taskId, Long.MIN_VALUE)) {
+        limit = 0;
+        continue;
+      }
+
+      if (!notIncluded.contains(taskId) && !jobTaskIds.contains(taskId)) {
+        limit -= 1;
+        jobTaskIds.add(taskId);
+        counterManager.hIncrementJobCount(job.getId(), TASK_COUNTS.value, taskId.toString(), 1);
+        //      } else {
+        //        this.taskQueue.push(key.concat(job.getId().toString()), taskId);
+      }
+    }
+
+    List<TaskSession> taskSessions = this.createTaskSessionCanDuplicate(
+        jobUser,
+        jobTaskIds.toSet(),
+        LABELER,
+        DateUtils.now(),
+        batchSetting,
+        batchSetting.getDistributeType());
+
+    //    this.taskSessionDao.updateTaskSessionDisCount( // todo: DisCount remove temp
+    //        taskSessions.map(TaskSession::getTaskId).toList(), job.getId());
+
+    return taskSessions;
+  }
+
+  private List<TaskSession> createTaskSessionCanDuplicate(
+      JobUser jobUser,
+      Collection<Long> jobTasks,
+      JobUser.JobUserRole role,
+      Timestamp timestamp,
+      BatchSetting batchSetting,
+      BatchSetting.DistributeType distributeType) {
+    return switch (distributeType) {
+      case RAW -> jobTasks
+          .map(taskId -> taskSessionService.createTaskSessionCanDuplicate(
+              jobUser,
+              taskId,
+              role,
+              timestamp,
+              batchSetting.getAnnotatingTimesPerDatapointPerUser() + 1))
+          .toList();
+      case ASSEMBLED, SINGLE -> Collections.EMPTY_LIST;
+    };
+  }
+
+  @Override
+  public TaskSubmitter taskSubmitter() {
+    return this;
+  }
+
+  @Override
+  public TaskDistributor taskDistributor() {
+    return this;
+  }
+
+  @Override
+  public TaskLifecycleManager taskLifecycleManager() {
+    return this;
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/LabelerSingleTaskLifecycleManager.java b/src/main/java/ai/saharaa/distribution/LabelerSingleTaskLifecycleManager.java
new file mode 100644
index 00000000..3dd35023
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/LabelerSingleTaskLifecycleManager.java
@@ -0,0 +1,293 @@
+package ai.saharaa.distribution;
+
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.*;
+import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
+
+import ai.saharaa.config.LabelingMetricsController;
+import ai.saharaa.config.cluster.ClusterConfiguration;
+import ai.saharaa.daos.*;
+import ai.saharaa.dto.job.SubmitAnswersDTO;
+import ai.saharaa.model.*;
+import ai.saharaa.services.*;
+import ai.saharaa.utils.ControllerUtils;
+import ai.saharaa.utils.DateUtils;
+import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
+import java.util.Collections;
+import java.util.List;
+import java.util.Objects;
+import org.apache.commons.collections4.CollectionUtils;
+import org.springframework.stereotype.Component;
+import org.springframework.transaction.annotation.Transactional;
+
+@Component
+public class LabelerSingleTaskLifecycleManager extends AbstractTaskLifecycleManager
+    implements TaskLifecycleManager, TaskDistributor<TaskSession>, TypeProvider, TaskVisitor {
+
+  private static final String TASK_ID_KEY = "taskQueue:single:";
+  private final TaskQueue taskQueue;
+  private final CommonCounterManager counterManager;
+  private final JobTaskService jobTaskService;
+  private final TaskSessionDao taskSessionDao;
+  private final TaskSessionService taskSessionService;
+  private final BatchDao batchDao;
+  private final TaskListDao taskListDao;
+  private final TaskDao taskDao;
+  private final JobTaskDao jobTaskDao;
+  private final ProjectService projectService;
+  private final JobService jobService;
+  private final BatchService batchService;
+
+  private final WorkloadLimitService workloadLimitService;
+
+  public LabelerSingleTaskLifecycleManager(
+      TaskQueue taskQueue,
+      CommonCounterManager counterManager,
+      JobTaskService jobTaskService,
+      TaskSessionDao taskSessionDao,
+      TaskSessionService taskSessionService,
+      BatchDao batchDao,
+      TaskListDao taskListDao,
+      TaskDao taskDao,
+      JobTaskDao jobTaskDao,
+      WorkloadLimitService workloadLimitService,
+      ClusterConfiguration clusterConfiguration,
+      CaptchaService captchaService,
+      IndividualsService individualsService,
+      JobUserService jobUserService,
+      HoneyPotService honeyPotService,
+      LabelingMetricsController labelingMetricsController,
+      TaskAndReviewSessionDao taskAndReviewSessionDao,
+      TaskQuestionDao taskQuestionDao,
+      ResourceDao resourceDao,
+      NotificationService notificationService,
+      ProjectService projectService,
+      JobService jobService,
+      BatchService batchService) {
+    super(
+        clusterConfiguration,
+        captchaService,
+        individualsService,
+        jobUserService,
+        honeyPotService,
+        taskSessionService,
+        labelingMetricsController,
+        taskSessionDao,
+        taskAndReviewSessionDao,
+        workloadLimitService,
+        taskQuestionDao,
+        resourceDao,
+        notificationService);
+
+    this.taskQueue = taskQueue;
+    this.counterManager = counterManager;
+    this.jobTaskService = jobTaskService;
+    this.taskSessionDao = taskSessionDao;
+    this.taskSessionService = taskSessionService;
+    this.batchDao = batchDao;
+    this.taskListDao = taskListDao;
+    this.taskDao = taskDao;
+    this.jobTaskDao = jobTaskDao;
+    this.workloadLimitService = workloadLimitService;
+    this.projectService = projectService;
+    this.jobService = jobService;
+    this.batchService = batchService;
+  }
+
+  private String getKey(Long jobId, Long taskId) {
+    return TASK_ID_KEY.concat(jobId.toString()).concat(":taskId:").concat(taskId.toString());
+  }
+
+  //  @Override
+  //  @Transactional
+  //  public TaskSession submitTasks(
+  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
+  //    return super.submitTasks(answer, taskSessionId, userId, isTester);
+  //  }
+
+  @Override
+  @Transactional
+  public List<TaskSession> assignTasks(
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      String ipAddress,
+      Boolean isTester) {
+    if (isTester) ipAddress = null;
+    List<TaskSession> pendingTaskSession = getPendingTaskSessions(jobUser);
+    if (CollectionUtils.isNotEmpty(pendingTaskSession)) {
+      return pendingTaskSession;
+    }
+
+    List<TaskSession> reviseTasks = getRevisedTaskSessions(job, batch, batchSetting, jobUser);
+    if (CollectionUtils.isNotEmpty(reviseTasks)) {
+      return reviseTasks;
+    }
+
+    return getTaskSessions(job, batch, batchSetting, jobUser, ipAddress);
+  }
+
+  @Override
+  @Transactional
+  public Boolean submitTasks(
+      SubmitAnswersDTO answers,
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      Boolean isTester) {
+    return super.submitTasks(answers, job, batch, batchSetting, jobUser, isTester);
+  }
+
+  @Override
+  public void refreshTaskQueue(Job job, Integer repeatCount) {
+    List<JobTask> jobTasks = this.jobTaskDao.getTaskByJobId(job.getId());
+    jobTasks.forEach(x -> {
+      Object o = this.taskQueue.get().opsForValue().get(getKey(job.getId(), x.getTaskId()));
+      if (Objects.isNull(o) || Long.valueOf(o.toString()) < 0) {
+        Long count =
+            this.taskSessionDao.countTaskSessionsByTaskIdAndJobId(x.getTaskId(), job.getId());
+
+        if (count < repeatCount) {
+          this.taskQueue
+              .get()
+              .opsForValue()
+              .set(getKey(job.getId(), x.getTaskId()), repeatCount - count);
+        }
+      }
+    });
+  }
+
+  @Override
+  @Transactional
+  public void initializeTaskQueue(Batch batch, BatchSetting batchSetting) {
+    projectService
+        .getProjectById(batch.getProjectId())
+        .filter(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
+        .ifPresent(p -> jobService.assignJobToExternalBatchJob(batch));
+
+    List<Job> jobs = this.jobService.listJobsByBatchId(batch.getId());
+
+    var jobId = jobs.get(0).getId();
+
+    batchSetting.setDistributeType(BatchSetting.DistributeType.SINGLE);
+    batchDao.updateBatchSettingById(batchSetting);
+    counterManager.setJobCount(jobId, COMPLETE_COUNT.value, 0L);
+    counterManager.setJobCount(
+        jobId,
+        TOTAL_COUNT.value,
+        batchSetting.getAnnotatingTimesAnnotationPerDatapoint().longValue());
+    TaskList taskList =
+        taskListDao
+            .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
+            .stream()
+            .findFirst()
+            .get();
+    var task = taskDao.getFirstTaskInTaskList(taskList.getId());
+    task.ifPresent(taskEx -> counterManager.setJobCount(jobId, TASK_ID.value, taskEx.getId()));
+
+    this.batchService.update(
+        null,
+        new UpdateWrapper<Batch>()
+            .lambda()
+            .eq(Batch::getId, batch.getId())
+            .set(Batch::getStatus, Batch.BatchStatus.ASSIGN_JOBS)
+            .set(Batch::getUpdatedAt, DateUtils.now()));
+
+    this.refreshTaskQueue(jobs.get(0), batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
+  }
+
+  @Override
+  public List<BatchSetting.DistributeType> getDistributionTypes() {
+    return List.of(BatchSetting.DistributeType.SINGLE);
+  }
+
+  @Override
+  public List<JobUser.JobUserRole> getRoles() {
+    return List.of(LABELER);
+  }
+
+  private List<TaskSession> getPendingTaskSessions(JobUser jobUser) {
+    return jobTaskService.getOngoingTaskSessionsForJobUser(jobUser.getId(), LABELER);
+  }
+
+  private List<TaskSession> getRevisedTaskSessions(
+      Job job, Batch batch, BatchSetting batchSetting, JobUser jobUser) {
+    var reviseTasksExist =
+        this.jobTaskService.getReviseTaskSessionsForJobUser(jobUser.getId(), LABELER, 1);
+    if (reviseTasksExist.isEmpty()) return Collections.EMPTY_LIST;
+
+    Long totalTaskSessionsCount = this.taskSessionDao.countTaskSessionsByJobId(job.getId());
+    int reviseLimitBatchSize = Math.toIntExact(Math.min(
+        batch.getAnnotatingSubmitRequired(),
+        (batchSetting.getAnnotatingTimesAnnotationPerDatapoint() - totalTaskSessionsCount)));
+
+    if (reviseLimitBatchSize > 0) {
+      var reviseTasks = this.jobTaskService.getReviseTaskSessionsForJobUser(
+          jobUser.getId(), LABELER, reviseLimitBatchSize);
+      if (CollectionUtils.isNotEmpty(reviseTasks)) {
+        this.jobTaskService.clearWaitingReverseSession(jobUser.getId(), LABELER);
+        return reviseTasks.stream()
+            .map(this.taskSessionService::reCreateTaskSessionWithRevisedRecord)
+            .toList();
+      }
+    }
+    return Collections.EMPTY_LIST;
+  }
+
+  private List<TaskSession> getTaskSessions(
+      Job job, Batch batch, BatchSetting batchSetting, JobUser jobUser, String ipAddress) {
+    var jobId = job.getId();
+    this.workloadLimitService.checkIfWorkloadLimitExceeded(
+        job.getId(), jobUser.getUserId(), LABELER, ipAddress);
+
+    var singleTaskId = this.taskQueue.get().hasKey("job_${jobId}:taskId")
+        ? Long.valueOf(
+            this.taskQueue.get().opsForValue().get("job_${jobId}:taskId").toString())
+        : jobTaskDao.getSingleDatapointJobTaskByJobId(job.getId()).stream()
+            .findFirst()
+            .get()
+            .getTaskId();
+
+    Long decrement =
+        this.taskQueue.get().opsForValue().decrement(getKey(job.getId(), singleTaskId));
+    if (decrement <= 0) {
+      throw ControllerUtils.notFound("No task available for this job");
+    }
+
+    Object total = this.taskQueue
+        .get()
+        .opsForHash()
+        .get("job_${jobId}:user_${jobUser.getUserId()}:workloads", "total");
+    int userWorkloads = Objects.isNull(total) ? 0 : (int) total;
+    int limit = Objects.isNull(batchSetting.getWorkloadMaxPerJobUser())
+        ? batch.getAnnotatingSubmitRequired()
+        : Math.min(
+            batch.getAnnotatingSubmitRequired(),
+            batchSetting.getWorkloadMaxPerJobUser() - userWorkloads);
+
+    return taskSessionService.createTaskSessionForSingleData(
+        jobUser,
+        singleTaskId,
+        LABELER,
+        DateUtils.now(),
+        batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
+        limit,
+        batch.getAnnotatingSubmitRequired());
+  }
+
+  @Override
+  public TaskSubmitter taskSubmitter() {
+    return this;
+  }
+
+  @Override
+  public TaskDistributor taskDistributor() {
+    return this;
+  }
+
+  @Override
+  public TaskLifecycleManager taskLifecycleManager() {
+    return this;
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/ReviewerTaskLifecycleManager.java b/src/main/java/ai/saharaa/distribution/ReviewerTaskLifecycleManager.java
new file mode 100644
index 00000000..ede0e3fa
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/ReviewerTaskLifecycleManager.java
@@ -0,0 +1,299 @@
+package ai.saharaa.distribution;
+
+import static java.util.stream.Collectors.toMap;
+
+import ai.saharaa.config.LabelingMetricsController;
+import ai.saharaa.daos.TaskSessionDao;
+import ai.saharaa.dto.job.*;
+import ai.saharaa.exception.AppException;
+import ai.saharaa.model.*;
+import ai.saharaa.model.HoneyPot.HoneyPotReviewSession;
+import ai.saharaa.services.*;
+import ai.saharaa.utils.ControllerUtils;
+import jakarta.servlet.http.HttpServletRequest;
+import java.util.*;
+import java.util.function.Function;
+import java.util.stream.Collectors;
+import java.util.stream.Stream;
+import org.apache.commons.collections4.CollectionUtils;
+import org.springframework.stereotype.Component;
+import org.springframework.web.context.request.RequestContextHolder;
+import org.springframework.web.context.request.ServletRequestAttributes;
+
+@Component
+public class ReviewerTaskLifecycleManager
+    implements TaskLifecycleManager,
+        TaskDistributor<ReviewSession>,
+        TaskSubmitter<SubmitReviewsDTO, Boolean>,
+        TypeProvider,
+        TaskVisitor {
+  private static final String key = "taskQueue:review:";
+
+  private final TaskSessionDao taskSessionDao;
+  private final TaskQueue taskQueue;
+  private final JobTaskService jobTaskService;
+  private final WorkloadLimitService workloadService;
+  private final TaskSessionService taskSessionService;
+  private final ReviewSessionService reviewSessionService;
+  private final HoneyPotService honeyPotService;
+  private final LabelingMetricsController labelingMetricsController;
+
+  public ReviewerTaskLifecycleManager(
+      TaskSessionDao taskSessionDao,
+      TaskQueue taskQueue,
+      JobTaskService jobTaskService,
+      WorkloadLimitService workloadService,
+      TaskSessionService taskSessionService,
+      ReviewSessionService reviewSessionService,
+      HoneyPotService honeyPotService,
+      LabelingMetricsController labelingMetricsController) {
+    this.taskSessionDao = taskSessionDao;
+    this.taskQueue = taskQueue;
+    this.jobTaskService = jobTaskService;
+    this.workloadService = workloadService;
+    this.taskSessionService = taskSessionService;
+    this.reviewSessionService = reviewSessionService;
+    this.honeyPotService = honeyPotService;
+    this.labelingMetricsController = labelingMetricsController;
+  }
+
+  @Override
+  public List<ReviewSession> assignTasks(
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      String ipAddress,
+      Boolean isTester) {
+    if (isTester) ipAddress = null;
+
+    List<ReviewSession> pendingTasks =
+        this.jobTaskService.getOngoingReviewSessionsForJobUser(jobUser.getId());
+
+    if (CollectionUtils.isNotEmpty(pendingTasks)
+        && pendingTasks.size() >= batch.getReviewingRequiredDatapoint()) {
+      return pendingTasks;
+    }
+
+    this.checkIfWorkloadLimitExceeded(job, jobUser, ipAddress);
+
+    var batchAnnotationBatchSize = batch.getReviewingRequiredDatapoint();
+    var limit = batchAnnotationBatchSize - pendingTasks.size();
+    List<ReviewSession> reviewSessions = this.getCacheTask(job, jobUser, limit);
+    pendingTasks.addAll(reviewSessions);
+    if (!pendingTasks.isEmpty() && !reviewSessions.isEmpty()) {
+      honeyPotService.adaptHpReviewSessionByReviewSessionIds(
+          reviewSessions.mapToList(ReviewSession::getId), batch, jobUser, false);
+    }
+    return pendingTasks;
+  }
+
+  @Override
+  public Boolean submitTasks(
+      SubmitReviewsDTO reviews,
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      Boolean isTester) {
+
+    var reviewSessionIds =
+        reviews.getReviews().stream().map(SubmitReviewDTO::getReviewSessionId).toList();
+    var sessions = this.reviewSessionService.getReviewSessionByIdList(reviewSessionIds).stream()
+        .filter(s -> s.getUserId().equals(jobUser.getUserId()))
+        .filter(s -> s.getStatus().equals(ReviewSession.ReviewSessionStatus.PENDING))
+        .toList();
+    if (CollectionUtils.isEmpty(sessions) && CollectionUtils.isNotEmpty(reviewSessionIds)) {
+      throw ControllerUtils.notFound("You are not allowed to approve these submissions");
+    }
+    if (sessions.size() < reviewSessionIds.size()) {
+      throw ControllerUtils.notFound("Review session may expired, please reload");
+    }
+    var taskSessionIds =
+        reviews.getReviews().stream().map(SubmitReviewDTO::getTaskSessionId).toList();
+
+    var taskSessions = this.taskSessionService.getTaskSessionByIdList(taskSessionIds).stream()
+        .filter(s -> !s.getDeleted())
+        .filter(s -> s.getStatus().equals(TaskSession.TaskSessionStatus.PendingReview))
+        .toList();
+    if (CollectionUtils.isEmpty(taskSessions) && CollectionUtils.isNotEmpty(taskSessionIds)) {
+      throw ControllerUtils.notFound("You are not allowed to approve these sessions");
+    }
+    if (taskSessions.size() < reviewSessionIds.size()) {
+      throw ControllerUtils.notFound("task session may expired, please reload");
+    }
+
+    var hpReviews = Objects.isNull(reviews.getHpReviews())
+        ? List.<SubmitHoneyPotReviewsItemDTO>of()
+        : reviews.getHpReviews();
+
+    var hpSessions = this.honeyPotService.getSessionByReviewSessionIdList(reviewSessionIds).stream()
+        .filter(s -> !s.getDeleted())
+        .toList();
+    if (reviewSessionIds.size() < hpSessions.size()) {
+      throw ControllerUtils.notFound("Invalid data.");
+    }
+    var id2HpReviewSession = hpSessions.stream()
+        .collect(toMap(HoneyPotReviewSession::getReviewSessionId, Function.identity()));
+    var notIncluded = hpReviews.stream()
+        .filter(
+            r -> !id2HpReviewSession.containsKey(r.getHoneyPotReviewSession().getReviewSessionId()))
+        .findAny();
+    if (notIncluded.isPresent()) {
+      throw ControllerUtils.notFound("Invalid data.");
+    }
+    var rejectIdList = honeyPotService.submitReviewAndJudge(hpReviews, jobUser, isTester);
+
+    if (rejectIdList.getRight()) {
+      return Boolean.valueOf(false);
+    }
+
+    HttpServletRequest request =
+        ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
+    var ip = Web2AuthService.getClientIP(request);
+
+    labelingMetricsController.reviewAnnotate(reviews.getReviews().size());
+    var result = reviewSessionService.handleSubmitReviews(
+        reviews.getReviews(), batch, job, jobUser.getUserId(), rejectIdList.getLeft());
+    workloadService.updateJobUserWorkloadsForReview(
+        job.getId(),
+        jobUser.getUserId(),
+        (long) reviews.getReviews().size(),
+        JobUser.JobUserRole.REVIEWER,
+        ip,
+        result);
+
+    return Boolean.TRUE;
+  }
+
+  //  @Override
+  //  public TaskSession submitTasks(
+  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester) {
+  //    return null;
+  //  }
+
+  @Override
+  public void refreshTaskQueue(Job job, Integer repeatCount) {
+    Long length = this.taskQueue.length(key.concat(job.getId().toString()));
+    if (length > 0) {
+      return;
+    }
+
+    var pendingTaskIds = taskSessionDao
+        .getTaskIdsByJobIdAndStatus(job.getId(), TaskSession.TaskSessionStatus.PendingReview)
+        .map(TaskSession::getId)
+        .map(Objects::toString)
+        .toList();
+
+    if (!pendingTaskIds.isEmpty()) {
+      var refreshTaskDistributionStatus =
+          taskSessionDao
+              .refreshTaskQueueForReview(job.getId(), String.join(",", pendingTaskIds))
+              .stream()
+              .toMap(
+                  v -> v.getTaskSessionId().toString(),
+                  GroupByTaskSessionIdAndCountResultDTO::getCount);
+
+      for (int i = 0; i < repeatCount; i++) {
+        int finalI = i;
+        var taskIds = pendingTaskIds
+            .filter(r -> !refreshTaskDistributionStatus.containsKey(r)
+                || (refreshTaskDistributionStatus.get(r) + finalI) < repeatCount)
+            .map(Long::valueOf)
+            .toList();
+        if (!taskIds.isEmpty()) {
+          this.taskQueue.pushAll(key.concat(job.getId().toString()), taskIds);
+        }
+      }
+    }
+  }
+
+  @Override
+  public void initializeTaskQueue(Batch batch, BatchSetting batchSetting) {}
+
+  @Override
+  public List<BatchSetting.DistributeType> getDistributionTypes() {
+    return List.of(
+        BatchSetting.DistributeType.RAW,
+        BatchSetting.DistributeType.SINGLE,
+        BatchSetting.DistributeType.ASSEMBLED);
+  }
+
+  @Override
+  public List<JobUser.JobUserRole> getRoles() {
+    return List.of(JobUser.JobUserRole.REVIEWER);
+  }
+
+  @SuppressWarnings("unused")
+  private void checkIfWorkloadLimitExceeded(Job job, JobUser jobUser, String ipAddress) {
+    workloadService.checkIfWorkloadLimitExceeded(
+        job.getId(), jobUser.getUserId(), JobUser.JobUserRole.REVIEWER, ipAddress);
+  }
+
+  private List<ReviewSession> getCacheTask(Job job, JobUser jobUser, int limit) {
+
+    List<Long> notIncluded = Stream.concat(
+            this.taskSessionService
+                .getReviewSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
+                .stream(),
+            this.taskSessionService
+                .getSkipTaskSessionByJobIdAndUserId(job.getId(), jobUser.getUserId())
+                .stream()
+                .map(SkipTaskSession::getTaskSessionId))
+        .collect(Collectors.toList());
+
+    Set<Long> taskSessionIds = new HashSet<>(limit);
+    var maxTry = Math.min((job.getAssignDataVolume() - notIncluded.size()), 50);
+    int retryCount = 0;
+    while (limit > 0 && retryCount < maxTry) {
+      retryCount++;
+      Long taskSessionId = this.taskQueue.pop(key.concat(job.getId().toString()));
+      if (Objects.equals(taskSessionId, Long.MIN_VALUE)) {
+        limit = 0;
+        continue;
+      }
+
+      if (!notIncluded.contains(taskSessionId) && !taskSessionIds.contains(taskSessionId)) {
+        limit -= 1;
+        taskSessionIds.add(taskSessionId);
+        //      } else {
+        //        this.taskQueue.push(key.concat(job.getId().toString()), taskSessionId);
+      }
+    }
+    if (taskSessionIds.isEmpty()) return Collections.EMPTY_LIST;
+    List<TaskSession> taskSessions = this.taskSessionService.getTaskSessionByIdList(taskSessionIds);
+    taskSessions.stream().filter(TaskSession::getDeleted).findAny().ifPresent(taskSession -> {
+      throw new AppException("No more tasks available, please try again later");
+    });
+
+    taskSessions.stream()
+        .filter(ts -> !Objects.equals(ts.getStatus(), TaskSession.TaskSessionStatus.PendingReview))
+        .findAny()
+        .ifPresent(taskSession -> {
+          throw new AppException("No more tasks available, please try again later");
+        });
+
+    List<ReviewSession> reviewSessions = taskSessions.stream()
+        .map(j -> this.reviewSessionService.createReviewSession(jobUser, j))
+        .toList();
+    reviewSessionService.updateTaskSessionCountOfReviewingSession(
+        taskSessions.stream().map(TaskSession::getId).toList(), job.getId());
+
+    return reviewSessions;
+  }
+
+  @Override
+  public TaskSubmitter taskSubmitter() {
+    return this;
+  }
+
+  @Override
+  public TaskDistributor taskDistributor() {
+    return this;
+  }
+
+  @Override
+  public TaskLifecycleManager taskLifecycleManager() {
+    return this;
+  }
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskCounter.java b/src/main/java/ai/saharaa/distribution/TaskCounter.java
new file mode 100644
index 00000000..215a5488
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskCounter.java
@@ -0,0 +1,38 @@
+package ai.saharaa.distribution;
+
+import java.util.Map;
+
+public interface TaskCounter {
+
+  void removeJobData(Long jobId);
+
+  Long getJobCount(Long jobId, String subKey);
+
+  Long setJobCount(Long jobId, String subKey, Long value);
+
+  Long hgetJobCount(Long jobId, String subKey, String hashKey);
+
+  Long hgetJobHashSize(Long jobId, String subKey);
+
+  void hsetJobCount(Long jobId, String subKey, String hashKey, Long value);
+
+  void hsetAllJobCount(Long jobId, String subKey, Map<String, Long> values);
+
+  Map<Object, Object> hgetAllJobCount(Long jobId, String subKey);
+
+  void hIncrementJobCount(Long jobId, String subKey, String hashKey, Integer value);
+
+  void hDecreaseJobCount(Long jobId, String subKey, String hashKey, Integer value);
+
+  void setJobCountIncrement(Long jobId, String subKey, Long value);
+
+  void setJobCountDecrease(Long jobId, String subKey, Long value);
+
+  Long getJobUserCount(Long jobId, Long userId, String subKey);
+
+  Long setJobUserCount(Long jobId, Long userId, String subKey, Long value);
+
+  void setJobUserCountIncrement(Long jobId, Long userId, String subKey, Long value);
+
+  void setJobUserCountDecrease(Long jobId, Long userId, String subKey, Long value);
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskDistributor.java b/src/main/java/ai/saharaa/distribution/TaskDistributor.java
new file mode 100644
index 00000000..50dcf810
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskDistributor.java
@@ -0,0 +1,14 @@
+package ai.saharaa.distribution;
+
+import ai.saharaa.model.*;
+import java.util.List;
+
+public interface TaskDistributor<T> {
+  List<T> assignTasks(
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      String ipAddress,
+      Boolean isTester);
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskLifecycleManager.java b/src/main/java/ai/saharaa/distribution/TaskLifecycleManager.java
new file mode 100644
index 00000000..c370e790
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskLifecycleManager.java
@@ -0,0 +1,11 @@
+package ai.saharaa.distribution;
+
+import ai.saharaa.model.*;
+
+// TaskLifecycleManager
+public interface TaskLifecycleManager {
+
+  void refreshTaskQueue(Job job, Integer repeatCount);
+
+  void initializeTaskQueue(Batch batch, BatchSetting batchSetting);
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskQueue.java b/src/main/java/ai/saharaa/distribution/TaskQueue.java
new file mode 100644
index 00000000..3d0a87e1
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskQueue.java
@@ -0,0 +1,18 @@
+package ai.saharaa.distribution;
+
+import java.util.List;
+import org.springframework.data.redis.core.RedisTemplate;
+
+public interface TaskQueue {
+  RedisTemplate<String, Object> get();
+
+  Long push(String key, Long value);
+
+  Long pushAll(String key, List<Long> value);
+
+  Long pop(String key);
+
+  List<Long> popList(String key, Long count);
+
+  Long length(String key);
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskSubmitter.java b/src/main/java/ai/saharaa/distribution/TaskSubmitter.java
new file mode 100644
index 00000000..ed3cc839
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskSubmitter.java
@@ -0,0 +1,17 @@
+package ai.saharaa.distribution;
+
+import ai.saharaa.model.*;
+
+public interface TaskSubmitter<T, R> {
+
+  R submitTasks(
+      T answers,
+      Job job,
+      Batch batch,
+      BatchSetting batchSetting,
+      JobUser jobUser,
+      Boolean isTester);
+
+  //  TaskSession submitTasks(
+  //      SubmitAnswerDTO answer, Long taskSessionId, Long userId, Boolean isTester);
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskVisitor.java b/src/main/java/ai/saharaa/distribution/TaskVisitor.java
new file mode 100644
index 00000000..ecb9e43a
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskVisitor.java
@@ -0,0 +1,10 @@
+package ai.saharaa.distribution;
+
+public interface TaskVisitor {
+
+  TaskSubmitter taskSubmitter();
+
+  TaskDistributor taskDistributor();
+
+  TaskLifecycleManager taskLifecycleManager();
+}
diff --git a/src/main/java/ai/saharaa/distribution/TaskVisitorProvider.java b/src/main/java/ai/saharaa/distribution/TaskVisitorProvider.java
new file mode 100644
index 00000000..60671e57
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TaskVisitorProvider.java
@@ -0,0 +1,8 @@
+package ai.saharaa.distribution;
+
+import ai.saharaa.model.*;
+
+public interface TaskVisitorProvider {
+
+  TaskVisitor getTaskVisitor(BatchSetting.DistributeType distributeType, JobUser.JobUserRole role);
+}
diff --git a/src/main/java/ai/saharaa/distribution/TypeProvider.java b/src/main/java/ai/saharaa/distribution/TypeProvider.java
new file mode 100644
index 00000000..87a7360b
--- /dev/null
+++ b/src/main/java/ai/saharaa/distribution/TypeProvider.java
@@ -0,0 +1,11 @@
+package ai.saharaa.distribution;
+
+import ai.saharaa.model.BatchSetting;
+import ai.saharaa.model.JobUser;
+import java.util.List;
+
+public interface TypeProvider {
+  List<BatchSetting.DistributeType> getDistributionTypes();
+
+  List<JobUser.JobUserRole> getRoles();
+}
diff --git a/src/main/java/ai/saharaa/dto/job/GroupByTaskIdAndCountResultDTO.java b/src/main/java/ai/saharaa/dto/job/GroupByTaskIdAndCountResultDTO.java
new file mode 100644
index 00000000..ae731a42
--- /dev/null
+++ b/src/main/java/ai/saharaa/dto/job/GroupByTaskIdAndCountResultDTO.java
@@ -0,0 +1,11 @@
+package ai.saharaa.dto.job;
+
+import lombok.Builder;
+import lombok.Data;
+
+@Data
+@Builder
+public class GroupByTaskIdAndCountResultDTO {
+  private Long taskId;
+  private Long count;
+}
diff --git a/src/main/java/ai/saharaa/dto/job/GroupByTaskSessionIdAndCountResultDTO.java b/src/main/java/ai/saharaa/dto/job/GroupByTaskSessionIdAndCountResultDTO.java
new file mode 100644
index 00000000..86de5bfc
--- /dev/null
+++ b/src/main/java/ai/saharaa/dto/job/GroupByTaskSessionIdAndCountResultDTO.java
@@ -0,0 +1,11 @@
+package ai.saharaa.dto.job;
+
+import lombok.Builder;
+import lombok.Data;
+
+@Data
+@Builder
+public class GroupByTaskSessionIdAndCountResultDTO {
+  private Long taskSessionId;
+  private Long count;
+}
diff --git a/src/main/java/ai/saharaa/mappers/JobTaskMapper.java b/src/main/java/ai/saharaa/mappers/JobTaskMapper.java
index 6af01a92..c1098930 100644
--- a/src/main/java/ai/saharaa/mappers/JobTaskMapper.java
+++ b/src/main/java/ai/saharaa/mappers/JobTaskMapper.java
@@ -19,6 +19,8 @@ public interface JobTaskMapper extends MPJBaseMapper<JobTask> {
   List<JobTask> takeJobForJobUserAvoidSkipped(
       Long jobId, Long userId, Integer limit, Integer count);
 
+  List<Long> refreshTaskQueue(Long jobId, Integer repeatCount, Integer limit);
+
   Long countMyAvailableLeftTask(Long jobId, Long userId, Integer limit);
 
   Long unreviewedTaskCount(Long jobId);
diff --git a/src/main/java/ai/saharaa/mappers/TaskSessionMapper.java b/src/main/java/ai/saharaa/mappers/TaskSessionMapper.java
index 4066f80d..9c66955e 100644
--- a/src/main/java/ai/saharaa/mappers/TaskSessionMapper.java
+++ b/src/main/java/ai/saharaa/mappers/TaskSessionMapper.java
@@ -1,5 +1,6 @@
 package ai.saharaa.mappers;
 
+import ai.saharaa.dto.job.GroupByTaskSessionIdAndCountResultDTO;
 import ai.saharaa.dto.job.GroupByUserIdAndCountResultDTO;
 import ai.saharaa.dto.task.TaskSessionGroupedTaskIdDTO;
 import ai.saharaa.model.TaskSession;
@@ -16,6 +17,14 @@ public interface TaskSessionMapper extends MPJBaseMapper<TaskSession> {
   List<TaskSession> takeReviewJobsForUser(
       Long jobId, Long uid, Integer sessionCount, Integer repeatCount);
 
+  List<Long> refreshTaskQueue(Long jobId, Integer repeatCount, Integer limit);
+
+  @Select(
+      "select task_session_id, count(id) from review_session where job_id = #{jobId} and task_session_id in (${pendingIds}) and deleted = false "
+          + "group by task_session_id")
+  List<GroupByTaskSessionIdAndCountResultDTO> refreshTaskQueueForReview(
+      Long jobId, String pendingIds);
+
   Long countReviewJobsForUser(Long jobId, Long uid, Integer repeatCount);
 
   List<TaskSession> takeReviewJobForJobUser(Long jobId, Long jobUserId, Integer maxSession);
diff --git a/src/main/java/ai/saharaa/mappers/sessions/TaskSessionDisCountMapper.java b/src/main/java/ai/saharaa/mappers/sessions/TaskSessionDisCountMapper.java
index c265ffb0..0e785624 100644
--- a/src/main/java/ai/saharaa/mappers/sessions/TaskSessionDisCountMapper.java
+++ b/src/main/java/ai/saharaa/mappers/sessions/TaskSessionDisCountMapper.java
@@ -1,6 +1,14 @@
 package ai.saharaa.mappers.sessions;
 
+import ai.saharaa.dto.job.GroupByTaskIdAndCountResultDTO;
 import ai.saharaa.model.TaskSessionDisCount;
 import com.github.yulichang.base.MPJBaseMapper;
+import java.util.List;
+import org.apache.ibatis.annotations.Select;
 
-public interface TaskSessionDisCountMapper extends MPJBaseMapper<TaskSessionDisCount> {}
+public interface TaskSessionDisCountMapper extends MPJBaseMapper<TaskSessionDisCount> {
+
+  @Select(
+      "select task_id, count(id) as count from task_session where job_id = #{jobId} and deleted = false group by task_id")
+  List<GroupByTaskIdAndCountResultDTO> countTaskIdGroupByCount(Long jobId);
+}
diff --git a/src/main/java/ai/saharaa/services/BatchService.java b/src/main/java/ai/saharaa/services/BatchService.java
index a8764ffd..b83406a2 100644
--- a/src/main/java/ai/saharaa/services/BatchService.java
+++ b/src/main/java/ai/saharaa/services/BatchService.java
@@ -311,6 +311,19 @@ public class BatchService extends ServiceImpl<BatchMapper, Batch> {
       });
     }
 
+    BatchSetting batchSetting = Objects.equals(batch.getLabelType(), Batch.BatchLabelType.CONTEXT)
+            || Objects.equals(batch.getLabelType(), Batch.BatchLabelType.NO_NEED_TO_UPLOAD)
+        ? BatchSetting.builder()
+            .distributeType(BatchSetting.DistributeType.SINGLE)
+            .batchId(batchId)
+            .build()
+        : BatchSetting.builder()
+            .distributeType(BatchSetting.DistributeType.RAW)
+            .batchId(batchId)
+            .build();
+
+    this.batchDao.saveBatchSetting(batchSetting);
+
     return this.getBatchDetails(batchId);
   }
 
@@ -1091,6 +1104,7 @@ public class BatchService extends ServiceImpl<BatchMapper, Batch> {
             .orderByDesc(Batch::getId));
   }
 
+  @Deprecated
   public Batch launchBatch(Batch batch) {
     batch.setStatus(Batch.BatchStatus.ASSIGN_JOBS);
     batchMapper.update(
@@ -1110,27 +1124,25 @@ public class BatchService extends ServiceImpl<BatchMapper, Batch> {
         batchSetting.setDistributeType(BatchSetting.DistributeType.SINGLE);
         batchDao.updateBatchSettingById(batchSetting);
         redisCache.set(
-            "jobOpt_${jobId}:distributeType", BatchSetting.DistributeType.SINGLE.getValue());
+            "job_${jobId}:distributeType", BatchSetting.DistributeType.SINGLE.getValue());
         redisCache.set(
-            "jobOpt_${jobId}:totalCount", batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
+            "job_${jobId}:totalCount", batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
         TaskList taskList = taskListDao
             .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
             .first();
         var task = taskDao.getFirstTaskInTaskList(taskList.getId());
         task.ifPresent(taskEx -> {
-          redisCache.set("jobOpt_${jobId}:taskId", taskEx.getId());
+          redisCache.set("job_${jobId}:taskId", taskEx.getId());
         });
       } else { // distributeType is: BatchSetting.DistributeType.RAW
         var batchSetting = batchDao.getBatchSettingById(batch.getId());
+        redisCache.set("job_${jobId}:distributeType", BatchSetting.DistributeType.RAW.getValue());
         redisCache.set(
-            "jobOpt_${jobId}:distributeType", BatchSetting.DistributeType.RAW.getValue());
-        redisCache.set(
-            "jobOpt_${jobId}:singleRepeat",
-            batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
+            "job_${jobId}:singleRepeat", batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
         var taskIdList =
             jobTaskDao.selectTasksInJobInLimit(jobId, Math.min(7500, job.getAssignDataVolume()));
         redisCache.rSetAll(
-            "jobOpt_${jobId}:cachedTaskIds",
+            "job_${jobId}:cachedTaskIds",
             taskIdList.map(id -> (Object) id.toString()).toList());
       }
     }
diff --git a/src/main/java/ai/saharaa/services/HoneyPotService.java b/src/main/java/ai/saharaa/services/HoneyPotService.java
index c74b7d16..b1886108 100644
--- a/src/main/java/ai/saharaa/services/HoneyPotService.java
+++ b/src/main/java/ai/saharaa/services/HoneyPotService.java
@@ -57,11 +57,7 @@ import akka.actor.ActorRef;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.google.common.base.Suppliers;
-import java.util.ArrayList;
-import java.util.Arrays;
-import java.util.List;
-import java.util.Objects;
-import java.util.Optional;
+import java.util.*;
 import java.util.function.Function;
 import java.util.function.Supplier;
 import org.apache.commons.collections4.CollectionUtils;
@@ -418,114 +414,113 @@ public class HoneyPotService {
   public void adaptHoneyPotSession(
       List<TakeJobResultDTO> taskSessionResults,
       Batch batch,
-      Long curId,
-      Long jobUserId,
+      JobUser jobUser,
       Boolean onlyAdaptExist) {
     var taskSessionIdList =
         taskSessionResults.map(t -> t.getTaskSession().getId()).toList();
+    var hpSessionMap =
+        this.adaptHoneyPotSessionWithSessionIds(taskSessionIdList, batch, jobUser, onlyAdaptExist);
+    if (hpSessionMap == null) return;
+    taskSessionResults.forEach(ts -> {
+      if (hpSessionMap.containsKey(ts.getTaskSession().getId())) {
+        var hpSession = hpSessionMap.get(ts.getTaskSession().getId());
+        var taskQuestion =
+            taskQuestionDao.getTaskQuestionById(hpSession.getQuestionId()).get();
+        taskQuestion.setAnswer("");
+        var hpData = HoneyPotDataDTO.builder()
+            .question(taskQuestion)
+            .honeyPotSession(hpSession)
+            .build();
+        ts.setHoneyPotData(hpData);
+      }
+    });
+  }
+
+  public Map<Long, HoneyPotSession> adaptHoneyPotSessionWithSessionIds(
+      List<Long> taskSessionIdList, Batch batch, JobUser jobUser, Boolean onlyAdaptExist) {
     var hpSessions = honeyPotDao.getSessionByTaskSessionIdList(taskSessionIdList);
     if (hpSessions.isEmpty() && !onlyAdaptExist) {
       var batchSetting = batchDao.getBatchSettingById(batch.getId());
       var hpPercentageNumber = batchSetting.getHoneyPotNumberPerSubmit() / 100;
-      var taskSessionResultShouldAddHpList = new ArrayList<TakeJobResultDTO>();
-      taskSessionResults.forEach(takeJobResultDTO -> {
+      var taskSessionResultShouldAddHpList = new ArrayList<Long>();
+      taskSessionIdList.forEach(sessionId -> {
         // taskSession will always exist, but reviewSession not.
-        var sessionId = takeJobResultDTO.getTaskSession().getId();
         var sessionIdAddedWithStaticRandomNumber =
-            sessionId + batch.getId() * takeJobResultDTO.getTaskSession().getJobId();
+            sessionId + batch.getId() * jobUser.getTaskListSessionId();
         var mathRemainder = (int) (sessionIdAddedWithStaticRandomNumber % 100);
         var mathRemainderIndexInRandomOrder = HONEY_POT_RANDOM_ORDER.indexOf(mathRemainder);
         if (mathRemainderIndexInRandomOrder < hpPercentageNumber) {
-          taskSessionResultShouldAddHpList.add(takeJobResultDTO);
+          taskSessionResultShouldAddHpList.add(sessionId);
         }
       });
       if (!taskSessionResultShouldAddHpList.isEmpty()) {
-        var taskSessionIdsShouldAddedHp = taskSessionResultShouldAddHpList
-            .map(r -> r.getTaskSession().getId())
-            .toList();
         var newHPSessions =
             transactionUtils.executeWithTransaction(() -> generateHPSessionForTaskSessionId(
-                taskSessionIdsShouldAddedHp,
+                taskSessionResultShouldAddHpList,
                 batchSetting.getHoneyPotBatches(),
                 taskSessionResultShouldAddHpList.size(),
-                curId,
-                jobUserId));
+                jobUser.getUserId(),
+                jobUser.getId()));
         hpSessions.addAll(newHPSessions);
       }
     }
-    if (hpSessions.isEmpty()) return;
-    var hpSessionMap =
-        hpSessions.stream().toMap(HoneyPotSession::getTaskSessionId, Function.identity());
-    taskSessionResults.forEach(ts -> {
-      if (hpSessionMap.containsKey(ts.getTaskSession().getId())) {
-        var hpSession = hpSessionMap.get(ts.getTaskSession().getId());
+    if (hpSessions.isEmpty()) return null;
+    return hpSessions.stream().toMap(HoneyPotSession::getTaskSessionId, Function.identity());
+  }
+
+  public void adaptHpReviewSession(
+      List<TakeJobResultDTO> takeJobResults, Batch batch, JobUser jobUser, Boolean onlyAdaptExist) {
+    var reviewSessionIds = takeJobResults.map(t -> t.getReviewSession().getId()).toList();
+    var hpReviewSessionMap = this.adaptHpReviewSessionByReviewSessionIds(
+        reviewSessionIds, batch, jobUser, onlyAdaptExist);
+    takeJobResults.forEach(tjr -> {
+      if (hpReviewSessionMap.containsKey(tjr.getReviewSession().getId())) {
+        var hpReviewSession = hpReviewSessionMap.get(tjr.getReviewSession().getId());
         var taskQuestion =
-            taskQuestionDao.getTaskQuestionById(hpSession.getQuestionId()).get();
-        taskQuestion.setAnswer("");
+            taskQuestionDao.getTaskQuestionById(hpReviewSession.getQuestionId()).get();
+        taskQuestion.setAnswer(hpReviewSession.getAnswer());
         var hpData = HoneyPotDataDTO.builder()
             .question(taskQuestion)
-            .honeyPotSession(hpSession)
+            .hpReviewSession(hpReviewSession)
             .build();
-        ts.setHoneyPotData(hpData);
+        tjr.setHoneyPotData(hpData);
       }
     });
   }
 
-  public void adaptHpReviewSession(
-      List<TakeJobResultDTO> takeJobResults,
-      Batch batch,
-      Long curId,
-      Long jobUserId,
-      Boolean onlyAdaptExist) {
-    var reviewSessionIds = takeJobResults.map(t -> t.getReviewSession().getId()).toList();
+  public Map<Long, HoneyPotReviewSession> adaptHpReviewSessionByReviewSessionIds(
+      List<Long> reviewSessionIds, Batch batch, JobUser jobUser, Boolean onlyAdaptExist) {
     var hpReviewSessions = honeyPotDao.getReviewSessionByReviewSessionIdList(reviewSessionIds);
     if (hpReviewSessions.isEmpty() && !onlyAdaptExist) {
       var batchSetting = batchDao.getBatchSettingById(batch.getId());
       var hpPercentageNumber = batchSetting.getHoneyPotNumberPerSubmitReview() / 100;
-      var taskSessionResultShouldAddHpList = new ArrayList<TakeJobResultDTO>();
-      takeJobResults.forEach(takeJobResultDTO -> {
+      var taskSessionResultShouldAddHpList = new ArrayList<Long>();
+      reviewSessionIds.forEach(sessionId -> {
         // taskSession will always exist, but reviewSession not.
-        var sessionId = takeJobResultDTO.getReviewSession().getId();
         var sessionIdAddedWithStaticRandomNumber =
-            sessionId + batch.getId() * takeJobResultDTO.getTaskSession().getJobId();
+            sessionId + batch.getId() * jobUser.getTaskListSessionId();
         var mathRemainder = (int) (sessionIdAddedWithStaticRandomNumber % 100);
         var mathRemainderIndexInRandomOrder = HONEY_POT_RANDOM_ORDER.indexOf(mathRemainder);
         if (mathRemainderIndexInRandomOrder <= hpPercentageNumber) {
-          taskSessionResultShouldAddHpList.add(takeJobResultDTO);
+          taskSessionResultShouldAddHpList.add(sessionId);
         }
       });
       if (!taskSessionResultShouldAddHpList.isEmpty()) {
-        var taskSessionIdsShouldAddedHp = taskSessionResultShouldAddHpList
-            .map(r -> r.getReviewSession().getId())
-            .toList();
         var newHPSessions =
             transactionUtils.executeWithTransaction(() -> generateHPSessionForReviewSessionId(
-                taskSessionIdsShouldAddedHp,
+                taskSessionResultShouldAddHpList,
                 batchSetting.getHoneyPotReviewBatches(),
                 taskSessionResultShouldAddHpList.size(),
-                curId,
-                jobUserId));
+                jobUser.getUserId(),
+                jobUser.getId()));
         hpReviewSessions.addAll(newHPSessions);
       }
     }
     if (hpReviewSessions.isEmpty()) {
-      return;
+      return null;
     }
-    var hpReviewSessionMap = hpReviewSessions.stream()
+    return hpReviewSessions.stream()
         .toMap(HoneyPotReviewSession::getReviewSessionId, Function.identity());
-    takeJobResults.forEach(tjr -> {
-      if (hpReviewSessionMap.containsKey(tjr.getReviewSession().getId())) {
-        var hpReviewSession = hpReviewSessionMap.get(tjr.getReviewSession().getId());
-        var taskQuestion =
-            taskQuestionDao.getTaskQuestionById(hpReviewSession.getQuestionId()).get();
-        taskQuestion.setAnswer(hpReviewSession.getAnswer());
-        var hpData = HoneyPotDataDTO.builder()
-            .question(taskQuestion)
-            .hpReviewSession(hpReviewSession)
-            .build();
-        tjr.setHoneyPotData(hpData);
-      }
-    });
   }
 
   // Use suppliers so computation can be delayed. Results are memoized while a supplier is being
diff --git a/src/main/java/ai/saharaa/services/JobService.java b/src/main/java/ai/saharaa/services/JobService.java
index d1538a9a..8573edc0 100644
--- a/src/main/java/ai/saharaa/services/JobService.java
+++ b/src/main/java/ai/saharaa/services/JobService.java
@@ -1,6 +1,7 @@
 package ai.saharaa.services;
 
 import static ai.saharaa.daos.activity.PlatformActivityDao.PLATFORM_ADDRESS;
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.*;
 import static ai.saharaa.enums.MarketSortType.Earliest;
 import static ai.saharaa.enums.MarketSortType.EndingSoon;
 import static ai.saharaa.enums.MarketSortType.MaxEarning;
@@ -57,6 +58,7 @@ import ai.saharaa.daos.activity.PlatformActivityDao;
 import ai.saharaa.daos.season.SeasonDao;
 import ai.saharaa.daos.season.SeasonJobDao;
 import ai.saharaa.daos.stat.UserJobStatDao;
+import ai.saharaa.distribution.CommonCounterManager;
 import ai.saharaa.dto.batch.BatchDetailsDTO;
 import ai.saharaa.dto.job.CreateInvitationDTO;
 import ai.saharaa.dto.job.InviteJobUserDTO;
@@ -87,32 +89,8 @@ import ai.saharaa.mappers.ProjectMapper;
 import ai.saharaa.mappers.TaskMapper;
 import ai.saharaa.mappers.TaskSessionMapper;
 import ai.saharaa.mappers.UserMapper;
-import ai.saharaa.model.Batch;
-import ai.saharaa.model.BatchAccessRequirement;
-import ai.saharaa.model.BatchNDA;
-import ai.saharaa.model.BatchSetting;
-import ai.saharaa.model.ExamSession;
-import ai.saharaa.model.Job;
-import ai.saharaa.model.JobAnnotatorStatisticDTO;
-import ai.saharaa.model.JobInvitation;
-import ai.saharaa.model.JobStatisticDTO;
-import ai.saharaa.model.JobTask;
-import ai.saharaa.model.JobTaskReportItemDTO;
-import ai.saharaa.model.JobUser;
-import ai.saharaa.model.NDASignRecord;
-import ai.saharaa.model.Node;
-import ai.saharaa.model.PageResult;
-import ai.saharaa.model.PreTaskExam;
-import ai.saharaa.model.Project;
-import ai.saharaa.model.ReviewSession;
-import ai.saharaa.model.SpotSession;
-import ai.saharaa.model.Task;
-import ai.saharaa.model.TaskAuditForNodeManagerDTO;
-import ai.saharaa.model.TaskList;
-import ai.saharaa.model.TaskSession;
+import ai.saharaa.model.*;
 import ai.saharaa.model.TaskSession.TaskSessionStatus;
-import ai.saharaa.model.User;
-import ai.saharaa.model.UserRequirementsStatus;
 import ai.saharaa.model.activity.PlatformActivity;
 import ai.saharaa.model.stat.NodeStat;
 import ai.saharaa.model.stat.TaskSessionUserStat;
@@ -144,6 +122,7 @@ import java.util.Optional;
 import java.util.Set;
 import java.util.function.Function;
 import java.util.stream.Collectors;
+import java.util.stream.Stream;
 import org.apache.commons.lang3.StringUtils;
 import org.bouncycastle.jcajce.provider.digest.Blake3;
 import org.bouncycastle.util.encoders.Hex;
@@ -161,6 +140,7 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
   private final TaskMapper taskMapper;
   private final TaskSessionMapper taskSessionMapper;
   private final IGlobalCache cache;
+  private final CommonCounterManager counterManager;
 
   private final ReviewSessionDao reviewSessionDao;
   private final JobTaskReportDao jobTaskReportDao;
@@ -232,6 +212,7 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
       NotificationService notificationService,
       TaskDao taskDao,
       IGlobalCache cache,
+      CommonCounterManager counterManager,
       BatchService batchService,
       BatchAccessRequirementDao batchAccessRequirementDao,
       JobTaskReportDao jobTaskReportDao,
@@ -249,6 +230,7 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
     this.jobDao = jobDao;
     this.userDao = userDao;
     this.cache = cache;
+    this.counterManager = counterManager;
     this.batchMapper = batchMapper;
     this.taskMapper = taskMapper;
     this.spotSessionDao = spotSessionDao;
@@ -547,8 +529,7 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
   public List<MarketJobDTO> getSpecialTaskList(Long curId, String achieveSymbol) {
     var cacheKey =
         String.format(Constants.SpecialTaskAchievementSymbol.CACHE_KEY_FORMATTER, achieveSymbol);
-    var specialTaskList =
-        getSpecialTaskListByStatus(cacheKey, achieveSymbol, Arrays.asList(WORKING));
+    var specialTaskList = getSpecialTaskListByStatus(cacheKey, achieveSymbol, List.of(WORKING));
     if (specialTaskList.isEmpty()) {
       return new ArrayList<>();
     }
@@ -702,10 +683,8 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
       }
       var leftTask = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
           ? getLeftAnnoTaskCountForSingle(j.getId(), curId, batchSetting)
-          : getLeftAnnoTaskCount2(
-              j.getId(),
-              j.getAssignDataVolume(),
-              batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
+          : getPublicLeftAnnoTaskCountForRaw(
+              j.getId(), batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
       result.setJobLeftTaskCount(leftTask);
       if (finalBanExpireDuration != null) {
         result.setTempBanExpire(finalBanExpireDuration);
@@ -966,37 +945,83 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
     return res;
   }
 
-  public Long getLeftAnnoTaskCount3(Long jobId, Integer assignDataVolume, Integer repeatCount) {
+  public Long getLeftAnnoTaskCount3(
+      Integer assignDataVolume, Integer repeatCount, Long jobCompleteCount, Long myPendingCount) {
     if (repeatCount == null) repeatCount = 1;
-    var pCount = taskSessionDao.countApprovedTasksByJobId2(jobId);
-    return (assignDataVolume.longValue() * repeatCount) - pCount;
+    return (assignDataVolume.longValue() * repeatCount) - jobCompleteCount - myPendingCount;
   }
 
   public Long getLeftAnnoTaskCount2(Long jobId, Integer assignDataVolume, Integer repeatCount) {
     if (repeatCount == null) repeatCount = 1;
-    var pCount = taskSessionDao.countTasksByJobId(jobId);
+    var pCount = counterManager.getJobCount(
+        jobId, ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value);
     return (assignDataVolume.longValue() * repeatCount) - pCount;
   }
 
+  public Long getLeftAnnoTaskCountForRaw(
+      Long jobId, Long userId, BatchSetting batchSetting, List<Long> skippedTaskIds) {
+    //    var key = String.format("job_%d:leftCountForUser:%d", jobId, userId);
+    //    if (cache.hasKey(key)) return Long.parseLong(cache.get(key).toString());
+    var myDidTaskIds = taskSessionDao.getMyEffectTaskSessionTaskIds(jobId, userId);
+    var myExcludeIds =
+        Stream.concat(myDidTaskIds.stream(), skippedTaskIds.stream()).toSet();
+    var maxRepeatCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
+    var publicLeftFromJob = new ArrayList<>(getPublicLeftAnnoTaskForRaw(jobId, maxRepeatCount));
+    if (!myExcludeIds.isEmpty()) {
+      publicLeftFromJob.removeAll(myExcludeIds);
+    }
+    var myLeftInJobCount = (long) publicLeftFromJob.size();
+    var pCount = counterManager.getJobUserCount(jobId, userId, SUBMITTED_COUNT.value);
+
+    var userMaxWorkload = batchSetting.getWorkloadMaxPerJobUser();
+    if (null == userMaxWorkload || userMaxWorkload <= 0) {
+      //      cache.set(key, myLeftInJobCount, 30);
+      return myLeftInJobCount;
+    }
+    var workloadLeft = userMaxWorkload - pCount - skippedTaskIds.size();
+    var res = Math.min(workloadLeft, workloadLeft);
+    //    cache.set(key, res, 60);
+    return res;
+  }
+
+  public Long getPublicLeftAnnoTaskCountForRaw(Long jobId, Integer maxRepeatCount) {
+    return (long) getPublicLeftAnnoTaskForRaw(jobId, maxRepeatCount).size();
+  }
+
+  public List<Long> getPublicLeftAnnoTaskForRaw(Long jobId, Integer maxRepeatCount) {
+    var key = String.format("job_%d:publicLeftTaskCount", jobId);
+    if (cache.hasKey(key))
+      return Arrays.stream(cache.get(key).toString().split(","))
+          .map(Long::parseLong)
+          .toList();
+    var disCountMap = counterManager.hgetAllJobCount(
+        jobId, ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.TASK_COUNTS.value);
+    var res = disCountMap
+        .entrySet()
+        .filter(en -> Long.parseLong(en.getValue().toString()) < maxRepeatCount)
+        .map(en -> Long.parseLong(en.getKey().toString()))
+        .toList();
+    cache.set(key, String.join(",", res.mapToList(Object::toString)), 15);
+    return res;
+  }
+
   public Long getLeftAnnoTaskCountForSingle(Long jobId, Long userId, BatchSetting batchSetting) {
-    var taskId = jobTaskDao.getSingleDatapointJobTaskByJobId(jobId).first().getTaskId();
-    var pCount =
-        taskSessionDao.countAllTaskSessionsByTaskIdAndJobIdAndUserId(taskId, jobId, userId);
-    var taskSessionsLeftCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint()
-        - taskSessionDao.countTasksByJobId(jobId);
     var userMaxWorkload = batchSetting.getWorkloadMaxPerJobUser();
+    var taskSessionsLeftCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint()
+        - counterManager.getJobCount(
+            jobId,
+            ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value);
     if (null == userMaxWorkload || userMaxWorkload <= 0) {
       return taskSessionsLeftCount;
     }
+    var pCount = counterManager.getJobUserCount(jobId, userId, COMPLETE_COUNT.value);
     return Math.min(taskSessionsLeftCount, userMaxWorkload - pCount);
   }
 
-  public Long getLeftReviewTaskCountForSingle(Long jobId, Long userId, BatchSetting batchSetting) {
+  public Long getLeftReviewTaskCountForSingle(
+      Long jobId, Long doneAnnotation, BatchSetting batchSetting, Long myReviewedCount) {
     var allAnnoTimes = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
-    var doneAnnotation = taskSessionDao.countReviewedSessionsByJobId(jobId);
-    var myReviewedReview = reviewSessionDao.countUserSessionInJobWithStatus(
-        jobId, userId, ReviewSession.ReviewSessionStatus.FINISH);
-    return allAnnoTimes - doneAnnotation - myReviewedReview;
+    return allAnnoTimes - doneAnnotation - myReviewedCount;
   }
 
   public Long getMyPendingTaskCount(Long jobId, Long userId) {
@@ -1680,7 +1705,8 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
         return true;
       }
       var examType = b.getExamType();
-      // Regular exam size is the number of data points; QA exam size is the number of questions.
+      // Regular exam size is the number of data points; QA exam size is the number of
+      // questions.
       switch (examType) {
         case REGULAR -> {
           var examTaskLists = taskListDao.listTaskListsByBatchIdAndType(
@@ -2290,8 +2316,8 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
         batchAccessRequirementDao.getBatchAccessRequirementsByBatchId(job.getBatchId());
     var leftTask = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
         ? getLeftAnnoTaskCountForSingle(id, userId, batchSetting)
-        : getLeftAnnoTaskCount2(
-            id, job.getAssignDataVolume(), batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
+        : getPublicLeftAnnoTaskCountForRaw(
+            id, batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
     res.setJobLeftTaskCount(leftTask);
     res.setRequirements(requirements);
     fetchUserRequirementStatus(userId, requirements, res, job.getId());
@@ -2345,34 +2371,23 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
     res.setBonusEarning(bonusPoints);
 
     if (ju.getRole().equals(LABELER)) {
-      var skippedCount = skipTaskSessionDao.getSkippedCount(id, userId);
+      var approvedCount = counterManager.getJobUserCount(j.getId(), userId, CORRECT_COUNT.value);
+      var completeCount = counterManager.getJobUserCount(j.getId(), userId, COMPLETE_COUNT.value);
+      var submittedCount = counterManager.getJobUserCount(j.getId(), userId, SUBMITTED_COUNT.value);
+
+      var havePendingCount = taskSessionDao.countMyPendingTasksByJobId(id, userId);
+      var skippedRecs = skipTaskSessionDao.getUserSkippedTaskSessionIdListInJob(id, userId);
+      var skippedCount = skippedRecs.size();
       var jobLeftTaskCount = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
           ? getLeftAnnoTaskCountForSingle(id, userId, batchSetting)
-          : jobTaskDao.countMyLeftTaskCount(
-              ju.getTaskListSessionId(),
-              ju.getUserId(),
-              batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
-      var pinnedCount = taskSessionDao.countUserPinnedSessionInJob(id, userId);
-      res.setPinCount(pinnedCount);
-      var approvedCount = taskSessionDao.countUserApprovedSessionInJob(id, userId);
+          : getLeftAnnoTaskCountForRaw(
+              id, userId, batchSetting, skippedRecs.mapToList(SkipTaskSession::getTaskId));
       res.setApprovedCount(approvedCount);
-      var waitingForMajorityVoteCount =
-          taskSessionDao.countUserWaitingForMajorityVoteInJob(id, userId);
+      var waitingForMajorityVoteCount = submittedCount - completeCount - havePendingCount;
       res.setWaitingReviewCount(waitingForMajorityVoteCount);
-      var rejectedByMajorityVoteCount =
-          taskSessionDao.countUserRejectedByMajorityVoteSessionsInJob(id, userId);
-      var rejectedByPipelineCount = taskSessionDao.countUserRejectedByPplSessionsInJob(id, userId);
-      var pendingPipelineCount = taskSessionDao.countPendingPipeline(id, userId);
-
-      res.setRevisedCount(rejectedByMajorityVoteCount);
-      var submittedCount = waitingForMajorityVoteCount
-          + approvedCount
-          + rejectedByMajorityVoteCount
-          + rejectedByPipelineCount
-          + pendingPipelineCount;
+      res.setRevisedCount(completeCount - approvedCount);
       res.setSubmittedCount(submittedCount);
       if (userTempBan.isEmpty()) {
-        var havePendingCount = taskSessionDao.countMyPendingTasksByJobId(id, userId);
         if (havePendingCount <= 0) {
           var reviseCount = getReviseCount(ju.getTaskListSessionId(), ju.getUserId());
           res.setReviseCountForLabeler(reviseCount);
@@ -2382,13 +2397,12 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
         var rejectedByMajorityVoteAndWaitingRevisionCount =
             taskSessionDao.countUserRejectedByMajorityVoteAndWaitingRevisionSessionsInJob(
                 id, userId);
-        res.setJobLeftTaskCount(jobLeftTaskCount
-            + rejectedByMajorityVoteAndWaitingRevisionCount
-            + havePendingCount
-            + pinnedCount);
+        res.setJobLeftTaskCount(
+            jobLeftTaskCount + rejectedByMajorityVoteAndWaitingRevisionCount + havePendingCount);
+        var rejectedByMajorityVoteCount =
+            taskSessionDao.countUserRejectedByMajorityVoteSessionsInJob(id, userId);
         var skipQuota = Math.floor(submittedCount / 50f) * 5 + rejectedByMajorityVoteCount + 5;
-        res.setLeftSkipQuota(
-            Double.valueOf(skipQuota).intValue() - Long.valueOf(skippedCount).intValue());
+        res.setLeftSkipQuota(Double.valueOf(skipQuota).intValue() - skippedCount);
       }
 
       Long basePoints = sessionCount == null ? 0 : sessionCount * b.getAnnotatingPrice();
@@ -2397,32 +2411,38 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
           + bonusPoints);
       res.setWorkloadType(workloadLimitService.getJobUserWorkloadType(ju, LABELER, userIp));
     } else if (ju.getRole().equals(REVIEWER)) {
-      var leftCount = taskSessionDao.countReviewJobsForUser(
-          ju.getTaskListSessionId(), ju.getUserId(), b.getReviewingTimesReviewPerDatapoint());
+      var approvedCount = counterManager.getJobUserCount(j.getId(), userId, CORRECT_COUNT.value);
+      var completeCount = counterManager.getJobUserCount(j.getId(), userId, COMPLETE_COUNT.value);
+      var jobCompleteCount = counterManager.getJobCount(
+          j.getId(),
+          ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value);
+      var submittedCount = counterManager.getJobUserCount(j.getId(), userId, SUBMITTED_COUNT.value);
+
       var myPendingCount = reviewSessionDao.countUserSessionInJobWithStatus(
           ju.getTaskListSessionId(), ju.getUserId(), ReviewSession.ReviewSessionStatus.PENDING);
+      var waitingCount = submittedCount - completeCount - myPendingCount;
+
+      var leftCount = taskSessionDao.countReviewJobsForUser(
+          ju.getTaskListSessionId(), ju.getUserId(), b.getReviewingTimesReviewPerDatapoint());
       res.setJobLeftTaskCount(leftCount + myPendingCount);
-      var approvedCount = reviewSessionDao.countUserApprovedSessionInJob(id, userId);
       res.setApprovedCount(approvedCount);
-      var wrongCount = reviewSessionDao.countUserWrongSessionInJob(id, userId);
+      var wrongCount = completeCount - approvedCount;
       res.setRevisedCount(wrongCount);
       if (userTempBan.isEmpty()) {
         var leftTask = batchSetting.getDistributeType() == BatchSetting.DistributeType.SINGLE
-            ? getLeftReviewTaskCountForSingle(id, userId, batchSetting)
+            ? getLeftReviewTaskCountForSingle(
+                id, jobCompleteCount, batchSetting, submittedCount - completeCount)
             : getLeftAnnoTaskCount3(
-                id,
                 j.getAssignDataVolume(),
-                batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
+                batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
+                jobCompleteCount,
+                submittedCount - completeCount);
         res.setJobLeftAnnotateCountForReviewer(leftTask);
-        var waitingCount = reviewSessionDao.countUserSessionInJobWithStatus(
-            id, userId, ReviewSession.ReviewSessionStatus.FINISH);
         res.setWaitingReviewCount(waitingCount);
-        var submittedCount = waitingCount + approvedCount + wrongCount;
         res.setSubmittedCount(submittedCount);
         var skipQuota = Math.floor(submittedCount / 50f) * 5 + wrongCount + 5;
         var skippedCount = skipTaskSessionDao.getSkippedCount(id, userId);
-        res.setLeftSkipQuota(
-            Double.valueOf(skipQuota).intValue() - Long.valueOf(skippedCount).intValue());
+        res.setLeftSkipQuota(Double.valueOf(skipQuota).intValue() - skippedCount.intValue());
       }
       Long basePoints = sessionCount == null ? 0 : sessionCount * b.getReviewingPrice();
       res.setEarning((res.getPointAmplifierEarning() == null ? 0 : res.getPointAmplifierEarning())
@@ -2528,14 +2548,26 @@ public class JobService extends ServiceImpl<JobMapper, Job> {
     return jobDao.getJobById(jobId);
   }
 
-  public List<Job> getIndividualJobByStatus(Job.JobStatus status) {
+  public List<Job> getIndividualJobByStatus(List<Job.JobStatus> statuses) {
     return jobMapper.selectList(new QueryWrapper<Job>()
         .lambda()
         .eq(Job::getJobType, Job.JobType.INDIVIDUAL)
-        .eq(Job::getStatus, status)
+        .in(Job::getStatus, statuses)
         .eq(Job::getDeleted, false));
   }
 
+  public List<Job> reQueue() {
+    return jobMapper.selectJoinList(
+        Job.class,
+        JoinWrappers.lambda(Job.class)
+            .selectAll(Job.class)
+            .leftJoin(Batch.class, Batch::getId, Job::getBatchId)
+            .eq(Job::getDeleted, false)
+            .eq(Batch::getDeleted, false)
+            .in(Job::getStatus, List.of(Job.JobStatus.WORKING, Job.JobStatus.COMMITTED))
+            .eq(Batch::getTaskType, Batch.TaskType.TASK));
+  }
+
   public List<Job> getJobsByBatchId(Long batchId) {
     return jobMapper.selectList(
         new QueryWrapper<Job>().lambda().eq(Job::getBatchId, batchId).eq(Job::getDeleted, false));
diff --git a/src/main/java/ai/saharaa/services/ReviewSessionService.java b/src/main/java/ai/saharaa/services/ReviewSessionService.java
index 6b5a08fb..a918e11d 100644
--- a/src/main/java/ai/saharaa/services/ReviewSessionService.java
+++ b/src/main/java/ai/saharaa/services/ReviewSessionService.java
@@ -1,5 +1,7 @@
 package ai.saharaa.services;
 
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT;
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.CORRECT_COUNT;
 import static ai.saharaa.model.Batch.BatchStatus.FINISHED;
 import static ai.saharaa.model.ReviewSession.ReviewSessionStatus.PENDING;
 import static ai.saharaa.utils.Constants.BanReasonAccTemplate;
@@ -13,6 +15,8 @@ import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.config.cluster.ClusterConfiguration;
 import ai.saharaa.daos.*;
 import ai.saharaa.daos.activity.PlatformActivityDao;
+import ai.saharaa.distribution.CommonCounterManager;
+import ai.saharaa.distribution.Contants.Constants;
 import ai.saharaa.dto.job.JobUserStatDTO;
 import ai.saharaa.dto.job.PreSignedUrlTaskSessionResultDTO;
 import ai.saharaa.dto.job.QuestionReviewDTO;
@@ -58,7 +62,7 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
   private final TaskSessionMapper taskSessionMapper;
   private final TaskSessionDao taskSessionDao;
   private final TaskAndReviewSessionDao taskAndReviewSessionDao;
-  private final HoneyPotService honeyPotService;
+  private final CommonCounterManager counterManager;
   private final HoneyPotDao honeyPotDao;
   private final SkipTaskSessionDao skipTaskSessionDao;
   private final JobUserPointsDao jobUserPointsDao;
@@ -89,6 +93,7 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
       JobDao jobDao,
       IGlobalCache iGlobalCache,
       BatchDao batchDao,
+      CommonCounterManager counterManager,
       JobUserDao jobUserDao,
       IndividualsDao individualsDao,
       IndividualsService individualsService,
@@ -100,7 +105,6 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
       HoneyPotDao honeyPotDao,
       UserDao userDao,
       NotificationService notificationService,
-      HoneyPotService honeyPotService,
       TaskSessionService taskSessionService,
       TaskAndReviewSessionDao taskAndReviewSessionDao,
       akka.actor.typed.ActorRef<MachineReviewBehavior.Command> machineReviewActor,
@@ -125,11 +129,11 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
     this.individualsService = individualsService;
     this.userDao = userDao;
     this.notificationService = notificationService;
-    this.honeyPotService = honeyPotService;
     this.taskSessionService = taskSessionService;
     this.machineReviewActor = machineReviewActor;
     this.transactionUtils = transactionUtils;
     this.taskAndReviewSessionDao = taskAndReviewSessionDao;
+    this.counterManager = counterManager;
   }
 
   public ReviewSession createReviewSession(JobUser jobUser, TaskSession taskSession) {
@@ -197,12 +201,12 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
         transactionUtils.executeWithTransaction(() -> updateTaskSessionIfVotesAreEnough(
             session, taskSession, batch.getReviewingTimesReviewPerDatapoint(), true));
     if (finishReview) {
-      clusterConfiguration
-          .getIndividualJobSessionActor()
-          .tell(
-              new IndividualJobSessionActor.UpdateTaskSessionDisCount(
-                  taskSession.getJobId(), List.of(taskSession.getTaskId())),
-              ActorRef.noSender());
+      //      clusterConfiguration // todo: DisCount remove temp
+      //          .getIndividualJobSessionActor()
+      //          .tell(
+      //              new IndividualJobSessionActor.UpdateTaskSessionDisCount(
+      //                  taskSession.getJobId(), List.of(taskSession.getTaskId())),
+      //              ActorRef.noSender());
       clusterConfiguration
           .getIndividualJobSessionActor()
           .tell(
@@ -256,12 +260,12 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
     var finishReview = updateTaskSessionIfVotesAreEnough(
         session, taskSession, batch.getReviewingTimesReviewPerDatapoint(), false);
     if (finishReview) {
-      clusterConfiguration
-          .getIndividualJobSessionActor()
-          .tell(
-              new IndividualJobSessionActor.UpdateTaskSessionDisCount(
-                  taskSession.getJobId(), List.of(taskSession.getTaskId())),
-              ActorRef.noSender());
+      //      clusterConfiguration // todo: DisCount remove temp
+      //          .getIndividualJobSessionActor()
+      //          .tell(
+      //              new IndividualJobSessionActor.UpdateTaskSessionDisCount(
+      //                  taskSession.getJobId(), List.of(taskSession.getTaskId())),
+      //              ActorRef.noSender());
       clusterConfiguration
           .getIndividualJobSessionActor()
           .tell(
@@ -275,17 +279,18 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
   public void checkJobNeedClose(Batch batch, Job job) {
     var now = DateUtils.now();
     var batchSetting = batchDao.getBatchSettingById(batch.getId());
+    var jobCompleteCount = counterManager.getJobCount(
+        job.getId(),
+        ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value);
     if (batchSetting.getDistributeType().equals(BatchSetting.DistributeType.SINGLE)) {
       var taskAllDatapointCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
-      var taskWaitingForTake =
-          taskAllDatapointCount - taskSessionDao.countPendingSpotTaskSessionsByJobId(job.getId());
+      var taskWaitingForTake = taskAllDatapointCount - jobCompleteCount;
       taskAndReviewSessionDao.doCloseJob(job, taskWaitingForTake);
       return;
     }
     var datapointRepeatCount = batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
     var taskAllDatapointCount = ((long) job.getAssignDataVolume() * datapointRepeatCount);
-    var taskWaitingForTake =
-        taskAllDatapointCount - taskSessionDao.countPendingSpotTaskSessionsByJobId(job.getId());
+    var taskWaitingForTake = taskAllDatapointCount - jobCompleteCount;
     if (taskWaitingForTake <= 5) { // close the job
       if (taskWaitingForTake <= 0) {
         job.setStatus(Job.JobStatus.FINISHED);
@@ -387,6 +392,9 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
               .set(TaskSession::getReviewerStatus, taskSessionReviewResult));
       if (TaskSession.TaskSessionStatus.PendingSpot.equals(taskSessionStatusResult)) {
         taskSession.setFinalJudgeAt(dateNow);
+        counterManager.setJobCountIncrement(taskSession.getJobId(), COMPLETE_COUNT.value, 1L);
+        counterManager.setJobUserCountIncrement(
+            taskSession.getJobId(), taskSession.getUserId(), CORRECT_COUNT.value, 1L);
         jobUserPointsDao.recordJobUserPointPreCalc(taskSession);
       }
       if (taskSessionShouldBeDeleted) {
@@ -704,6 +712,9 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
       Job job,
       Long userId,
       List<Long> rejectIdList) {
+    counterManager.setJobUserCountIncrement(
+        job.getId(), userId, Constants.CACHE_KEY_FOR_JOB_USER.SUBMITTED_COUNT.value, (long)
+            submitReviews.size());
     AtomicReference<Long> rejectedReviewSessionId = new AtomicReference<>(null);
     var res = submitReviews.stream()
         .map(submitReview -> {
@@ -760,29 +771,30 @@ public class ReviewSessionService extends ServiceImpl<ReviewSessionMapper, Revie
     return reviewSessionDao.getMyReviewSessionByTaskSessionId(id, curId);
   }
 
-  public void generateNewReviewSessionIfNecessaryCauseOfHoneyPot(TaskSession session, Long curId) {
-    var reviewSession = getMyReviewSessionByTaskSessionId(session.getId(), curId);
-    var job = jobDao.getJobById(session.getJobId());
-    var jobUser = jobUserDao.getJobUserByJobAndUserId(session.getJobId(), curId);
-    var batch = batchDao.getBatchById(job.get().getBatchId());
-    var batchSetting = batchDao.getBatchSettingById(job.get().getBatchId());
-    var oldHoneyPotReviewSession =
-        honeyPotDao.getSessionByReviewSessionIdEvenDeleted(reviewSession.getId());
-    if (oldHoneyPotReviewSession != null) {
-      var tryTakeNewTaskSession = taskSessionDao.takeReviewJobsForUser(
-          job.get().getId(), curId, 1, batch.get().getReviewingTimesReviewPerDatapoint());
-
-      if (tryTakeNewTaskSession.size() == 1) {
-        var newReviewSession = createReviewSession(jobUser, tryTakeNewTaskSession.get(0));
-        honeyPotService.generateHPSessionForReviewSessionId(
-            Collections.singletonList(newReviewSession.getId()),
-            batchSetting.getHoneyPotReviewBatches(),
-            1,
-            curId,
-            jobUser.getId());
-      }
-    }
-  }
+  //  public void generateNewReviewSessionIfNecessaryCauseOfHoneyPot(TaskSession session, Long
+  // curId, JobUser jobUser) {
+  //    var reviewSession = getMyReviewSessionByTaskSessionId(session.getId(), curId);
+  //    var job = jobDao.getJobById(session.getJobId());
+  //    var batch = batchDao.getBatchById(job.get().getBatchId());
+  //    var batchSetting = batchDao.getBatchSettingById(job.get().getBatchId());
+  //    var oldHoneyPotReviewSession =
+  //        honeyPotDao.getSessionByReviewSessionIdEvenDeleted(reviewSession.getId());
+  //    var currentPendingSessions =
+  // reviewSessionDao.getPendingReviewSessionByJobIdAndUserId(jobUser.getTaskListSessionId(),
+  // jobUser.getUserId()).map(ReviewSession::getTaskSessionId).toList();
+  //    if (oldHoneyPotReviewSession != null) {
+  //      TaskDistributor<ReviewSession> taskDistributor = this.taskVisitorProvider
+  //          .getTaskVisitor(batchSetting.getDistributeType(), JobUser.JobUserRole.REVIEWER)
+  //          .taskDistributor();
+  //      var res = taskDistributor.assignTasks(job.get(), batch.get(), batchSetting, jobUser, null,
+  // false);
+  //      var newRsOpt = res.filter(r -> !currentPendingSessions.contains(r.getId())).findFirst();
+  //      newRsOpt.ifPresent(newRs ->
+  // honeyPotDao.createHoneyPotReviewsByQuestionIdsAndReviewSessionIds(
+  //            List.of(oldHoneyPotReviewSession.getQuestionId()), List.of(newRs.getId()),
+  // jobUser.getUserId(), jobUser.getId()));
+  //    }
+  //  }
 
   public Optional<ReviewSession> deleteById(Long id) {
     reviewSessionDao.deleteReviewSessionById(id);
diff --git a/src/main/java/ai/saharaa/services/TaskService.java b/src/main/java/ai/saharaa/services/TaskService.java
index 2248879e..23abd0f1 100644
--- a/src/main/java/ai/saharaa/services/TaskService.java
+++ b/src/main/java/ai/saharaa/services/TaskService.java
@@ -18,10 +18,7 @@ import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import com.github.yulichang.toolkit.JoinWrappers;
-import java.util.List;
-import java.util.Map;
-import java.util.Objects;
-import java.util.Optional;
+import java.util.*;
 import java.util.function.Function;
 import java.util.stream.Collectors;
 import manifold.rt.api.util.Pair;
@@ -85,6 +82,10 @@ public class TaskService extends ServiceImpl<TaskMapper, Task> {
     return taskDao.getTaskById(id);
   }
 
+  public List<Task> getTaskByIds(Collection<Long> ids) {
+    return taskDao.getTaskByIds(ids);
+  }
+
   public Optional<List<HybridResourceDTO>> getHybridTaskResource(Long taskId) {
     List<HybridTaskResource> hybridResourceDTOS =
         hybridTaskResourceMapper.selectList(new QueryWrapper<HybridTaskResource>()
@@ -510,6 +511,10 @@ public class TaskService extends ServiceImpl<TaskMapper, Task> {
     return getTaskById(id).flatMap(t -> resourceDao.getResourceById(t.getResourceId()));
   }
 
+  public List<Resource> getResourceByTaskIds(List<Long> ids) {
+    return resourceDao.getResourceByIds(ids);
+  }
+
   public void deleteTaskByResourceNonHybridIdNorLabeling(Long resourceId, Long labelingTaskId) {
     taskMapper.update(
         null,
diff --git a/src/main/java/ai/saharaa/services/TaskSessionService.java b/src/main/java/ai/saharaa/services/TaskSessionService.java
index 4e68dc22..2be91c71 100644
--- a/src/main/java/ai/saharaa/services/TaskSessionService.java
+++ b/src/main/java/ai/saharaa/services/TaskSessionService.java
@@ -1,48 +1,25 @@
 package ai.saharaa.services;
 
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT;
+import static ai.saharaa.distribution.Contants.Constants.CACHE_KEY_FOR_JOB_USER.*;
 import static ai.saharaa.model.BatchSetting.DistributeType.RAW;
 import static ai.saharaa.model.TaskSession.RejectedBy.MAJORITY_VOTE;
 import static ai.saharaa.model.TaskSession.RejectedBy.PIPELINE_REJECT;
 
 import ai.saharaa.actors.tasks.MachineReviewBehavior;
-import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.config.ChainConfig;
 import ai.saharaa.config.PlatformConfig;
 import ai.saharaa.daos.*;
-import ai.saharaa.daos.BatchDao;
-import ai.saharaa.daos.JobUserDao;
 import ai.saharaa.daos.season.SeasonDao;
-import ai.saharaa.dto.job.JobAmAuditStatDTO;
-import ai.saharaa.dto.job.JobUserStatDTO;
-import ai.saharaa.dto.job.SubmitAnswersItemDTO;
-import ai.saharaa.dto.job.SubmitPipelineResultDTO;
-import ai.saharaa.dto.job.SubmitPipelineResultFeedbackDTO;
-import ai.saharaa.dto.job.TaskSessionWithLabelerDTO;
+import ai.saharaa.distribution.CommonCounterManager;
+import ai.saharaa.distribution.Contants.Constants;
+import ai.saharaa.dto.job.*;
 import ai.saharaa.dto.sign.BatchRequesterAcceptSignMessageDTO;
 import ai.saharaa.dto.sign.data.RequesterAcceptBatch;
 import ai.saharaa.dto.task.AnswerDetailsDTO;
 import ai.saharaa.enums.TaskSessionAmReviewStatus;
-import ai.saharaa.mappers.AmAuditSessionMapper;
-import ai.saharaa.mappers.ClientAuditSessionMapper;
-import ai.saharaa.mappers.ReviewSessionMapper;
-import ai.saharaa.mappers.TaskSessionMapper;
-import ai.saharaa.mappers.UserMapper;
-import ai.saharaa.model.AmAuditSession;
-import ai.saharaa.model.Batch;
-import ai.saharaa.model.BatchSetting;
-import ai.saharaa.model.ClientAuditSession;
-import ai.saharaa.model.HoneyPotBatch;
-import ai.saharaa.model.Job;
-import ai.saharaa.model.JobTask;
-import ai.saharaa.model.JobUser;
-import ai.saharaa.model.PageResult;
-import ai.saharaa.model.ReviewSession;
-import ai.saharaa.model.SkipTaskSession;
-import ai.saharaa.model.Task;
-import ai.saharaa.model.TaskList;
-import ai.saharaa.model.TaskQuestion;
-import ai.saharaa.model.TaskSession;
-import ai.saharaa.model.User;
+import ai.saharaa.mappers.*;
+import ai.saharaa.model.*;
 import ai.saharaa.utils.ControllerUtils;
 import ai.saharaa.utils.DateUtils;
 import ai.saharaa.utils.ObjectUtils;
@@ -56,12 +33,7 @@ import com.fasterxml.jackson.databind.ObjectMapper;
 import com.github.yulichang.toolkit.JoinWrappers;
 import java.sql.Timestamp;
 import java.time.Duration;
-import java.util.ArrayList;
-import java.util.Arrays;
-import java.util.Collections;
-import java.util.List;
-import java.util.Objects;
-import java.util.Optional;
+import java.util.*;
 import java.util.function.Function;
 import org.apache.commons.lang3.StringUtils;
 import org.slf4j.Logger;
@@ -80,13 +52,11 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
   private final JobUserPointsDao jobUserPointsDao;
   private final BatchDao batchDao;
   private final TaskAndReviewSessionDao taskAndReviewSessionDao;
+  private final CommonCounterManager counterManager;
   private final JobDao jobDao;
   private final HoneyPotDao honeyPotDao;
-  private final JobTaskDao jobTaskDao;
   private final ReviewSessionDao reviewSessionDao;
   private final JobUserDao jobUserDao;
-  private final TaskListDao taskListDao;
-  private final TaskDao taskDao;
   private final TaskQuestionDao taskQuestionDao;
   private final SkipTaskSessionDao skipTaskSessionDao;
   private final AmAuditSessionMapper amAuditSessionMapper;
@@ -99,7 +69,6 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
   private final ActorRef<MachineReviewBehavior.Command> machineReviewActor;
   private final PlatformTransactionManager transactionManager;
   private final SeasonDao seasonDao;
-  private final IGlobalCache cache;
 
   public TaskSessionService(
       TaskSessionMapper taskSessionMapper,
@@ -112,31 +81,26 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
       JobUserDao jobUserDao,
       ClientAuditSessionMapper clientAuditSessionMapper,
       TaskAndReviewSessionDao taskAndReviewSessionDao,
+      CommonCounterManager counterManager,
       ChainConfig chainConfig,
       SkipTaskSessionDao skipTaskSessionDao,
       ReviewSessionDao reviewSessionDao,
       HoneyPotDao honeyPotDao,
-      JobTaskDao jobTaskDao,
       BatchDao batchDao,
       JobDao jobDao,
       TaskQuestionDao taskQuestionDao,
-      TaskDao taskDao,
-      TaskListDao taskListDao,
       ReviewSessionMapper reviewSessionMapper,
       ActorRef<MachineReviewBehavior.Command> machineReviewActor,
       PlatformTransactionManager transactionManager,
-      SeasonDao seasonDao,
-      IGlobalCache cache) {
+      SeasonDao seasonDao) {
     this.taskSessionMapper = taskSessionMapper;
     this.taskSessionDao = taskSessionDao;
     this.jobUserPointsDao = jobUserPointsDao;
     this.honeyPotDao = honeyPotDao;
     this.jobUserDao = jobUserDao;
-    this.taskListDao = taskListDao;
     this.batchDao = batchDao;
     this.taskQuestionDao = taskQuestionDao;
     this.jobDao = jobDao;
-    this.taskDao = taskDao;
     this.platformConfig = platformConfig;
     this.userMapper = userMapper;
     this.userDao = userDao;
@@ -144,14 +108,13 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
     this.clientAuditSessionMapper = clientAuditSessionMapper;
     this.skipTaskSessionDao = skipTaskSessionDao;
     this.reviewSessionDao = reviewSessionDao;
-    this.jobTaskDao = jobTaskDao;
     this.chainConfig = chainConfig;
     this.reviewSessionMapper = reviewSessionMapper;
     this.machineReviewActor = machineReviewActor;
     this.transactionManager = transactionManager;
     this.taskAndReviewSessionDao = taskAndReviewSessionDao;
+    this.counterManager = counterManager;
     this.seasonDao = seasonDao;
-    this.cache = cache;
   }
 
   public TaskSession createTaskSession(JobUser jobUser, JobTask task, JobUser.JobUserRole role) {
@@ -193,17 +156,17 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
   }
 
   public TaskSession createTaskSessionCanDuplicate(
-      JobUser jobUser, JobTask task, JobUser.JobUserRole role, Timestamp date, Integer limit) {
+      JobUser jobUser, Long taskId, JobUser.JobUserRole role, Timestamp date, Integer limit) {
     Long checkTaskSessionsCount = this.taskSessionDao.countTaskSessionsByTaskIdAndJobIdAndUserId(
-        task.getTaskId(), task.getJobId(), jobUser.getUserId());
+        taskId, jobUser.getTaskListSessionId(), jobUser.getUserId());
     if (checkTaskSessionsCount >= limit) {
       throw new RuntimeException("Task session already exists");
     }
 
     var taskSession = TaskSession.builder()
         .jobUserId(jobUser.getId())
-        .taskId(task.getTaskId())
-        .jobId(task.getJobId())
+        .taskId(taskId)
+        .jobId(jobUser.getTaskListSessionId())
         .userId(jobUser.getUserId())
         .userRole(role)
         .answer("")
@@ -220,15 +183,17 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
       Integer totalLimit,
       Integer userLimit,
       Integer batchAnnotationSize) {
-    Long totalTaskSessionsCount =
-        taskSessionDao.countTaskSessionsByTaskIdAndJobId(taskId, jobUser.getTaskListSessionId());
-    if (totalTaskSessionsCount >= totalLimit) {
-      return new ArrayList<>();
-    }
-    var limit =
-        Math.min(batchAnnotationSize, Math.min(totalLimit - totalTaskSessionsCount, userLimit));
+    //    Long totalTaskSessionsCount =
+    //        taskSessionDao.countTaskSessionsByTaskIdAndJobId(taskId,
+    // jobUser.getTaskListSessionId());
+    //    if (totalTaskSessionsCount >= totalLimit) {
+    //      return Collections.EMPTY_LIST;
+    //    }
+    //    var limit =
+    //        Math.min(batchAnnotationSize, Math.min(totalLimit - totalTaskSessionsCount,
+    // userLimit));
     List<TaskSession> result = new ArrayList<>();
-    for (int i = 0; i < limit; i++) {
+    for (int i = 0; i < userLimit; i++) {
       var taskSession = TaskSession.builder()
           .jobUserId(jobUser.getId())
           .taskId(taskId)
@@ -340,7 +305,7 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
     return taskSessionMapper.selectById(id).asOpt();
   }
 
-  public List<TaskSession> getTaskSessionByIdList(List<Long> idList) {
+  public List<TaskSession> getTaskSessionByIdList(Collection<Long> idList) {
     return taskSessionMapper.selectBatchIds(idList);
   }
 
@@ -373,6 +338,11 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
       Long projectId,
       List<Long> rejectIdList,
       Boolean isTester) {
+    counterManager.setJobUserCountIncrement(
+        answers.first().getTaskSession().getJobId(),
+        answers.first().getTaskSession().getUserId(),
+        SUBMITTED_COUNT.value,
+        (long) answers.size());
     if (isTester) {
       answers.forEach(a -> {
         var now = DateUtils.now();
@@ -471,6 +441,11 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
       Long projectId,
       List<Long> rejectIdList,
       Boolean isTester) {
+    counterManager.setJobUserCountIncrement(
+        answers.first().getTaskSession().getJobId(),
+        answers.first().getTaskSession().getUserId(),
+        SUBMITTED_COUNT.value,
+        (long) answers.size());
     if (isTester) {
       answers.forEach(a -> {
         var now = DateUtils.now();
@@ -488,6 +463,20 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
         taskSessionMapper.updateById(targetTs);
         jobUserPointsDao.recordJobUserPointPreCalc(targetTs);
       });
+      counterManager.setJobCountIncrement(
+          answers.first().getTaskSession().getJobId(),
+          Constants.CACHE_KEY_FOR_JOB.COMPLETE_COUNT.value,
+          (long) answers.size());
+      counterManager.setJobUserCountIncrement(
+          answers.first().getTaskSession().getJobId(),
+          answers.first().getTaskSession().getUserId(),
+          CORRECT_COUNT.value,
+          (long) answers.size());
+      counterManager.setJobUserCountIncrement(
+          answers.first().getTaskSession().getJobId(),
+          answers.first().getTaskSession().getUserId(),
+          COMPLETE_COUNT.value,
+          (long) answers.size());
       return taskSessionDao.getTaskSessionsByIds(
           answers.mapToList(a -> a.getTaskSession().getId()));
     }
@@ -885,11 +874,14 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
     skipTaskSessionDao.createSkipTaskSession(targetObject);
     if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
       taskSessionDao.deleteTaskSessionById(ts.getId());
-      honeyPotDao.deleteHPSessionByTaskSessionIdList(Arrays.asList(ts.getId()));
-      taskSessionDao.updateTaskSessionDisCount(List.of(ts.getTaskId()), ts.getJobId());
+      honeyPotDao.deleteHPSessionByTaskSessionIdList(Collections.singletonList(ts.getId()));
+      // todo: DisCount remove temp
+      //      taskSessionDao.updateTaskSessionDisCount(List.of(ts.getTaskId()), ts.getJobId());
     } else if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
       reviewSessionDao.deleteReviewSessionByTaskSessionId(ts.getId(), userId);
-      reviewSessionDao.updateTaskSessionCountOfReviewingSession(List.of(ts.getId()), ts.getJobId());
+      // todo: DisCount remove temp
+      //      reviewSessionDao.updateTaskSessionCountOfReviewingSession(List.of(ts.getId()),
+      // ts.getJobId());
     }
   }
 
@@ -918,54 +910,36 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
     return taskSessionDao.getAllTaskSessionByJobId(id);
   }
 
-  public void generateNewTaskSessionIfNecessaryCauseOfHoneyPot(TaskSession session) {
-    var job = jobDao.getJobById(session.getJobId());
-    var batchSetting = batchDao.getBatchSettingById(job.get().getBatchId());
-    var oldHoneyPotSession = honeyPotDao.getSessionByTaskSessionIdEvenDeleted(session.getId());
-    if (oldHoneyPotSession != null) {
-      var takeOneNewJobTaskForNewTaskSession = new ArrayList<JobTask>();
-      var cacheKeyOfTaskIdQueue = "jobOpt_${session.getJobId()}:cachedTaskIds";
-      if (cache.hasKey(cacheKeyOfTaskIdQueue) && cache.lGetListSize(cacheKeyOfTaskIdQueue) > 0) {
-        var singleTaskId = Long.parseLong(cache.lPop(cacheKeyOfTaskIdQueue).toString());
-        takeOneNewJobTaskForNewTaskSession.add(jobTaskDao.getByTaskId(singleTaskId));
-      } else {
-        takeOneNewJobTaskForNewTaskSession.addAll(jobTaskDao.takeJobForJobUserAvoidSkipped(
-            session.getJobId(),
-            session.getUserId(),
-            batchSetting.getAnnotatingTimesAnnotationPerDatapoint(),
-            1));
-      }
-
-      if (takeOneNewJobTaskForNewTaskSession.size() == 1) {
-        var newTaskSession = createTaskSession(
-            jobUserDao.getJobUserByJobAndUserId(session.getJobId(), session.getUserId()),
-            takeOneNewJobTaskForNewTaskSession.get(0),
-            JobUser.JobUserRole.LABELER,
-            session.getCreatedAt());
-        var honeyPotBatchIdList =
-            batchSetting.getHoneyPotBatches().map(HoneyPotBatch::getBatchId).toList();
-        var tl = taskListDao.listTaskListsByBatchIdsAndType(
-            honeyPotBatchIdList, TaskList.TaskListType.LABEL);
-        var tsk = tl.stream()
-            .map(taskList -> taskDao.getFirstTaskInTaskList(taskList.getId()))
-            .filter(Optional::isPresent)
-            .map(Optional::get)
-            .toList();
-        String tskIds = tsk.map(Task::getId).toList().join(",");
-        var taskQuestionForHP =
-            taskQuestionDao.getRandomQuestionForHPSessionByMultiTask(tskIds, 1L);
-        if (taskQuestionForHP.size() == 1) {
-          honeyPotDao.createHoneyPotsByQuestionIdListAndTaskSessionIdList(
-              Collections.singletonList(taskQuestionForHP.get(0).getId()),
-              Collections.singletonList(newTaskSession.getId()),
-              newTaskSession.getUserId(),
-              newTaskSession.getJobUserId());
-        }
-        taskSessionDao.updateTaskSessionDisCount(
-            List.of(newTaskSession.getTaskId()), newTaskSession.getJobId());
-      }
-    }
-  }
+  //  public void generateNewTaskSessionIfNecessaryCauseOfHoneyPot(TaskSession session, JobUser
+  // jobUser) {
+  //    var job = jobDao.getJobById(session.getJobId());
+  //    var batch = batchDao.getBatchById(job.get().getBatchId()).get();
+  //    var batchSetting = batchDao.getBatchSettingById(job.get().getBatchId());
+  //    var oldHoneyPotSession = honeyPotDao.getSessionByTaskSessionIdEvenDeleted(session.getId());
+  //    var currentPendingTaskSessions =
+  // taskSessionDao.getTaskIdsByJobUserIdAndStatus(jobUser.getId(),
+  // TaskSession.TaskSessionStatus.PENDING).map(TaskSession::getId).toList();
+  //    if (oldHoneyPotSession != null) {
+  //      TaskDistributor<TaskSession> taskDistributor = this.taskVisitorProvider
+  //          .getTaskVisitor(batchSetting.getDistributeType(), JobUser.JobUserRole.LABELER)
+  //          .taskDistributor();
+  //      var res = taskDistributor.assignTasks(job.get(), batch, batchSetting, jobUser, null,
+  // false);
+  //      var newTsOpt = res.filter(r ->
+  // !currentPendingTaskSessions.contains(r.getId())).findFirst();
+  //      newTsOpt.ifPresent(newTs -> {
+  //        var newTsHpSession = HoneyPotSession.builder()
+  //            .taskSessionId(newTs.getId())
+  //            .jobUserId(oldHoneyPotSession.getJobUserId())
+  //            .questionId(oldHoneyPotSession.getQuestionId())
+  //            .userId(oldHoneyPotSession.getUserId())
+  //            .answer("")
+  //            .status(HoneyPotSession.HPSessionStatus.PENDING)
+  //            .build();
+  //        honeyPotDao.createHoneyPot(newTsHpSession);
+  //      });
+  //    }
+  //  }
 
   public Long countApprovedReviewForLabeler(Long userId) {
     var count = taskSessionMapper.selectJoinCount(JoinWrappers.lambda(TaskSession.class)
@@ -1024,7 +998,13 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
           ts.setStatus(TaskSession.TaskSessionStatus.PendingSpot);
           ts.setUpdatedAt(dateNow);
           ts.setFinalJudgeAt(dateNow);
+          counterManager.setJobUserCountIncrement(
+              ts.getJobId(), ts.getUserId(), CORRECT_COUNT.value, 1L);
+          counterManager.setJobUserCountIncrement(
+              ts.getJobId(), ts.getUserId(), COMPLETE_COUNT.value, 1L);
         });
+        counterManager.setJobCountIncrement(
+            jobId, COMPLETE_COUNT.value, (long) notApprovedPipelinePendingSessions.size());
         taskSessionMapper.updateById(notApprovedPipelinePendingSessions, 50);
         jobUserPointsDao.recordJobUserPointPreCalcs(notApprovedPipelinePendingSessions);
         res.setApprovedCount(notApprovedPipelinePendingSessions.size());
@@ -1066,10 +1046,11 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
               .set(TaskSession::getRejectedBy, PIPELINE_REJECT)
               .set(TaskSession::getDeleted, true)
               .set(TaskSession::getUpdatedAt, DateUtils.now())));
-      if (targetBatchSetting.getDistributeType().equals(RAW)) {
-        var taskIds = taskSessionDao.getTaskSessionsTaskIdByIds(dto.getRejectIdList());
-        taskSessionDao.updateTaskSessionDisCount(taskIds, jobId);
-      }
+      //      if (targetBatchSetting.getDistributeType().equals(RAW)) { // todo: DisCount remove
+      // temp
+      //        var taskIds = taskSessionDao.getTaskSessionsTaskIdByIds(dto.getRejectIdList());
+      //        taskSessionDao.updateTaskSessionDisCount(taskIds, jobId);
+      //      }
     }
     var j = jobDao.getJobById(jobId);
     j.ifPresent(job -> {
@@ -1134,4 +1115,26 @@ public class TaskSessionService extends ServiceImpl<TaskSessionMapper, TaskSessi
         difficulty);
     return (Objects.isNull(count) ? 0L : count) + (Objects.isNull(reviewCount) ? 0L : reviewCount);
   }
+
+  public List<Long> getTaskSessionByJobIdAndUserId(Long jobId, Long userId) {
+    return this.taskSessionMapper
+        .selectList(new QueryWrapper<TaskSession>()
+            .lambda()
+            .eq(TaskSession::getJobId, jobId)
+            .eq(TaskSession::getUserId, userId)
+            .eq(TaskSession::getDeleted, false))
+        .stream()
+        .map(TaskSession::getTaskId)
+        .toList();
+  }
+
+  public List<SkipTaskSession> getSkipTaskSessionByJobIdAndUserId(Long jobId, Long userId) {
+    return this.skipTaskSessionDao.getUserSkippedTaskSessionIdListInJob(jobId, userId);
+  }
+
+  public List<Long> getReviewSessionByJobIdAndUserId(Long jobId, Long userId) {
+    return this.reviewSessionDao.getWorkingReviewSessionByJobIdAndUserId(jobId, userId).stream()
+        .map(ReviewSession::getTaskSessionId)
+        .toList();
+  }
 }
diff --git a/src/main/java/ai/saharaa/services/Web3AuthService.java b/src/main/java/ai/saharaa/services/Web3AuthService.java
index 8e02a061..b6cfbe2f 100644
--- a/src/main/java/ai/saharaa/services/Web3AuthService.java
+++ b/src/main/java/ai/saharaa/services/Web3AuthService.java
@@ -21,18 +21,21 @@ public class Web3AuthService {
 
   public Web3AuthService(
       @Value("${web3auth.token-expire}") Long tokenExpire,
-      @Qualifier("redisTemplateForAuth") RedisTemplate redisTemplate) {
+      @Qualifier("redisTemplate") RedisTemplate redisTemplate) {
     this.tokenExpire = tokenExpire;
     this.redisTemplate = redisTemplate;
   }
 
   private static String signMessageTemplate(
       Web3AuthGeneratedMessageDTO web3AuthGeneratedMessageDTO) {
-    return "\n" + "\t\tWelcome to saharaa\n" + "\n"
+    return "\n"
+        + "\t\tWelcome to saharaa\n"
+        + "\n"
         + "ChainId: ${web3AuthGeneratedMessageDTO.getChainId()}\n"
         + "Address: ${web3AuthGeneratedMessageDTO.getAddress()}\n"
         + "Expire Time: ${web3AuthGeneratedMessageDTO.getExpireAt()}\n"
-        + "Random Nonce: ${web3AuthGeneratedMessageDTO.getRandomNonce()}\n" + "\t";
+        + "Random Nonce: ${web3AuthGeneratedMessageDTO.getRandomNonce()}\n"
+        + "\t";
   }
 
   public String generateMessage(String chainId, String address) {
diff --git a/src/main/java/ai/saharaa/services/WorkloadLimitService.java b/src/main/java/ai/saharaa/services/WorkloadLimitService.java
index 66a03240..7ca91061 100644
--- a/src/main/java/ai/saharaa/services/WorkloadLimitService.java
+++ b/src/main/java/ai/saharaa/services/WorkloadLimitService.java
@@ -3,12 +3,11 @@ package ai.saharaa.services;
 import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
 import static ai.saharaa.model.JobUser.JobUserRole.REVIEWER;
 
-import ai.saharaa.actors.jobs.IndividualJobSessionActor;
-import ai.saharaa.actors.jobs.IndividualJobSessionForReviewerActor;
 import ai.saharaa.common.cache.IGlobalCache;
 import ai.saharaa.daos.BatchDao;
 import ai.saharaa.daos.JobUserDao;
 import ai.saharaa.daos.TaskSessionDao;
+import ai.saharaa.dto.job.SubmitAnswersItemDTO;
 import ai.saharaa.enums.WorkloadType;
 import ai.saharaa.model.BatchSetting;
 import ai.saharaa.model.JobUser;
@@ -74,26 +73,24 @@ public class WorkloadLimitService {
         });
   }
 
-  public IndividualJobSessionActor.OpSubmitAnswers filterAnswersByWorkloadLimit(
-      IndividualJobSessionActor.OpSubmitAnswers op) {
+  public List<SubmitAnswersItemDTO> filterAnswersByWorkloadLimit(
+      List<SubmitAnswersItemDTO> answers, Long jobId, Long userId, String ipAddress) {
     // remove answers which are not revised
-    var jobUser = jobUserDao.getJobUserByJobAndUserId(
-        op.getSubmitAnswers().first().getTaskSession().getJobId(), op.getUserId());
-    var workloadType = getJobUserWorkloadType(jobUser, LABELER, op.getIp());
+    var jobUser = jobUserDao.getJobUserByJobAndUserId(jobId, userId);
+    var workloadType = getJobUserWorkloadType(jobUser, LABELER, ipAddress);
     if (workloadType != WorkloadType.NO_WORKLOAD) {
       // filter answers which are not revised
-      var revisedIds = taskSessionDao
-          .getTaskSessionsByIds(
-              op.getSubmitAnswers().mapToList(a -> a.getTaskSession().getId()))
-          .stream()
-          .filter(taskSession ->
-              TaskSession.TaskSessionReviewStatus.REVISED.equals(taskSession.getSpotterStatus()))
-          .map(TaskSession::getId)
-          .toList();
-      op.getSubmitAnswers()
-          .removeIf(answer -> !revisedIds.contains(answer.getTaskSession().getTaskId()));
+      var revisedIds =
+          taskSessionDao
+              .getTaskSessionsByIds(answers.mapToList(a -> a.getTaskSession().getId()))
+              .stream()
+              .filter(taskSession -> TaskSession.TaskSessionReviewStatus.REVISED.equals(
+                  taskSession.getSpotterStatus()))
+              .map(TaskSession::getId)
+              .toList();
+      answers.removeIf(answer -> !revisedIds.contains(answer.getTaskSession().getTaskId()));
     }
-    return op;
+    return answers;
   }
 
   public void updateJobUserWorkloads(
@@ -102,7 +99,12 @@ public class WorkloadLimitService {
   }
 
   public void updateJobUserWorkloadsForReview(
-      IndividualJobSessionForReviewerActor.OpSubmitReviews op, List<ReviewSession> sessions) {
+      Long jobId,
+      Long userId,
+      Long size,
+      JobUser.JobUserRole role,
+      String ip,
+      List<ReviewSession> sessions) {
     long subtractCount = taskSessionDao
         .getTaskSessionsByIds(
             sessions.stream().map(ReviewSession::getTaskSessionId).collect(Collectors.toList()))
@@ -110,14 +112,7 @@ public class WorkloadLimitService {
         .filter(TaskSession::getDeleted)
         .count();
 
-    updateWorkloads(
-        op.getJobId(),
-        op.getUserId(),
-        (long) op.getSubmitReviews().size(),
-        REVIEWER,
-        op.getIp(),
-        true,
-        subtractCount);
+    updateWorkloads(jobId, userId, size, REVIEWER, ip, true, subtractCount);
   }
 
   private void updateWorkloads(
@@ -130,9 +125,8 @@ public class WorkloadLimitService {
       Long subtractCount) {
 
     BatchSetting batchSetting = getBatchSetting(jobId);
-    if (batchSetting == null || !isBatchWorkloadSet(batchSetting, role)) {
-      return;
-    }
+
+    var noJobUserWorkload = batchSetting == null || !isBatchWorkloadSet(batchSetting, role);
 
     String formattedIpAddress = formatIpAddress(ipAddress);
     String ipKey = generateIpKey(formattedIpAddress);
@@ -143,10 +137,16 @@ public class WorkloadLimitService {
       if (isDecrement) {
         cache.decr(ipKey, workloadSize);
         cache.decr(userKey, workloadSize);
+        if (noJobUserWorkload) {
+          return;
+        }
         updateJobUserHash(jobUserHashKey, -workloadSize);
       } else {
         cache.incr(ipKey, workloadSize);
         cache.incr(userKey, workloadSize);
+        if (noJobUserWorkload) {
+          return;
+        }
         updateJobUserHash(jobUserHashKey, workloadSize);
       }
     };
diff --git a/src/main/resources/application.properties b/src/main/resources/application.properties
index 3be142d8..18318600 100644
--- a/src/main/resources/application.properties
+++ b/src/main/resources/application.properties
@@ -29,7 +29,7 @@ spring.data.redis.ssl.enabled=${AI_REDIS_SSL_ENABLED:false}
 cache.default.db.index=0
 cache.api.db.index=1
 cache.rateLimit.db.index=2
-cache.auth.db.index=15
+cache.taskQueue.db.index=15
 # Spring's support of otlp grpc is being added
 # https://github.com/spring-projects/spring-boot/issues/41460
 management.otlp.tracing.endpoint=http://tempo:4318/v1/traces
diff --git a/src/main/resources/mapper/JobTaskMapper.xml b/src/main/resources/mapper/JobTaskMapper.xml
index 910f2a71..6dee3dbb 100644
--- a/src/main/resources/mapper/JobTaskMapper.xml
+++ b/src/main/resources/mapper/JobTaskMapper.xml
@@ -1,112 +1,147 @@
 <?xml version="1.0" encoding="UTF-8" ?>
 <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
 <mapper namespace="ai.saharaa.mappers.JobTaskMapper">
-<resultMap id="jobTaskResultMap" type="ai.saharaa.model.JobTask">
-  <id property="id" column="id" jdbcType="BIGINT"/>
-  <id property="jobId" column="job_id" jdbcType="BIGINT" />
-  <id property="taskId" column="task_id" jdbcType="BIGINT" />
-  <id property="deleted" column="deleted" jdbcType="BOOLEAN" />
-  <id property="createdAt" column="created_at" jdbcType="TIMESTAMP_WITH_TIMEZONE" />
-  <id property="updatedAt" column="updated_at" jdbcType="TIMESTAMP_WITH_TIMEZONE" />
-</resultMap>
-  <select id="takeJobForUser">
-    SELECT jt.id, jt.job_id, jt.task_id, jt.deleted, jt.created_at, jt.updated_at
-    FROM job_task jt
-    LEFT JOIN (
-    SELECT task_id,job_id
-    FROM task_session
-    WHERE job_id = #{jobId}
-    and deleted = false
-    and status != 'rejected'
-    ) ts ON jt.task_id = ts.task_id
-    WHERE jt.job_id = #{jobId}
-    AND jt.deleted = false
-    AND NOT EXISTS (
-    SELECT 1
-    FROM task_session
-    WHERE job_id = #{jobId}
-    AND task_id = jt.task_id
-    AND user_id = #{uid}
-    and user_role = #{role}
-    and deleted = false
-    )
-    GROUP BY jt.id, jt.task_id
-    HAVING COUNT(ts.task_id) 	&lt; #{maxSession}
-    limit 5;
-  </select>
-  <select id="takeJobForJobUser">
-    select jt.* from job_task jt
-    where jt.job_id = #{jobId}
-      and jt.deleted = false
-      and jt.task_id not in (
-            SELECT task_id FROM task_session
-                WHERE job_id = #{jobId} and user_role = #{role} and deleted = false and status != 'rejected'
-      ) limit 5
-  </select>
-  
-  <select id="countMyAvailableLeftTask" resultType="java.lang.Long">
-    WITH excluded_tasks AS (
-      SELECT task_id
-      FROM task_session
-      WHERE user_id = #{userId} AND job_id = #{jobId} AND deleted = false
-      UNION
-      SELECT task_id
-      FROM skip_task_session
-      WHERE user_id = #{userId} AND job_id = #{jobId} AND deleted = false
-      UNION
-      SELECT task_id
-      FROM task_session
-      WHERE job_id = #{jobId} AND deleted = false
-      GROUP BY task_id
-      HAVING COUNT(task_id) &gt;= #{limit}
-    )
-    SELECT count(*)
-    FROM job_task WHERE deleted = false AND job_id = #{jobId} AND task_id NOT IN (SELECT task_id FROM excluded_tasks);
-  </select>
-  <select id="takeJobForJobUserAvoidSkipped" resultType="ai.saharaa.model.JobTask">
-    WITH excluded_tasks AS (
-      SELECT task_id
-      FROM task_session
-      WHERE user_id = #{userId} AND job_id = #{jobId} AND deleted = false
-      UNION
-      SELECT task_id
-      FROM skip_task_session
-      WHERE user_id = #{userId} AND job_id = #{jobId} AND deleted = false
-      UNION
-      SELECT task_id
-      FROM task_session_dis_count
-      WHERE job_id = #{jobId} AND deleted = false AND dis_count &gt;= #{limit}
-    )
-    SELECT jt.*
-    FROM job_task jt WHERE jt.deleted = false
-      AND jt.job_id = #{jobId}
-      AND jt.task_id NOT IN (SELECT task_id FROM excluded_tasks)
-    ORDER BY jt.task_id ASC LIMIT #{count};
-  </select>
-  
-  <select id="unreviewedTaskCount" resultType="java.lang.Long">
-    select count(*) from job_task jt
-    where
-      jt.deleted = false and jt.job_id = #{jobId}
-      and not exists(
-      select * from task_session ts
-      where ts.deleted = false
-        and ts.job_id = jt.job_id
-        and ts.task_id = jt.task_id
-        and ts.reviewer_status in ('approve', 'revised'))
-  </select>
-  <select id="getJobTaskPagesByJobId" resultMap="jobTaskResultMap">
-    SELECT jt.id, jt.job_id, jt.task_id, jt.deleted, jt.created_at, jt.updated_at
-    FROM job_task jt WHERE jt.job_id = #{jobId} AND jt.deleted = false LIMIT #{size} OFFSET #{offset}
-  </select>
-  <update id="deleteJobTasksInWorkingByJobId">
-    UPDATE job_task SET deleted = true, updated_at = now()
-    WHERE job_id = #{jobId} AND deleted = false
-      AND task_id NOT IN (
-      SELECT DISTINCT ts.task_id
-      FROM task_session AS ts
-      WHERE ts.job_id = #{jobId} AND ts.deleted = false
-        AND (ts.status = 'pending_spot' OR ts.status = 'finish')
-      )
-  </update>
-</mapper>
\ No newline at end of file
+    <resultMap id="jobTaskResultMap" type="ai.saharaa.model.JobTask">
+        <id property="id" column="id" jdbcType="BIGINT"/>
+        <id property="jobId" column="job_id" jdbcType="BIGINT"/>
+        <id property="taskId" column="task_id" jdbcType="BIGINT"/>
+        <id property="deleted" column="deleted" jdbcType="BOOLEAN"/>
+        <id property="createdAt" column="created_at" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
+        <id property="updatedAt" column="updated_at" jdbcType="TIMESTAMP_WITH_TIMEZONE"/>
+    </resultMap>
+    <select id="takeJobForUser">
+        SELECT jt.id, jt.job_id, jt.task_id, jt.deleted, jt.created_at, jt.updated_at
+        FROM job_task jt
+                 LEFT JOIN (SELECT task_id, job_id
+                            FROM task_session
+                            WHERE job_id = #{jobId}
+                              and deleted = false
+                              and status != 'rejected') ts ON jt.task_id = ts.task_id
+        WHERE jt.job_id = #{jobId}
+          AND jt.deleted = false
+          AND NOT EXISTS(
+                SELECT 1
+                FROM task_session
+                WHERE job_id = #{jobId}
+                  AND task_id = jt.task_id
+                  AND user_id = #{uid}
+                  and user_role = #{role}
+                  and deleted = false
+            )
+        GROUP BY jt.id, jt.task_id
+        HAVING COUNT(ts.task_id) &lt; #{maxSession} limit 5;
+    </select>
+    <select id="takeJobForJobUser">
+        select jt.*
+        from job_task jt
+        where jt.job_id = #{jobId}
+          and jt.deleted = false
+          and jt.task_id not in (SELECT task_id
+                                 FROM task_session
+                                 WHERE job_id = #{jobId}
+                                   and user_role = #{role}
+                                   and deleted = false
+                                   and status
+            != 'rejected'
+            ) limit 5
+    </select>
+
+    <select id="countMyAvailableLeftTask" resultType="java.lang.Long">
+        WITH excluded_tasks AS (SELECT task_id
+                                FROM task_session
+                                WHERE user_id = #{userId}
+                                  AND job_id = #{jobId}
+                                  AND deleted = false
+                                UNION
+                                SELECT task_id
+                                FROM skip_task_session
+                                WHERE user_id = #{userId}
+                                  AND job_id = #{jobId}
+                                  AND deleted = false
+                                UNION
+                                SELECT task_id
+                                FROM task_session
+                                WHERE job_id = #{jobId}
+                                  AND deleted = false
+                                GROUP BY task_id
+                                HAVING COUNT(task_id) &gt;= #{limit})
+        SELECT count(*)
+        FROM job_task
+        WHERE deleted = false
+          AND job_id = #{jobId}
+          AND task_id NOT IN (SELECT task_id FROM excluded_tasks);
+    </select>
+    <select id="takeJobForJobUserAvoidSkipped" resultType="ai.saharaa.model.JobTask">
+        WITH excluded_tasks AS (SELECT task_id
+                                FROM task_session
+                                WHERE user_id = #{userId}
+                                  AND job_id = #{jobId}
+                                  AND deleted = false
+                                UNION
+                                SELECT task_id
+                                FROM skip_task_session
+                                WHERE user_id = #{userId}
+                                  AND job_id = #{jobId}
+                                  AND deleted = false
+                                UNION
+                                SELECT task_id
+                                FROM task_session_dis_count
+                                WHERE job_id = #{jobId}
+                                  AND deleted = false
+                                  AND dis_count &gt;= #{limit})
+        SELECT jt.*
+        FROM job_task jt
+        WHERE jt.deleted = false
+          AND jt.job_id = #{jobId}
+          AND jt.task_id NOT IN (SELECT task_id FROM excluded_tasks)
+        ORDER BY jt.task_id ASC LIMIT #{count};
+    </select>
+
+
+    <select id="refreshTaskQueue" resultType="java.lang.Long">
+        WITH excluded_tasks AS (SELECT task_id
+                                FROM task_session_dis_count
+                                WHERE job_id = #{jobId}
+                                  AND deleted = false
+                                  AND dis_count &gt;= #{repeatCount})
+        SELECT jt.task_id
+        FROM job_task jt
+        WHERE jt.deleted = false
+          AND jt.job_id = #{jobId}
+          AND jt.task_id NOT IN (SELECT task_id FROM excluded_tasks)
+        ORDER BY jt.task_id ASC LIMIT #{limit};
+    </select>
+
+    <select id="unreviewedTaskCount" resultType="java.lang.Long">
+        select count(*)
+        from job_task jt
+        where jt.deleted = false
+          and jt.job_id = #{jobId}
+          and not exists(
+                select *
+                from task_session ts
+                where ts.deleted = false
+                  and ts.job_id = jt.job_id
+                  and ts.task_id = jt.task_id
+                  and ts.reviewer_status in ('approve', 'revised'))
+    </select>
+    <select id="getJobTaskPagesByJobId" resultMap="jobTaskResultMap">
+        SELECT jt.id, jt.job_id, jt.task_id, jt.deleted, jt.created_at, jt.updated_at
+        FROM job_task jt
+        WHERE jt.job_id = #{jobId}
+          AND jt.deleted = false LIMIT #{size}
+        OFFSET #{offset}
+    </select>
+    <update id="deleteJobTasksInWorkingByJobId">
+        UPDATE job_task
+        SET deleted    = true,
+            updated_at = now()
+        WHERE job_id = #{jobId}
+          AND deleted = false
+          AND task_id NOT IN (SELECT DISTINCT ts.task_id
+                              FROM task_session AS ts
+                              WHERE ts.job_id = #{jobId}
+                                AND ts.deleted = false
+                                AND (ts.status = 'pending_spot' OR ts.status = 'finish'))
+    </update>
+</mapper>
diff --git a/src/main/resources/mapper/TaskSessionMapper.xml b/src/main/resources/mapper/TaskSessionMapper.xml
index 347de40f..86bf4c6a 100644
--- a/src/main/resources/mapper/TaskSessionMapper.xml
+++ b/src/main/resources/mapper/TaskSessionMapper.xml
@@ -1,395 +1,533 @@
 <?xml version="1.0" encoding="UTF-8" ?>
 <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
 <mapper namespace="ai.saharaa.mappers.TaskSessionMapper">
-  <select id="takeReviewJobForUser">
-    SELECT ts.id, ts.job_user_id, ts.job_id, ts.task_id, ts.user_id, ts.user_role,
-           ts.answer, ts.feedback, ts.status, ts.reviewer_status, ts.spotter_status,
-           ts.nm_review_status, ts.am_review_status, ts.client_review_status,
-           ts.duration,ts.deleted, ts.created_at, ts.updated_at
-    FROM task_session ts
-    LEFT JOIN (
-    SELECT task_session_id
-    FROM review_session
-    WHERE job_id = #{jobId}
-    AND deleted = false and status != 'rejected'
-    ) rs ON ts.id = rs.task_session_id
-    WHERE ts.job_id = #{jobId} and ts.status = 'pending_review' and ts.deleted = false
-    AND NOT EXISTS (
-    SELECT 1
-    FROM review_session
-    WHERE job_id = #{jobId}
-    AND task_session_id  = ts.id
-    AND user_id = #{uid}
-    And deleted = false
-    AND status != 'rejected'
+    <select id="takeReviewJobForUser">
+        SELECT ts.id,
+               ts.job_user_id,
+               ts.job_id,
+               ts.task_id,
+               ts.user_id,
+               ts.user_role,
+               ts.answer,
+               ts.feedback,
+               ts.status,
+               ts.reviewer_status,
+               ts.spotter_status,
+               ts.nm_review_status,
+               ts.am_review_status,
+               ts.client_review_status,
+               ts.duration,
+               ts.deleted,
+               ts.created_at,
+               ts.updated_at
+        FROM task_session ts
+                 LEFT JOIN (SELECT task_session_id
+                            FROM review_session
+                            WHERE job_id = #{jobId}
+                              AND deleted = false
+                              and status != 'rejected') rs ON ts.id = rs.task_session_id
+        WHERE ts.job_id = #{jobId}
+          and ts.status = 'pending_review'
+          and ts.deleted = false
+          AND NOT EXISTS(
+                SELECT 1
+                FROM review_session
+                WHERE job_id = #{jobId}
+                  AND task_session_id = ts.id
+                  AND user_id = #{uid}
+                  And deleted = false
+                  AND status != 'rejected'
     )
-    GROUP BY ts.id
-    HAVING COUNT(rs.task_session_id) &lt; #{maxSession}
-    limit 5;
- </select>
+        GROUP BY ts.id
+        HAVING COUNT(rs.task_session_id) &lt; #{maxSession} limit 5;
+    </select>
+
+    <!--<select id="takeReviewJobsForUserOld">-->
+    <!--  SELECT t.* FROM task_session t-->
+    <!--  left join review_session rs on rs.task_session_id = t.id and rs.user_id = #{uid}-->
+    <!--  left join skip_task_session skp on skp.task_session_id = t.id and skp.user_id = #{uid} and skp.deleted = false-->
+    <!--  left join (select id, task_session_id, user_id, deleted, status, ROW_NUMBER() OVER (PARTITION BY id) as ct from review_session) r-->
+    <!--  on t.id = r.task_session_id and r.deleted = false and rs.deleted = false and r.status != 'rejected'-->
+    <!--  where t.status = 'pending_review' and t.job_id = #{jobId}-->
+    <!--  and rs.id is null-->
+    <!--  and skp.id is null-->
+    <!--  and t.deleted = false and (r.ct &lt; #{repeatCount} or r.id is null) order by t.id limit #{sessionCount};-->
+    <!--</select>-->
+    <select id="takeReviewJobsForUser">
+        SELECT t.*
+        FROM task_session t
+        where t.status = 'pending_review'
+          and t.job_id = #{jobId}
+          and t.id not in
+              (select task_session_id
+               from review_session
+               where user_id = #{uid}
+                 and job_id = #{jobId}
+                 and deleted = false)
+          and t.id not in (select task_session_id
+                           from skip_task_session
+                           where user_id = #{uid}
+                             and job_id = #{jobId}
+                             and deleted = false)
+          and t.id not in (select task_session_id
+                           from task_session_review_count
+                           where job_id = #{jobId}
+                             and deleted = false
+                             and review_count &gt;= #{repeatCount})
+          and t.deleted = false
+        order by t.id limit #{sessionCount};
+    </select>
+
+    <select id="refreshTaskQueue" resultType="java.lang.Long">
+        SELECT t.id
+        FROM task_session t
+        where t.status = 'pending_review'
+          and t.job_id = #{jobId}
+          and t.id not in (select task_session_id
+                           from task_session_review_count
+                           where job_id = #{jobId}
+                             and deleted = false
+                             and review_count &gt;= #{repeatCount})
+          and t.deleted = false
+        order by t.id limit #{limit};
+    </select>
 
-<!--<select id="takeReviewJobsForUserOld">-->
-<!--  SELECT t.* FROM task_session t-->
-<!--  left join review_session rs on rs.task_session_id = t.id and rs.user_id = #{uid}-->
-<!--  left join skip_task_session skp on skp.task_session_id = t.id and skp.user_id = #{uid} and skp.deleted = false-->
-<!--  left join (select id, task_session_id, user_id, deleted, status, ROW_NUMBER() OVER (PARTITION BY id) as ct from review_session) r-->
-<!--  on t.id = r.task_session_id and r.deleted = false and rs.deleted = false and r.status != 'rejected'-->
-<!--  where t.status = 'pending_review' and t.job_id = #{jobId}-->
-<!--  and rs.id is null-->
-<!--  and skp.id is null-->
-<!--  and t.deleted = false and (r.ct &lt; #{repeatCount} or r.id is null) order by t.id limit #{sessionCount};-->
-<!--</select>-->
-<select id="takeReviewJobsForUser">
-  SELECT t.* FROM task_session t
-  where t.status = 'pending_review' and t.job_id = #{jobId}
-  and t.id not in (select task_session_id from review_session where user_id = #{uid} and job_id = #{jobId} and deleted = false)
-  and t.id not in (select task_session_id from skip_task_session where user_id = #{uid} and job_id = #{jobId} and deleted = false)
-  and t.id not in (select task_session_id from task_session_review_count where job_id = #{jobId} and deleted = false and review_count &gt;= #{repeatCount})
-  and t.deleted = false order by t.id limit #{sessionCount};
-</select>
-<select id="countReviewJobsForUser">
-  SELECT count(t.id) FROM task_session t
-  where t.status = 'pending_review' and t.job_id = #{jobId}
-  and t.id not in (select task_session_id from review_session where user_id = #{uid} and job_id = #{jobId} and deleted = false)
-  and t.id not in (select task_session_id from skip_task_session where user_id = #{uid} and job_id = #{jobId} and deleted = false)
-    and t.id not in (select task_session_id from task_session_review_count where job_id = #{jobId} and deleted = false and review_count &gt;= #{repeatCount})
-  and t.deleted = false;
-</select>
+    <select id="countReviewJobsForUser">
+        SELECT count(t.id)
+        FROM task_session t
+        where t.status = 'pending_review'
+          and t.job_id = #{jobId}
+          and t.id not in
+              (select task_session_id
+               from review_session
+               where user_id = #{uid}
+                 and job_id = #{jobId}
+                 and deleted = false)
+          and t.id not in (select task_session_id
+                           from skip_task_session
+                           where user_id = #{uid}
+                             and job_id = #{jobId}
+                             and deleted = false)
+          and t.id not in (select task_session_id
+                           from task_session_review_count
+                           where job_id = #{jobId}
+                             and deleted = false
+                             and review_count &gt;= #{repeatCount})
+          and t.deleted = false;
+    </select>
 
     <select id="takeReviewJobForJobUser">
-        SELECT ts.id, ts.job_user_id, ts.job_id, ts.task_id, ts.user_id, ts.user_role,
-               ts.answer, ts.feedback, ts.status, ts.reviewer_status, ts.spotter_status,
-               ts.nm_review_status, ts.am_review_status, ts.client_review_status,
-               ts.duration,ts.deleted, ts.created_at, ts.updated_at
+        SELECT ts.id,
+               ts.job_user_id,
+               ts.job_id,
+               ts.task_id,
+               ts.user_id,
+               ts.user_role,
+               ts.answer,
+               ts.feedback,
+               ts.status,
+               ts.reviewer_status,
+               ts.spotter_status,
+               ts.nm_review_status,
+               ts.am_review_status,
+               ts.client_review_status,
+               ts.duration,
+               ts.deleted,
+               ts.created_at,
+               ts.updated_at
         FROM task_session ts
-                 LEFT JOIN (
-            SELECT task_session_id
-            FROM review_session
-            WHERE job_id = #{jobId}
-              AND deleted = false and status != 'rejected'
-        ) rs ON ts.id = rs.task_session_id
-        WHERE ts.job_id = #{jobId} and ts.status = 'pending_review' and ts.deleted = false
-          AND NOT EXISTS (
+                 LEFT JOIN (SELECT task_session_id
+                            FROM review_session
+                            WHERE job_id = #{jobId}
+                              AND deleted = false
+                              and status != 'rejected') rs ON ts.id = rs.task_session_id
+        WHERE ts.job_id = #{jobId}
+          and ts.status = 'pending_review'
+          and ts.deleted = false
+          AND NOT EXISTS(
                 SELECT 1
                 FROM review_session
                 WHERE job_user_id = #{jobUserId}
-                  AND task_session_id  = ts.id
+                  AND task_session_id = ts.id
                   And deleted = false
                   AND status != 'rejected'
     )
         GROUP BY ts.id
-        HAVING COUNT(rs.task_session_id) &lt; #{maxSession}
-            limit 5;
+        HAVING COUNT(rs.task_session_id) &lt; #{maxSession} limit 5;
     </select>
 
-  <select id="takeSpotJobForUser">
-    SELECT ts.id, ts.job_user_id, ts.job_id, ts.task_id, ts.user_id, ts.user_role, ts.answer,
-           ts.feedback, ts.status, ts.reviewer_status, ts.spotter_status,
-           ts.nm_review_status, ts.am_review_status, ts.client_review_status,
-           ts.duration, ts.deleted, ts.created_at, ts.updated_at
-    FROM task_session ts
-           LEFT JOIN (
-      SELECT task_session_id
-      FROM spot_session
-      WHERE job_id = #{jobId}
-        AND deleted = false and status != 'rejected'
-    ) ss ON ts.id = ss.task_session_id
-    WHERE ts.job_id = #{jobId} and ts.status = 'pending_spot' and ts.deleted = false
-      AND NOT EXISTS (
-      SELECT 1
-      FROM spot_session
-      WHERE job_id = #{jobId}
-        AND task_session_id  = ts.id
-        AND user_id = #{uid}
-        And deleted = false
-        AND status != 'rejected'
+    <select id="takeSpotJobForUser">
+        SELECT ts.id,
+               ts.job_user_id,
+               ts.job_id,
+               ts.task_id,
+               ts.user_id,
+               ts.user_role,
+               ts.answer,
+               ts.feedback,
+               ts.status,
+               ts.reviewer_status,
+               ts.spotter_status,
+               ts.nm_review_status,
+               ts.am_review_status,
+               ts.client_review_status,
+               ts.duration,
+               ts.deleted,
+               ts.created_at,
+               ts.updated_at
+        FROM task_session ts
+                 LEFT JOIN (SELECT task_session_id
+                            FROM spot_session
+                            WHERE job_id = #{jobId}
+                              AND deleted = false
+                              and status != 'rejected') ss ON ts.id = ss.task_session_id
+        WHERE ts.job_id = #{jobId}
+          and ts.status = 'pending_spot'
+          and ts.deleted = false
+          AND NOT EXISTS(
+                SELECT 1
+                FROM spot_session
+                WHERE job_id = #{jobId}
+                  AND task_session_id = ts.id
+                  AND user_id = #{uid}
+                  And deleted = false
+                  AND status != 'rejected'
     )
-    GROUP BY ts.id
-    HAVING COUNT(ss.task_session_id) &lt; #{maxSession}
-    limit 5;
-  </select>
-  <select id="getSubmissionCountByBatchId" resultType="java.lang.Long">
-      select count(task_session.*) from task_session
-                                          join job on task_session.job_id = job.id
-      where job.batch_id = #{batchId} and job.deleted = false
-        and task_session.deleted = false
-        and task_session.status in ('pending_spot', 'finished');
-  </select>
+        GROUP BY ts.id
+        HAVING COUNT(ss.task_session_id) &lt; #{maxSession} limit 5;
+    </select>
+    <select id="getSubmissionCountByBatchId" resultType="java.lang.Long">
+        select count(task_session.*)
+        from task_session
+                 join job on task_session.job_id = job.id
+        where job.batch_id = #{batchId}
+          and job.deleted = false
+          and task_session.deleted = false
+          and task_session.status in ('pending_spot', 'finished');
+    </select>
     <select id="getRequesterIdByTaskSessionId" resultType="java.lang.Long">
-      select batch.owner_id from batch
-      join job on batch.id = job.batch_id
-      join task_session ts on ts.job_id = job.id
-      where ts.id = #{id} limit 1;
+        select batch.owner_id
+        from batch
+                 join job on batch.id = job.batch_id
+                 join task_session ts on ts.job_id = job.id
+        where ts.id = #{id} limit 1;
     </select>
-  <update id="resetReviewStatusByJobAndReviewerId">
-    update task_session
-    set reviewer_status = 'none', status = 'pending_review'
-    where
-        job_id = #{jobId} and deleted = false and spotter_status = 'none'
-    and id in (select distinct rs.task_session_id
-               from review_session rs where rs.user_id = #{reviewerId}
-                                        and rs.job_id = #{jobId}
-                                        and rs.deleted = false
-                                        and rs.status != 'spotted');
-  </update>
-  <select id="getPendingAuditSessions" resultType="ai.saharaa.model.TaskSession">
-    SELECT ts.id, ts.job_user_id, ts.job_id, ts.task_id, ts.user_id, ts.user_role, ts.answer,
-           ts.feedback, ts.status, ts.reviewer_status, ts.spotter_status,
-           ts.nm_review_status, ts.am_review_status, ts.client_review_status,
-           ts.duration, ts.deleted, ts.created_at, ts.updated_at
-    FROM task_session ts
-    left join review_session rs on rs.task_session_id = ts.id
-    WHERE ts.job_id = #{jobId} and ts.status = 'pending_spot' and ts.deleted = false
-      and rs.deleted = false and rs.status != 'rejected'
-      AND NOT EXISTS (
-      SELECT 1
-      FROM spot_session
-      WHERE job_id = #{jobId}
-        AND task_session_id  = ts.id
-        And deleted = false
-        and status != 'rejected'
-        and status != 'pending'
-      )
-    order by ts.id
-    offset #{offset}
-    limit #{pageSize};
-  </select>
-  <select id="getPendingAuditSessionCount" resultType="java.lang.Long">
-    SELECT count(ts.id)
-    FROM task_session ts
-    WHERE ts.job_id = #{jobId} and ts.status = 'pending_spot' and ts.deleted = false
+    <update id="resetReviewStatusByJobAndReviewerId">
+        update task_session
+        set reviewer_status = 'none',
+            status          = 'pending_review'
+        where job_id = #{jobId}
+          and deleted = false
+          and spotter_status = 'none'
+          and id in (select distinct rs.task_session_id
+                     from review_session rs
+                     where rs.user_id = #{reviewerId}
+                       and rs.job_id = #{jobId}
+                       and rs.deleted = false
+                       and rs.status
+            != 'spotted');
+    </update>
+    <select id="getPendingAuditSessions" resultType="ai.saharaa.model.TaskSession">
+        SELECT ts.id,
+               ts.job_user_id,
+               ts.job_id,
+               ts.task_id,
+               ts.user_id,
+               ts.user_role,
+               ts.answer,
+               ts.feedback,
+               ts.status,
+               ts.reviewer_status,
+               ts.spotter_status,
+               ts.nm_review_status,
+               ts.am_review_status,
+               ts.client_review_status,
+               ts.duration,
+               ts.deleted,
+               ts.created_at,
+               ts.updated_at
+        FROM task_session ts
+                 left join review_session rs on rs.task_session_id = ts.id
+        WHERE ts.job_id = #{jobId}
+          and ts.status = 'pending_spot'
+          and ts.deleted = false
+          and rs.deleted = false
+          and rs.status != 'rejected'
       AND NOT EXISTS (
       SELECT 1
       FROM spot_session
       WHERE job_id = #{jobId}
-        AND task_session_id  = ts.id
-        And deleted = false
-        and status != 'rejected'
+          AND task_session_id = ts.id
+          And deleted = false
+          and status != 'rejected'
+          and status != 'pending'
+            )
+        order by ts.id
+        offset #{offset} limit #{pageSize};
+    </select>
+    <select id="getPendingAuditSessionCount" resultType="java.lang.Long">
+        SELECT count(ts.id)
+        FROM task_session ts
+        WHERE ts.job_id = #{jobId}
+          and ts.status = 'pending_spot'
+          and ts.deleted = false
+          AND NOT EXISTS(
+                SELECT 1
+                FROM spot_session
+                WHERE job_id = #{jobId}
+                  AND task_session_id = ts.id
+                  And deleted = false
+                  and status != 'rejected'
         and status != 'pending'
     );
-  </select>
-  <select id="getUserStat" resultType="ai.saharaa.model.stat.TaskSessionUserStat">
-    select count(case when ts.status != 'rejected' then 1 end) as session_count,
-           sum(case when ts.status != 'rejected' then ts.duration end) as duration,
-           count(case when ts.status != 'rejected' and ts.reviewer_status = 'approve' then 1 end) as approved,
-           count(case when ts.status != 'rejected' and ts.reviewer_status = 'revised' then 1 end) as revised,
-           count(case when ts.status != 'rejected' and ts.feedback != '' then 1 end) as feedback_count,
-           count(case when ts.status = 'rejected' then 1 end) as rejected
-    from task_session ts
-    where ts.user_id = #{uid} and ts.job_id = #{jobId}
-      and ts.deleted = false;
-  </select>
-  <select id="getTaskSessionStat" resultType="ai.saharaa.model.stat.TaskSessionUserStat">
-    select count(ts.id) as session_count,
-           sum(ts.duration) as duration,
-           count(case when ts.reviewer_status = 'approve' then 1 end) as approved,
-           count(case when ts.reviewer_status = 'revised' then 1 end) as revised,
-           count(case when ts.feedback != '' then 1 end) as feedbackCount
-    from task_session ts
-    where ts.job_id = #{jobId} and ts.deleted = false and ts.status != 'pending' and ts.status != 'rejected';
-  </select>
+    </select>
+    <select id="getUserStat" resultType="ai.saharaa.model.stat.TaskSessionUserStat">
+        select count(case when ts.status != 'rejected' then 1 end)                                    as session_count,
+               sum(case when ts.status != 'rejected' then ts.duration end)                            as duration,
+               count(case when ts.status != 'rejected' and ts.reviewer_status = 'approve' then 1 end) as approved,
+               count(case when ts.status != 'rejected' and ts.reviewer_status = 'revised' then 1 end) as revised,
+               count(case when ts.status != 'rejected' and ts.feedback != '' then 1 end)              as feedback_count,
+               count(case when ts.status = 'rejected' then 1 end)                                     as rejected
+        from task_session ts
+        where ts.user_id = #{uid}
+          and ts.job_id = #{jobId}
+          and ts.deleted = false;
+    </select>
+    <select id="getTaskSessionStat" resultType="ai.saharaa.model.stat.TaskSessionUserStat">
+        select count(ts.id)                                               as session_count,
+               sum(ts.duration)                                           as duration,
+               count(case when ts.reviewer_status = 'approve' then 1 end) as approved,
+               count(case when ts.reviewer_status = 'revised' then 1 end) as revised,
+               count(case when ts.feedback != '' then 1 end)              as feedbackCount
+        from task_session ts
+        where ts.job_id = #{jobId}
+          and ts.deleted = false
+          and ts.status != 'pending' and ts.status != 'rejected';
+    </select>
     <select id="getNodeStat" resultType="ai.saharaa.model.stat.TaskSessionNodeStat">
-      select count(ts.id) as reviewed_count,
-             count(case when ts.client_review_status = 'approved' then 1 end) as approved_count,
-             count(case when ts.client_review_status = 'rejected' then 1 end) as rejected_count
-      from task_session ts
-      where ts.job_id = #{jobId} and ts.client_review_status != 'none'
+        select count(ts.id)                                                     as reviewed_count,
+               count(case when ts.client_review_status = 'approved' then 1 end) as approved_count,
+               count(case when ts.client_review_status = 'rejected' then 1 end) as rejected_count
+        from task_session ts
+        where ts.job_id = #{jobId}
+          and ts.client_review_status != 'none'
         and ts.deleted = false;
     </select>
-  <select id="getLabelerTimeTotal" resultType="java.lang.Long">
-    select sum(ts.duration) as duration
-    from task_session ts
-    where ts.job_id = #{jobId} and ts.user_id = #{uid} and ts.user_role = 'Labeler'
-      and ts.deleted = false and ts.status != 'pending';
-  </select>
+    <select id="getLabelerTimeTotal" resultType="java.lang.Long">
+        select sum(ts.duration) as duration
+        from task_session ts
+        where ts.job_id = #{jobId}
+          and ts.user_id = #{uid}
+          and ts.user_role = 'Labeler'
+          and ts.deleted = false
+          and ts.status != 'pending';
+    </select>
 
-  <select id="getRejectedBySpotterTaskSessions" resultType="ai.saharaa.model.TaskSession">
-    select ts.* from task_session ts join review_session rs
-    on ts.id = rs.task_session_id
-    where ts.job_id = #{jobId} and rs.status = 'rejected'
-  </select>
+    <select id="getRejectedBySpotterTaskSessions" resultType="ai.saharaa.model.TaskSession">
+        select ts.*
+        from task_session ts
+                 join review_session rs
+                      on ts.id = rs.task_session_id
+        where ts.job_id = #{jobId}
+          and rs.status = 'rejected'
+    </select>
 
-  <select id="takeAuditJobForAm" resultType="ai.saharaa.model.TaskSession">
-    select ts.* from task_session ts
-    left join am_audit_session aas
-    on aas.task_session_id = ts.id and aas.result = 'none'
-    where ts.job_id = #{jobId}
-    and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and ts.deleted = false and aas.id is null
-    order by random() limit 1;
-  </select>
+    <select id="takeAuditJobForAm" resultType="ai.saharaa.model.TaskSession">
+        select ts.*
+        from task_session ts
+                 left join am_audit_session aas
+                           on aas.task_session_id = ts.id and aas.result = 'none'
+        where ts.job_id = #{jobId}
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and ts.deleted = false
+          and aas.id is null
+        order by random() limit 1;
+    </select>
 
-  <select id="countApprovedAuditForAm" resultType="java.lang.Long">
-    select count(*) from task_session ts
-    join am_audit_session aas
-    on aas.task_session_id = ts.id
-    where ts.job_id = #{jobId} and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and aas.status = 'approved' and aas.result = 'none' and aas.deleted = false
-  </select>
+    <select id="countApprovedAuditForAm" resultType="java.lang.Long">
+        select count(*)
+        from task_session ts
+                 join am_audit_session aas
+                      on aas.task_session_id = ts.id
+        where ts.job_id = #{jobId}
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and aas.status = 'approved'
+          and aas.result = 'none'
+          and aas.deleted = false
+    </select>
 
-  <select id="countRejectedAuditForAm" resultType="java.lang.Long">
-    select count(*) from task_session ts
-    join am_audit_session aas
-     on aas.task_session_id = ts.id
-    where ts.job_id = #{jobId} and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and aas.status = 'rejected' and aas.result = 'none' and aas.deleted = false
-  </select>
+    <select id="countRejectedAuditForAm" resultType="java.lang.Long">
+        select count(*)
+        from task_session ts
+                 join am_audit_session aas
+                      on aas.task_session_id = ts.id
+        where ts.job_id = #{jobId}
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and aas.status = 'rejected'
+          and aas.result = 'none'
+          and aas.deleted = false
+    </select>
 
-  <select id="countPendingAuditForAm" resultType="java.lang.Long">
-    select count(*) from task_session ts
-    left join am_audit_session aas
-    on aas.task_session_id = ts.id and aas.result = 'none'
-    where ts.job_id = #{jobId}
-    and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and ts.deleted = false and aas.id is null
-  </select>
+    <select id="countPendingAuditForAm" resultType="java.lang.Long">
+        select count(*)
+        from task_session ts
+                 left join am_audit_session aas
+                           on aas.task_session_id = ts.id and aas.result = 'none'
+        where ts.job_id = #{jobId}
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and ts.deleted = false
+          and aas.id is null
+    </select>
 
-  <select id="takeAuditJobForClient" resultType="ai.saharaa.model.TaskSession">
-    select ts.* from task_session ts
-    left join client_audit_session cas
-    on cas.task_session_id = ts.id and cas.result = 'none'
-    where ts.job_id in (
-      select id from job where batch_id = #{batchId} and deleted = false and status in ('am_audit', 'committed')
-    )
-    and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and ts.deleted = false and cas.id is null
-    order by random() limit 1;
-  </select>
+    <select id="takeAuditJobForClient" resultType="ai.saharaa.model.TaskSession">
+        select ts.*
+        from task_session ts
+                 left join client_audit_session cas
+                           on cas.task_session_id = ts.id and cas.result = 'none'
+        where ts.job_id in
+              (select id
+               from job
+               where batch_id = #{batchId}
+                 and deleted = false
+                 and status in ('am_audit', 'committed'))
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and ts.deleted = false
+          and cas.id is null
+        order by random() limit 1;
+    </select>
 
-  <select id="countApprovedAuditForClient" resultType="java.lang.Long">
-    select count(*) from task_session ts
-    join client_audit_session cas
-    on cas.task_session_id = ts.id
-    left join job j on ts.job_id = j.id
-    where ts.job_id in (
-      select id from job where batch_id = #{batchId} and deleted = false
-    )
-    and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and cas.status = 'approved' and cas.result = 'none' and cas.deleted = false
-    and j.status != 'dropped'
-  </select>
+    <select id="countApprovedAuditForClient" resultType="java.lang.Long">
+        select count(*)
+        from task_session ts
+                 join client_audit_session cas
+                      on cas.task_session_id = ts.id
+                 left join job j on ts.job_id = j.id
+        where ts.job_id in (select id
+                            from job
+                            where batch_id = #{batchId}
+                              and deleted = false)
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and cas.status = 'approved'
+          and cas.result = 'none'
+          and cas.deleted = false
+          and j.status != 'dropped'
+    </select>
 
-  <select id="countRejectedAuditForClient" resultType="java.lang.Long">
-    select count(*) from task_session ts
-    join client_audit_session cas
-    on cas.task_session_id = ts.id
-    left join job j on ts.job_id = j.id
-    where ts.job_id in (
-      select id from job where batch_id = #{batchId} and deleted = false
-    )
-    and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and cas.status = 'rejected' and cas.result = 'none' and cas.deleted = false
-    and j.status != 'dropped'
-  </select>
+    <select id="countRejectedAuditForClient" resultType="java.lang.Long">
+        select count(*)
+        from task_session ts
+                 join client_audit_session cas
+                      on cas.task_session_id = ts.id
+                 left join job j on ts.job_id = j.id
+        where ts.job_id in (select id
+                            from job
+                            where batch_id = #{batchId}
+                              and deleted = false)
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and cas.status = 'rejected'
+          and cas.result = 'none'
+          and cas.deleted = false
+          and j.status != 'dropped'
+    </select>
 
-  <select id="countPendingAuditForClient" resultType="java.lang.Long">
-    select count(*) from task_session ts
-    left join client_audit_session cas
-    on cas.task_session_id = ts.id and cas.result = 'none'
-    left join job j on ts.job_id = j.id
-    where ts.job_id in (
-      select id from job where batch_id = #{batchId} and deleted = false
-    )
-    and ts.status in ('finish', 'pending_review', 'pending_spot')
-    and ts.deleted = false and cas.id is null
-    and j.status != 'dropped'
-  </select>
+    <select id="countPendingAuditForClient" resultType="java.lang.Long">
+        select count(*)
+        from task_session ts
+                 left join client_audit_session cas
+                           on cas.task_session_id = ts.id and cas.result = 'none'
+                 left join job j on ts.job_id = j.id
+        where ts.job_id in (select id
+                            from job
+                            where batch_id = #{batchId}
+                              and deleted = false)
+          and ts.status in ('finish', 'pending_review', 'pending_spot')
+          and ts.deleted = false
+          and cas.id is null
+          and j.status != 'dropped'
+    </select>
 
-  <select id="getTaskIdsInWorking" resultType="java.lang.Long">
-    SELECT DISTINCT ts.task_id
-    FROM task_session AS ts
-    WHERE ts.job_id = #{jobId} AND ts.deleted = false
-    AND (ts.status = 'pending' OR ts.status = 'pending_machine_review' OR ts.status = 'pending_review')
-    ORDER BY ts.task_id
-    LIMIT #{maxCount}
-  </select>
-  <select id="takeReviewJobsForUserAssembled">
-    WITH FirstElement AS (
-      SELECT t.job_user_id, t.lot_number
-      FROM task_session t
-      WHERE t.status = 'pending_review'
-        AND t.job_id = #{jobId}
-        AND t.id NOT IN (
-        SELECT task_session_id
-        FROM review_session
-        WHERE user_id = #{uid}
-          AND job_id = #{jobId}
-          AND deleted = false
-      )
-        AND t.id NOT IN (
-        SELECT task_session_id
-        FROM skip_task_session
-        WHERE user_id = #{uid}
-          AND job_id = #{jobId}
-          AND deleted = false
-      )
-        AND t.id NOT IN (
-        SELECT task_session_id
-        FROM review_session
-        WHERE job_id = #{jobId}
-          AND deleted = false
-        GROUP BY task_session_id HAVING count(id) &gt;= #{repeatCount}
-      )
-        AND t.deleted = false
-      LIMIT 1
-      )
-    SELECT t.*
-    FROM task_session t
-           JOIN FirstElement f ON t.job_user_id = f.job_user_id AND t.lot_number = f.lot_number
-    WHERE t.status = 'pending_review'
-      AND t.job_id = #{jobId}
-      AND t.id NOT IN (
-      SELECT task_session_id
-      FROM review_session
-      WHERE user_id = #{uid}
-        AND job_id = #{jobId}
-        AND deleted = false
-    )
-      AND t.id NOT IN (
-      SELECT task_session_id
-      FROM skip_task_session
-      WHERE user_id = #{uid}
-        AND job_id = #{jobId}
-        AND deleted = false
-    )
-      AND t.id NOT IN (
-      SELECT task_session_id
-      FROM review_session
-      WHERE job_id = #{jobId}
-        AND deleted = false
-      GROUP BY task_session_id HAVING count(id) &gt;= #{repeatCount}
-    )
-      AND t.deleted = false  order by t.id limit #{sessionCount};
-  </select>
+    <select id="getTaskIdsInWorking" resultType="java.lang.Long">
+        SELECT DISTINCT ts.task_id
+        FROM task_session AS ts
+        WHERE ts.job_id = #{jobId}
+          AND ts.deleted = false
+          AND (ts.status = 'pending' OR ts.status = 'pending_machine_review' OR ts.status = 'pending_review')
+        ORDER BY ts.task_id
+            LIMIT #{maxCount}
+    </select>
+    <select id="takeReviewJobsForUserAssembled">
+        WITH FirstElement AS (SELECT t.job_user_id, t.lot_number
+                              FROM task_session t
+                              WHERE t.status = 'pending_review'
+                                AND t.job_id = #{jobId}
+                                AND t.id NOT IN (SELECT task_session_id
+                                                 FROM review_session
+                                                 WHERE user_id = #{uid}
+                                                   AND job_id = #{jobId}
+                                                   AND deleted = false)
+                                AND t.id NOT IN (SELECT task_session_id
+                                                 FROM skip_task_session
+                                                 WHERE user_id = #{uid}
+                                                   AND job_id = #{jobId}
+                                                   AND deleted = false)
+                                AND t.id NOT IN (SELECT task_session_id
+                                                 FROM review_session
+                                                 WHERE job_id = #{jobId}
+                                                   AND deleted = false
+                                                 GROUP BY task_session_id
+                                                 HAVING count(id) &gt;= #{repeatCount})
+                                AND t.deleted = false
+            LIMIT 1
+            )
+        SELECT t.*
+        FROM task_session t
+                 JOIN FirstElement f ON t.job_user_id = f.job_user_id AND t.lot_number = f.lot_number
+        WHERE t.status = 'pending_review'
+          AND t.job_id = #{jobId}
+          AND t.id NOT IN (SELECT task_session_id
+                           FROM review_session
+                           WHERE user_id = #{uid}
+                             AND job_id = #{jobId}
+                             AND deleted = false)
+          AND t.id NOT IN (SELECT task_session_id
+                           FROM skip_task_session
+                           WHERE user_id = #{uid}
+                             AND job_id = #{jobId}
+                             AND deleted = false)
+          AND t.id NOT IN (SELECT task_session_id
+                           FROM review_session
+                           WHERE job_id = #{jobId}
+                             AND deleted = false
+                           GROUP BY task_session_id
+                           HAVING count(id) &gt;= #{repeatCount})
+          AND t.deleted = false
+        order by t.id limit #{sessionCount};
+    </select>
 
-  <select id="takeReviewJobsForUserAssembledByJobUserId">
-    SELECT t.*
-    FROM task_session t
-    WHERE t.status = 'pending_review'
-      AND t.job_id = #{jobId}
-      AND t.job_user_id = #{jobUserId}
-      AND t.lot_number = #{lotNumber}
-      AND t.id NOT IN (
-      SELECT task_session_id
-      FROM review_session
-      WHERE user_id = #{uid}
-        AND job_id = #{jobId}
-        AND deleted = false
-    )
-      AND t.id NOT IN (
-      SELECT task_session_id
-      FROM skip_task_session
-      WHERE user_id = #{uid}
-        AND job_id = #{jobId}
-        AND deleted = false
-    )
-      AND t.id NOT IN (
-      SELECT task_session_id
-      FROM review_session
-      WHERE job_id = #{jobId}
-        AND deleted = false
-      GROUP BY task_session_id HAVING count(id) &gt;= #{repeatCount}
-    )
-      AND t.deleted = false  order by t.id limit #{sessionCount};
-  </select>
-</mapper>
\ No newline at end of file
+    <select id="takeReviewJobsForUserAssembledByJobUserId">
+        SELECT t.*
+        FROM task_session t
+        WHERE t.status = 'pending_review'
+          AND t.job_id = #{jobId}
+          AND t.job_user_id = #{jobUserId}
+          AND t.lot_number = #{lotNumber}
+          AND t.id NOT IN (SELECT task_session_id
+                           FROM review_session
+                           WHERE user_id = #{uid}
+                             AND job_id = #{jobId}
+                             AND deleted = false)
+          AND t.id NOT IN (SELECT task_session_id
+                           FROM skip_task_session
+                           WHERE user_id = #{uid}
+                             AND job_id = #{jobId}
+                             AND deleted = false)
+          AND t.id NOT IN (SELECT task_session_id
+                           FROM review_session
+                           WHERE job_id = #{jobId}
+                             AND deleted = false
+                           GROUP BY task_session_id
+                           HAVING count(id) &gt;= #{repeatCount})
+          AND t.deleted = false
+        order by t.id limit #{sessionCount};
+    </select>
+</mapper>
diff --git a/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java b/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java
index 5f4f63ea..50612f93 100644
--- a/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java
+++ b/src/test/java/ai/saharaa/actors/tasks/MachineReviewBehaviorTest.java
@@ -8,6 +8,7 @@ import static org.junit.jupiter.api.Assertions.assertTrue;
 
 import ai.saharaa.config.cluster.ClusterConfiguration;
 import ai.saharaa.daos.*;
+import ai.saharaa.distribution.CommonCounterManager;
 import ai.saharaa.dto.job.SubmitAnswerDTO;
 import ai.saharaa.dto.job.SubmitAnswersItemDTO;
 import ai.saharaa.dto.task.AnswerDetailsDTO;
@@ -90,6 +91,9 @@ public class MachineReviewBehaviorTest {
   @Autowired
   private BatchDao batchDao;
 
+  @Autowired
+  private CommonCounterManager counterManager;
+
   @Autowired
   private ClusterConfiguration clusterConfiguration;
 
@@ -360,6 +364,7 @@ public class MachineReviewBehaviorTest {
             jobUserPointsDao,
             taskAndReviewSessionDao,
             batchDao,
+            counterManager,
             restClient,
             taskQuestionMapper,
             objectMapper,
@@ -442,6 +447,7 @@ public class MachineReviewBehaviorTest {
               jobUserPointsDao,
               taskAndReviewSessionDao,
               batchDao,
+              counterManager,
               restClient,
               taskQuestionMapper,
               objectMapper,
@@ -499,6 +505,7 @@ public class MachineReviewBehaviorTest {
               jobUserPointsDao,
               taskAndReviewSessionDao,
               batchDao,
+              counterManager,
               restClient,
               taskQuestionMapper,
               objectMapper,
diff --git a/src/test/resources/application.properties b/src/test/resources/application.properties
index 005ef858..d9de3e90 100644
--- a/src/test/resources/application.properties
+++ b/src/test/resources/application.properties
@@ -6,7 +6,7 @@ spring.data.redis.ssl.enabled=${AI_REDIS_SSL_ENABLED:false}
 cache.default.db.index=0
 cache.api.db.index=1
 cache.rateLimit.db.index=2
-cache.auth.db.index=15
+cache.taskQueue.db.index=15
 # datasource-micrometer-spring-boot is current not compatible with embedded-database-spring-test
 # https://github.com/zonkyio/embedded-database-spring-test/issues/281
 jdbc.datasource-proxy.enabled=false
diff --git a/tmp/e3/b0/e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855.: b/tmp/e3/b0/e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855.:
new file mode 100644
index 00000000..e69de29b
