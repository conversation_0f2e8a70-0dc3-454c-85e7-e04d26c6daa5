FROM eclipse-temurin:17-jdk as builder
WORKDIR /application

RUN apt-get update && apt-get install -y maven

COPY . .

RUN ./gradlew build -x check -x spotlessApply --info
RUN cp build/libs/ai-platform-0.0.1-SNAPSHOT.jar application.jar
RUN java -Djarmode=tools -jar application.jar extract --layers --destination extracted



# ==================== runtime =========================
FROM eclipse-temurin:17-jdk
WORKDIR /application

#COPY --from=builder /application/build/libs/ai-platform-0.0.1-SNAPSHOT.jar application.jar



COPY --from=builder /application/extracted/dependencies/ ./
COPY --from=builder /application/extracted/spring-boot-loader/ ./
COPY --from=builder /application/extracted/snapshot-dependencies/ ./
COPY --from=builder /application/extracted/application/ ./

RUN mv lib/aspectjweaver-********.jar aspectjweaver.jar && \
    mv lib/kanela-agent-1.0.18.jar kanela-agent.jar

ENTRYPOINT ["java", "-javaagent:aspectjweaver.jar", "-javaagent:kanela-agent.jar", "-jar", "application.jar"]
