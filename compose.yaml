services:
  postgres:
    image: 'postgres:17'
    environment:
      - 'POSTGRES_DB=ai'
      - 'POSTGRES_PASSWORD=123456'
      - 'POSTGRES_USER=root'
      - 'PGDATA=/var/lib/postgresql/data/pgdata'
    volumes:
      - ./data/pg_data/data:/var/lib/postgresql/data/pgdata
    ports:
      - 5432:5432

  redis:
    image: 'redis:7'
    container_name: redis
    command: redis-server --requirepass 123456
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis/data:/data
  kafka:
    image: 'apache/kafka:latest'
    container_name: kafka
    ports:
      - "9092:9092"
