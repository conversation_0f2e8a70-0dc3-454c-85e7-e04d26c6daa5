# campfire-platform


## Integration/Unit Testing
### Dependencies
#### Docker
1. Install Docker in order to use DevContainer for testing
2. Currently we run postgresql in DevContainer for testing

## Code Formatting

The project is configured to apply Spotless formatting automatically during compilation. Additionally, you can set up
automatic code formatting with
the [Spotless Applier plugin for IntelliJ](https://plugins.jetbrains.com/plugin/22455-spotless-applier).
