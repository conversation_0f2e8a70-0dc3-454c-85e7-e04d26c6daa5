FROM eclipse-temurin:17-jdk as builder
WORKDIR /builder
ARG JAR_FILE=build/libs/ai-platform-0.0.1-SNAPSHOT.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=tools -jar application.jar extract --layers --destination extracted

FROM eclipse-temurin:17-jdk
WORKDIR /application
ARG CF_PRIVATE_KEY_FILE=build/cfpk.pem
COPY ${CF_PRIVATE_KEY_FILE} /opt/site-data/cfpk.pem
COPY --from=builder /builder/extracted/dependencies/ ./
COPY --from=builder /builder/extracted/spring-boot-loader/ ./
COPY --from=builder /builder/extracted/snapshot-dependencies/ ./
COPY --from=builder /builder/extracted/application/ ./
RUN mv lib/aspectjweaver-********.jar aspectjweaver.jar && \
    mv lib/kanela-agent-1.0.18.jar kanela-agent.jar

ENTRYPOINT ["java", "-javaagent:aspectjweaver.jar", "-javaagent:kanela-agent.jar", "-jar", "application.jar"]

