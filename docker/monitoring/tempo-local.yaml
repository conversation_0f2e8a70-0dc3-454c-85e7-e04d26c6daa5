server:
  http_listen_port: 3200

distributor:
  receivers:
    zipkin: #default port 9411
    otlp:
      protocols:
        # Spring's support of otlp grpc is being added
        # https://github.com/spring-projects/spring-boot/issues/41460
        http:
          # seems to be required if tempo is access by hostname (e.g. http://tempo)
          endpoint: "0.0.0.0:4318"

storage:
  trace:
    backend: local
    local:
      path: /tmp/tempo/blocks