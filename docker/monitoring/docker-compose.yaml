version: '3.7'

# reference: https://github.com/marios-code-path/path-to-springboot-3/blob/main/infra/docker-compose.yml
services:
  tempo:
    image: grafana/tempo
    extra_hosts: [ 'host.docker.internal:host-gateway' ]
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./tempo-local.yaml:/etc/tempo.yaml:ro
      - ./tempo-data:/tmp/tempo
    ports:
      - "14268"  # jaeger ingest
      - "9411:9411" # zipkin
      - "4318:4318" # otlp http

  loki:
    image: grafana/loki
    extra_hosts: [ 'host.docker.internal:host-gateway' ]
    command: [ "-config.file=/etc/loki/local-config.yaml" ]
    ports:
      - "3100:3100"                                   # loki needs to be exposed so it receives logs
    environment:
      - JAEGER_AGENT_HOST=tempo
      - JAEGER_ENDPOINT=http://tempo:14268/api/traces # send traces to Tempo
      - JAEGER_SAMPLER_TYPE=const
      - JAEGER_SAMPLER_PARAM=1

  prometheus:
    image: prom/prometheus
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    container_name: grafana
    ports:
      - "13000:3000"
    restart: unless-stopped
    volumes:
      - ./datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml