commit 13aeffd0e42a4251c75daddbd0d55eb9f0398247
Author: 1ci<PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Fri May 9 09:51:20 2025 +0800

    Revert "feat: web3j oracle addUserTimeRangeTask&addUserBarTask (#2188) (#2191)" (#2198)

diff --git a/helm/templates/configmap.yaml b/helm/templates/configmap.yaml
index 86b94fe7..45633f29 100644
--- a/helm/templates/configmap.yaml
+++ b/helm/templates/configmap.yaml
@@ -36,7 +36,6 @@ data:
   AI_WEB3_RPC_URL: "{{.Values.platform.settings.AI_WEB3_RPC_URL}}"
   AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS}}"
   AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS}}"
-  AI_WEB3_ACHIEVEMENT_ORACLE_PK: "{{.Values.platform.settings.AI_WEB3_ACHIEVEMENT_ORACLE_PK}}"
   GOOGLE_RECAPTCHA_SECRET_V3: "{{.Values.platform.settings.GOOGLE_RECAPTCHA_SECRET_V3}}"
   GOOGLE_RECAPTCHA_SECRET_V2: "{{.Values.platform.settings.GOOGLE_RECAPTCHA_SECRET_V2}}"
   AWS_ACCESS_KEY_ID: "{{.Values.platform.settings.AWS_ACCESS_KEY_ID}}"
diff --git a/helm/values.yaml b/helm/values.yaml
index 16839231..72e91dde 100644
--- a/helm/values.yaml
+++ b/helm/values.yaml
@@ -22,7 +22,6 @@ platform:
     AI_WEB3_RPC_URL: ""
     AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: ""
     AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS: ""
-    AI_WEB3_ACHIEVEMENT_ORACLE_PK: ""
     GOOGLE_RECAPTCHA_SECRET_V3: ""
     GOOGLE_RECAPTCHA_SECRET_V2: ""
     AWS_ACCESS_KEY_ID: ""
diff --git a/src/main/java/ai/saharaa/common/AchievementOracleSender.java b/src/main/java/ai/saharaa/common/AchievementOracleSender.java
index 2acc0a78..58625897 100644
--- a/src/main/java/ai/saharaa/common/AchievementOracleSender.java
+++ b/src/main/java/ai/saharaa/common/AchievementOracleSender.java
@@ -6,7 +6,7 @@ import ai.saharaa.daos.UserDao;
 import ai.saharaa.daos.achievement.AchievementOnContractDao;
 import ai.saharaa.daos.achievement.AchievementOracleBatchDao;
 import ai.saharaa.daos.achievement.AchievementOracleLogDao;
-import ai.saharaa.dto.UUIDMessageDTO;
+import ai.saharaa.dto.KafkaMessageDTO;
 import ai.saharaa.dto.achievement.AchievementProgressOracleDTO;
 import ai.saharaa.dto.achievement.AchievementProgressReq;
 import ai.saharaa.enums.AchievementOracleTxStatus;
@@ -173,11 +173,11 @@ public class AchievementOracleSender {
     var batchOracles = batchSplitList(achievementProgressOnchainBatchSize, messages);
 
     for (var oracles : batchOracles) {
-      sendMessage(UUIDMessageDTO.of(oracles));
+      sendMessage(KafkaMessageDTO.of(oracles));
     }
   }
 
-  private void sendMessage(UUIDMessageDTO<List<AchievementProgressOracleDTO>> message) {
+  private void sendMessage(KafkaMessageDTO<List<AchievementProgressOracleDTO>> message) {
     var uuid = message.getUuid().toString();
     try {
       String payload = objectMapper.writeValueAsString(message);
diff --git a/src/main/java/ai/saharaa/config/ContractConfig.java b/src/main/java/ai/saharaa/config/ContractConfig.java
index 1c4f4994..50aad985 100644
--- a/src/main/java/ai/saharaa/config/ContractConfig.java
+++ b/src/main/java/ai/saharaa/config/ContractConfig.java
@@ -17,21 +17,18 @@ public class ContractConfig {
 
   private final String rpcUrl;
   private final String credentialsPrivateKey;
-  private final String achievementOraclePrivateKey;
   private final String achievementManagerContractAddress;
   private final String achievementOnContractAddress;
 
   public ContractConfig(
       @Value("${ai.saharaa.web3.rpc_url}") String rpcUrl,
       @Value("${ai.saharaa.web3.credentials_pk}") String credentialsPrivateKey,
-      @Value("${ai.saharaa.web3.achievement_oracle_pk}") String achievementOraclePrivateKey,
       @Value("${ai.saharaa.web3.achievement_manager_contract_address}")
           String achievementManagerContractAddress,
       @Value("${ai.saharaa.web3.achievement_on_contract_address}")
           String achievementOnContractAddress) {
     this.rpcUrl = rpcUrl;
     this.credentialsPrivateKey = credentialsPrivateKey;
-    this.achievementOraclePrivateKey = achievementOraclePrivateKey;
     this.achievementManagerContractAddress = achievementManagerContractAddress;
     this.achievementOnContractAddress = achievementOnContractAddress;
   }
@@ -41,16 +38,6 @@ public class ContractConfig {
     return Web3j.build(new HttpService(rpcUrl));
   }
 
-  @Bean
-  public Long web3ChainId(Web3j web3j) throws Exception {
-    return web3j.ethChainId().send().getChainId().longValue();
-  }
-
-  @Bean
-  public Credentials achievementOracleCredentials() throws Exception {
-    return Credentials.create(achievementOraclePrivateKey);
-  }
-
   @Bean
   public LevelAchievementManager levelAchievementManager(Web3j web3j) throws Exception {
     var credentials = Credentials.create(credentialsPrivateKey);
@@ -62,11 +49,11 @@ public class ContractConfig {
   }
 
   @Bean
-  public AchievementOnContractManager achievementOnContractManager(
-      Web3j web3j, Credentials achievementOracleCredentials) throws Exception {
+  public AchievementOnContractManager achievementOnContractManager(Web3j web3j) throws Exception {
+    var credentials = Credentials.create(credentialsPrivateKey);
     var gasProvider = new DefaultGasProvider();
     var achievementOnContractManager = AchievementOnContractManager.load(
-        achievementOnContractAddress, web3j, achievementOracleCredentials, gasProvider);
+        achievementOnContractAddress, web3j, credentials, gasProvider);
     log.info("achievementOnContractManager created: {}", achievementOnContractManager);
     return achievementOnContractManager;
   }
diff --git a/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java b/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java
index bcff5ea4..d243702b 100644
--- a/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java
+++ b/src/main/java/ai/saharaa/daos/achievement/AchievementOnContractDao.java
@@ -165,10 +165,6 @@ public class AchievementOnContractDao
                 Objects.nonNull(achievement.getOracleSymbol()),
                 AchievementOnContract::getOracleSymbol,
                 achievement.getOracleSymbol())
-            .set(
-                Objects.nonNull(achievement.getProgressType()),
-                AchievementOnContract::getProgressType,
-                achievement.getProgressType())
             .set(
                 Objects.nonNull(achievement.getContractAddress()),
                 AchievementOnContract::getContractAddress,
diff --git a/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java b/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java
index 32e928ed..1ceb9ae4 100644
--- a/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java
+++ b/src/main/java/ai/saharaa/daos/achievement/AchievementOracleBatchDao.java
@@ -3,11 +3,9 @@ package ai.saharaa.daos.achievement;
 import ai.saharaa.enums.AchievementOracleTxStatus;
 import ai.saharaa.mappers.achievement.AchievementOracleBatchMapper;
 import ai.saharaa.model.achievement.AchievementOracleBatch;
-import ai.saharaa.utils.DateUtils;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
-import java.util.List;
 import lombok.AllArgsConstructor;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.stereotype.Component;
@@ -42,19 +40,7 @@ public class AchievementOracleBatchDao
         new UpdateWrapper<AchievementOracleBatch>()
             .lambda()
             .set(AchievementOracleBatch::getStatus, status)
-            .set(AchievementOracleBatch::getUpdatedAt, DateUtils.now())
             .eq(AchievementOracleBatch::getUuid, uuid)
             .eq(AchievementOracleBatch::getDeleted, false));
   }
-
-  public AchievementOracleBatch pickOneToSend() {
-    return achievementOracleBatchMapper.selectOne(new QueryWrapper<AchievementOracleBatch>()
-        .lambda()
-        .in(
-            AchievementOracleBatch::getStatus,
-            List.of(AchievementOracleTxStatus.PENDING, AchievementOracleTxStatus.FAILED))
-        .eq(AchievementOracleBatch::getDeleted, false)
-        .orderByAsc(AchievementOracleBatch::getUpdatedAt)
-        .last("LIMIT 1"));
-  }
 }
diff --git a/src/main/java/ai/saharaa/dto/KafkaMessageDTO.java b/src/main/java/ai/saharaa/dto/KafkaMessageDTO.java
new file mode 100644
index 00000000..1546eb52
--- /dev/null
+++ b/src/main/java/ai/saharaa/dto/KafkaMessageDTO.java
@@ -0,0 +1,16 @@
+package ai.saharaa.dto;
+
+import java.util.UUID;
+import lombok.Builder;
+import lombok.Data;
+
+@Data
+@Builder
+public class KafkaMessageDTO<T> {
+  private UUID uuid;
+  private T message;
+
+  public static <T> KafkaMessageDTO<T> of(T message) {
+    return KafkaMessageDTO.<T>builder().uuid(UUID.randomUUID()).message(message).build();
+  }
+}
diff --git a/src/main/java/ai/saharaa/dto/UUIDMessageDTO.java b/src/main/java/ai/saharaa/dto/UUIDMessageDTO.java
deleted file mode 100644
index dab39f76..00000000
--- a/src/main/java/ai/saharaa/dto/UUIDMessageDTO.java
+++ /dev/null
@@ -1,16 +0,0 @@
-package ai.saharaa.dto;
-
-import java.util.UUID;
-import lombok.Builder;
-import lombok.Data;
-
-@Data
-@Builder
-public class UUIDMessageDTO<T> {
-  private UUID uuid;
-  private T message;
-
-  public static <T> UUIDMessageDTO<T> of(T message) {
-    return UUIDMessageDTO.<T>builder().uuid(UUID.randomUUID()).message(message).build();
-  }
-}
diff --git a/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java b/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java
index 4a6ddaee..12dcef0b 100644
--- a/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java
+++ b/src/main/java/ai/saharaa/dto/achievement/AchievementOnContractCreateUpdateDTO.java
@@ -1,6 +1,5 @@
 package ai.saharaa.dto.achievement;
 
-import ai.saharaa.enums.AchievementProgressType;
 import lombok.AllArgsConstructor;
 import lombok.Builder;
 import lombok.Data;
@@ -15,7 +14,6 @@ public class AchievementOnContractCreateUpdateDTO {
   private Long seasonId;
   private String oracleSymbol;
   private String contractAddress;
-  private AchievementProgressType progressType;
   private Integer sort;
   private Boolean active;
 }
diff --git a/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java b/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java
index b2509336..18cef433 100644
--- a/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java
+++ b/src/main/java/ai/saharaa/services/achievement/AchievementOnContractService.java
@@ -124,7 +124,6 @@ public class AchievementOnContractService {
         .seasonId(dto.getSeasonId())
         .oracleSymbol(dto.getOracleSymbol())
         .contractAddress(dto.getContractAddress())
-        .progressType(dto.getProgressType())
         .sort(dto.getSort())
         .active(dto.getActive())
         .build());
@@ -157,7 +156,6 @@ public class AchievementOnContractService {
         .seasonId(dto.getSeasonId())
         .oracleSymbol(dto.getOracleSymbol())
         .contractAddress(dto.getContractAddress())
-        .progressType(dto.getProgressType())
         .sort(dto.getSort())
         .active(dto.getActive())
         .build());
diff --git a/src/main/resources/akka/akka-base.conf b/src/main/resources/akka/akka-base.conf
index 8b0468d1..a82b4075 100644
--- a/src/main/resources/akka/akka-base.conf
+++ b/src/main/resources/akka/akka-base.conf
@@ -15,10 +15,7 @@ akka {
   loglevel = "DEBUG"
 
   # NOTE: turn this on in prod
-  coordinated-shutdown {
-    exit-jvm = on
-    termination-timeout = 600s
-  }
+  coordinated-shutdown.exit-jvm = on
 
 
   actor {
diff --git a/src/main/resources/application.properties b/src/main/resources/application.properties
index 69273967..bc2ef6af 100644
--- a/src/main/resources/application.properties
+++ b/src/main/resources/application.properties
@@ -97,7 +97,6 @@ ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
 ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
 ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
 ai.saharaa.web3.credentials_pk=${AI_WEB3_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
-ai.saharaa.web3.achievement_oracle_pk=${AI_WEB3_ACHIEVEMENT_ORACLE_PK:0x4329a9e86a94ec6971f7d3b7f9848c3065bc2d727edcc174309306abbf839339}
 ai.saharaa.web3.achievement_manager_contract_address=${AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS:0x265EED7bF4387a5d1816850f7174cC6469ed9018}
 ai.saharaa.web3.achievement_on_contract_address=${AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS:0xD9348667cef8c0bcaE5186db9478FC6e55Fa8946}
 ai.saharaa.achievement_progress_onchain_batch_size=${ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE:10}
diff --git a/src/test/resources/application.properties b/src/test/resources/application.properties
index 04b9db74..b533a3ec 100644
--- a/src/test/resources/application.properties
+++ b/src/test/resources/application.properties
@@ -67,7 +67,6 @@ ai.saharaa.web3.chain_id=${AI_WEB3_CHAIN_ID:534351}
 ai.saharaa.web3.verifying_contract=${AI_WEB3_VERIFYING_CONTRACT:0xed543e231F15666d528a5d85166e92c4a85370Ca}
 ai.saharaa.web3.rpc_url=${AI_WEB3_RPC_URL:https://testnet.saharalabs.ai}
 ai.saharaa.web3.credentials_pk=${AI_WEB3_CREDENTIALS_PK:0x1111111111111111111111111111111111111111111111111111111111111111}
-ai.saharaa.web3.achievement_oracle_pk=${AI_WEB3_ACHIEVEMENT_ORACLE_PK:0x4329a9e86a94ec6971f7d3b7f9848c3065bc2d727edcc174309306abbf839339}
 ai.saharaa.web3.achievement_manager_contract_address=${AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS:0x265EED7bF4387a5d1816850f7174cC6469ed9018}
 ai.saharaa.web3.achievement_on_contract_address=${AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS:0xD9348667cef8c0bcaE5186db9478FC6e55Fa8946}
 ai.saharaa.achievement_progress_onchain_batch_size=${ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE:10}
